import axiosInstance from './interceptors';
import { endpoints } from './api-endpoints';

export interface ConversationParams {
  agent_id: string;
  conversation_id: string;
  text: string;
  states: Array<{
    key: string;
    value: string;
    active_rounds: number;
  }>;
}

export interface ConversationInitResponse {
  id: string;
  agent_id: string;
  agent_name: string;
  title: string;
  titleAlias: string;
  user: {
    id: string;
    user_name: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    type: string;
    role: string;
    full_name: string;
    source: string;
    external_id: string;
    avatar: string;
    permissions: string[];
    agent_actions: Array<{
      id: string;
      agent_id: string;
      agent: {
        id: string;
        name: string;
        description: string;
        type: string;
        createdDateTime: string;
        updatedDateTime: string;
        llmConfig: {
          is_inherit: boolean;
          provider: string;
          model: string;
          max_recursion_depth: number;
          max_tokens: number;
          temperature: number;
        };
        isPublic: boolean;
        disabled: boolean;
        iconUrl: string;
        profiles: string[];
        mergeUtility: boolean;
        utilities: Array<{
          name: string;
          disabled: boolean;
          functions: Array<{name: string}>;
          templates: Array<{name: string}>;
        }>;
        rules: Array<{
          trigger_name: string;
          disabled: boolean;
          criteria: string;
        }>;
        knowledgeBases: Array<{
          name: string;
          type: string;
          disabled: boolean;
        }>;
        inheritAgentId: string;
        maxMessageCount: number;
        routingRules: Array<{
          type: string;
          field: string;
          description: string;
          field_type: string;
          required: boolean;
          redirectTo: string;
          redirect_to_agent: string;
        }>;
        inputVariables: Array<{
          name: string;
          description: string;
          type: string;
        }>;
        outputVariables: Array<{
          name: string;
          description: string;
          type: string;
        }>;
        workflowId: string;
        applicationId: string;
      };
      actions: string[];
    }>;
    create_date: string;
    update_date: string;
    regionCode: string;
  };
  event: string;
  channel: string;
  task_id: string;
  status: string;
  states: {
    [key: string]: string;
  };
  tags: string[];
  updated_time: string;
  created_time: string;
}

export interface ConversationMessageResponse {
  message_id: string;
  conversation_id: string;
  text: string;
  created_at: string;
  sender: {
    id: string;
    user_name: string;
    first_name: string;
    last_name: string | null;
    email: string | null; 
    phone: string | null;
    type: string;
    role: string;
    full_name: string;
    source: string | null;
    external_id: string | null;
    avatar: string;
    permissions: string[];
    agent_actions: string[];
    create_date: string;
    update_date: string;
    regionCode: string;
  };
  function: string | null;
  instruction: {
    question: string;
    function: string;
    next_action_reason: string;
    conversation_end: boolean;
    task_completed: boolean;
    is_new_task: boolean;
    response: string;
    next_action_agent: string;
    user_goal_agent: string;
    user_goal_description: string;
  };
  rich_content: {
    recipient: {
      id: string;
    };
    messaging_type: string;
    message: {
      rich_type: string;
      text: string;
    };
    fill_postback: boolean;
    editor: string;
  };
  has_message_files: boolean;
  data: any;
  states: {
    [key: string]: string;
  };
}

export interface ConversationLogResponse {
  conversation_id: string;
  sender: {
    id: string;
    user_name: string;
    first_name: string;
    last_name: string | null;
    email: string | null;
    phone: string | null;
    type: string;
    role: string;
    full_name: string;
    source: string | null;
    external_id: string | null;
    avatar: string;
    permissions: string[];
    agent_actions: string[];
    create_date: string;
    update_date: string;
    regionCode: string;
  };
  function: string | null;
  rich_content?: {
    recipient: {
      id: string;
    };
    messaging_type: string;
    message: {
      rich_type: string;
      text: string;
    };
    fill_postback: boolean;
    editor: string;
  };
  has_message_files: boolean;
  created_at: string;
  message_id: string;
  text: string;
  data: any;
  states: {
    [key: string]: string;
  };
}



/**
 * 新建会话
 * @param agentId 代理ID
 * @returns Promise<ConversationInitResponse>
 */
export async function createConversation(
  agentId: string
): Promise<ConversationInitResponse> {
  try {
    const response = await axiosInstance.post(
      endpoints.conversationInitUrl.replace('{agentId}', agentId),
      {},
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error('新建会话失败:', error);
    throw error;
  }
}

/**
 * 发送消息
 * @param conversationId 会话ID
 * @param message 消息
 * @returns Promise<ConversationMessageResponse>
 */
export async function sendMessage(
  param: ConversationParams
): Promise<ConversationMessageResponse> {
  try {
    const response = await axiosInstance.post(endpoints.conversationMessageUrl.replace('{agentId}', param.agent_id).replace('{conversationId}', param.conversation_id), {
      text: param.text,
      states: param.states
    }, {
      timeout: 300000
    });
    return response.data;
  } catch (error) {
    console.error('发送消息失败:', error);
    throw error;
  }
}

/**
 * 获取会话日志
 * @param conversationId 会话ID
 * @returns Promise<ConversationLogResponse>
 */
export async function getConversationLog(
  conversationId: string
): Promise<ConversationLogResponse> {
  try {
    const response = await axiosInstance.get(endpoints.conversationDialogsUrl.replace('{conversationId}', conversationId));
    return response.data;
  } catch (error) {
    console.error('获取会话日志失败:', error);
    throw error;
  }
}