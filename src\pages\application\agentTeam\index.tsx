import React from 'react';
import { Tabs } from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import styles from '../style/index.module.less';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';
import ApplicationList from './components/list';

function AppList() {
  const locale = useLocale();

  return (
    <div className={styles.container}>
      <Tabs className={styles.tabs}>
        {/* 应用页面内容 */}
        <TabPane
          key="appList"
          title={locale['menu.application.header.appList']}
        >
          <ApplicationList />
        </TabPane>
      </Tabs>
    </div>
  );
}

export default AppList;
