import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Grid,
  Typography,
  Modal,
} from '@arco-design/web-react';
import { IconSearch } from '@arco-design/web-react/icon';
import {
  listEmployees,
  listEmployeesall,
} from '@/pages/knowledge/components/knowledge/services/aiStaff-service';
import { fetchKnowledgeCollections } from '@/pages/knowledge/components/knowledge/services/aiStaff-service';
import { Modal as ArcoModal, Input as ArcoInput } from '@arco-design/web-react';
import { getAgentDetail } from '@/lib/services/agent-service';
const { Row, Col } = Grid;
const { Text } = Typography;
const FormItem = Form.Item;
const TextArea = Input.TextArea;

function AbilitySelectModal({ visible, onOk, onCancel, selected }) {
  const [search, setSearch] = useState('');
  const [hovered, setHovered] = useState('');
  const [loading, setLoading] = useState(false);
  const [employeeList, setEmployeeList] = useState([]);
  const [nameModalOpen, setNameModalOpen] = useState(false);
  const [pendingAbility, setPendingAbility] = useState(null);
  const [newAbilityName, setNewAbilityName] = useState('');

  useEffect(() => {
    if (!visible) return;
    setLoading(true);
    listEmployeesall({
      Pager: {
        Page: 1,
        Size: 20,
      },
    })
      .then((res) => {
        setEmployeeList(res?.data?.items || []);
        setLoading(false);
      })
      .catch(() => setLoading(false));
  }, [visible, search]);

  const handleUse = (item) => {
    setPendingAbility(item);
    setNewAbilityName('');
    setNameModalOpen(true);
  };
  const handleNameOk = () => {
    if (!newAbilityName.trim()) return;
    onOk({ ...pendingAbility, newAbilityName });
    setNameModalOpen(false);
  };

  return (
    <>
      <Modal
        title="选择能力"
        visible={visible}
        onCancel={onCancel}
        footer={null}
        style={{ width: 700 }}
      >
        <div style={{ display: 'flex', gap: 12, marginBottom: 16 }}>
          <Input
            prefix={<IconSearch />}
            placeholder="AI搜索..."
            value={search}
            onChange={setSearch}
            style={{ flex: 1 }}
          />
        </div>
        <div>
          {loading ? (
            <div>加载中...</div>
          ) : (
            employeeList
              .filter((a) => a.name.includes(search))
              .map((item) => (
                <div
                  key={item.id}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    background: hovered === item.id ? '#f7f8fa' : '#fff',
                    borderRadius: 8,
                    marginBottom: 12,
                    padding: 16,
                  }}
                  onMouseEnter={() => setHovered(item.id)}
                  onMouseLeave={() => setHovered('')}
                >
                  <div
                    style={{
                      width: 40,
                      height: 40,
                      background: '#e6f0ff',
                      borderRadius: 8,
                      marginRight: 16,
                    }}
                  />
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 500 }}>{item.name}</div>
                    <div
                      style={{ color: '#888', fontSize: 13, margin: '4px 0' }}
                    >
                      {item.description}
                    </div>
                  </div>
                  <Button
                    type="primary"
                    size="small"
                    style={{ minWidth: 60 }}
                    onClick={() => handleUse(item)}
                    disabled={selected === item.id}
                  >
                    {selected === item.id ? '已选择' : '使用'}
                  </Button>
                </div>
              ))
          )}
        </div>
      </Modal>
      <ArcoModal
        title="输入新能力名称"
        visible={nameModalOpen}
        onOk={handleNameOk}
        onCancel={() => setNameModalOpen(false)}
        okButtonProps={{ disabled: !newAbilityName.trim() }}
      >
        <ArcoInput
          placeholder="请输入新能力名称"
          value={newAbilityName}
          onChange={setNewAbilityName}
        />
      </ArcoModal>
    </>
  );
}

function KnowledgeSelectModal({ visible, onOk, onCancel, selected }) {
  const [search, setSearch] = useState('');
  const [hovered, setHovered] = useState('');
  const [loading, setLoading] = useState(false);
  const [knowledgeList, setKnowledgeList] = useState([]);
  // 新增：内部维护临时多选状态
  const [selectedList, setSelectedList] = useState(selected || []);

  useEffect(() => {
    if (!visible) return;
    setLoading(true);
    fetchKnowledgeCollections({ KeyWord: search })
      .then((res) => {
        setKnowledgeList(res?.data || res || []);
        setLoading(false);
      })
      .catch(() => setLoading(false));
  }, [visible, search]);

  // 弹窗打开时同步外部selected
  useEffect(() => {
    if (visible) setSelectedList(selected || []);
  }, [visible, selected]);

  const handleAdd = (item) => {
    if (
      !selectedList.some(
        (k) => (typeof k === 'object' ? k.name : k) === item.name
      )
    ) {
      setSelectedList([...selectedList, item]);
    }
  };
  const handleRemove = (item) => {
    setSelectedList(
      selectedList.filter(
        (k) => (typeof k === 'object' ? k.name : k) !== item.name
      )
    );
  };
  const handleOk = () => {
    onOk(selectedList);
    onCancel();
  };

  return (
    <Modal
      title="选择库"
      visible={visible}
      onCancel={onCancel}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Button onClick={onCancel} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button type="primary" onClick={handleOk}>
            确定
          </Button>
        </div>
      }
      style={{ width: 700 }}
    >
      <div style={{ display: 'flex', gap: 12, marginBottom: 16 }}>
        <Input
          prefix={<IconSearch />}
          placeholder="AI搜索..."
          value={search}
          onChange={setSearch}
          style={{ flex: 1 }}
        />
        <Input placeholder="标签" style={{ width: 120 }} disabled />
      </div>
      <div>
        {loading ? (
          <div>加载中...</div>
        ) : (
          knowledgeList
            .filter((k) => k.name.includes(search))
            .map((item) => {
              const isSelected = selectedList.some(
                (k) => (typeof k === 'object' ? k.name : k) === item.name
              );
              return (
                <div
                  key={item.name}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    background: hovered === item.name ? '#f7f8fa' : '#fff',
                    borderRadius: 8,
                    marginBottom: 12,
                    padding: 16,
                  }}
                  onMouseEnter={() => setHovered(item.name)}
                  onMouseLeave={() => setHovered('')}
                >
                  <div
                    style={{
                      width: 40,
                      height: 40,
                      background: '#e6f0ff',
                      borderRadius: 8,
                      marginRight: 16,
                    }}
                  />
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 500 }}>{item.name}</div>
                    <div
                      style={{ color: '#888', fontSize: 13, margin: '4px 0' }}
                    >
                      {item.desc || item.description}
                    </div>
                  </div>
                  {isSelected ? (
                    <Button
                      type="outline"
                      size="small"
                      style={{
                        minWidth: 60,
                        color: '#f53f3f',
                        borderColor: '#f53f3f',
                      }}
                      onClick={() => handleRemove(item)}
                    >
                      移除
                    </Button>
                  ) : (
                    <Button
                      type="primary"
                      size="small"
                      style={{ minWidth: 60 }}
                      onClick={() => handleAdd(item)}
                    >
                      添加
                    </Button>
                  )}
                </div>
              );
            })
        )}
      </div>
    </Modal>
  );
}

export default function AiStaffSettings({ formData, setFormData, isEditing }) {
  const [form] = Form.useForm();
  const [abilityModal, setAbilityModal] = useState(false);
  const [knowledgeModal, setKnowledgeModal] = useState(false);
  const [selectedAbility, setSelectedAbility] = useState(
    formData.abilityAgentId || ''
  );
  const [selectedAbilityName, setSelectedAbilityName] = useState(
    formData.abilityName || ''
  );
  const [selectedKnowledge, setSelectedKnowledge] = useState(
    formData.knowledges || []
  );
  // 新增：标记用户是否手动编辑过知识库
  const [knowledgeManuallyEdited, setKnowledgeManuallyEdited] = useState(false);

  // 同步到父组件
  const syncForm = () => {
    const values = form.getFieldsValue();
    setFormData({
      ...formData,
      ...values,
      abilityAgentId: selectedAbility,
      abilityName: selectedAbilityName,
      knowledges: selectedKnowledge,
    });
  };

  // 外部prompt变化时同步到表单输入框
  useEffect(() => {
    form.setFieldsValue({ prompt: formData.prompt || '' });
  }, [formData.prompt]);

  // 新增：监听selectedKnowledge变化自动同步到父组件
  useEffect(() => {
    setSelectedKnowledge(formData.knowledges || []);
  }, [formData.knowledges]);

  return (
    <div style={{ padding: 0 }}>
      <Form
        form={form}
        layout="vertical"
        autoComplete="off"
        onChange={syncForm}
        initialValues={{
          prompt: formData.prompt || '',
          ability: formData.ability || '',
          knowledges: formData.knowledges || [],
        }}
      >
        <Row style={{ marginTop: 16 }}>
          <Col span={24}>
            <FormItem
              label="提示词"
              field="prompt"
              rules={[{ maxLength: 100, message: '最多100字' }]}
            >
              <TextArea
                placeholder="请输入提示词"
                maxLength={100}
                showWordLimit
                disabled={!isEditing}
                style={{ height: 60 }}
              />
            </FormItem>
          </Col>
        </Row>
        <Row style={{ marginTop: 16 }}>
          <Col span={24}>
            <FormItem
              label="员工能力"
              field="abilityAgentId"
              rules={[{ required: true, message: '请选择员工能力' }]}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <Input
                  value={selectedAbilityName}
                  placeholder="请选择能力"
                  readOnly
                  style={{ flex: 1 }}
                  disabled
                />
                <Button
                  type="primary"
                  onClick={() => setAbilityModal(true)}
                  disabled={!isEditing}
                >
                  选择能力
                </Button>
              </div>
            </FormItem>
          </Col>
        </Row>
        <Row style={{ marginTop: 16 }}>
          <Col span={24}>
            <FormItem label="知识库" field="knowledges">
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <Input
                  value={selectedKnowledge
                    .map((k) => (typeof k === 'string' ? k : k?.name))
                    .filter(Boolean)
                    .join('、')}
                  placeholder="请选择知识库"
                  readOnly
                  style={{ flex: 1 }}
                  disabled
                />
                <Button
                  type="primary"
                  onClick={() => setKnowledgeModal(true)}
                  disabled={!isEditing}
                >
                  选择库
                </Button>
              </div>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <AbilitySelectModal
        visible={abilityModal}
        selected={selectedAbility}
        onOk={async (item) => {
          setSelectedAbility(item.id);
          setSelectedAbilityName(item.newAbilityName);
          // 只有在未手动编辑知识库时才覆盖
          if (!knowledgeManuallyEdited) {
            try {
              const agentDetailRes = await getAgentDetail(item.agent.id);
              const agentDetail = agentDetailRes?.data;
              const knowledges = Array.isArray(agentDetail.knowledge_bases)
                ? agentDetail.knowledge_bases.map((kb) => ({
                    name: kb.name,
                    type: kb.type,
                    disabled: kb.disabled,
                  }))
                : [];
              setFormData({
                ...formData,
                abilityName: item.newAbilityName,
                prompt: agentDetail.instruction,
                knowledges,
              });
            } catch {
              setFormData({
                ...formData,
                abilityName: item.newAbilityName,
              });
            }
          } else {
            setFormData({
              ...formData,
              abilityName: item.newAbilityName,
            });
          }
          setAbilityModal(false);
        }}
        onCancel={() => setAbilityModal(false)}
      />
      <KnowledgeSelectModal
        visible={knowledgeModal}
        selected={selectedKnowledge}
        onOk={(list) => {
          setSelectedKnowledge(list);
          setKnowledgeModal(false);
          setKnowledgeManuallyEdited(true); // 用户手动编辑知识库
          setFormData({
            ...formData,
            knowledges: list,
          });
        }}
        onCancel={() => setKnowledgeModal(false)}
      />
    </div>
  );
}
