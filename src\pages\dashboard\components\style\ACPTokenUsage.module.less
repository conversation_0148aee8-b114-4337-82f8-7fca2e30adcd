@layout-text-color: #333333;

.card {
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid #f5f5f5;
    border-radius: 8px;
    padding: 20px 24px;

    :global(.arco-card-body) {
        padding: 0;
    }

    :global(.arco-card-header) {
        padding: 0;
        height: auto;
        margin-bottom: 16px;
    }

    .cardHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .HeaderTag {
            display: flex;
            align-items: center;
            gap: 12px;

            .title {
                color: var(--color-title-1);
                font-weight: 600;
                font-size: 14px;
                line-height: 24px;
            }
        }

        .HeaderControls {
            display: flex;
            align-items: center;

            //筛选select
            :global(.arco-select-size-default.arco-select-single .arco-select-view) {
                padding: 6px 8px;
                height: auto;
                font-weight: 400;
                font-size: 12px;
                line-height: 20px;
                color: #adadad;
                border-radius: 8px;
                border: 1px solid #f5f5f5;
                background-color: #ffffff;
                transition: all 0.2s;
                box-sizing: border-box;


                &:hover {
                    background-color: #fafafa;
                    border-color: #ebebeb;
                }

                :global(.arco-select-prefix) {
                    margin-right: 4px;
                }

                :global(.arco-select-view-input) {
                    &::placeholder {
                        color: #d6d6d6;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 24px;
                    }
                }
            }

            :global(.arco-btn-group) {
                display: flex;
                padding: 4px;
                border-radius: 8px;
                border: 1px solid #f5f5f5;
                gap: 4px;
                box-sizing: border-box;

                :global(.arco-btn-secondary:not(.arco-btn-disabled)) {
                    background-color: transparent;
                    color: #adadad;
                }

                :global(.arco-btn-primary:not(.arco-btn-disabled)) {
                    background-color: #fafafa;
                    color: #5c5c5c;
                }

                button {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 4px;
                    border: none !important;
                    padding: 2px 8px;
                    height: 24px;

                    span {
                        font-weight: 500;
                        font-size: 12px;
                        line-height: 20px;
                        text-align: center;
                    }
                }
            }
        }
    }

    .chart {
        width: 100%;
        min-height: 200px;
        transition: height 0.3s ease;
    }
}