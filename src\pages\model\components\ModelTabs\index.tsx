// src/components/ModelTabs.jsx
import React, { useState } from 'react';
import { Tabs } from '@arco-design/web-react';
import ModelList from '../ModelList';
import SecretKey from '../SecretKey';
import Supplier from '../Supplier';
import ModelConfig from '../ModelConfig';
import styles from './style/index.module.less';
import { useLocation } from 'react-router-dom';

const TabPane = Tabs.TabPane;

function ModelTabs() {
    const location = useLocation(); // 获取路由状态
    const [activeTab, setActiveTab] = useState(() => {
        return location.state?.fromModel ? 'modelList' : 'modeConfig';
    });// 根据 location.state.fromDetail 动态设置初始值

    // 如果需要监听路由变化，可以添加 useEffect（可选）
    // useEffect(() => {
    //     if (location.state?.fromDetail) {
    //         setActiveTab('modelList');
    //     }
    // }, [location.state]);

    return (
        <div className={styles.container}>
            <Tabs
                activeTab={activeTab}
                onChange={setActiveTab}
                type="line"
                className={styles.tabs}
            >
                <TabPane key="modeConfig" title="模型配置">
                    <ModelConfig />
                </TabPane>
                <TabPane key="supplier" title="供应商">
                    <Supplier />
                </TabPane>
                <TabPane key="modelList" title="模型">
                    <ModelList />
                </TabPane>
                <TabPane key="secretKey" title="密钥">
                    <SecretKey />
                </TabPane>
            </Tabs>
        </div>
    );
}

export default ModelTabs;