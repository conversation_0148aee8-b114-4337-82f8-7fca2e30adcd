// AI员工相关请求参数类型定义

export type PagerType = {
    Page?: number;
    Size?: number;
    Sort?: string;
    Order?: string;
    Offset?: number;
    ReturnTotal?: boolean;
};

export interface IFetchEmployeeListRequestParams {
    EmployeeIds?: string[];
    Name?: string;
    IsPublic?: boolean;
    Disabled?: boolean;
    Tags?: string[];
    CreateUserId?: string;
    AgentId?: string;
    Pager?: PagerType;
}
