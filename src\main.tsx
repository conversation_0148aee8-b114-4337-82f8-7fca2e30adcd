import './style/global.less';
import './style/tailwind.css';
import React, { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';
import { createStore } from 'redux';
import { Provider } from 'react-redux';
import { ConfigProvider } from '@arco-design/web-react';
import zhCN from '@arco-design/web-react/es/locale/zh-CN';
import enUS from '@arco-design/web-react/es/locale/en-US';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import axios from 'axios';
import rootReducer from './store';
import PageLayout from './layout';
import { GlobalContext } from './context';
import LoginMain from './pages/login/components/main'; // 直接导入登录主组件
import checkLogin from './utils/checkLogin';
import changeTheme from './utils/changeTheme';
import useStorage from './utils/useStorage';
import { myInfo, getUserAvatar } from '@/lib/services/auth-service';

const store = createStore(rootReducer);

function Index() {
  const [lang, setLang] = useStorage('arco-lang', 'en-US');
  const [theme, setTheme] = useStorage('arco-theme', 'light');

  function getArcoLocale() {
    switch (lang) {
      case 'zh-CN':
        return zhCN;
      case 'en-US':
        return enUS;
      default:
        return zhCN;
    }
  }

  useEffect(() => {
    if (checkLogin()) {
      const fetchData = async () => {
        try {
          const response = await myInfo();
          if (response) {
            // 获取用户头像
            try {
              const avatarData = await getUserAvatar();
              if (
                avatarData &&
                typeof avatarData === 'string' &&
                avatarData.startsWith('data:')
              ) {
                // 如果成功获取头像，将其添加到用户信息中
                response.avatar_url = avatarData;
              }
            } catch (avatarError) {
              console.log('获取头像失败，使用默认头像:', avatarError);
              // 头像获取失败不影响用户信息加载
            }

            store.dispatch({
              type: 'update-userInfo',
              payload: { userInfo: response, userLoading: false },
            });
          } else {
            console.error('Invalid response structure:', response);
          }
        } catch (error) {
          console.error('Failed to fetch user info:', error);
          // 可选：处理错误，例如跳转到登录页
          // window.location.pathname = '/login';
        }
      };

      fetchData();
    } else if (window.location.pathname.replace(/\//g, '') !== 'login') {
      window.location.pathname = '/login';
    }
  }, []);

  useEffect(() => {
    changeTheme(theme);
  }, [theme]);

  const contextValue = {
    lang,
    setLang,
    theme,
    setTheme,
  };

  return (
    <BrowserRouter>
      <ConfigProvider
        locale={getArcoLocale()}
        componentConfig={{
          Card: {
            bordered: false,
          },
          List: {
            bordered: false,
          },
          Table: {
            border: false,
          },
        }}
      >
        <Provider store={store}>
          <GlobalContext.Provider value={contextValue}>
            <Routes>
              <Route path="/login" element={<LoginMain />} />
              {/* 动态路由定义 */}
              <Route path="/acp/servers/:id" element={<PageLayout />} />
              <Route path="/timeSequenceCard/cards/:id" element={<PageLayout />} />
              <Route path="/*" element={<PageLayout />} />
            </Routes>
          </GlobalContext.Provider>
        </Provider>
      </ConfigProvider>
    </BrowserRouter>
  );
}

const container = document.getElementById('root');
if (!container) throw new Error('Failed to find the root element');

const root = createRoot(container);
root.render(<Index />);