// AI员工管理接口服务

import axiosInstance from '@/lib/services/interceptors';

export interface EmployeeListParams {
  Pager?: {
    Page: number;
    Size: number;
  };
  Name?: string;
  Disabled?: boolean | null;
  Tags?: string;
  'Pager.Sort'?: string;
  'Pager.Order'?: string;
  IsPublic?: boolean;
  CreateUserId?: string;
}

export interface EmployeeListResponse {
  items: any[];
  count: number;
}

export async function listEmployees(
  params: EmployeeListParams
): Promise<EmployeeListResponse> {
  try {
    const response = await axiosInstance.get('/api/employee/manage-employees', {
      params: {
        CreateUserId: params.CreateUserId || '',
        'Pager.Page': params.Pager?.Page || 1,
        'Pager.Size': params.Pager?.Size || 16,
        Name: params?.Name,
        Tags: params?.Tags,
      },
    });
    return response.data;
  } catch (error) {
    console.error('获取AI员工列表失败:', error);
    throw error;
  }
}


export const listEmployeesall = (
  params?: {
    Pager?: {
      Page: number;
      Size: number;
    };
    Name?: string;
    Disabled?: boolean | null;
    Tags?: string;
    'Pager.Sort'?: string;
    'Pager.Order'?: string;
    IsPublic?: boolean;
    ExcludePersonalCreated?: boolean;
  }
) => axiosInstance.get('/api/employees', {
  params: {
    ExcludePersonalCreated: false,
    'Pager.Page': params.Pager?.Page || 1,
    'Pager.Size': params.Pager?.Size || 16,
    IsPublic: true,
    Disabled: false,
    Name: params?.Name || '',
  }
});


// 启用AI员工
export const enableEmployee = (employeeId: string) =>
  axiosInstance.put(`/api/employee/${employeeId}/enable`);

// 禁用AI员工
export const disableEmployee = (employeeId: string) =>
  axiosInstance.put(`/api/employee/${employeeId}/disable`);

// 删除AI员工
export const deleteEmployee = (employeeId: string) =>
  axiosInstance.delete(`/api/employee/${employeeId}`);


export const getEmployeeTag = () =>
  axiosInstance.get(`/api/employee/tags`);

// 创建AI员工
export const createEmployee = (data: {
  name?: string;
  description?: string;
  tags?: string[];
  isPublic?: boolean;
  agentId?: string | null;
}) => axiosInstance.post('/api/employee', data);

// 获取知识库列表
export const fetchKnowledgeCollections = (params?: {
  page?: number;
  page_size?: number;
  keywords?: string;
  orderby?: string;
  desc?: string;
  role?: string;
  permission?: string;
}) => axiosInstance.post('/v1/kb/list', { params });


export const getEmployeeDetail = (employeeId: string) =>
  axiosInstance.get(`/api/employee/${employeeId}`);

export const updateEmployee = (employeeId: string, data: any) =>
  axiosInstance.put(`/api/employee/${employeeId}`, data);


/**
 * 获取知识库关联的AI员工列表
 * @param knowledgeId 知识库ID
 * @returns AI员工列表
 */
export async function getKnowledgeAssociatedEmployees(knowledgeId: string) {
  const response = await axiosInstance.get(
    `/api/employee/knowledge-associated-employees/${knowledgeId}`
  );
  return response.data;
}

