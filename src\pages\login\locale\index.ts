const i18n = {
  'en-US': {
    'login.logo.alt': 'Logo Image',
    'login.logo.text': 'Agent foundry',
    'login.platform.name': '智用大模型应用平台',
    'login.platform.subtitle': '以Agent为核心、模型为驱动力融合首创ACP多智能体协作新范式，打造的多智能体协作运行管理平台',
    'login.experience.button': '立即体验',
    'login.agent.team.title': 'Agent Team',
    'login.agent.team.description': '颠覆人机交互，实现传统应用到应用智能的质的飞跃，链接每一个智能体从点到线再到面，具像化"多智能"',
    'login.acp.title': 'ACP (Agentic Context Protocol)',
    'login.acp.description': '多智能体协作新范式，标准化智能体间的交互逻辑与语义规则，构建轻量化、高扩展的协作框架，降低多智能体系统开发的复杂性，实现高效协同与自主决策的无缝衔接。',
  },
  'zh-CN': {
    'login.logo.alt': 'Logo Image',
    'login.logo.text': 'Agent foundry',
    'login.platform.name': '智用大模型应用平台',
    'login.platform.subtitle': '以Agent为核心、模型为驱动力融合首创ACP多智能体协作新范式，打造的多智能体协作运行管理平台',
    'login.experience.button': '立即体验',
    'login.agent.team.title': 'Agent Team',
    'login.agent.team.description': '颠覆人机交互，实现传统应用到应用智能的质的飞跃，链接每一个智能体从点到线再到面，具像化"多智能"',
    'login.acp.title': 'ACP (Agentic Context Protocol)',
    'login.acp.description': '多智能体协作新范式，标准化智能体间的交互逻辑与语义规则，构建轻量化、高扩展的协作框架，降低多智能体系统开发的复杂性，实现高效协同与自主决策的无缝衔接。',
  },
};

export default i18n;
