:global(body) {
  overflow: hidden;
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
  /* 对于基于 WebKit 的浏览器 */
}

/* 对于IE和Edge */
html {
  -ms-overflow-style: none;
  /* IE 和 Edge */
}

/* 对于Firefox */
* {
  scrollbar-width: none;
  /* Firefox */
}

:global(.arco-popover-content) {
  padding: 8px;
  background: #FFFFFF;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
  border: 1px solid #f5f5f5;
  border-radius: 8px;

  :global(.arco-popover-inner-content) {
    span {
      padding: 4px 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      height: 32px !important;
      width: 120px !important;
      text-align: left;
      color: #333333 !important;
      border-radius: 4px;
      box-sizing: border-box;

      &:hover {
        background: #f5f5f5;
      }

      &:global(.enable) {
        color: RGBA(0, 180, 42, 0.95) !important;
        background: RGBA(0, 180, 42, 0.04);

        &:hover {
          background: RGBA(0, 180, 42, 0.08);
        }
      }
    }

    .black {
      color: RGBA(0, 0, 0, 0.8) !important;
      background: RGBA(0, 0, 0, 0.02) !important;

      &:hover {
        background: RGBA(0, 0, 0, 0.04) !important;
      }
    }
  }
}

:global(.knowledge-select-popup .arco-select-popup) {
  margin-right: 24px;

  :global(.arco-select-popup-inner) {
    width: 200px;
    max-height: 400px;
    overflow-y: auto;

    /* 恢复滚动条样式 */
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    :global(.arco-select-option) {
      height: 40px;
      line-height: 40px;
      padding: 0 12px;
    }
  }
}

.container {
  display: flex;
  flex-direction: column;
  will-change: transform;
  -webkit-overflow-scrolling: touch;
  position: relative;

  .titleSection {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .titleDom {
      margin: 0;
      margin-right: 16px;
    }

    .tabs {
      :global(.arco-tabs-header-nav::before) {
        display: none;
      }

      :global(.arco-tabs-header) {
        width: 100%;
      }

      :global(.arco-tabs-header-ink) {
        display: none;
      }

      :global(.arco-tabs-header-title) {
        padding: 0;
        font-weight: 600;
        font-size: 20px;
        color: RGBA(0, 0, 0, .35);
        margin-left: 0px !important;
        margin-right: 24px;

        &:hover {
          color: RGBA(0, 0, 0, .8);
          background: none !important;
        }
      }

      :global(.arco-tabs-header-title-active) {
        color: #333333;

        &:hover {
          color: #333333;
        }
      }

      :global(.arco-tabs-content) {
        padding-top: 24px;
      }
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid #f5f5f5;

    //搜索输入框
    :global(.arco-input-inner-wrapper) {
      padding: 8px;
      background-color: #ffffff;
      border-radius: 8px;
      border: 1px solid #f5f5f5;
      transition: all 0.2s;

      &:hover {
        background-color: #fafafa;
        border-color: #ebebeb;
      }

      ::placeholder {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #d6d6d6;
      }

      :global(.arco-input) {
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 8px;
      }
    }

    //筛选select
    :global(.arco-select-size-default.arco-select-single .arco-select-view) {
      padding: 8px;
      height: auto;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #d6d6d6;
      border-radius: 8px;
      border: 1px solid #f5f5f5;
      background-color: #ffffff;
      transition: all 0.2s;

      &:hover {
        background-color: #fafafa;
        border-color: #ebebeb;
      }

      :global(.arco-select-prefix) {
        margin-right: 4px;
      }

      :global(.arco-select-view-input) {
        &::placeholder {
          color: #d6d6d6;
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
        }
      }
    }

    .addBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 24px;
      background-color: #4d5ef3;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      border-radius: 8px;
      transition: all 0.3s;
      height: 40px;

      &:hover {
        background-color: #3144f1;
      }
    }

    .rowEndCenter {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .countAppText {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #adadad;
        margin-right: 8px;
      }

      .searchBox {
        width: 240px;
        height: 38px;
        border-radius: 8px;
        outline: none;
        font-size: 14px;
        font-weight: 400;
        background-color: #ffffff;
        margin-right: 8px;
        transition: all 0.2s ease;

        &:hover {
          background-color: RGBA(0, 0, 0, 0.02);
        }

        &:focus {
          background-color: #ffffff;
        }

        :global(.arco-input-inner-wrapper) {
          background-color: transparent;
          border-radius: 8px;
          height: 40px;
          padding: 0 8px;
        }

        :global(.arco-input) {
          height: 40px;
          line-height: 40px;
        }
      }

      .selectBox {
        height: 40px;
        width: 160px;
        border-radius: 8px;
        transition: all 0.2s ease;

        &:hover {
          background-color: RGBA(0, 0, 0, 0.02);
        }

        &:focus-within {
          background-color: #ffffff;
        }

        :global(.arco-select-view) {
          height: 40px;
          width: 160px;
          border-radius: 8px;
          background-color: transparent;
          display: flex;
          align-items: center;
        }

        :global(.arco-select-view-value) {
          color: RGBA(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
        }

        :global(.arco-select-view-value[title='']) {
          color: RGBA(0, 0, 0, 0.25);
        }
      }
    }
  }

  .customCardBox {
    height: calc(100vh - 160px);
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    padding-top: 16px;
    position: relative;
    will-change: transform;
    align-content: flex-start;

    .rowStartCenter {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
    }

    .customCard {
      margin-bottom: 0;
      margin-right: 0;
      min-width: 240px;
      height: 240px;
      border-radius: 8px;
      border: 1px solid #f5f5f5;
      cursor: pointer;

      &:hover {
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

      }

      .folderIcon {
        max-width: 48px;
        max-height: 48px;
        margin-right: 12px;
        display: flex;
        align-items: center;
        padding-top: 0;

        img {
          max-width: 48px;
          max-height: 48px;
        }
      }

      .name {
        color: #333333;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        word-break: break-word;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        min-height: 48px;
        padding-top: 0;
        display: flex;
        align-items: center;
      }

      .groupName {
        color: RGBA(0, 0, 0, 0.8);
        font-size: 20px;
        font-weight: 600;
      }

      .tag {
        padding: 2px 6px;
        border-radius: 4px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        font-weight: 400;
        font-size: 12px;
        margin-right: 4px;
        color: RGBA(0, 0, 0, 0.65);
      }

      .description {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
      }

      .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: absolute;
        bottom: 30px;
        left: 24px;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        width: calc(100% - 48px);
        z-index: 1;

        .footerText {
          color: #adadad;
          font-weight: 400;
          font-size: 12px;
          line-height: 20px;
        }

        .hoverContainer {
          display: flex;
          gap: 8px;
          align-items: center;
          min-width: 0;
          transition: all 0.2s ease;
          position: absolute;
          right: 0;
          opacity: 0;
          transform: translateX(10px);
          width: 100%;

          .metaText {
            font-weight: 400;
            font-size: 12px;
            line-height: 20px;
            color: #adadad;
            line-height: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            margin-right: 8px;
          }

          .triggerBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            // opacity: 0;
            transition: all 0.3s;

            &:hover {
              background-color: #fafafa;
            }
          }

          .iconMoreContainer {
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            flex-shrink: 0;

            &:active {
              background-color: rgba(0, 0, 0, 0.04);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            &.active {
              background-color: rgba(0, 0, 0, 0.04);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }

      &:hover {
        .footer {
          .footerText {
            opacity: 0;
            transform: translateX(-10px);
          }

          .hoverContainer {
            opacity: 1;
            transform: translateX(0);
          }
        }
      }
    }
  }

  .loadingContainer {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    z-index: 1;

    .loadingText {
      margin-top: 12px;
      color: RGBA(0, 0, 0, 0.5);
      font-size: 14px;
    }
  }

  .emptyContainer {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;

    svg {
      width: 120px;
      height: 120px;
      margin-bottom: 16px;
    }

    .emptyText {
      font-size: 14px;
      color: RGBA(0, 0, 0, 0.45);
      line-height: 22px;
    }
  }
}

.profileTag {
  display: inline-block;
  padding: 2px 6px;
  margin-right: 8px;
  background-color: #ffffff;
  border: 1px solid #ebebeb;
  border-radius: 4px;
  color: #5c5c5c;
  cursor: default;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;


  &:hover {
    background-color: RGBA(0, 0, 0, 0.02);
  }

  .tooltipProfilesContainer {
    padding: 4px;
    max-width: 200px;
  }

  .tooltipProfileItem {
    padding: 4px 8px;
    margin-bottom: 4px;
    background-color: #ffffff;
    border: 1px solid RGBA(0, 0, 0, 0.15);
    border-radius: 4px;
    font-size: 12px;
    color: RGBA(0, 0, 0, 0.65);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.backToTop {
  position: fixed;
  left: 55%;
  bottom: 40px;
  width: 130px;
  transform: translateX(-50%);
  height: 38px;
  border-radius: 24px;
  background: #fff;
  border: 1px solid RGBA(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 9999;
  gap: 8px;

  span {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.64);
    font-weight: 500;
  }

  &:hover {
    background: #f2f3f5;
  }

  &.hidden {
    opacity: 0;
    pointer-events: none;
  }
}

.subtitleTitle {
  font-size: 20px;
  font-weight: 600;
  line-height: 32px;
  color: #333333;
}

:global(.arco-form-item-wrapper) {
  width: 100%;
  flex: 1;
}

.iconContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;

  .nameContainer {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    :global(.arco-form-item) {
      width: 100%;
      margin-bottom: 0px;

      :global(.arco-form-item-control-children) {
        width: 100%;
      }

      :global(.arco-input) {
        width: 100%;
      }
    }

  }

  .divider {
    width: 1px;
    height: 72px;
    background-color: #f5f5f5;
    margin: 0 24px;
    flex-shrink: 0;
  }
}

.titleRow {
  margin-top: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  width: 100%;

  .titleContent {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .subtitlePlaceholder {
      font-size: 12px;
      font-weight: 400;
      color: RGBA(0, 0, 0, 0.32);
    }

  }

  .switchContainer {
    display: flex;
    align-items: center;
    gap: 8px;
    height: 40px;
    width: 76px;
    justify-content: center;
    border-radius: 8px;
    background-color: transparent;
    padding: 0 2px;

    :global(.arco-switch) {
      min-width: 40px;
      height: 24px;
    }

    :global(.arco-switch-checked) {
      background-color: RGBA(13, 41, 254, 0.95) !important;
    }
  }

  .addLabelBut {
    width: 76px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid RGBA(0, 0, 0, 0.08);
    border-radius: 8px;
    cursor: pointer;
    background-color: transparent;
    padding: 0 2px;

    :global(.arco-icon) {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      flex-shrink: 0;
    }

    .operateText {
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      display: flex;
      align-items: center;
      height: 100%;
      color: #000000A3;
    }

    &:hover {
      background-color: RGBA(0, 0, 0, 0.02);
    }
  }
}

.labelContainer {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  border-radius: 8px;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
  margin-top: 8px;


  .selectedItemList {
    box-sizing: border-box;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 8px;
    border-radius: 4px;
    min-height: 48px;
    background-color: RGBA(0, 0, 0, 0.01);
    transition: all 0.3s ease;
    border: 1px solid RGBA(0, 0, 0, .08);
    border-radius: 8px;

    &:hover {
      border-color: RGBA(0, 0, 0, 0.15);
    }

    .selectedItemRow {
      width: 100%;
      display: flex;
      align-items: center;
      border-radius: 8px;

      .selectedItemCol {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;

        :global(.arco-input-inner-wrapper) {
          height: 40px;
          border-radius: 8px;
          background-color: #fff;
          border: 1px solid RGBA(0, 0, 0, .08);
        }

        .deleteIcon {
          width: 24px;
          height: 24px;
          cursor: pointer;
          opacity: 0.65;
          transition: opacity 0.2s ease;

          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }
}

.operateButGroup {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  margin: 0 24px 24px 0;

  .text {
    font-size: 14px;
    font-weight: 600;
  }

  .but {
    border-radius: 8px;
    width: 76px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .cancelBut {
    background-color: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #00000014;

    .text {
      color: #000000A3;
      font-size: 14px;
      font-weight: 500;

    }

    &:hover {
      background-color: RGBA(0, 0, 0, 0.02);
    }
  }

  .createBut {
    background-color: #4455F2;
    transition: all 0.3s ease;

    .text {
      color: white;
    }

    &:hover {
      background-color: #3144f1;
    }

    &.disabled {
      opacity: 0.32;
      cursor: not-allowed;
      pointer-events: none;
      background-color: #4455F2;
    }

    &[disabled] {
      opacity: 0.32;
      cursor: not-allowed;
      pointer-events: none;
      background-color: #4455F2;
    }
  }
}


:global(.arco-modal-footer) {
  padding: 0;
  border-top: none;
}

:global(.arco-input)::placeholder {
  color: #00000029;
  font-size: 14px;
}

:global(.arco-textarea)::placeholder {
  color: #00000029;
  font-size: 14px;
}

.customModal {
  :global(.arco-modal-close-icon) {
    color: #000000A3;
    top: 24px;
    font-size: 20px;
  }
}