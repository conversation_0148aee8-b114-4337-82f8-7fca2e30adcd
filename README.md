# Arco Design Pro

## 版本要求


#### node建议版本 18.20.x


## 快速开始

```
// 初始化项目
npm install

// 开发模式
npm run dev

// 构建
npm run build

```

# AgentFoundry UI

## Docker部署说明

本项目提供了两种部署模式：开发模式和生产模式。

### 开发模式

开发模式使用Vite热重载开发服务器，适合开发和测试。

```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up -d
```

开发模式会启动以下服务：
- Nginx服务器（端口3000）
- Vite开发服务器
- Elsa工作流服务

### 生产模式

生产模式构建静态资源并使用Nginx直接提供服务，适合生产环境部署。

```bash
# 构建并启动生产环境
docker-compose up -d
```

生产模式会启动以下服务：
- 前端服务（包含Nginx和构建好的静态资源，端口3000）
- Elsa工作流服务

### 自定义配置

如需修改配置，可以：
1. 修改环境变量
2. 修改elsa/nginx.conf文件（开发模式下）
3. 直接修改Dockerfile.prod中的nginx配置（生产模式下）

## 本地开发

```bash
# 安装依赖
yarn install

# 启动开发服务器
yarn dev
```
