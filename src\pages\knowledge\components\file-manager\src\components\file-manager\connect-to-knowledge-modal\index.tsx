import { useTranslate } from '@/pages/knowledge/components/file-manager/src/hooks/common-hooks';
import { useFetchKnowledgeList } from '@/pages/knowledge/components/file-manager/src/hooks/knowledge-hooks';
import { IModalProps } from '@/pages/knowledge/components/file-manager/src/interfaces/common';
import { filterOptionsByInput } from '@/pages/knowledge/components/file-manager/src/utils/common-util';
import { Form, Modal, Select } from 'antd';
import { useEffect } from 'react';

const ConnectToKnowledgeModal = ({
  visible,
  hideModal,
  onOk,
  initialValue,
  loading,
}: IModalProps<string[]> & { initialValue: string[] }) => {
  const [form] = Form.useForm();
  const { list, loading: listLoading } = useFetchKnowledgeList();
  const { t } = useTranslate('fileManager');

  // console.log('🔄 ConnectToKnowledgeModal - visible:', visible, 'list:', list, 'listLoading:', listLoading);

  const options = list?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  // console.log('🎯 ConnectToKnowledgeModal - options:', options);

  const handleOk = async () => {
    const values = await form.getFieldsValue();
    const knowledgeIds = values.knowledgeIds ?? [];
    return onOk?.(knowledgeIds);
  };

  useEffect(() => {
    if (visible) {
      form.setFieldValue('knowledgeIds', initialValue);
    }
  }, [visible, initialValue, form]);

  return (
    <Modal
      title={t('addToKnowledge')}
      open={visible}
      onOk={handleOk}
      onCancel={hideModal}
      confirmLoading={loading}
    >
      <Form form={form}>
        <Form.Item name="knowledgeIds" noStyle>
          <Select
            mode="multiple"
            allowClear
            showSearch
            style={{ width: '100%' }}
            placeholder={t('pleaseSelect')}
            options={options}
            optionFilterProp="children"
            filterOption={filterOptionsByInput}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ConnectToKnowledgeModal;
