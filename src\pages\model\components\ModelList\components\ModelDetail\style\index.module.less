.addModelContainer {
    height: calc(100vh - 88px); // 确保容器占满视口高度
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    .addModelTitle {
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f5;
    }

    .modelIcon {
        font-weight: 600;
        font-size: 14px;
        color: #5c5c5c;
    }

    .required {
        font-weight: 400;
        font-size: 12px;
        color: #4b5cf2;
    }

    .Card {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        border-left: 1px solid #ebebeb;
    }

    .idInput,
    .nameInput,
    .embeddingDimensionInput,
    .temperatureInput,
    .deployNameInput,
    .modelVersionInput,
    .apiVersionInput {
        padding: 8px 12px;
        background-color: transparent;
        border-radius: 8px;
        border: 1px solid #ebebeb;

        &::placeholder {
            color: #d6d6d6;
        }

        &:hover {
            background-color: #fafafa;
            border-color: #ebebeb;
        }

        &:disabled {
            background-color: #fcfcfc;
            color: #666666;
        }
    }

    .addModelHeader {
        display: flex;
        margin-bottom: 24px;
        width: 100%;

        .iconAndName {
            display: flex;
            align-items: center;
            width: 100%;

            .addModelIconWrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 72px;
                height: 72px;
                border-radius: 8px;
                background-color: #f5f5f5;
                flex-shrink: 0;

                .addModelIcon {
                    width: 72px;
                    height: 72px;
                }
            }

            .divider {
                width: 1px;
                height: 72px;
                background-color: #f5f5f5;
                margin: 0 24px;
            }

            .nameFormContainer {
                flex: 1;

                .nameFormItem {
                    margin-bottom: 0;
                }
            }
        }
    }

    .Switchlabel {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: #333333;
    }

    .SwitchTitle {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #adadad;
    }

    // 添加标签列表样式
    .tagFormItem {
        padding-bottom: 24px;
        border-bottom: 1px solid #ebebeb;

        .selectedItemList {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 8px;
            background-color: #fcfcfc;
            border: 1px solid #f5f5f5;
            border-radius: 8px;
        }

        .selectedItemRow {
            width: 100%;

            :global(.arco-input) {
                padding: 0;
                border: none;
                border-radius: 0;
                background-color: transparent;
            }

            :global(.arco-input-inner-wrapper) {
                padding: 8px 8px 8px 12px;
                border: 1px solid #f5f5f5;
                border-radius: 8px;
                background-color: #ffffff;

                &:hover {
                    background-color: #ffffff;
                }
            }

            // 输入框禁用状态
            :global(.arco-input-disabled) {
                background-color: transparent !important;
                color: #adadad !important;
            }

            :global(.arco-input-inner-wrapper-disabled) {
                background-color: #fcfcfc !important;
                border-color: #f5f5f5;
            }
        }

        .deleteIcon {
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .disabledTagInput {
            color: #adadad !important;
        }

        .disabledDeleteIcon {
            cursor: not-allowed;
            opacity: 0.5;
        }

        .disabledTagBtn {
            background-color: #fcfcfc !important;
            color: #adadad !important;
            cursor: not-allowed;
        }
    }

    /* Tag 样式 */
    .tagContainer {
        background-color: #fcfcfc;
        padding: 8px;
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        display: flex;
        flex-wrap: wrap;
        margin-top: 8px;
        width: 100%;

        .tagItem {
            height: 40px;
            width: 100%;
            padding: 8px 12px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #5c5c5c;
            border-bottom: none;
        }
    }

    .addTagBtn {
        cursor: pointer;
        padding: 8px 24px;
        background-color: #ffffff !important;
        border: 1px solid #ebebeb !important;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;
    }

    .labelExact {
        font-weight: 400;
        font-size: 12px;
        color: #adadad;
    }

    .upload {
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #ebebeb;
        border-radius: 4px;
        padding: 8px 16px 8px 8px;
        font-weight: 400;
        font-size: 14px;
        color: #5c5c5c;
    }

    // 上传组件在禁用状态下的样式
    :global(.arco-upload-disabled) {
        .upload {
            background-color: #fcfcfc;
            opacity: 0.8;
            cursor: not-allowed;
        }
    }

    .switchContainer {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        .switchLeftContent {
            display: flex;
            flex-direction: column;
        }

        .switchRightContent {
            display: flex;
            justify-content: flex-end;
        }
    }

    .requiredIcon {
        color: #4455f2;
        font-size: 15px;
        line-height: 16px;
        margin-left: 4px;
        position: relative;
        top: -1px;
        font-weight: bold;
    }

    :global(.arco-form-item) {
        margin-bottom: 24px;

        // 隐藏 Arco Design 默认的必填标识
        :global(.arco-form-item-symbol) {
            display: none;
        }

        // 始终禁用的输入框（如创建时间和更新时间）
        &:has(input[disabled]) {
            .arco-input-inner-wrapper {
                background-color: #fcfcfc;

                .arco-input {
                    background-color: #fcfcfc;
                    color: #666666;
                }
            }
        }
    }

    // 更通用的方法，直接为禁用输入框添加样式
    :global(.arco-input-disabled) {
        background-color: transparent !important;
        color: #adadad !important;
    }

    :global(.arco-input-inner-wrapper-disabled) {
        background-color: #fcfcfc !important;
        border-color: #f5f5f5;
    }

    :global(.arco-form-label-item > label) {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: #333333;
    }

    :global(.arco-space-item) {
        margin-bottom: 0;
        margin-right: 0 !important;
    }

    :global(.arco-tag-checked) {
        padding: 8px 16px 8px 8px;
        background-color: white;
        border-radius: 4px;
        border: 1px solid #ebebeb;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    :global(.arco-tag-icon) {
        box-sizing: border-box;
        height: 24px;
        width: 24px;
    }

    :global(.arco-card-size-default .arco-card-body) {
        padding: 0;
    }

    //Switch组件样式
    :global(.arco-switch-checked) {
        background-color: #4b5cf2;
    }

    // 非编辑模式下关闭的Switch背景颜色
    :global(.arco-switch[disabled]:not(.arco-switch-checked)) {
        background-color: #e8e8e8;
        opacity: 1;
    }

    :global(.arco-switch[disabled]) {
        background-color: #c3c8fa;
    }

    :global(.arco-input-inner-wrapper) {
        padding: 8px;
        background-color: transparent;
        border-radius: 8px;
        border: 1px solid #ebebeb;
        transition: all 0.3s;

        :global(.arco-input) {
            padding-top: 0;
            padding-bottom: 0;
            padding-left: 8px;
        }

        &:hover {
            background-color: #fafafa;
            border-color: #ebebeb;
        }

        // 禁用状态的输入框容器背景色
        &.arco-input-inner-wrapper-disabled {
            background-color: #fcfcfc;

            :global(.arco-input) {
                background-color: #fcfcfc;
                color: #666666;
            }
        }

        ::placeholder {
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
        }
    }

    :global(.arco-input.arco-input-size-large) {
        padding: 8px 12px;
        background-color: transparent;
        border: 1px solid #ebebeb;
        border-radius: 8px;

        ::placeholder {
            font-weight: 400;
            font-size: 14px;
            color: #d6d6d6;
        }

        :global(.arco-input) {
            padding-top: 0;
            padding-bottom: 0;
            padding-left: 8px;
        }
    }

    :global(.arco-form) {
        height: 100%;
    }

    :global(.arco-row) {
        height: 100%;
    }

    :global(.arco-select-size-default.arco-select-single .arco-select-view) {
        padding: 8px 12px;
        height: auto;
        line-height: 24px;
        font-weight: 400;
        font-size: 14px;
        color: #d6d6d6;
        border-radius: 8px;
        border: 1px solid #ebebeb;
        background-color: transparent;

        &:hover {
            background-color: #fafafa;
            border-color: #ebebeb;
        }

        // 禁用状态的选择器背景色
        &.arco-select-view-disabled {
            background-color: #fcfcfc;
            color: #666666;
        }

        :global(.arco-select-prefix) {
            margin-right: 4px;
        }

        :global(.arco-select-view-input) {
            &::placeholder {
                color: #d6d6d6;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
            }
        }

        :global(.arco-select-view-value) {
            &::placeholder {
                color: #d6d6d6;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
            }
        }
    }

    :global(.arco-select-disabled .arco-select-view) {
        background-color: #fcfcfc !important;
    }

    .cancelButton,
    .submitButton {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px 24px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        transition: all 0.3s;
    }

    .cancelButton {
        background-color: #ffffff;
        border: 1px solid #ebebeb;
        color: #5c5c5c;
        margin-right: 8px;

        &:hover {
            background-color: #fafafa !important;
            border-color: #f5f5f5 !important;
        }
    }

    .submitButton {
        background-color: #4b5cf2;
        color: #ffffff;

        &:hover {
            background-color: #4152e9 !important;
            color: #ffffff !important;
        }
    }

    // 禁用状态下的保存按钮样式
    .submitButtonDisabled,
    .submitButton[disabled],
    .submitButton[disabled]:hover {
        background-color: #c3c8fa !important;
        color: #ffffff !important;
        cursor: not-allowed;
        opacity: 1;
    }

    // 禁用状态的 InputNumber 组件
    :global(.arco-input-number-disabled) {
        .arco-input-number-input-wrap {
            background-color: #fcfcfc;

            input {
                color: #666666;
            }
        }
    }

    // 设置禁用状态下标签的样式
    :global(.arco-tag) {
        &.arco-tag-disabled {
            background-color: #fcfcfc;
            color: #666666;
            cursor: not-allowed;
            border-color: #ebebeb;
        }
    }

    // 禁用状态下的表单项字体颜色
    :global(.arco-form-item-disabled) {
        :global(.arco-form-item-label) {
            color: #666666;
        }
    }
}