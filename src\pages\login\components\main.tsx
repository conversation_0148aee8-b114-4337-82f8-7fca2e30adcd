import React, { useState, useEffect } from 'react';
import styles from './style/index.module.less';
import { getAuthConfig } from '@/lib/services/AuthHelper'
import { initializeKeycloak, triggerLogin } from '@/keycloak';
import useLocale from '@/utils/useLocale';
import locale from '../locale';
import logo2 from '@/assets/logo2.png';
import ExperienceBtnArrow from '@/assets/ExperienceBtnArrow.svg';
import IconAcpTag from '@/assets/IconAcpTag.svg';
import IconAgentTeam from '@/assets/IconAgentTeamTag.svg';
import AcpServerImage from '@/assets/AcpServerImage.svg';
import AgentTeamImage from '@/assets/AgentTeamImage2.svg';
import IconArrowtag from '@/assets/IconArrowtag.svg';
import AcpCardImage from '@/assets/AcpCardImage.svg';
import AgentTeamCreateImage from '@/assets/AgentTeamCreateImage.svg';


function LoginMain() {
    const t = useLocale(locale);
    const [isAgentTeamHovered, setIsAgentTeamHovered] = useState(false);
    const [isAcpHovered, setIsAcpHovered] = useState(false);

    useEffect(() => {
        document.body.setAttribute('arco-theme', 'light');
    }, []);

    useEffect(() => {
        // Initialize Keycloak
        const initKeycloak = async () => {
            try {
                // 检查用户是否刚刚登出
                const userJustLoggedOut = localStorage.getItem('userJustLoggedOut') === 'true';

                // 如果用户刚刚登出，清除该标志
                if (userJustLoggedOut) {
                    localStorage.removeItem('userJustLoggedOut');
                }

                const config = await getAuthConfig();
                // 传入第二个参数，如果用户刚刚登出，则不自动检查SSO
                initializeKeycloak(config, !userJustLoggedOut);
            } catch (error) {
                console.error('Failed to initialize auth:', error);
            }
        };

        // Initialize Keycloak
        initKeycloak();
    }, []);

    const handleLoginClick = () => {
        triggerLogin();// 触发登录
    };

    return (
        <div className={`${styles.container} ${isAgentTeamHovered ? styles.agentTeamHovered : ''}`}>
            {/* 最左侧边栏区域 */}
            <div className={styles.sidebar}></div>

            {/* 左侧内容区域 */}
            <div className={styles.contentArea}>
                {/* 顶部空白区域 */}
                <div className={styles.topSpacerArea}></div>

                {/* 品牌区域 */}
                <div className={styles.brandSection}>
                    <div className={styles.logoSection}>
                        <div className={styles.logoContainer}>
                            <img
                                src={logo2}
                                alt={t['login.logo.alt']}
                                className={styles.logoImage}
                            />
                            <span className={styles.logoText}>Agent foundry</span>
                        </div>

                        <h1 className={styles.mainTitle}>智用大模型应用平台</h1>
                        <p className={styles.subtitle}>


                            以Agent为核心、模型为驱动力融合首创ACP多智能体协作新范式，<br />
                            打造的多智能体协作运行管理平台
                        </p>
                    </div>
                    {/* 操作按钮 */}
                    <div className={styles.actionSection}>
                        <button
                            className={styles.experienceBtn}
                            onClick={handleLoginClick}
                        >
                            <span className={styles.btnText}>立即体验</span>
                            <ExperienceBtnArrow className={styles.experienceBtnArrow} />
                        </button>
                    </div>
                </div>

                {/* 功能模块展示区 */}
                <div className={styles.featuresSection}>
                    {/* Agent Team 模块 */}
                    <div 
                        className={styles.featureItem}
                        onMouseEnter={() => setIsAgentTeamHovered(true)}
                        onMouseLeave={() => setIsAgentTeamHovered(false)}
                    >
                        <div className={styles.featureIcon}>
                            <IconAgentTeam />
                        </div>
                        <div className={styles.featureContent}>
                            <h3 className={styles.featureTitle}>Agent Team</h3>
                            <p className={styles.featureDescription}>
                            颠覆人机交互，实现传统应用到应用智能的质的飞跃，链接每一个智能体从点到线再到面，具像化"多智能"
                            </p>
                        </div>
                        <div className={styles.featureArrow}>
                            <IconArrowtag />
                        </div>
                    </div>

                    {/* ACP 模块 */}
                    <div 
                        className={styles.featureItem}
                        onMouseEnter={() => setIsAcpHovered(true)}
                        onMouseLeave={() => setIsAcpHovered(false)}
                    >
                        <div className={styles.featureIcon}>
                        <IconAcpTag />
                        </div>
                        <div className={styles.featureContent}>
                            <h3 className={styles.featureTitle}>ACP (Agentic Context Protocol)</h3>
                            <p className={styles.featureDescription}>
                            多智能体协作新范式，标准化智能体间的交互逻辑与语义规则，构建轻量化、高扩展的协作框架，降低多智能体系统开发的复杂性，实现高效协同与自主决策的无缝衔接。
                            </p>
                        </div>
                        <div className={styles.featureArrow}>
                            <IconArrowtag />
                        </div>
                    </div>
                </div>

                {/* 底部空白区域 */}
                <div className={styles.bottomSpacerArea}></div>
            </div>

                        {/* 右侧展示区域 */}
            <div className={styles.showcaseArea}>
                <div className={styles.showcaseImage}>
                    <AcpServerImage 
                        className={`${styles.realImage} ${isAgentTeamHovered ? styles.fadeOut : styles.fadeIn}`} 
                    />
                </div>
                <div className={styles.showcaseImage}>
                    <div className={styles.imageContainer}>
                        <AgentTeamImage 
                            className={`${styles.realImage} ${isAgentTeamHovered ? styles.moveToTop : ''} ${isAcpHovered ? styles.fadeOut : styles.fadeIn}`} 
                        />
                        <AcpCardImage 
                            className={`${styles.realImage} ${styles.overlayImage} ${isAcpHovered ? styles.fadeIn : styles.fadeOut}`} 
                        />
                        <AgentTeamCreateImage 
                            className={`${styles.realImage} ${styles.overlayImage} ${styles.agentTeamCreateImage} ${isAgentTeamHovered ? styles.fadeIn : styles.fadeOut}`} 
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}

export default LoginMain; 