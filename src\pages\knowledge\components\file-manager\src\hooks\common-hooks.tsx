import { ExclamationCircleFilled } from '@ant-design/icons';
import { Modal } from 'antd';
import isEqual from 'lodash/isEqual';
import { ReactNode, useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

export const useSetModalState = () => {
  const [visible, setVisible] = useState(false);

  const showModal = useCallback(() => {
    setVisible(true);
  }, []);
  const hideModal = useCallback(() => {
    setVisible(false);
  }, []);

  const switchVisible = useCallback(() => {
    setVisible(!visible);
  }, [visible]);

  return { visible, showModal, hideModal, switchVisible };
};

export const useDeepCompareEffect = (
  effect: React.EffectCallback,
  deps: React.DependencyList,
) => {
  const ref = useRef<React.DependencyList>();
  let callback: ReturnType<React.EffectCallback> = () => {};
  if (!isEqual(deps, ref.current)) {
    callback = effect();
    ref.current = deps;
  }
  useEffect(() => {
    return () => {
      if (callback) {
        callback();
      }
    };
  }, []);
};

export interface UseDynamicSVGImportOptions {
  onCompleted?: (
    name: string,
    SvgIcon: React.FC<React.SVGProps<SVGSVGElement>> | undefined,
  ) => void;
  onError?: (err: Error) => void;
}

export function useDynamicSVGImport(
  name: string,
  options: UseDynamicSVGImportOptions = {},
) {
  const ImportedIconRef = useRef<React.FC<React.SVGProps<SVGSVGElement>>>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error>();

  const { onCompleted, onError } = options;
  useEffect(() => {
    setLoading(true);
    const importIcon = async (): Promise<void> => {
      try {
        ImportedIconRef.current = (await import(/* @vite-ignore */ name)).ReactComponent;
        onCompleted?.(name, ImportedIconRef.current);
      } catch (err: any) {
        onError?.(err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };
    importIcon();
  }, [name, onCompleted, onError]);

  return { error, loading, SvgIcon: ImportedIconRef.current };
}

interface IProps {
  title?: string;
  content?: ReactNode;
  onOk?: (...args: any[]) => any;
  onCancel?: (...args: any[]) => any;
}

export const useShowDeleteConfirm = () => {
  const { t } = useTranslation();

  const showDeleteConfirm = useCallback(
    ({ title, content, onOk, onCancel }: IProps): Promise<number> => {
              return new Promise((resolve, reject) => {
          // 直接使用 Modal.confirm，避免 App.useApp() 的依赖问题
          Modal.confirm({
          title: title ?? t('common.deleteModalTitle'),
          icon: <ExclamationCircleFilled />,
          content,
          okText: t('common.ok'),
          okType: 'danger',
          cancelText: t('common.cancel'),
          async onOk() {
            try {
              const ret = await onOk?.();
              resolve(ret);
              console.info(ret);
            } catch (error) {
              reject(error);
            }
          },
          onCancel() {
            onCancel?.();
          },
        });
      });
    },
    [t],
  );

  return showDeleteConfirm;
};

export const useShowFileManagerDeleteConfirm = () => {
  const { t } = useTranslation();

  const showDeleteConfirm = useCallback(
    ({ title, content, onOk, onCancel }: IProps): Promise<number> => {
      return new Promise((resolve, reject) => {
        // 自定义删除确认模态框
        Modal.confirm({
          title: (
            <div style={{ 
              fontSize: '20px', 
              fontWeight: '600', 
              color: '#333333',
              marginBottom: '16px'
            }}>
              {title ?? '删除文件'}
            </div>
          ),
          content: (
            <div style={{ 
              fontSize: '14px', 
              color: '#333333',
              lineHeight: '20px',
              marginBottom: '12px'
            }}>
              {content}
            </div>
          ),
          icon: null,
          okText: '删除',
          cancelText: '取消',
          okType: 'danger',
          centered: true,
          width: 480,
          className: 'custom-delete-modal',
          styles: {
            content: {
              borderRadius: '16px',
              padding: '24px',
            },
            header: {
              borderBottom: 'none',
              padding: '0',
              marginBottom: '16px',
            },
            body: {
              padding: '0',
            },
            footer: {
              borderTop: 'none',
              padding: '0',
              display: 'flex',
              justifyContent: 'flex-end',
              gap: '12px',
            },
          },
          okButtonProps: {
            style: {
              height: '40px',
              padding: '8px 24px',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: '#d54941',
              border: 'none',
              color: '#ffffff',
            },
            onMouseEnter: (e) => {
              e.currentTarget.style.backgroundColor = '#cd463e';
            },
            onMouseLeave: (e) => {
              e.currentTarget.style.backgroundColor = '#d54941';
            },
          },
          cancelButtonProps: {
            style: {
              height: '40px',
              padding: '8px 24px',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: '#ffffff',
              border: '1px solid #e8e8e8',
              color: '#666666',
            },
            onMouseEnter: (e) => {
              e.currentTarget.style.backgroundColor = '#fafafa';
              e.currentTarget.style.borderColor = '#d9d9d9';
              e.currentTarget.style.color = '#333333';
            },
            onMouseLeave: (e) => {
              e.currentTarget.style.backgroundColor = '#ffffff';
              e.currentTarget.style.borderColor = '#e8e8e8';
              e.currentTarget.style.color = '#666666';
            },
          },
          async onOk() {
            try {
              const ret = await onOk?.();
              resolve(ret);
            } catch (error) {
              reject(error);
            }
          },
          onCancel() {
            onCancel?.();
          },
        });
      });
    },
    [t],
  );

  return showDeleteConfirm;
};

export const useTranslate = (keyPrefix: string) => {
  return useTranslation('translation', { keyPrefix });
};

export const useCommonTranslation = () => {
  return useTranslation('translation', { keyPrefix: 'common' });
};
