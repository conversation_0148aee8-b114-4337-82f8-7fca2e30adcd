// store.js
// @ts-nocheck
const createStore = (initialValue) => {
    let value = initialValue;
    const subscribers = new Set();

    return {
        subscribe: (callback) => {
            subscribers.add(callback);
            callback(value);
            return () => subscribers.delete(callback);
        },
        set: (newValue) => {
            value = newValue;
            subscribers.forEach(callback => callback(value));
        },
        get: () => value
    };
};

// 判断是否在浏览器环境
const browser = typeof window !== 'undefined';

const userKey = "user";

// 创建 globalEventStore
export const globalEventStore = createStore({ name: "", payload: {} });

// 创建 userStore
export const userStore = createStore({ 
    id: "", 
    full_name: "", 
    expires: 0, 
    token: null 
});

// 获取用户存储
export function getUserStore() {
    if (browser) {
        // 在浏览器环境中访问 localStorage
        const json = localStorage.getItem(userKey);
        if (json) {
            try {
                return JSON.parse(json);
            } catch (e) {
                console.error('Failed to parse user from localStorage:', e);
                return userStore.get();
            }
        }
        return userStore.get();
    }
    // 非浏览器环境返回默认值
    return userStore.get();
}

// 订阅 userStore 的变化并同步到 localStorage
if (browser) {
    userStore.subscribe(value => {
        if (value.token) {
            try {
                localStorage.setItem(userKey, JSON.stringify(value));
            } catch (e) {
                console.error('Failed to save user to localStorage:', e);
            }
        }
    });
}

// 重置 localStorage
export function resetLocalStorage(resetUser = false) {
    if (browser && resetUser) {
        localStorage.removeItem(userKey);
        localStorage.removeItem('userStatus');
        userStore.set({ id: "", full_name: "", expires: 0, token: null });
    }
}