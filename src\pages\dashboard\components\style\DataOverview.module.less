.container {

    .titleWrapper {
        margin-bottom: 8px;

        .pageTitle {
            font-weight: 500;
            font-size: 16px;
            line-height: 28px;
            color: #333333;
            margin: 0;
        }
    }

    :global(.arco-card-body) {
        padding: 0;
    }

    .card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #fff;
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        padding: 20px 24px;
        transition: all 0.3s;
        height: 100%;

        .cardContent {
            flex: 1;
            width: 100%;

            .header {
                display: flex;
                flex-direction: column;
                width: 100%;

                .title {
                    color: #adadad;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 24px;
                }

                .value {
                    font-weight: 600;
                    font-size: 32px;
                    line-height: 56px;
                    color: #333333;
                }


            }

            .footer {
                display: flex;
                align-items: center;
                justify-content: flex-start;

                .dayInfo {
                    .dayText,
                    .avgText {
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 20px;
                        display: block;

                        &.dayText{
                            padding: 2px 6px;
                            background-color: #f8f8ff;
                            border-radius: 4px;
                            color: #4455f2;
                        }

                        &.avgText{
                            color: #adadad;
                        }
                    }
                }

                .change {
                    display: flex;
                    align-items: center;
                    // padding: 2px 6px;
                    border-radius: 4px;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 20px;

                    &.positive {
                        color: #2ba471;
                        background-color: #f7fcfa;
                    }

                    &.negative {
                        color: #d54941;
                        background-color: #fef8f8;
                    }

                    .changeText {
                        padding: 2px 6px 2px 0px;
                    }
                }

                .compareText {
                    color: #adadad;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 20px;
                }
            }
        }
    }
}