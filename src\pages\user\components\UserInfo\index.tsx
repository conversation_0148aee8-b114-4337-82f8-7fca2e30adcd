import React, { useState, useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Typography, Grid, Form, Input, Button, Message, Modal, Upload } from '@arco-design/web-react';
import { IconUpload } from '@arco-design/web-react/icon';
import useLocale from '@/utils/useLocale';
import locale from './locale';
import styles from './style/index.module.less';
import DefaultAvatarPlus from '@/assets/user/DefaultAvatarPlus.svg';
import { uploadUserAvatar, getUserAvatar } from '@/lib/services/auth-service';
// import IconClose from '@/assets/model/IconClose.svg';

function UserInfo() {
  const t = useLocale(locale);
  const userInfo = useSelector((state: any) => state.userInfo);
  // const loading = useSelector((state: any) => state.userLoading);
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const { Row, Col } = Grid;
  const FormItem = Form.Item;
  const [avatarUrl, setAvatarUrl] = useState('');
  const [uploading, setUploading] = useState(false);
  const [loadingAvatar, setLoadingAvatar] = useState(false);
  const fileInputRef = useRef(null);
  const isMountedRef = useRef(true); // 跟踪组件挂载状态
  // const [nameForm] = Form.useForm();
  // const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
  // const [confirmEmail, setConfirmEmail] = useState('');
  // const [deleteForm] = Form.useForm();
  // const [editNameVisible, setEditNameVisible] = useState(false);
  // const [nameChanged, setNameChanged] = useState(false);
  // const [currentName, setCurrentName] = useState('');
  // const { Title, Text } = Typography;

  // 头像URL有效性验证函数
  const isValidAvatarUrl = (url: string | null | undefined): boolean => {
    if (!url) return false;
    if (url === '/user/avatar') return false;
    return url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:');
  };

  // 初始化表单数据
  React.useEffect(() => {
    if (userInfo) {
      form.setFieldsValue({
        user_name: userInfo.full_name || '',
        email: userInfo.email || '',
        organization: userInfo.tenant_name || '',
        role: userInfo.role || '',
        phone: userInfo.phone || '暂无',
        create_date: userInfo.create_date ? formatDate(userInfo.create_date) : '',
      });
    }
  }, [userInfo, form]);

  // 初始化头像数据（只在userInfo初次加载时执行一次）
  React.useEffect(() => {
    if (userInfo && !avatarUrl && isMountedRef.current) {
      // 优先从用户信息中获取头像URL
      if (userInfo.avatar_url) {
        setAvatarUrl(userInfo.avatar_url);
      } else if (userInfo.avatar) {
        // 兼容旧的avatar字段
        setAvatarUrl(userInfo.avatar);
      } else {
        // 如果用户信息中没有任何头像，则从服务器获取
        loadUserAvatar();
      }
    }
  }, [userInfo?.id]); // 只依赖用户ID，避免循环

  // 组件卸载时清理URL和标记组件状态
  React.useEffect(() => {
    // 组件挂载时设置为true
    isMountedRef.current = true;

    return () => {
      // 标记组件已卸载
      isMountedRef.current = false;

      // 清理blob URL
      if (avatarUrl && avatarUrl.startsWith('blob:')) {
        URL.revokeObjectURL(avatarUrl);
      }
    };
  }, []); // 移除avatarUrl依赖，避免不必要的重新执行

  // 账号信息格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0].replace(/-/g, '/');
    } catch (error) {
      console.error('日期格式化错误:', error);
      return dateString;
    }
  };

  // 显示修改名称模态框
  // const handleShowEditName = () => {
  //   const initialName = userInfo.full_name || '';
  //   nameForm.setFieldsValue({
  //     user_name: initialName,
  //   });
  //   setCurrentName(initialName);
  //   setNameChanged(false);
  //   setEditNameVisible(true);
  // };

  // 处理名称修改提交
  // const handleNameSubmit = async () => {
  //   try {
  //     const values = await nameForm.validate();
  //     // TODO: 这里应该添加实际的API调用来更新用户信息
  //     // 模拟成功提交
  //     Message.success('用户名称更新成功');
  //     setEditNameVisible(false);

  //     // 更新Redux中的用户信息
  //     dispatch({
  //       type: 'update-userInfo',
  //       payload: {
  //         userInfo: { ...userInfo, ...values },
  //         userLoading: false,
  //       },
  //     });
  //   } catch (error) {
  //     console.error('表单验证失败:', error);
  //   }
  // };

  // 取消名称修改
  // const handleCancelEditName = () => {
  //   setEditNameVisible(false);
  //   setNameChanged(false);
  // };

  // 监听名称输入变化
  // const handleNameChange = (value) => {
  //   setNameChanged(value !== currentName && value.trim() !== '');
  // };

  // 处理删除账户确认
  // const handleDeleteConfirm = () => {
  //   setConfirmEmail('');
  //   setConfirmDeleteVisible(true);
  //   deleteForm.resetFields();
  // };

  // 检查邮箱是否匹配
  // const isEmailMatched = () => {
  //   const userEmail = userInfo?.email || '';
  //   return confirmEmail === userEmail && confirmEmail !== '';
  // };

  // 确认删除账户
  // const handleConfirmDelete = async () => {
  //   if (!isEmailMatched()) {
  //     Message.error('邮箱不匹配，无法删除账户');
  //     return;
  //   }

  //   try {
  //     // TODO: 实现实际的删除账户API调用
  //     // 模拟成功删除
  //     Message.success('账户已成功删除');
  //     setConfirmDeleteVisible(false);
  //     setConfirmEmail('');

  //     // 这里可以添加登出逻辑或重定向到登录页
  //   } catch (error) {
  //     console.error('删除账户失败:', error);
  //     Message.error('删除账户失败，请稍后重试');
  //   }
  // };

  // 取消删除
  // const handleCancelDelete = () => {
  //   setConfirmDeleteVisible(false);
  //   setConfirmEmail('');
  // };

  // 邮箱输入变化处理
  // const handleEmailChange = (value) => {
  //   setConfirmEmail(value);
  // };

  // 获取用户头像
  const loadUserAvatar = async () => {
    try {
      // 检查组件是否仍然挂载
      if (!isMountedRef.current) return;

      setLoadingAvatar(true);
      const avatarData = await getUserAvatar();

      // 再次检查组件是否仍然挂载（异步操作完成后）
      if (!isMountedRef.current) return;

      if (avatarData) {
        // getUserAvatar返回data URL字符串
        if (typeof avatarData === 'string' && avatarData.startsWith('data:')) {
          setAvatarUrl(avatarData);
        } else if (typeof avatarData === 'string') {
          // 如果是其他字符串格式（比如URL）
          setAvatarUrl(avatarData);
        } else if (avatarData && typeof avatarData === 'object') {
          // 处理对象格式的响应
          const avatarObj = avatarData as any;
          let newAvatarUrl = '';
          if (avatarObj.avatar_url) {
            newAvatarUrl = avatarObj.avatar_url;
          } else if (avatarObj.file_data) {
            newAvatarUrl = avatarObj.file_data;
          }

          if (newAvatarUrl) {
            setAvatarUrl(newAvatarUrl);
          }
        }
      } else {
        // 如果avatarData为null或undefined，清空头像URL，显示默认头像
        setAvatarUrl('');
      }
    } catch (error) {
      console.error('获取头像失败:', error);
      // 获取失败时清空头像URL，使用默认头像
      if (isMountedRef.current) {
        setAvatarUrl('');
      }
    } finally {
      if (isMountedRef.current) {
        setLoadingAvatar(false);
      }
    }
  };

  // 重新加载头像并更新Redux状态
  const reloadAvatarAndUpdateRedux = async () => {
    try {
      // 检查组件是否仍然挂载
      if (!isMountedRef.current) return null;

      setLoadingAvatar(true);
      const avatarData = await getUserAvatar();

      // 再次检查组件是否仍然挂载（异步操作完成后）
      if (!isMountedRef.current) return null;

      let newAvatarUrl = '';

      if (avatarData) {
        // getUserAvatar返回data URL字符串
        if (typeof avatarData === 'string' && avatarData.startsWith('data:')) {
          newAvatarUrl = avatarData;
          setAvatarUrl(avatarData);
        } else if (typeof avatarData === 'string') {
          // 如果是其他字符串格式（比如URL）
          newAvatarUrl = avatarData;
          setAvatarUrl(avatarData);
        } else if (avatarData && typeof avatarData === 'object') {
          // 处理对象格式的响应
          const avatarObj = avatarData as any;
          if (avatarObj.avatar_url) {
            newAvatarUrl = avatarObj.avatar_url;
          } else if (avatarObj.file_data) {
            newAvatarUrl = avatarObj.file_data;
          }

          if (newAvatarUrl) {
            setAvatarUrl(newAvatarUrl);
          }
        }
      } else {
        // 如果avatarData为null或undefined，清空头像URL，显示默认头像
        setAvatarUrl('');
      }

      // 更新Redux状态
      if (userInfo) {
        dispatch({
          type: 'update-userInfo',
          payload: {
            userInfo: { ...userInfo, avatar_url: newAvatarUrl },
            userLoading: false,
          },
        });
      }

      return newAvatarUrl;
    } catch (error) {
      console.error('获取头像失败:', error);
      // 获取失败时清空头像URL，使用默认头像
      if (isMountedRef.current) {
        setAvatarUrl('');

        // 更新Redux状态
        if (userInfo) {
          dispatch({
            type: 'update-userInfo',
            payload: {
              userInfo: { ...userInfo, avatar_url: '' },
              userLoading: false,
            },
          });
        }
      }
      return null;
    } finally {
      if (isMountedRef.current) {
        setLoadingAvatar(false);
      }
    }
  };

  // 处理头像点击，触发文件选择
  const handleAvatarClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 处理文件选择变化
  const handleFileChange = async (event) => {
    const file = event.target.files?.[0];

    if (!file) {
      return;
    }

    // 立即清空input值，确保下次选择同一文件也能触发onChange
    if (event.target) {
      event.target.value = '';
    }

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      Message.error('请选择图片文件');
      return;
    }

    // 验证文件大小（限制为5MB）
    if (file.size > 5 * 1024 * 1024) {
      Message.error('图片大小不能超过5MB');
      return;
    }

    try {
      // 只在组件仍然挂载时设置状态
      if (isMountedRef.current) {
        setUploading(true);
      }

      // 调用上传API - 即使组件状态变化也要完成上传
      const result = await uploadUserAvatar(file);

      if (result) {
        // 显示成功消息（即使组件状态变化也要显示）
        Message.success('头像上传成功');

        // 只在组件仍然挂载时重新获取头像和更新Redux状态
        if (isMountedRef.current) {
          // 重新获取最新头像
          const latestAvatarUrl = await reloadAvatarAndUpdateRedux();
        }
      } else {
        Message.error('头像上传失败');
      }
    } catch (error) {
      console.error('头像上传失败:', error);
      Message.error('头像上传失败，请重试');
    } finally {
      // 只在组件仍然挂载时重置状态
      if (isMountedRef.current) {
        setUploading(false);
      }
    }
  };

  return (
    <div className={styles.userInfoContainer}>
      <Row gutter={16}>
        <Col span={12}>
          <Form
            form={form}
            layout="vertical"
            autoComplete="off"
            disabled={true}
          >
            <Row className={styles.userInfoHeader}>
              <div className={styles.avatarAndName}>
                <div className={styles.avatarWrapper} onClick={handleAvatarClick}>
                  {isValidAvatarUrl(avatarUrl || userInfo?.avatar_url) ? (
                    <img
                      src={avatarUrl || userInfo?.avatar_url}
                      alt="用户头像"
                      className={styles.avatar}
                      style={{
                        width: '72px',
                        height: '72px',
                        borderRadius: '50%',
                        objectFit: 'cover',
                        cursor: 'pointer'
                      }}
                    />
                  ) : (
                    <DefaultAvatarPlus
                      className={styles.avatar}
                      style={{ cursor: 'pointer' }}
                    />
                  )}

                  {/* 悬浮遮罩 */}
                  {!uploading && !loadingAvatar && (
                    <div className={styles.hoverOverlay}>
                      <IconUpload style={{ color: '#fff' }} />
                      <div className={styles.hoverText}>上传头像</div>
                    </div>
                  )}

                  {/* 上传/加载状态遮罩 */}
                  {(uploading || loadingAvatar) && (
                    <div className={styles.uploadingOverlay}>
                      <div className={styles.uploadingText}>
                        {uploading ? '上传中...' : '加载中...'}
                      </div>
                    </div>
                  )}

                  {/* 隐藏的文件输入 */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    style={{ display: 'none' }}
                    onChange={handleFileChange}
                    key="file-input" // 添加固定key防止重新创建
                  />
                </div>
                <div className={styles.divider}></div>
                <div className={styles.nameFormContainer}>
                  <FormItem label="名称" field="user_name">
                    <Input placeholder="请输入用户名" />
                  </FormItem>
                </div>
                {/* <Button type="text" onClick={handleShowEditName} className={styles.EditButton}>
                    修改
                  </Button> */}
              </div>
            </Row>
            <Row gutter={8}>
              {/* 组织 */}
              <Col span={12}>
                <FormItem label="组织" field="organization">
                  <Input placeholder="请输入组织" />
                </FormItem>
              </Col>
              {/* 角色 */}
              <Col span={12}>
                <FormItem label="角色" field="role">
                  <Input placeholder="请输入角色" />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={8}>
              {/* 手机号 */}
              <Col span={12}>
                <FormItem label="手机号" field="phone">
                  <Input placeholder="请输入手机号" />
                </FormItem>
              </Col>
              {/* 邮箱 */}
              <Col span={12}>
                <FormItem label="邮箱" field="email">
                  <Input placeholder="请输入邮箱" />
                </FormItem>
              </Col>
            </Row>

            {/* 创建时间 */}
            <FormItem label="创建时间" field="create_date" style={{ marginBottom: '24px', paddingBottom: '24px', borderBottom: '1px solid #f5f5f5' }}>
              <Input placeholder="创建时间" />
            </FormItem>
            {/* <Card className={styles.accountSection}>
              <Title style={{ fontSize: '14px', color: '#333333', marginBottom: '4px' }}>账户</Title>
              <Title style={{ fontSize: '12px', color: '#adadad', marginBottom: '8px' }}>你的账户和用户数据</Title>
              <Button
                onClick={handleDeleteConfirm}
                className={styles.DeleteAccount}
              >
                删除账户
              </Button>
            </Card> */}
          </Form>
        </Col>
      </Row>

      {/* 修改名称模态框 */}
      {/* <Modal
        visible={editNameVisible}
        title="修改名称"
        onCancel={handleCancelEditName}
        closeIcon={<IconClose />}
        className={styles.editNameModal}
        maskClosable={false}
        footer={null}
      >
        <div className={styles.modalContent}>
          <Form form={nameForm} autoComplete="off">
            <FormItem field="user_name" className={styles.emailFormItem}>
              <Input
                placeholder="请输入"
                maxLength={10}
                showWordLimit
                onChange={handleNameChange}
              />
            </FormItem>
          </Form>
        </div>

        <div className={styles.modalFooter}>
          <Button
            onClick={handleCancelEditName}
            className={styles.cancelDeleteBtn}
          >
            取消
          </Button>
          <Button
            type="primary"
            onClick={handleNameSubmit}
            className={`${styles.saveNameBtn} ${!nameChanged ? styles.disabledSaveBtn : ''}`}
            disabled={!nameChanged}
          >
            保存
          </Button>
        </div>
      </Modal> */}

      {/* 删除账户确认弹窗 */}
      {/* <Modal
        visible={confirmDeleteVisible}
        title="删除账户"
        onCancel={handleCancelDelete}
        closeIcon={<IconClose />}
        className={styles.confirmDeleteModal}
        maskClosable={false}
        footer={null}
      >
        <div className={styles.modalContent}>
          <Text className={styles.modalContentText}>
            我们将永久删除您的账户和用户数据
          </Text>
          <div className={styles.emailConfirmSection}>
            <Text className={styles.emailConfirmLabel}>输入邮箱以确定</Text>
            <Form form={deleteForm} autoComplete="off">
              <FormItem className={styles.emailFormItem}>
                <Input
                  placeholder="请输入"
                  onChange={handleEmailChange}
                  className={styles.emailConfirmInput}
                />
              </FormItem>
            </Form>
          </div>
        </div>

        <div className={styles.modalFooter}>
          <Button
            onClick={handleCancelDelete}
            className={styles.cancelDeleteBtn}
          >
            取消
          </Button>
          <Button
            type="primary"
            status="danger"
            onClick={handleConfirmDelete}
            className={`${styles.confirmDeleteBtn} ${!isEmailMatched() ? styles.disabledDeleteBtn : ''}`}
            disabled={!isEmailMatched()}
          >
            删除
          </Button>
        </div>
      </Modal> */}
    </div>
  );
}

export default UserInfo;
