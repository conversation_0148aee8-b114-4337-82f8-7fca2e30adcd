import React, { useEffect, useRef, useState } from 'react';
import { Card, Spin, Select, Button, Space } from '@arco-design/web-react';
import { Column } from '@antv/g2plot';
import styles from './style/ACPTokenUsage.module.less';
import IconACPTokenUsage from '@/assets/dashboard/ACPTokenUsage.svg'

const ButtonGroup = Button.Group;
const Option = Select.Option;

type DataType = {
    date: string;
    count: number;
};

type PeriodType = 'week' | 'month' | 'quarter';

const ACPTokenUsage = () => {
    const chartRef = useRef<Column | null>(null);
    const containerRef = useRef<HTMLDivElement | null>(null);
    const [loading, setLoading] = useState(false);
    const [chartHeight, setChartHeight] = useState(300); // 默认高度
    const [selectedACP, setSelectedACP] = useState('全部ACP');
    const [period, setPeriod] = useState<PeriodType>('week');
    const [chartInitialized, setChartInitialized] = useState(false);

    // 周数据
    const weekData: DataType[] = [
        { date: '第1周 (1.1-1.7)', count: 350 },
        { date: '第2周 (1.8-1.14)', count: 450 },
        { date: '第3周 (1.15-1.21)', count: 350 },
        { date: '第4周 (1.22-1.28)', count: 450 },
        { date: '第5周 (1.29-2.4)', count: 215 },
        { date: '第6周 (2.5-2.11)', count: 515 },
        { date: '第7周 (2.12-2.18)', count: 315 },
        { date: '第8周 (2.19-2.25)', count: 450 },
    ];

    // 月度数据
    const monthData: DataType[] = [
        { date: '1月', count: 600 },
        { date: '2月', count: 200 },
        { date: '3月', count: 660 },
        { date: '4月', count: 1800 },
        { date: '5月', count: 260 },
        { date: '6月', count: 600 },
        { date: '7月', count: 280 },
        { date: '8月', count: 400 },
        { date: '9月', count: 560 },
        { date: '10月', count: 760 },
        { date: '11月', count: 260 },
        { date: '12月', count: 560 },
    ];

    // 季度数据
    const quarterData: DataType[] = [
        { date: '第1季度', count: 2200 },
        { date: '第2季度', count: 5400 },
        { date: '第3季度', count: 4600 },
        { date: '第4季度', count: 3180 },
    ];

    const getDataByPeriod = () => {
        switch (period) {
            case 'week':
                return weekData;
            case 'month':
                return monthData;
            case 'quarter':
                return quarterData;
            default:
                return weekData;
        }
    };

    const updateChartSize = () => {
        if (!containerRef.current) return;

        // 根据容器宽度计算适当的高度
        const parent = containerRef.current.parentElement;
        if (!parent) return;
        const containerWidth = parent.clientWidth;

        // 使用比例来确定高度，并确保最小高度为200px，最大高度为300px
        const calculatedHeight = Math.min(Math.max(containerWidth * 0.33, 200), 300);

        setChartHeight(calculatedHeight);

        // 如果图表已经初始化，更新其尺寸
        if (chartRef.current) {
            chartRef.current.changeSize(containerWidth, calculatedHeight);
        }
    };

    const initChart = () => {
        if (!containerRef.current || chartInitialized) return;

        // 获取当前周期的数据
        const currentData = getDataByPeriod();

        // 找出最大值，用于设置Y轴刻度
        const maxValue = Math.max(...currentData.map(item => item.count));
        // 计算合适的Y轴最大值，避免顶部空白过多
        const yAxisMax = Math.ceil(maxValue * 1.1);

        const column = new Column(containerRef.current, {
            data: currentData,
            padding: [20, 10, 20, 30],
            xField: 'date',
            yField: 'count',
            color: '#4455f2',
            autoFit: true, // 开启自动适配
            xAxis: {
                grid: null,
                line: {
                    style: {
                        stroke: '#f5f5f5',
                    },
                },
                label: {
                    style: {
                        fill: '#adadad',
                        fontSize: 12,
                        fontWeight: 'normal',
                    },
                    offset: 8,
                    autoRotate: false,
                    autoHide: true,
                },
            },
            yAxis: {
                grid: {
                    line: {
                        style: {
                            stroke: '#f5f5f5',
                        },
                    },
                },
                // 设置Y轴的刻度线
                min: 0,
                max: yAxisMax,
                tickCount: 5,
                label: {
                    // 格式化Y轴标签
                    formatter: (val) => {
                        const value = parseFloat(val);
                        // 确保0显示为0
                        if (value === 0) return '0';
                        // 1000及以上的值用k表示
                        if (value >= 1000) {
                            return `${(value / 1000).toFixed(0)}k`;
                        }
                        // 小于1000的值直接显示
                        return `${value}`;
                    },
                }
            },
            columnStyle: {
                radius: [4, 4, 4, 4], // 柱状图圆角
            },
            tooltip: {
                showMarkers: false,
                // 自定义提示框内容，显示完整的周信息
                formatter: (datum) => {
                    return {
                        name: '用量',
                        value: datum.count,
                        title: datum.date,
                    };
                }
            },
            state: {
                active: {
                    style: {
                        fill: '#6b7afb', // 悬浮时的颜色
                    },
                },
            },
            // 添加柱状图的间距设置
            columnWidthRatio: 0.6, // 增加柱状图宽度占比，使其看起来不那么拥挤
            minColumnWidth: 20, // 最小柱宽
            maxColumnWidth: 40, // 最大柱宽
            // 设置初始高度
            height: chartHeight,
        });

        chartRef.current = column;
        column.render();
        setChartInitialized(true);
    };

    // 当周期或ACP选择改变时更新图表数据
    useEffect(() => {
        if (chartRef.current && chartInitialized) {
            chartRef.current.changeData(getDataByPeriod());
        }
    }, [period, selectedACP]);

    // 初始化图表并处理窗口大小变化
    useEffect(() => {
        // 初始设置
        updateChartSize();
        initChart();

        // 添加窗口大小变化监听
        const handleResize = () => {
            updateChartSize();
        };

        window.addEventListener('resize', handleResize);

        // 清理函数
        return () => {
            window.removeEventListener('resize', handleResize);
            if (chartRef.current) {
                chartRef.current.destroy();
                chartRef.current = null;
                setChartInitialized(false);
            }
        };
    }, []);

    // ACP选项列表
    const acpOptions = [
        { label: '全部ACP', value: '全部ACP' },
        { label: 'ACP-A', value: 'ACP-A' },
        { label: 'ACP-B', value: 'ACP-B' },
        { label: 'ACP-C', value: 'ACP-C' },
    ];

    // 处理ACP选择变化
    const handleACPChange = (value) => {
        setSelectedACP(value);
        // 这里可以添加筛选逻辑，根据所选ACP过滤数据
    };

    // 处理周期选择变化
    const handlePeriodChange = (value: PeriodType) => {
        setPeriod(value);
    };

    return (
        <Card
            title={
                <div className={styles.cardHeader}>
                    <div className={styles.HeaderTag}>
                        <IconACPTokenUsage />
                        <div className={styles.title}>ACP Token用量统计</div>
                    </div>
                    <div className={styles.HeaderControls}>
                        <Space size={12}>
                            <Select
                                placeholder="选择ACP"
                                value={selectedACP}
                                onChange={handleACPChange}
                                style={{ width: 240 }}
                                allowClear={false}
                            >
                                {acpOptions.map((option) => (
                                    <Option key={option.value} value={option.value}>
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                            <ButtonGroup>
                                <Button 
                                    type={period === 'week' ? 'primary' : 'default'} 
                                    onClick={() => handlePeriodChange('week')}
                                >
                                    周
                                </Button>
                                <Button 
                                    type={period === 'month' ? 'primary' : 'default'} 
                                    onClick={() => handlePeriodChange('month')}
                                >
                                    月
                                </Button>
                                <Button 
                                    type={period === 'quarter' ? 'primary' : 'default'} 
                                    onClick={() => handlePeriodChange('quarter')}
                                >
                                    季度
                                </Button>
                            </ButtonGroup>
                        </Space>
                    </div>
                </div>
            }
            className={styles.card}
        >
            <Spin loading={loading} style={{ width: '100%' }}>
                <div
                    ref={containerRef}
                    className={styles.chart}
                    style={{ height: `${chartHeight}px` }}
                />
            </Spin>
        </Card>
    );
};

export default ACPTokenUsage; 