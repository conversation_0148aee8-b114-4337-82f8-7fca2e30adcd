import React, { useState, useEffect, useRef } from 'react';
import { Card, Typography, Form, Input, Button, Space, Grid, Switch, Message, Spin } from '@arco-design/web-react';
import IconTimeCard from '@/assets/application/IconTimeCard.svg';
import IconCloseTag from '@/assets/close.svg';
import styles from '../style/index.module.less';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { GlobalState } from '@/store/index';
import { isEqual } from 'lodash';

const { Text } = Typography;
const { Col, Row } = Grid;

// 生成默认版本号
const generateDefaultVersion = () => 'v1.0.0';

// 增加版本号
const incrementVersion = (version: string) => {
    // 如果没有版本号或格式不正确，返回默认版本号
    if (!version || !version.startsWith('v')) {
        return generateDefaultVersion();
    }

    try {
        // 解析版本号，格式为 vX.Y.Z
        const versionParts = version.substring(1).split('.');
        if (versionParts.length !== 3) {
            return generateDefaultVersion();
        }

        const major = parseInt(versionParts[0], 10);
        const minor = parseInt(versionParts[1], 10);
        const patch = parseInt(versionParts[2], 10) + 1;

        // 直接返回递增后的版本号，仅递增补丁版本
        return `v${major}.${minor}.${patch}`;
    } catch (error) {
        console.error('解析版本号失败:', error);
        return generateDefaultVersion();
    }
};

// 不显示原生必填图标的 Form.Item
const FormItemWithoutRequiredMark = (props) => {
    const { children, ...rest } = props;
    return (
        <Form.Item {...rest} requiredSymbol={false}>
            {children}
        </Form.Item>
    );
};

// 自定义的必填图标组件
const RequiredIcon = () => <span className={styles.requiredIcon}>*</span>;

// 自定义表单项标签组件
const CustomLabel = ({ label, required }) => {
    return (
        <Space>
            <span>{label}</span>
            {required && <RequiredIcon />}
        </Space>
    );
};

interface TimeSequenceCardBasicInfoProps {
    onNext?: () => void;
    loading?: boolean;
    isEditMode?: boolean;
    onToggleEditMode?: () => void;
    isDetailMode?: boolean;
    onSave?: () => void;
    submitting?: boolean;
}

const TimeSequenceCardBasicInfo: React.FC<TimeSequenceCardBasicInfoProps> = ({
    onNext,
    loading = false,
    isEditMode = false,
    onToggleEditMode,
    isDetailMode = false,
    onSave,
    submitting = false,
}) => {
    const [form] = Form.useForm();
    const [tags, setTags] = useState<string[]>([]);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const isUpdatingFromRedux = useRef(false);// 添加标志来防止循环更新
    const prevBasicInfo = useRef(null);
    const timeSequenceCardDetail = useSelector((state: GlobalState) => state.timeSequenceCardDetail);// 从Redux获取时序卡片详情数据
    const basicInfo = timeSequenceCardDetail?.basicInfo || {};
    const isReadOnly = isDetailMode && !isEditMode;// 计算只读状态 - 只有在详情模式且非编辑状态时表单才禁用

    // 设置初始版本号 - 针对新建卡片
    useEffect(() => {
        // 如果是新建卡片（非详情模式）
        if (!isDetailMode && !timeSequenceCardDetail?.basicInfo?.version) {
            const defaultVersion = generateDefaultVersion();
            form.setFieldValue('version', defaultVersion);
            
            // 如果已有timeSequenceCardDetail，更新Redux
            if (timeSequenceCardDetail) {
                dispatch({
                    type: 'update-timeSequence-card-detail',
                    payload: {
                        timeSequenceCardDetail: {
                            ...timeSequenceCardDetail,
                            basicInfo: {
                                ...timeSequenceCardDetail.basicInfo,
                                version: defaultVersion,
                            },
                        },
                    },
                });
            }
        }
    }, [isDetailMode, timeSequenceCardDetail, dispatch, form]);

    // 当Redux中的详情数据变化时，更新表单和标签
    useEffect(() => {
        if (timeSequenceCardDetail && timeSequenceCardDetail.basicInfo) {
            const currentBasicInfo = timeSequenceCardDetail.basicInfo;

            // 检查数据是否有变化，避免不必要的更新
            if (!isEqual(prevBasicInfo.current, currentBasicInfo)) {
                prevBasicInfo.current = currentBasicInfo;

                // 标记正在从Redux更新，避免循环
                isUpdatingFromRedux.current = true;

                // 设置表单值
                form.setFieldsValue({
                    name: currentBasicInfo.name,
                    description: currentBasicInfo.description,
                    version: currentBasicInfo.version || generateDefaultVersion(),
                    license: currentBasicInfo.license,
                    is_enabled: currentBasicInfo.is_enabled,
                    is_default: currentBasicInfo.is_default,
                });

                // 设置标签
                if (currentBasicInfo.tags && Array.isArray(currentBasicInfo.tags)) {
                    setTags(currentBasicInfo.tags);
                }

                // 重置标记
                setTimeout(() => {
                    isUpdatingFromRedux.current = false;
                }, 0);
            }
        }
    }, [timeSequenceCardDetail, form]);

    // 新增标签输入项
    const showTagInput = () => {
        if (tags.length >= 3) {
            Message.warning('标签至多添加3个');
            return;
        }
        // 直接添加一个空标签，然后自动聚焦到它
        setTags([...tags, '']);
    };

    // 处理表单值变化，直接更新Redux
    const handleFormValuesChange = (changedValues, allValues) => {
        // 如果是从Redux更新触发的变化，不要再次更新Redux
        if (isUpdatingFromRedux.current || !isEditMode || !timeSequenceCardDetail) {
            return;
        }

        // 合并表单值和标签
        const updatedBasicInfo = {
            ...timeSequenceCardDetail.basicInfo,
            ...allValues,
            tags: tags,
        };

        // 检查是否有实际变化
        if (!isEqual(updatedBasicInfo, timeSequenceCardDetail.basicInfo)) {
            // 更新Redux状态
            dispatch({
                type: 'update-timeSequence-card-detail',
                payload: {
                    timeSequenceCardDetail: {
                        ...timeSequenceCardDetail,
                        basicInfo: updatedBasicInfo,
                    },
                },
            });
        }
    };

    // 处理标签变化，更新Redux
    const handleTagChange = (newTags) => {
        // 如果是从Redux更新触发的变化，不要再次更新Redux
        if (isUpdatingFromRedux.current) {
            setTags(newTags);
            return;
        }

        setTags(newTags);

        if (isEditMode && timeSequenceCardDetail) {
            // 检查标签是否实际变化
            if (!isEqual(newTags, timeSequenceCardDetail.basicInfo.tags)) {
                // 更新Redux状态
                dispatch({
                    type: 'update-timeSequence-card-detail',
                    payload: {
                        timeSequenceCardDetail: {
                            ...timeSequenceCardDetail,
                            basicInfo: {
                                ...timeSequenceCardDetail.basicInfo,
                                tags: newTags,
                            },
                        },
                    },
                });
            }
        }
    };

    // 下一步按钮点击
    const handleNextStep = async () => {
        try {
            // 验证表单
            await form.validate(['name']); // 只验证名称必填字段

            // 获取表单数据
            const values = form.getFieldsValue();

            // 准备完整的表单数据，包括标签（过滤掉空标签）
            const nonEmptyTags = tags.filter((tag) => tag.trim() !== '');

            // 更新Redux状态
            if (timeSequenceCardDetail) {
                dispatch({
                    type: 'update-timeSequence-card-detail',
                    payload: {
                        timeSequenceCardDetail: {
                            ...timeSequenceCardDetail,
                            basicInfo: {
                                ...timeSequenceCardDetail.basicInfo,
                                ...values,
                                tags: nonEmptyTags,
                            },
                        },
                    },
                });
            } else {
                // 如果没有详情数据，创建一个新的
                dispatch({
                    type: 'update-timeSequence-card-detail',
                    payload: {
                        timeSequenceCardDetail: {
                            basicInfo: {
                                ...values,
                                tags: nonEmptyTags,
                            },
                            structureItems: [],
                            rawResponse: null,
                        },
                    },
                });
            }

            // 切换到下一个Tab
            if (onNext) {
                onNext();
            }
        } catch (error) {
            // 表单验证失败
            Message.error('请填写必填字段');
            console.error('表单验证失败:', error);
        }
    };

    // 保存更新的数据
    const handleSave = async () => {
        try {
            // 验证表单
            await form.validate(['name']); // 只验证名称必填字段

            // 获取表单数据
            const values = form.getFieldsValue();
            
            // 计算新版本号 - 在保存时递增版本号
            const currentVersion = timeSequenceCardDetail?.basicInfo?.version || generateDefaultVersion();
            const newVersion = incrementVersion(currentVersion);
            
            // 准备完整的表单数据，包括标签（过滤掉空标签）
            const nonEmptyTags = tags.filter((tag) => tag.trim() !== '');

            // 更新Redux状态
            if (timeSequenceCardDetail) {
                dispatch({
                    type: 'update-timeSequence-card-detail',
                    payload: {
                        timeSequenceCardDetail: {
                            ...timeSequenceCardDetail,
                            basicInfo: {
                                ...timeSequenceCardDetail.basicInfo,
                                ...values,
                                version: newVersion, // 使用递增后的版本号
                                tags: nonEmptyTags,
                            },
                        },
                    },
                });
            }

            // 调用保存回调
            if (onSave) {
                onSave();
            }
        } catch (error) {
            // 表单验证失败
            Message.error('请填写必填字段');
            console.error('表单验证失败:', error);
        }
    };

    // 修改编辑按钮的处理
    const handleEditButtonClick = () => {
        if (isEditMode) {
            // 已在编辑模式，点击表示保存
            handleSave();
        } else {
            // 未在编辑模式，点击表示切换到编辑模式
            if (onToggleEditMode) onToggleEditMode();
        }
    };

    // 处理返回列表页
    const handleBackToList = () => {
        // navigate('/application/timeSequenceCard');
        navigate(-1)
    };

    if (loading) {
        return (
            <div className={styles.loadingContainer}>
                <Spin tip="加载中..." />
            </div>
        );
    }

    return (
        <div className={styles.pageContainer}>
            <div className={styles.contentWrapper}>
                <Row className={styles.formRow}>
                    <Col span={12}>
                        <Card bordered={false} className={styles.TimeSequenceCardBasicInfo}>
                            <Form
                                form={form}
                                layout="vertical"
                                requiredSymbol={false}
                                validateMessages={{
                                    required: '名称是必填项',
                                }}
                                disabled={isReadOnly}
                                onValuesChange={handleFormValuesChange}
                            >
                                <Row className={styles.basicInfoHeader}>
                                    <div className={styles.iconAndName}>
                                        <div className={styles.IconWrapper}>
                                            <IconTimeCard className={styles.Icon} />
                                        </div>
                                        <div className={styles.divider}></div>
                                        <div className={styles.nameFormContainer}>
                                            <FormItemWithoutRequiredMark
                                                label={<CustomLabel label="名称" required={true} />}
                                                field="name"
                                                style={{ marginBottom: '0px' }}
                                                rules={[{ required: true }]}
                                            >
                                                <Input
                                                    placeholder="请输入"
                                                    className={styles.nameInput}
                                                />
                                            </FormItemWithoutRequiredMark>
                                        </div>
                                    </div>
                                </Row>
                                <Row>
                                    <Col span={24}>
                                        <FormItemWithoutRequiredMark
                                            label="描述"
                                            field="description"
                                        >
                                            <Input.TextArea
                                                placeholder="用简单明了的话描述~"
                                                rows={4}
                                                maxLength={100}
                                                className={styles.descriptionInput}
                                                showWordLimit
                                            />
                                        </FormItemWithoutRequiredMark>
                                    </Col>
                                </Row>
                                <Row
                                    style={{
                                        marginBottom: '24px',
                                        paddingBottom: '24px',
                                        borderBottom: '1px solid #f5f5f5',
                                    }}
                                >
                                    <Col span={24} style={{ marginBottom: '0px' }}>
                                        <FormItemWithoutRequiredMark
                                            className={styles.tagFormItem}
                                            style={{ marginBottom: '0px' }}
                                        >
                                            <Row
                                                justify="space-between"
                                                align="center"
                                                style={{ marginBottom: '8px' }}
                                            >
                                                <Space
                                                    className={styles.templatePlaceholder}
                                                    direction="vertical"
                                                    size={'mini'}
                                                >
                                                    <Text
                                                        style={{
                                                            color: '#5c5c5c',
                                                            fontWeight: '600',
                                                            fontSize: '14px',
                                                        }}
                                                    >
                                                        标签
                                                    </Text>
                                                    <Text className={styles.labelExact}>
                                                        标签至多添加3个
                                                    </Text>
                                                </Space>
                                                <Button
                                                    className={`${styles.addTagBtn} ${isReadOnly ? styles.disabledTagBtn : ''
                                                        }`}
                                                    onClick={showTagInput}
                                                    disabled={isReadOnly}
                                                >
                                                    添加
                                                </Button>
                                            </Row>

                                            {tags.length > 0 && (
                                                <div className={styles.selectedItemList}>
                                                    {tags.map((tag, index) => (
                                                        <Row
                                                            key={`tag-${index}`}
                                                            className={styles.selectedItemRow}
                                                        >
                                                            <Input
                                                                autoFocus={tag === ''}
                                                                value={tag}
                                                                onChange={(value) => {
                                                                    if (value && value.length > 20) {
                                                                        return;
                                                                    }
                                                                    const newTags = [...tags];
                                                                    newTags[index] = value;
                                                                    handleTagChange(newTags);
                                                                }}
                                                                onKeyDown={(e) => {
                                                                    if (e.key === 'Enter') {
                                                                        e.preventDefault();
                                                                        e.stopPropagation();
                                                                    }
                                                                }}
                                                                placeholder={`标签 ${index + 1}`}
                                                                disabled={isReadOnly}
                                                                className={
                                                                    isReadOnly ? styles.disabledTagInput : ''
                                                                }
                                                                suffix={
                                                                    <IconCloseTag
                                                                        className={`${styles.deleteIcon} ${isReadOnly
                                                                            ? styles.disabledDeleteIcon
                                                                            : ''
                                                                            }`}
                                                                        onClick={
                                                                            isReadOnly
                                                                                ? undefined
                                                                                : () => {
                                                                                    const newTags = [...tags];
                                                                                    newTags.splice(index, 1);
                                                                                    handleTagChange(newTags);
                                                                                }
                                                                        }
                                                                    />
                                                                }
                                                            />
                                                        </Row>
                                                    ))}
                                                </div>
                                            )}
                                        </FormItemWithoutRequiredMark>
                                    </Col>
                                </Row>
                                <Row gutter={16} style={{ borderBottom: '1px solid #f5f5f5' }}>
                                    <Col span={12}>
                                        <FormItemWithoutRequiredMark label="版本号" field="version">
                                            <Input 
                                                placeholder="保存时自动更新版本号" 
                                                disabled={true}
                                                style={{ 
                                                    backgroundColor: '#fcfcfc', 
                                                    cursor: 'not-allowed',
                                                    color: '#333' 
                                                }} 
                                            />
                                        </FormItemWithoutRequiredMark>
                                    </Col>
                                    <Col span={12}>
                                        {/* <FormItemWithoutRequiredMark label="许可证" field="license">
                                            <Input placeholder="请输入" />
                                        </FormItemWithoutRequiredMark> */}
                                    </Col>
                                </Row>
                                <div className={styles.switchContainer}>
                                    <Space
                                        className={styles.switchLeftContent}
                                        direction="vertical"
                                        size={4}
                                    >
                                        <Text className={styles.Switchlabel}>是否启用</Text>
                                        <Text className={styles.SwitchTitle}>控制时序卡片</Text>
                                    </Space>
                                    <div className={styles.switchRightContent}>
                                        <FormItemWithoutRequiredMark
                                            field="is_enabled"
                                            triggerPropName="checked"
                                            style={{ margin: 0 }}
                                        >
                                            <Switch />
                                        </FormItemWithoutRequiredMark>
                                    </div>
                                </div>
                            </Form>
                        </Card>
                    </Col>
                </Row>
            </div>
            <div className={styles.formActions}>
                <Space>
                    <Button className={styles.cancelBtn} onClick={handleBackToList}>
                        {isDetailMode ? '返回' : '取消'}
                    </Button>
                    {!isDetailMode && (
                        <Button className={styles.confirmBtn} onClick={handleNextStep}>
                            下一步
                        </Button>
                    )}
                    {isDetailMode && (
                        <Button
                            className={styles.editBtn}
                            type="primary"
                            onClick={handleEditButtonClick}
                            loading={submitting}
                        >
                            {isEditMode ? '保存' : '编辑'}
                        </Button>
                    )}
                </Space>
            </div>
        </div>
    );
};

export default TimeSequenceCardBasicInfo;
