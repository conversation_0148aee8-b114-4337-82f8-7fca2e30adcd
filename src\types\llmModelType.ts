// src/types/llmModelType.ts

/**
 * LLM模型列表请求参数接口
 */
export interface ModelListParams {
    pager?: {
        page?: number;
        size?: number;
        count?: number;
        sort?: string | null;
        order?: string;
        offset?: number;
        returnTotal?: boolean;
    };
    type?: string | null;
    name?: string | null;
    moduleId?: string | null;
    version?: string | null;
    deployName?: string | null;
}

/**
 * LLM模型列表数据响应接口
 */
export interface ModelListResponse {
    items?: LlmModel[];
    count?: number;
}

/**
 * LLM模型实体接口
 */
export interface LlmModel {
    id: string;
    createdTime: string;
    createUserId: string;
    updatedTime: string | null;
    updateUserId: string;
    llmProviderId: string;
    modelId: string;
    icon: string;
    name: string;
    deployName: string;
    version: string;
    apiVersion: string;
    type: number;
    tags: string[] | null;
    multiModal: boolean;
    imageGeneration: boolean;
    promptCost: number;
    completionCost: number;
    dimension: number;
    maxTokens: number;
    temperature: number;
    isEnabled: boolean;
}

/**
 * LLM提供者响应数据接口
 */
export interface LlmProviderResponse {
    id?: string | null;
    createdTime?: string | null;
    createdUserId?: string | null;
    updatedTime?: string | null;
    updatedUserId?: string | null;
    logo?: string | null;
    name?: string | null;
    provider?: string;
    description?: string | null;
}

/**
 * LLM提供者创建请求参数
 */
export interface LlmProviderCreateRequest {
    id?: string;
    name: string;     // 对外展示名称
    provider: string; // 部署名称/内部标识名称
    description?: string; // 描述
    logo?: string;    // 可选的logo
}

/**
 * LLM提供者更新请求参数
 */
export interface LlmProviderUpdateRequest {
    id: string;       // 提供者ID
    name: string;     // 对外展示名称
    provider: string; // 部署名称/内部标识名称
    description?: string; // 描述
    logo?: string;    // 可选的logo
}

/**
 * LLM提供者模型列表项类型定义
 */
export interface LlmProviderModelResponse {
    icon: string;              // 图标URL或路径
    id: string | null;         // 模型ID，可能为空
    name: string;              // 模型名称，如 "gpt-4o-mini"
    deployName: string | null; // 部署名称，可能为空
    version: string;           // 版本号，如 "1106-Preview"
    apiVersion: string | null; // API版本，可能为空
    group: string | null;      // 分组，可能为空
    apiKey: string;            // API密钥
    endpoint: string;          // API端点URL
    type: number;              // 模型类型（枚举值，假设为整数）
    multiModal: boolean;       // 是否支持多模态
    realTime: boolean;         // 是否支持实时处理
    imageGeneration: boolean;  // 是否支持图像生成
    promptCost: number;        // 提示成本（每单位价格）
    completionCost: number;    // 完成成本（每单位价格）
    dimension: number;         // 维度（用途待定）
    maxTokens: number | null;  // 最大token数，可能为空
    temperature: number;       // 温度参数，控制输出随机性
    isEnabled: boolean;        // 是否启用
}

/**
 * LLM创建模型请求参数类型定义
 */
export interface LlmModelCreateRequest {
    id: string | "";              // 模型ID，可能为空
    llmProviderId: string;          // LLM提供者ID
    modelId: string | "";         // 模型ID，可能为空
    icon: string | "";            // 图标URL或路径
    name: string;                   // 模型名称
    deployName: string | "";      // 部署模型名称，可能为空
    version: string;                // 版本号
    apiVersion: string | "";      // API版本，可能为空
    type: number;                   // 模型类型（枚举值）
    tags: string[] | null;          // 标签列表，可能为空
    multiModal: boolean;            // 是否支持多模态
    imageGeneration: boolean;       // 是否支持图像生成
    promptCost: number;             // 提示成本
    completionCost: number;         // 完成成本
    dimension: number | "";              // 维度
    maxTokens: number | "";       // 最大token数，可能为空
    temperature: number | "";     // 温度参数，可能为空
    isEnabled: boolean;             // 是否启用
}


/**
 * LLM模型密钥接口
 */
export interface LlmModelKeyResponse {
    id?: string;
    createdTime?: string | null;
    createUserId?: string | null;
    updatedTime?: string | null;
    updatedUserId?: string | null;
    llmProviderId?: string;
    llmProvider?: string;
    name?: string;
    apiKey?: string;
    endpoint?: string;
}

/**
 * 新增LLM模型密钥请求接口
 */
export interface LlmModelKeyCreateRequest {
    id: string | "", // 密钥id
    name: string; // 密钥名称
    apiKey: string; // 密钥值
    llmProviderId?: string; // 提供者ID，可选
    llmProvider?: string; // 提供者名称，可选
    endpoint?: string; // 接口地址，可选
}

/**
 * 更新LLM模型密钥请求接口
 */
export interface LlmModelKeyUpdateRequest {
    id: string , // 密钥id
    name: string; // 密钥名称
    apiKey: string; // 密钥值
    llmProviderId: string; // 提供者ID
    llmProvider?: string; // 提供者名称，可选
    endpoint: string; // 接口地址
}


/**
 * LLM模型配置响应数据类型
 */
export interface LlmModelConfigResponse {
    id: string;
    createdTime: string;
    createUserId: string;
    updatedTime: string | null;
    updateUserId: string;
    llmProviderId: string;
    llmModelKeyId: string;
    llmModelId: string;
    modelId: string;
    icon: string;
    name: string;
    deployName: string;
    version: string;
    apiVersion: string;
    group: string | null;
    type: number;
    tags: string[] | null;
    multiModal: boolean;
    imageGeneration: boolean;
    promptCost: number;
    completionCost: number;
    dimension: number;
    maxTokens: number;
    temperature: number;
    isEnabled: boolean;
}

/**
 * LLM模型配置创建请求参数类型定义
 */
export interface LlmModelConfigCreateRequest extends LlmModelCreateRequest {
    llmModelKeyId: string;         // 模型密钥ID
    group: string | null;          // 分组
}

/**
 * LLM模型配置实体参数类型定义
 */
export interface LlmModelConfig extends LlmModel {
    llmModelKeyId: string;         // 模型密钥ID
    llmModelId: string;
    group: string | null;          // 分组
}
