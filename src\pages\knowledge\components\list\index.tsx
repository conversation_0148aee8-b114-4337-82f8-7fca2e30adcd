import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Button,
  Input,
  Typography,
  Select,
  Modal,
  Message,
  Form,
  Tabs,
  Tooltip,
  Image,
  Spin,
  Grid,
} from '@arco-design/web-react';
import { useNavigate } from 'react-router-dom';
import IconSearch from '@/assets/knowledge/IconSearch.svg';
import IconScreen from '@/assets/application/screen.svg';
import IconKnowledge from '@/assets/knowledge/IconKnowledge.png';
import TopIcon from '@/assets/top.svg';
import NotFileIcon from '@/assets/knowledge/NotFile.svg';
import styles from './style/index.module.less';
import { useSelector, useDispatch } from 'react-redux';
import { GlobalState } from '@/store/index';
import {
  getKnowledgeList,
  deleteKnowledge,
  isKnowledgeExist,
  createKnowledge,
  KnowledgeListResponse,
} from '@/lib/services/knowledge-service';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import { Popover } from '@arco-design/web-react';
import IconMore from '@/assets/knowledge/iconMore.svg';
import useLocale from '@/utils/useLocale';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';
import IconCloseTag from '@/assets/close.svg';
import { getKnowledgeLabels } from '@/lib/services/knowledge-service';
const { Row, Col } = Grid;
const { Text } = Typography;
const Option = Select.Option;
const FormItem = Form.Item;
const TextArea = Input.TextArea;

function KnowledgeList() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const locale = useLocale();
  const projectDetailMenuName = useSelector(
    (state: GlobalState) => state.projectDetailMenuName
  );
  const [knowledgeType, setKnowledgeType] = useState('document');
  const [knowledgeItems, setKnowledgeItems] = useState<KnowledgeListResponse[]>(
    []
  );
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const isSubscribed = useRef(true);
  const isLoadingRef = useRef(false);
  const fetchTimeoutRef = useRef<NodeJS.Timeout>();
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [count, setCount] = useState(0);
  const [description, setDescription] = useState('');
  const [labels, setLabels] = useState<string[]>([]);
  const [labelOptions, setLabelsOptions] = useState<
    { value: string; label: string }[]
  >([]);
  const [collectionName, setCollectionName] = useState<string>('');

  // 添加分页相关状态
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchLabel, setSearchLabel] = useState<string>('');
  const pageSize = 16;
  const cardBoxRef = useRef<HTMLDivElement>(null);

  // 处理搜索输入变化
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
    setHasMore(true);
    setKnowledgeItems([]);
  };

  // 处理排序变化
  const handleSearchLabelChange = (value: string) => {
    setSearchLabel(value);
    setCurrentPage(1);
    setHasMore(true);
    setKnowledgeItems([]);
  };

  // 加载更多数据
  const loadMoreData = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    try {
      const nextPage = currentPage + 1;
      const params: any = {
        Pager: {
          Page: nextPage,
          Size: pageSize,
        },
      };

      if (searchValue) {
        params.keyWord = searchValue;
      }

      if (searchLabel) {
        params.labels = searchLabel;
      }

      if (knowledgeType) {
        params.type = knowledgeType;
      }

      const responseData = await getKnowledgeList(params);
      const knowledgeItems = Array.isArray(responseData) ? responseData : [];

      if (knowledgeItems.length > 0) {
        // 合并新旧数据
        setKnowledgeItems((prevData) => [...prevData, ...knowledgeItems]);
        setCurrentPage(nextPage);

        if (knowledgeItems.length < pageSize) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      Message.error({
        content: locale['menu.application.opreate.errMsg'],
      });
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    loading,
    hasMore,
    pageSize,
    locale,
    searchValue,
    searchLabel,
    knowledgeType,
  ]);

  useEffect(() => {
    fetchKnowledgeList();
    fetchKnowledgeLabels();
  }, [knowledgeType, searchValue, searchLabel, locale]);

  const fetchKnowledgeList = useCallback(async () => {
    if (isLoadingRef.current) return;

    try {
      isLoadingRef.current = true;
      setLoading(true);
      const params: any = {
        type: knowledgeType,
      };

      if (searchValue) {
        params.keyWord = searchValue;
      }

      if (searchLabel) {
        params.labels = searchLabel;
      }

      const response = await getKnowledgeList(params);
      if (isSubscribed.current) {
        setKnowledgeItems(response);
        setCurrentPage(1);
        setHasMore(response.length === pageSize);
      }
    } catch (error) {
      if (isSubscribed.current) {
        setHasMore(false);
      }
    } finally {
      if (isSubscribed.current) {
        isLoadingRef.current = false;
        setLoading(false);
      }
    }
  }, [knowledgeType, searchValue, searchLabel, locale, pageSize]);

  const fetchKnowledgeLabels = useCallback(async () => {
    const response = await getKnowledgeLabels(knowledgeType);
    const formattedLabels = response.map((label) => ({
      value: label,
      label: label,
    }));
    setLabelsOptions(formattedLabels);
  }, [knowledgeType]);

  // 滚动监听函数
  const handleScroll = useCallback(() => {
    if (!cardBoxRef.current) return;

    const container = cardBoxRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;

    // 处理回到顶部按钮显示
    setShowBackToTop(scrollTop > 300);

    // 处理加载更多数据
    if (!loading && hasMore && scrollHeight - scrollTop - clientHeight < 150) {
      loadMoreData();
    }
  }, [loading, hasMore, loadMoreData]);

  // 独立监测容器和卡片位置关系
  useEffect(() => {
    if (!cardBoxRef.current || loading || !hasMore) return;

    const checkCardPosition = () => {
      const container = cardBoxRef.current;
      if (!container) return;

      const lastCard = container.querySelector(
        `.${styles.customCard}:last-child`
      );
      if (lastCard) {
        const containerBottom = container.getBoundingClientRect().bottom;
        const lastCardBottom = lastCard.getBoundingClientRect().bottom;
        const distanceToBottom = containerBottom - lastCardBottom;

        // 如果最后一个卡片到容器底部的距离大于100px
        if (distanceToBottom > 100) {
          loadMoreData();
        }
      }
    };

    // 创建 ResizeObserver 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      checkCardPosition();
    });

    // 监听容器大小变化
    resizeObserver.observe(cardBoxRef.current);

    // 初始检查
    checkCardPosition();

    return () => {
      resizeObserver.disconnect();
    };
  }, [loading, hasMore, loadMoreData, knowledgeItems.length]);

  // 回到顶部
  const scrollToTop = useCallback(() => {
    if (!cardBoxRef.current) return;

    setShowBackToTop(false);

    cardBoxRef.current.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  // 添加滚动监听
  useEffect(() => {
    const container = cardBoxRef.current;
    if (!container) return;

    // 使用防抖处理滚动事件，避免频繁触发
    let scrollTimeout: NodeJS.Timeout;

    const scrollListener = () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      scrollTimeout = setTimeout(() => {
        handleScroll();
      }, 50);
    };

    container.addEventListener('scroll', scrollListener);

    return () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      container.removeEventListener('scroll', scrollListener);
    };
  }, [handleScroll]);

  const updateBreadcrumbData = (newBreadcrumb: Map<string, string>) => {
    // 使用 dispatch 来发送 action 更新 breadcrumb
    dispatch({
      type: 'update-breadcrumb-menu-name',
      payload: { breadcrumbMenuName: newBreadcrumb }, // 设置新的breadcrumb数组
    });
  };

  const gotoProjectDetail = (item) => {
    const detailPath = '/knowledge/list';
    const breadcrumbData = new Map<string, string>([
      [detailPath, item.display_name || item.name],
    ]);
    updateBreadcrumbData(breadcrumbData);

    navigate(detailPath, {
      state: { knowledgeInfo: item, type: knowledgeType },
    });
  };

  const comfirmDel = (event, agentId) => {
    event.stopPropagation();
    const title = locale['menu.application.knowledge.delete.title'];
    const content = locale['menu.application.knowledge.delete.content'];
    Modal.confirm({
      title: (
        <div style={{ textAlign: 'left', fontSize: '16px', fontWeight: 600 }}>
          {title}
        </div>
      ),
      content: <div style={{ color: '#86909C' }}>{content}</div>,
      cancelText: locale['cancelBut'],
      okText: locale['deleteBut'],
      okButtonProps: {
        status: 'danger',
      },
      icon: null,
      footer: (
        <div style={{ textAlign: 'right' }}>
          <Button
            style={{ height: 40, width: 76 }}
            onClick={() => Modal.destroyAll()}
          >
            {locale['cancelBut']}
          </Button>
          <Button
            status="danger"
            style={{ height: 40, width: 76, marginLeft: 8 }}
            onClick={() => {
              return new Promise<void>((resolve, reject) => {
                deleteKnowledge(agentId)
                  .then(async () => {
                    if (isSubscribed.current) {
                      Message.success({
                        content: locale['menu.application.opreate.okMsg'],
                      });
                      // 刷新列表
                      await fetchKnowledgeList();
                    }
                    resolve();
                    Modal.destroyAll();
                  })
                  .catch((e) => {
                    reject(e);
                    if (isSubscribed.current) {
                      Message.error({
                        content: locale['menu.application.opreate.errMsg'],
                      });
                    }
                    console.error(e);
                  });
              });
            }}
          >
            {locale['deleteBut']}
          </Button>
        </div>
      ),
    });
  };

  const handleCreateKnowledge = async () => {
    try {
      setLoading(true);

      // 检查知识库是否存在
      const existResult = await isKnowledgeExist(collectionName);
      if (existResult) {
        Message.error('知识库名称已存在');
        return;
      }

      // 创建知识库
      const res = await createKnowledge({
        collection_name: collectionName,
        collection_type: knowledgeType,
        description: description,
        labels: labels,
        dimension: 1536,
        model: 'text-embedding-3-small',
        provider: 'azure-openai',
      });

      if (!res) {
        Message.error('创建失败');
        return;
      }

      if (isSubscribed.current) {
        Message.success('创建成功');
        // 刷新列表
        await fetchKnowledgeList();
        // 关闭弹窗并重置表单
        setVisible(false);
        form.resetFields();
      }
    } catch (error) {
      if (isSubscribed.current) {
        console.error('创建知识库失败:', error);
        Message.error('创建失败');
      }
    } finally {
      if (isSubscribed.current) {
        setLoading(false);
      }
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.titleSection}>
        <Tabs
          className={styles.tabs}
          activeTab={knowledgeType}
          onChange={setKnowledgeType}
        >
          <TabPane key="document" title="文档库"></TabPane>

          <TabPane key="question-answer" title="Q&A库"></TabPane>
        </Tabs>
      </div>
      <RowComponent className={styles.header}>
        <RowComponent>
          <Button
            type="primary"
            className={styles.addBtn}
            onClick={() => setVisible(true)}
          >
            {knowledgeType === 'document'
              ? locale['menu.application.knowledge.create']
              : locale['menu.application.knowledge.createQanda']}
          </Button>
        </RowComponent>

        <RowComponent className={styles.rowEndCenter}>
          <Text className={styles.countAppText}>
            共 {knowledgeItems.length} 个知识库
          </Text>
          <Input
            className={styles.searchBox}
            prefix={<IconSearch />}
            placeholder={
              locale['menu.application.knowledge.search.placeholder'] ||
              '搜索知识库名称'
            }
            value={searchValue}
            onChange={handleSearchChange}
            allowClear
          />
          <Select
            className={styles.selectBox}
            placeholder={locale['menu.application.agent.search.tags']}
            prefix={<IconScreen />}
            value={searchLabel || undefined}
            onChange={handleSearchLabelChange}
            allowClear
            triggerProps={{
              autoAlignPopupWidth: false,
              position: 'bl',
              className: 'knowledge-select-popup',
            }}
          >
            {labelOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </RowComponent>
      </RowComponent>

      <div className={styles.customCardBox} ref={cardBoxRef}>
        {knowledgeItems.map((item) => (
          <Card
            key={item.name}
            className={styles.customCard}
            onClick={() => gotoProjectDetail(item)}
            hoverable
          >
            <RowComponent className={styles.rowStartCenter}>
              <Image src={IconKnowledge} className={styles.folderIcon} />
              <div className={styles.name}>
                {item.display_name || item.name}
              </div>
            </RowComponent>

            <RowComponent
              className={styles.rowStartCenter}
              style={{ marginTop: 8 }}
            >
              {/* <Tooltip content={item.description}>
                
              </Tooltip> */}
              <Text className={styles.description}>
                {item.description || '-'}
              </Text>
              {item.labels && item.labels.length > 0 && (
                <RowComponent
                  className={styles.rowStartCenter}
                  style={{ marginTop: 8 }}
                >
                  {item.labels.slice(0, 3).map((label, index) => (
                    <Text key={index} className={styles.profileTag}>
                      {label}
                    </Text>
                  ))}
                  {item.labels.length > 3 && (
                    <Tooltip
                      content={
                        <div className={styles.tooltipProfilesContainer}>
                          {item.labels.slice(3).map((label, index) => (
                            <div
                              key={index}
                              className={styles.tooltipProfileItem}
                            >
                              {label}
                            </div>
                          ))}
                        </div>
                      }
                      position="tl"
                    >
                      <Text className={styles.profileTag}>
                        +{item.labels.length - 3}
                      </Text>
                    </Tooltip>
                  )}
                </RowComponent>
              )}
            </RowComponent>

            <RowComponent className={styles.footer}>
              <Text className={styles.footerText}>{item.count} 个文件</Text>
              <div className={styles.hoverContainer}>
                <Text className={styles.metaText}>
                  @{item.user.user_name || '-'} | 创建时间:{' '}
                  {new Date(item.create_date)
                    .toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                    })
                    .replace(/\//g, '/')}
                </Text>
                <Popover
                  trigger="click"
                  position="rt"
                  content={
                    <span className={styles.red}
                      onClick={(event) => {
                        event.stopPropagation();
                        comfirmDel(event, item.name);
                      }}>
                      {locale['menu.application.opreate.del']}
                    </span>
                  }
                >
                  <div onClick={(e) => e.stopPropagation()} className={styles.triggerBtn}>
                    <IconMore style={{ fontSize: 20, cursor: 'pointer' }} />
                  </div>
                </Popover>
              </div>
            </RowComponent>
          </Card>
        ))}
        {loading && (
          <div className={styles.loadingContainer}>
            <Text className={styles.loadingText}>{locale['loading']}</Text>
          </div>
        )}
        {!loading && knowledgeItems.length === 0 && (
          <div className={styles.emptyContainer}>
            <NotFileIcon />
            <Text className={styles.emptyText}>
              {locale['menu.application.knowledge.empty']}
            </Text>
          </div>
        )}
        {/* 底部空白区域 */}
        <div
          style={{ height: '100px', width: '100%', gridColumn: '1 / -1' }}
        ></div>
      </div>

      <div
        className={`${styles.backToTop} ${!showBackToTop ? styles.hidden : ''}`}
        onClick={scrollToTop}
      >
        <TopIcon />
        <span>{locale['backToTop']}</span>
      </div>

      <Modal
        visible={visible}
        onOk={handleCreateKnowledge}
        onCancel={() => {
          setVisible(false);
          form.resetFields();
        }}
        className={styles.customModal}
        confirmLoading={loading}
        footer={
          <RowComponent className={styles.operateButGroup}>
            <Button
              className={[styles.cancelBut, styles.but]}
              onClick={() => {
                setVisible(false);
                form.resetFields();
              }}
            >
              {locale['cancelBut']}
            </Button>
            <Button
              type="primary"
              className={`${styles.createBut} ${styles.but} ${!collectionName ? styles.disabled : ''
                }`}
              onClick={handleCreateKnowledge}
              loading={loading}
              disabled={!collectionName}
            >
              {locale['menu.application.opreate.create']}
            </Button>
          </RowComponent>
        }
        style={{
          borderRadius: '16px',
          width: 640,
        }}
      >
        <RowComponent>
          <Text className={styles.subtitleTitle}>
            {knowledgeType === 'document'
              ? locale['menu.application.knowledge.create']
              : locale['menu.application.knowledge.createQanda']}
          </Text>
        </RowComponent>
        <RowComponent style={{ marginTop: 16 }}>
          <div className={styles.iconContainer}>
            {/* 图标 */}
            <div>
              <Image src={IconKnowledge} />
            </div>

            <div className={styles.divider}></div>

            {/* 命名 */}
            <div className={styles.nameContainer}>
              <RowComponent>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.basic.names']}
                  <span style={{ color: '#4455F2', marginLeft: 4 }}>*</span>
                </Text>
              </RowComponent>
              <RowComponent style={{ width: '100%' }}>
                <FormItem
                  field="collection_name"
                  validateTrigger={['onBlur', 'onChange']}
                >
                  <div style={{ position: 'relative', width: '100%' }}>
                    <Input
                      placeholder={
                        locale['menu.application.knowledge.create.input']
                      }
                      maxLength={50}
                      value={collectionName}
                      onChange={(value) => {
                        setCollectionName(value);
                        form.setFieldValue('collection_name', value);
                      }}
                      style={{
                        backgroundColor: '#fff',
                        border: '1px solid #00000014',
                        width: '100%',
                        resize: 'none',
                        height: '40px',
                        borderRadius: '8px',
                      }}
                    />
                  </div>
                </FormItem>
              </RowComponent>
            </div>
          </div>
        </RowComponent>

        {/* 描述 */}
        <RowComponent style={{ marginTop: 24 }}>
          <Text className={styles.subtitle}>
            {locale['menu.application.info.basic.descript']}
          </Text>
        </RowComponent>
        <RowComponent style={{ marginTop: 8 }}>
          <FormItem
            field="description"
            validateTrigger={['onBlur', 'onChange']}
            style={{ marginBottom: 0 }}
          >
            <div style={{ position: 'relative', width: '100%' }}>
              <TextArea
                placeholder={locale['menu.application.knowledge.create.input']}
                maxLength={100}
                value={description}
                onChange={(value) => {
                  setDescription(value);
                  form.setFieldValue('description', value);
                }}
                style={{
                  backgroundColor: '#fff',
                  border: '1px solid #00000014',
                  width: '100%',
                  resize: 'none',
                  height: '64px',
                  borderRadius: '8px',
                }}
              />
              <div
                style={{
                  position: 'absolute',
                  bottom: '8px',
                  right: '8px',
                  fontSize: '12px',
                  color: '#00000052',
                  pointerEvents: 'none',
                }}
              >
                {description.length}/100
              </div>
            </div>
          </FormItem>
        </RowComponent>

        {/* 标签 */}
        <RowComponent className={styles.titleRow}>
          <div className={styles.titleContent}>
            <Text className={styles.subtitle}>
              {locale['menu.application.info.basic.label']}
            </Text>
            <Text className={styles.subtitlePlaceholder}>
              {locale['menu.application.info.basic.placeholder.label']}
            </Text>
          </div>
          <Button
            className={styles.addLabelBut}
            disabled={labels.length >= 3}
            onClick={() => {
              if (labels.length >= 3) {
                return;
              }
              setLabels([...labels, '']);
            }}
            style={{
              opacity: labels.length >= 3 ? 0.5 : 1,
              cursor: labels.length >= 3 ? 'not-allowed' : 'pointer',
            }}
          >
            <Text className={styles.operateText}>
              {locale['menu.application.template.setting.adds']}
            </Text>
          </Button>
        </RowComponent>
        <Col span={24} className={styles.labelContainer}>
          {/* 渲染已选择的标签 */}
          {labels.length > 0 && (
            <div className={styles.selectedItemList}>
              {labels.map((label, index) => (
                <Row
                  key={`${label}-${index}`}
                  className={styles.selectedItemRow}
                >
                  <Input
                    value={label}
                    autoFocus={true}
                    onChange={(value) => {
                      if (value && value.length > 10) {
                        return;
                      }
                      const newLabels = [...labels];
                      newLabels[index] = value;
                      setLabels(newLabels);
                      form.setFieldValue('labels', newLabels);
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        e.stopPropagation();
                      }
                    }}
                    placeholder={locale[
                      'menu.application.create.group.form.label.placeholder'
                    ].replace('{index}', (index + 1).toString())}
                    suffix={
                      <IconCloseTag
                        className={styles.deleteIcon}
                        onClick={() => {
                          const newLabels = [...labels];
                          newLabels.splice(index, 1);
                          setLabels(newLabels);
                          form.setFieldValue('labels', newLabels);
                        }}
                      />
                    }
                    className={styles.selectedItemCol}
                  />
                </Row>
              ))}
            </div>
          )}
        </Col>
      </Modal>
    </div>
  );
}

export default KnowledgeList;
