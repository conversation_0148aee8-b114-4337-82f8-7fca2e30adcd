import axios, { AxiosRequestConfig } from 'axios';
import { getUserStore } from '@/lib/helpers/store';
import { isTokenExpired } from '@/keycloak';
import { Message } from '@arco-design/web-react';

// 创建 axiosInstance
const axiosInstance = axios.create({
    timeout: 10000, // 请求超时时间
    headers: {
        'Content-Type': 'application/json', // 默认请求头
    },
});

// 跳过加载状态的配置（可选）
const skipLoader = (config) => {
    return config.skipLoader === true; // 可通过 config.skipLoader 控制
};

// 请求拦截器
axiosInstance.interceptors.request.use(
    (config) => {
        // 从 userStore 获取 token
        const user = getUserStore();

        if (isTokenExpired()) {
            console.log('令牌已过期，请重新登录');

            // 使用普通 localStorage 操作替代 Hook
            localStorage.setItem('userStatus', 'logout');
            localStorage.removeItem('user');
            window.location.href = '/login';

            // 拒绝当前请求
            return Promise.reject(new Error('令牌已过期，正在重定向到登录页面'));
        }

        if (!skipLoader(config)) {
            // loaderStore.set(true); // 设置加载状态
        }
        // 添加 token 到请求头
        if (user.token) {
            config.headers.Authorization = `Bearer ${user.token}`;
        }
        return config;
    },
    (error) => {
        // loaderStore.set(false); // 请求失败时关闭加载状态
        return Promise.reject(error);
    }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
    (response) => {
        // 统一格式化响应数据
        return response;
    },
    (error) => {
        const e = error.response;
        
        // 统一处理未授权错误
        if (e.status === 401) {
            console.log('响应显示未授权，可能是令牌无效');

            // 使用普通 localStorage 操作
            localStorage.setItem('userStatus', 'logout');
            localStorage.removeItem('token');
            window.location.href = '/login';
        } else if (e.data.success === false) {
            // 统一处理其他错误，显示错误消息弹窗
            Message.error({
                content: e.data.message,
                duration: 4000, // 显示4秒
            });
        } else if (error.message) {
            // 如果没有响应状态，显示网络错误等信息
            Message.error({
                content: `请检查网络或联系管理员: ${error.message}`,
                duration: 4000,
            });
        }
        // 统一处理其他错误
        return Promise.reject(error);
    }
);

export default axiosInstance;