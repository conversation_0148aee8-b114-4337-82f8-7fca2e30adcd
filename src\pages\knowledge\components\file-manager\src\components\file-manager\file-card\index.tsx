import { IFile } from '@/pages/knowledge/components/file-manager/src/interfaces/database/file-manager';
import { formatDate } from '@/pages/knowledge/components/file-manager/src/utils/date';
import { Button, Space, Tag, Typography, Popover } from 'antd';
import { formatNumberWithThousandsSeparator } from '@/pages/knowledge/components/file-manager/src/utils/common-util';
import FileIcon from '@/assets/knowledge/FileIcon.svg';
import IconAction from '@/assets/knowledge/iconMore.svg';
import styles from './index.module.less';

const { Text } = Typography;

interface FileCardProps {
    file: IFile;
    onNavigate: (folderId: string) => void;
    onRename: (file: IFile) => void;
    onDelete: (fileId: string, parentId: string, fileName: string) => void;
}

const FileCard = ({ file, onNavigate, onRename, onDelete }: FileCardProps) => {
    // 处理标签数据
    const renderTags = () => {
        const tagsArray: string[] = [];
        if (typeof file.tags === 'string') {
            tagsArray.push(...file.tags.split(',').filter(tag => tag.trim()));
        } else if (Array.isArray(file.tags)) {
            tagsArray.push(...file.tags);
        }

        return (
            <div className={styles.cardTags}>
                {/* 文件类型标签 */}
                <Tag className={`${styles.fileTypeTag} ${file.creator_role === 'admin' ? styles.system :
                    file.creator_role === 'client' ? styles.user : ''
                    }`}>
                    {file.creator_role === 'admin' ? '系统' :
                        file.creator_role === 'client' ? '用户' :
                            file.type === 'folder' ? '文件夹' : '文件'}
                </Tag>

                {/* 自定义标签 */}
                {tagsArray.length > 0 && (
                    <>
                        {tagsArray.map((tag, index) => (
                            <Tag key={index} className={styles.customTag}>
                                {tag.trim()}
                            </Tag>
                        ))}
                    </>
                )}

                {/* 知识库标签 */}
                {file.kbs_info && Array.isArray(file.kbs_info) && file.kbs_info.length > 0 && (
                    <>
                        {file.kbs_info.slice(0, 2).map((kb) => (
                            <Tag key={kb.kb_id} className={styles.kbTag}>
                                {kb.kb_name}
                            </Tag>
                        ))}
                        {file.kbs_info.length > 2 && (
                            <Tag className={styles.moreTag}>+{file.kbs_info.length - 2}</Tag>
                        )}
                    </>
                )}
            </div>
        );
    };

    // 处理卡片点击
    const handleCardClick = () => {
        if (file.type === 'folder') {
            onNavigate(file.id);
        }
    };

    // 处理重命名
    const handleRename = (e: React.MouseEvent) => {
        e.stopPropagation();
        onRename(file);
    };

    // 处理删除
    const handleDelete = (e: React.MouseEvent) => {
        e.stopPropagation();
        onDelete(file.id, file.parent_id, file.name);
    };

    return (
        <div
            className={styles.fileCard}
            onClick={handleCardClick}
        >
            <div className={styles.cardContent}>
                <div className={styles.cardHeader}>
                    <div className={styles.iconWrapper}>
                        <FileIcon style={{ width: 48, height: 48 }} />
                    </div>
                    <Text className={styles.cardTitle} ellipsis={{ tooltip: file.name }}>
                        {file.name}
                    </Text>
                </div>

                <div className={styles.cardBody}>
                    {/* 文件描述 */}
                    <div className={styles.cardDescription}>
                        <Text className={styles.descriptionText}>
                            {file.description && file.description.trim()
                                ? file.description
                                : file.type === 'folder'
                                    ? '文件夹'
                                    : `${formatNumberWithThousandsSeparator((file.size / 1024).toFixed(2))} KB`
                            }
                        </Text>
                    </div>

                    {/* 标签区域 */}
                    {renderTags()}
                </div>

                <div className={styles.cardFooter}>
                    {/* 默认显示的信息 */}
                    <div className={styles.defaultInfo}>
                        <Text className={styles.cardTime}>
                            @{file.creator_name || '未知用户'} | {file.file_count}个文件
                        </Text>
                    </div>

                    {/* 悬浮时显示的信息 */}
                    <div className={styles.hoverInfo}>
                        <Text className={styles.hoverTime}>
                            创建时间：{formatDate(file.create_time)}
                        </Text>
                    </div>

                    {/* 操作按钮 */}
                    <Popover
                        trigger="click"
                        placement="right"
                        content={
                            <Space direction="vertical" size="small">
                                <Button
                                    type="text"
                                    size="small"
                                    onClick={handleRename}
                                >
                                    编辑
                                </Button>
                                <Button
                                    type="text"
                                    size="small"
                                    danger
                                    onClick={handleDelete}
                                >
                                    删除
                                </Button>
                            </Space>
                        }
                    >
                        <Button
                            type="text"
                            size="small"
                            className={styles.cardActionButton}
                            onClick={(e) => {
                                e.stopPropagation();
                            }}
                        >
                            <IconAction />
                        </Button>
                    </Popover>
                </div>
            </div>
        </div>
    );
};

export default FileCard; 