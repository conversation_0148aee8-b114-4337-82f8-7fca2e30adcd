import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Input,
  Typography,
  Space,
  Select,
  Spin,
  Modal,
  Form,
  Message,
  Popover,
} from '@arco-design/web-react';
import IconSearch from '@/assets/application/IconSearch.svg';
import IconAction from '@/assets/application/IconAction.svg';
import IconClose from '@/assets/application/IconClose.svg';
import IconAIaction from '@/assets/application/IconAIaction.svg';
// import IconAIaction2 from '@/assets/application/IconAIaction2.svg';
// import IconAIaction3 from '@/assets/application/IconAIaction3.svg';
import styles from './style/index.module.less';
import { useNavigate } from 'react-router-dom';

const { Text, Paragraph } = Typography;
const Option = Select.Option;
const FormItem = Form.Item;

// 创建自定义标签组件
const CustomLabel = ({ label, required }) => {
  return (
    <Space>
      <span>{label}</span>
      {required && (
        <Typography.Text type="secondary" className={styles.required}>
          (必填)
        </Typography.Text>
      )}
    </Space>
  );
};

function AiAction() {
  // 模拟数据 - AI actions
  const mockAiActions = [
    {
      id: '1',
      name: '动作1',
      description:
        '这是一段关于这个应用的描述，具体包括应用功能和应用的运用场景，这个应用能更好的帮用户，欢迎踊跃...',
      tags: ['教育培训', '办公人事'],
      createdTime: '2024-10-25T10:30:00',
      updatedTime: '2024-10-25T10:30:00',
      creator: 'AI4C',
    },
    {
      id: '2',
      name: '动作2',
      description:
        '这是一段关于这个应用的描述，具体包括应用功能和应用的运用场景，这个应用能更好的帮用户，欢迎踊跃...',
      tags: ['教育培训', '办公人事'],
      createdTime: '2024-10-25T09:30:00',
      updatedTime: '2024-10-25T09:30:00',
      creator: 'AI4C',
    },
    {
      id: '3',
      name: '动作3',
      description:
        '这是一段关于这个应用的描述，具体包括应用功能和应用的运用场景，这个应用能更好的帮用户，欢迎踊跃...',
      tags: ['教育培训', '办公人事'],
      createdTime: '2024-10-25T08:30:00',
      updatedTime: '2024-10-25T08:30:00',
      creator: 'AI4C',
    },
    {
      id: '4',
      name: '动作4',
      description:
        '这是一段关于这个应用的描述，具体包括应用功能和应用的运用场景，这个应用能更好的帮用户，欢迎踊跃...',
      tags: ['教育培训', '办公人事'],
      createdTime: '2024-10-25T07:30:00',
      updatedTime: '2024-10-25T07:30:00',
      creator: 'AI4C',
    },
    {
      id: '5',
      name: '动作5',
      description:
        '这是一段关于这个应用的描述，具体包括应用功能和应用的运用场景，这个应用能更好的帮用户，欢迎踊跃...',
      tags: ['教育培训', '办公人事'],
      createdTime: '2024-10-25T07:30:00',
      updatedTime: '2024-10-25T07:30:00',
      creator: 'AI4C',
    },
    {
      id: '6',
      name: '动作6',
      description:
        '这是一段关于这个应用的描述，具体包括应用功能和应用的运用场景，这个应用能更好的帮用户，欢迎踊跃...',
      tags: ['教育培训', '办公人事'],
      createdTime: '2024-10-25T07:30:00',
      updatedTime: '2024-10-25T07:30:00',
      creator: 'AI4C',
    },
    {
      id: '7',
      name: '动作7',
      description:
        '这是一段关于这个应用的描述，具体包括应用功能和应用的运用场景，这个应用能更好的帮用户，欢迎踊跃...',
      tags: ['教育培训', '办公人事'],
      createdTime: '2024-10-25T07:30:00',
      updatedTime: '2024-10-25T07:30:00',
      creator: 'AI4C',
    },
    {
      id: '8',
      name: '动作8',
      description:
        '这是一段关于这个应用的描述，具体包括应用功能和应用的运用场景，这个应用能更好的帮用户，欢迎踊跃...',
      tags: ['教育培训', '办公人事'],
      createdTime: '2024-10-25T07:30:00',
      updatedTime: '2024-10-25T07:30:00',
      creator: 'AI4C',
    },
  ];

  const [actions, setActions] = useState([]);
  const [filteredActions, setFilteredActions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedAction, setSelectedAction] = useState(null);
  const [form] = Form.useForm(); // 创建Form实例
  const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
  const [actionToDelete, setActionToDelete] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [sortType, setSortType] = useState('createTime');
  const actionIcons = [IconAIaction]
  const navigate = useNavigate();

  // 模拟获取AI actions的函数
  const fetchAiActions = async () => {
    try {
      setLoading(true);
      // 模拟API延迟
      await new Promise((resolve) => setTimeout(resolve, 500));

      setActions(mockAiActions);
      setError(null);
    } catch (err) {
      console.error('获取AI action列表失败:', err);
      setError('获取AI action数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAiActions();
  }, []);

  // 当actions或搜索/排序条件改变时，更新过滤后的列表
  useEffect(() => {
    filterAndSortActions();
  }, [actions, searchText, sortType]);

  // 筛选和排序AI action列表
  const filterAndSortActions = () => {
    let result = [...actions];

    // 搜索过滤
    if (searchText) {
      const lowerCaseSearch = searchText.toLowerCase();
      result = result.filter(
        (action) =>
          action.name.toLowerCase().includes(lowerCaseSearch) ||
          action.description.toLowerCase().includes(lowerCaseSearch) ||
          action.tags.some((tag) => tag.toLowerCase().includes(lowerCaseSearch))
      );
    }

    // 排序
    switch (sortType) {
      case 'createTime':
        result.sort(
          (a, b) =>
            new Date(b.createdTime || 0).getTime() -
            new Date(a.createdTime || 0).getTime()
        );
        break;
      case 'updateTime':
        result.sort((a, b) => {
          const timeA = a.updatedTime ? new Date(a.updatedTime).getTime() : 0;
          const timeB = b.updatedTime ? new Date(b.updatedTime).getTime() : 0;
          return timeB - timeA;
        });
        break;
      case 'name':
        result.sort((a, b) => a.name.localeCompare(b.name));
        break;
      default:
        break;
    }

    setFilteredActions(result);
  };

  // 处理排序变化
  const handleSort = (value) => {
    setSortType(value);
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
  };

  // 处理"创建AI action"按钮
  // const handleAddAction = () => {
  // };

  // 处理"查看"AI action详情
  const handleViewAction = (action) => { };

  // 处理删除确认
  const handleDeleteConfirm = (action) => {
    setActionToDelete(action);
    setConfirmDeleteVisible(true);
  };

  // 确认删除
  const handleConfirmDelete = async () => {
    if (actionToDelete) {
      try {
        // 模拟删除操作
        await new Promise((resolve) => setTimeout(resolve, 300));
        setActions(actions.filter((item) => item.id !== actionToDelete.id));
        Message.success('AI action删除成功！');
      } catch (error) {
        console.error('删除AI action失败:', error);
        Message.error('删除AI action失败，请检查网络或联系管理员！');
      } finally {
        setConfirmDeleteVisible(false);
        setActionToDelete(null);
      }
    }
  };

  // 取消删除
  const handleCancelDelete = () => {
    setConfirmDeleteVisible(false);
    setActionToDelete(null);
  };

  // 获取图标组件的函数
  const getActionIcon = (id) => {
    // 使用 id 的数字部分取模，循环使用三个图标
    const index = parseInt(id) % actionIcons.length;
    const IconComponent = actionIcons[index];
    return <IconComponent />;
  };

  const handleAddAction = (item?: { name: string }) => {
    const modelName = item ? item.name : '新增模型配置';
    // let breadcrumbData = new Map([[createModelConfigMenuName, modelName]]);
    // updateBreadcrumbData(breadcrumbData);
    navigate('/application/aiaction/create');
  };

  return (
    <div className={styles.aiAction}>
      <Text className={styles.headerText}>AI action列表</Text>
      <div className={styles.header}>
        <Button
          type="primary"
          className={styles.addActionBtn}
          onClick={() => handleAddAction()}
        >
          创建AI action
        </Button>
        <Space size={'small'}>
          <Text className={styles.actionNumber}>
            共 {filteredActions.length} 个AI action
          </Text>
          <Input
            prefix={<IconSearch />}
            placeholder="AI搜索..."
            style={{ width: 240 }}
            onChange={(value) => handleSearch(value)}
            allowClear
          />
          <Select
            placeholder="按创建时间排序"
            style={{ width: 160 }}
            onChange={handleSort}
            value={sortType}
          >
            <Option value="createTime">按创建时间排序</Option>
            <Option value="updateTime">按更新时间排序</Option>
            <Option value="name">按名称排序</Option>
          </Select>
        </Space>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <Spin tip="加载中..." />
        </div>
      ) : error ? (
        <div className={styles.errorContainer}>
          <Text type="error">{error}</Text>
        </div>
      ) : (
        <div className={styles.content}>
          {filteredActions.length > 0 ? (
            filteredActions.map((action, index) => (
              <Card key={action.id || index} className={styles.actionCard}>
                <div className={styles.actionInfo}>
                  <Space direction="vertical" size={8}>
                    <Space className={styles.nameWrapper} size={12}>
                      <span className={styles.icon}>
                        {getActionIcon(action.id)}
                      </span>
                      <Text
                        className={styles.name}
                        onClick={() => handleViewAction(action)}
                      >
                        {action.name}
                      </Text>
                    </Space>

                    <div className={styles.cardDescription}>
                      <Text
                        type="secondary"
                        style={{
                          color: '#000000A3',
                          fontSize: '14px',
                          fontWeight: '400',
                        }}
                      >
                        {action.description}
                      </Text>
                    </div>

                    <Space className={styles.tags}>
                      {action.tags.map((tag, tIndex) => (
                        <Button key={tIndex} type="secondary" size="mini">
                          {tag}
                        </Button>
                      ))}
                    </Space>
                  </Space>
                  <div
                    className={styles.metaInfo}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginTop: '16px',
                    }}
                  >
                    <Space>
                      <Text className={styles.creator}>@{action.creator}</Text>
                      <Text>|</Text>
                      <Text className={styles.updateTime}>
                        最近更新：{' '}
                        {new Date(action.updatedTime).toLocaleDateString(
                          'zh-CN'
                        )}
                      </Text>
                    </Space>

                    <Popover
                      trigger="click"
                      position="right"
                      className={styles.popoverContent}
                      content={
                        <Space
                          className={styles.popoverContent}
                          direction="vertical"
                          size={'mini'}
                        >
                          <Button
                            className={`${styles.actionBtn} ${styles.deleteBtn}`}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteConfirm(action);
                            }}
                          >
                            删除
                          </Button>
                        </Space>
                      }
                    >
                      <Button className={styles.triggerBtn}>
                        <IconAction />
                      </Button>
                    </Popover>
                  </div>
                </div>
              </Card>
            ))
          ) : (
            <div className={styles.emptyContainer}>
              <Text type="secondary">
                {searchText ? '未找到匹配的AI action' : '暂无AI action数据'}
              </Text>
            </div>
          )}
        </div>
      )}

      {/* 删除确认弹窗 */}
      <Modal
        visible={confirmDeleteVisible}
        title="删除AI action"
        onCancel={handleCancelDelete}
        closeIcon={<IconClose />}
        className={styles.confirmDeleteModal}
        maskClosable={false}
      >
        <div className={styles.modalContent}>
          <Text className={styles.modalContentText}>
            {`AI action ${actionToDelete?.name || ''
              } 将被删除，请确认您是否要删除？`}
          </Text>
        </div>
        <div className={styles.modalFooter}>
          <Space>
            <Button
              onClick={handleCancelDelete}
              className={styles.cancelDeleteBtn}
            >
              取消
            </Button>
            <Button
              type="primary"
              status="danger"
              onClick={handleConfirmDelete}
              className={styles.confirmDeleteBtn}
            >
              删除
            </Button>
          </Space>
        </div>
      </Modal>
    </div>
  );
}

export default AiAction;
