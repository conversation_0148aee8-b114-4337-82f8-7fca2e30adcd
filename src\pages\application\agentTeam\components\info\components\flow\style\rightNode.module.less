.RightNodeContainer {
  padding: 24px;
  max-width: 400px;
  min-width: 400px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 100%);
  // border: 1px solid rgba(0, 0, 0, 0.2);

  .RightNodeTopContent {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .RightNodeContent {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .RightNodeLogo {
        width: 32px;
        height: 32px;
      }

      .RightNodeTitleBox {
        margin-left: 8px;

        .RightNodeTitleText {
          font-family: 'PingFang SC';
          font-weight: 500;
          font-size: 16px;
          line-height: 24px;
          letter-spacing: 0%;
        }
      }
    }

    .more {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      &.moreActive {
        background: #00000005;
      }
    }
  }

  .RightNodeDescBox {
    margin-top: 12px;
    text-align: left;

    .RightNodeDescText {
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      letter-spacing: 0%;
      color: rgba(0, 0, 0, 32%);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .RightNodeBottomBox {
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;

    .RightNodeBottomBtn {
      padding: 4px 8px;
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      letter-spacing: 0%;
      color: rgba(0, 0, 0, 32%);
      border-radius: 4px;
      border: 1px solid rgba(0, 0, 0, 8%);
      background: rgba(255, 255, 255, 100%);

      &:nth-child(2) {
        margin-left: 8px;
      }
    }
  }
}
