/* 隐藏滚动条 */
::-webkit-scrollbar {
    display: none;
    /* 对于基于 WebKit 的浏览器 */
}

/* 对于IE和Edge */
html {
    -ms-overflow-style: none;
    /* IE 和 Edge */
}

/* 对于Firefox */
* {
    scrollbar-width: none;
    /* Firefox */
}

.container {

    :global(.arco-card-body) {
        padding: 24px;
    }

    .basicInfo {
        border: 1px solid #ebebeb;
        border-radius: 4px;
        margin-bottom: 16px;

        .sdkBasic {
            display: flex;
            align-items: center;

            .sdkIcon {
                width: 48px;
                height: 48px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 16px;
                font-size: 24px;
            }

            .sdkMeta {
                display: flex;
                flex-direction: column;

                .sdkName {
                    font-weight: 600;
                    font-size: 20px;
                    color: #333333;
                    margin-bottom: 4px;
                }

                .sdkVersion {
                    font-weight: 400;
                    font-size: 14px;
                    color: #a6a6a6;
                }
            }
        }
    }

    .sdkDownloadBtn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 24px;
        background-color: #4455f2;
        color: #ffffff;
        border-radius: 8px;
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
    }

    .sdkTitle {
        font-weight: 600;
        font-size: 20px;
        color: #333333;
    }

    .divider {
        margin: 16px 0;
    }
}

.detailContainer {
    display: flex;
    flex-direction: column;

    .metaInfo {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 24px;

        .meta {
            :global(.arco-typography) {
                font-weight: 400;
                font-size: 12px;
                line-height: 20px;
                color: #adadad;
            }
        }

        .title {
            font-weight: 600;
            font-size: 20px;
            line-height: 32px;
            color: #333333;
            margin-bottom: 0px;
        }
    }

    .contentArea {

        :global(.arco-card-body) {
            padding: 0;
        }

        .detailContent {
            height: 100%;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            scroll-behavior: smooth;

            .sectionTitle {
                color: #333333;
                margin-bottom: 8px;
                font-weight: 600;
                font-size: 14px;
                line-height: 24px;
            }

            .sectionContent {
                color: #5c5c5c;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
            }

            .contentSection {
                margin-bottom: 24px;
                font-weight: 400;
                font-size: 14px;
                color: #595959;

                &:last-child {
                    margin-bottom: 0;
                }
            }

            .subSection {
                margin-left: 24px;
                margin-top: 16px;
            }
        }
    }

    .tocArea {
        .tocSticky {
            position: sticky;
            top: 20px;

            :global(.arco-anchor-line-slider) {
                position: absolute;
                height: 24px;
                width: 2px;
                margin-top: 4.0005px;
                background-color: #333333;
            }

            :global(.arco-anchor-link-active > .arco-anchor-link-title) {
                font-weight: 600;
                font-size: 14px;
                color: #333333;
            }

            :global(.arco-anchor-link-title) {
                color: #a6a6a6;
                line-height: 1.5715;
                font-weight: 400;
                font-size: 14px;
                margin-bottom: 8px;
            }

            .tocTitle {
                font-weight: 600;
                font-size: 14px;
                color: #333333;
            }

            .anchor {
                overflow-y: auto;
                margin-left: 12px;

                .subLink {
                    padding-left: 16px;
                }
            }
        }
    }
}



// Add style for sub-links
:global(.arco-anchor-link-title.subLink) {
    padding-left: 24px;
    font-size: 13px;
    color: rgb(var(--gray-7));
}

// Responsive adjustments
@media (max-width: 768px) {
    .container {
        padding: 12px;
    }

    .basicInfo {
        .sdkBasic {
            flex-direction: column;
            align-items: flex-start;

            .sdkIcon {
                margin-bottom: 12px;
            }
        }
    }

    .tocArea {
        margin-top: 24px;
        order: -1;

        .tocSticky {
            position: relative;
            top: 0;
        }
    }
}