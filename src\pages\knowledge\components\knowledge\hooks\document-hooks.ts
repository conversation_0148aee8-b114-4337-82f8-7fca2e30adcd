import {
  useHandleSearchChange,
  useGetPaginationWithRouter,
} from '../hooks/logic-hooks';
import { IDocumentInfo } from '../interfaces/document';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import kbService, { listDocument } from '../services/knowledge-service';
import { useCallback, useEffect, useRef, useState } from 'react';
import { UploadFile, message } from 'antd';
import { get } from 'lodash';
import {
  useSetPaginationParams,
  useGetKnowledgeSearchParams,
} from './route-hook';
import {
  IChangeParserConfigRequestBody,
  IDocumentMetaRequestBody,
} from '../interfaces/request/document';
import { useSearchParams } from 'react-router-dom';
export const useFetchNextDocumentList = () => {
  const { searchString, handleInputChange } = useHandleSearchChange();
  const { pagination, setPagination } = useGetPaginationWithRouter();
  const { knowledgeId: id } = useGetKnowledgeSearchParams();
  const { data, isFetching: loading } = useQuery<{
    docs: IDocumentInfo[];
    total: number;
  }>({
    queryKey: ['fetchDocumentList', id, searchString, pagination],
    refetchInterval: 15000,
    enabled: !!id,
    queryFn: async () => {
      const ret = await listDocument({
        kb_id: id,
        keywords: searchString,
        page_size: pagination.pageSize,
        page: pagination.current,
      });
      if (ret.data.code === 0) {
        return ret.data.data;
      }
      return {
        docs: [],
        total: 0,
      };
    },
  });

  const onInputChange: React.ChangeEventHandler<HTMLInputElement> = useCallback(
    (e) => {
      setPagination({ page: 1 });
      handleInputChange(e);
    },
    [handleInputChange, setPagination]
  );

  return {
    loading,
    searchString,
    documents: data?.docs || [],
    pagination: { ...pagination, total: data?.total },
    handleInputChange: onInputChange,
    setPagination,
  };
};

export const useNextWebCrawl = () => {
  const { knowledgeId } = useGetKnowledgeSearchParams();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['webCrawl'],
    mutationFn: async ({ name, url }: { name: string; url: string }) => {
      const formData = new FormData();
      formData.append('name', name);
      formData.append('url', url);
      formData.append('kb_id', knowledgeId);

      const ret = await kbService.web_crawl(formData);
      const code = get(ret, 'data.code');
      if (code === 0) {
        message.success('上传成功');
      }

      return code;
    },
  });

  return {
    data,
    loading,
    webCrawl: mutateAsync,
  };
};

export const useCreateNextDocument = () => {
  const { knowledgeId } = useGetKnowledgeSearchParams();
  const { setPaginationParams, page } = useSetPaginationParams();
  const queryClient = useQueryClient();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['createDocument'],
    mutationFn: async (name: string) => {
      const { data } = await kbService.document_create({
        name,
        kb_id: knowledgeId,
      });
      if (data.code === 0) {
        if (page === 1) {
          queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] });
        } else {
          setPaginationParams(); // fetch document list
        }

        message.success('创建于');
      }
      return data.code;
    },
  });

  return { createDocument: mutateAsync, loading, data };
};

export const useUploadNextDocument = () => {
  const queryClient = useQueryClient();
  const { knowledgeId } = useGetKnowledgeSearchParams();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['uploadDocument'],
    mutationFn: async (fileList: UploadFile[]) => {
      const formData = new FormData();
      formData.append('kb_id', knowledgeId);
      fileList.forEach((file: any) => {
        formData.append('file', file);
      });

      try {
        const ret = await kbService.document_upload(formData);
        const code = get(ret, 'data.code');

        if (code === 0 || code === 500) {
          queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] });
        }
        return ret?.data;
      } catch (error) {
        console.warn(error);
        return {
          code: 500,
          message: error + '',
        };
      }
    },
  });

  return { uploadDocument: mutateAsync, loading, data };
};

export const useRunNextDocument = () => {
  const queryClient = useQueryClient();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['runDocumentByIds'],
    mutationFn: async ({
      documentIds,
      run,
      shouldDelete,
      parseConfig,
      embed = false,
    }: {
      documentIds: string[];
      run: number;
      shouldDelete: boolean;
      parseConfig: IChangeParserConfigRequestBody;
      embed: boolean;
    }) => {
      queryClient.invalidateQueries({
        queryKey: ['fetchDocumentList'],
      });

      const ret = await kbService.document_run({
        doc_ids: documentIds,
        run,
        delete: shouldDelete,
        parse_config: parseConfig,
        embed, // 是否向量化（默认false）
      });
      const code = get(ret, 'data.code');
      if (code === 0) {
        queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] });
        message.success('操作成功');
      }

      return code;
    },
  });

  return { runDocumentByIds: mutateAsync, loading, data };
};

export const useSetNextDocumentStatus = () => {
  const queryClient = useQueryClient();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['updateDocumentStatus'],
    mutationFn: async ({
      status,
      documentId,
    }: {
      status: boolean;
      documentId: string;
    }) => {
      const { data } = await kbService.document_change_status({
        doc_id: documentId,
        status: Number(status),
      });
      if (data.code === 0) {
        message.success('更新成功');
        queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] });
      }
      return data;
    },
  });

  return { setDocumentStatus: mutateAsync, data, loading };
};

export const useRemoveNextDocument = () => {
  const queryClient = useQueryClient();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['removeDocument'],
    mutationFn: async (documentIds: string | string[]) => {
      const { data } = await kbService.document_rm({ doc_id: documentIds });
      if (data.code === 0) {
        message.success('删除成功');
        queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] });
      }
      return data.code;
    },
  });

  return { data, loading, removeDocument: mutateAsync };
};

export const useSaveNextDocumentName = () => {
  const queryClient = useQueryClient();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['saveDocumentName'],
    mutationFn: async ({
      name,
      documentId,
    }: {
      name: string;
      documentId: string;
    }) => {
      const { data } = await kbService.document_rename({
        doc_id: documentId,
        name: name,
      });
      if (data.code === 0) {
        message.success('重命名成功');
        queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] });
      }
      return data.code;
    },
  });

  return { loading, saveName: mutateAsync, data };
};

export const useSetNextDocumentParser = () => {
  const queryClient = useQueryClient();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['setDocumentParser'],
    mutationFn: async ({
      parserId,
      documentId,
      parserConfig,
    }: {
      parserId: string;
      documentId: string;
      parserConfig: IChangeParserConfigRequestBody;
    }) => {
      const { data } = await kbService.document_change_parser({
        parser_id: parserId,
        doc_id: documentId,
        parser_config: parserConfig,
      });
      if (data.code === 0) {
        queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] });

        // message.success('更新成功');
      }
      return data.code;
    },
  });

  return { setDocumentParser: mutateAsync, data, loading };
};

export const useSetDocumentMeta = () => {
  const queryClient = useQueryClient();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['setDocumentMeta'],
    mutationFn: async (params: IDocumentMetaRequestBody) => {
      try {
        const { data } = await kbService.setMeta({
          meta: params.meta,
          doc_id: params.documentId,
        });

        if (data?.code === 0) {
          queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] });

          message.success('更新成功');
        }
        return data?.code;
      } catch (error) {
        message.error('error');
      }
    },
  });

  return { setDocumentMeta: mutateAsync, data, loading };
};

// 获取向量化进度
export const useEmbedProgressDocument = () => {
  const { knowledgeId: id, documentId } = useGetKnowledgeSearchParams();
  const [currentQueryParameters] = useSearchParams();
  const page = currentQueryParameters.get('doc_page')!;
  const pageSize = currentQueryParameters.get('doc_page_size')!;
  const [progress, setProgress] = useState<number | null>(null);
  let timer = useRef<ReturnType<typeof setInterval>>(undefined);

  useEffect(() => {
    if (progress !== null && progress >= 1.0) {
      resetEmbedProgressDocument();
    }
  }, [progress]);

  useEffect(() => {
    return () => {
      resetEmbedProgressDocument();
    };
  }, []);

  const resetEmbedProgressDocument = () => {
    setProgress(null);
    clearInterval(timer.current);
  };

  const embedProgressDocument = () => {
    if (timer.current) {
      clearInterval(timer.current);
      timer.current = undefined;
    }
    timer.current = setInterval(async () => {
      try {
        const ret = await listDocument({
          kb_id: id,
          page_size: Number(pageSize),
          page: Number(page),
        });
        if (ret.data.code === 0) {
          const { docs } = ret.data.data;
          const doc = docs.find(({ id }: { id: string }) => documentId === id);
          if (doc) {
            const progress = doc.progress as number;
            setProgress(progress);
          }
        } else {
          clearInterval(timer.current);
        }
      } catch (err) {
        console.log(err);
        clearInterval(timer.current);
      }
    }, 1500);
  };
  return {
    progress,
    embedProgressDocument,
    resetEmbedProgressDocument,
  };
};

// 取消向量化
export const useCancelEmbedDocument = () => {
  const { documentId } = useGetKnowledgeSearchParams();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['cancelEmbedDocument'],
    mutationFn: async () => {
      const ret = await kbService.document_run({
        doc_ids: [documentId],
        run: 2, // 运行标签(1:运行2取消)
      });
      const code = get(ret, 'data.code');
      if (code === 0) {
        message.success('操作成功');
      }
      return code;
    },
  });

  return { cancelEmbedDocument: mutateAsync, loading, data };
};

// 执行文档向量化
export const useEmbedDocument = () => {
  const { documentId } = useGetKnowledgeSearchParams();
  // const parser_id = searchParams.get('parser_id');

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['embedDocumentByIds'],
    mutationFn: async () => {
      const ret = await kbService.document_embed({
        doc_ids: [documentId],
      });
      const code = get(ret, 'data.code');
      if (code === 0) {
        message.success('操作成功');
      }

      return code;
    },
  });

  return { embedDocumentByIds: mutateAsync, loading };
};
