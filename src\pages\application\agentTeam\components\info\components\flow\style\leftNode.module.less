.leftNodeContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 400px;
  padding: 24px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 100%);
  // border: 1px solid rgba(0, 0, 0, 0.2);

  .leftNodeContent {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    .leftNodeLogo {
      width: 32px;
      height: 32px;
    }

    .leftNodeTitleBox {
      margin-left: 8px;

      .leftNodeTitleText {
        font-family: "PingFang SC";
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        letter-spacing: 0%;
      }
    }
  }

  .LeftNodeDescBox {
    margin-top: 12px;
    text-align: left;

    .LeftNodeDescText {
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      letter-spacing: 0%;
      color: rgba(0, 0, 0, 32%);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .LeftNodeBottomBox {
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
    
    .LeftNodeBottomBtn {
      padding: 4px 8px;
      font-family: 'PingFang SC';
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      letter-spacing: 0%;
      color: rgba(0, 0, 0, 32%);
      border-radius: 4px;
      border: 1px solid rgba(0, 0, 0, 8%);
      background: rgba(255, 255, 255, 100%);
    }
  }

  .more {
    width: 32px;
    height: 32px;
  }
}
