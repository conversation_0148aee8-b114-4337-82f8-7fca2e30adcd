.templateModal {
    position: relative;
    padding: 16px;
    border-radius: 16px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

    @media (min-width: 768px) {
        padding: 24px;
    }

    .templateContainer {
        max-height: 80vh;
        overflow-y: auto;
        padding: 8px 4px;

        @media (min-width: 768px) {
            max-height: 600px;
            padding: 16px 8px;
        }

        .cardWrapper {
            display: flex;
            flex-direction: column;
            position: relative;

            &:hover {
                .useButton {
                    opacity: 1;
                }
            }

            .templateCard {
                margin-bottom: 12px;
                border-radius: 8px;
                overflow: hidden;
                transition: all 0.3s;
                border: 1px solid #ebebeb;

                &:hover {
                    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
                }

                .templatePreview {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 100%;
                    height: 100%;
                    padding: 16px;

                    // 空白卡片样式
                    .emptyCardTemplatePreview {
                        display: flex;
                        justify-content: center;
                        align-items: center;

                        .emptyCardIcon {
                            font-size: 36px;
                            color: #c9cdd4;
                            font-weight: 300;
                        }
                    }

                    // 问答卡片样式
                    .qaCardTemplatePreview {
                        width: 90%;
                        height: auto;
                        padding: 16px;
                        background-color: #ffffff;
                        border-radius: 8px;
                        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
                        border: 1px solid #f5f5f5;
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;

                        @media (min-width: 768px) {
                            width: 80%;
                        }
                    }

                    .docCardTemplatePreview {
                        width: 90%;
                        height: auto;
                        padding: 16px;
                        background-color: #ffffff;
                        border-radius: 8px;
                        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;

                        @media (min-width: 768px) {
                            width: 80%;
                            padding: 20px;
                        }
                    }

                    .mediaCardTemplatePreview {
                        width: 90%;
                        height: auto;
                        padding: 16px;
                        background-color: #ffffff;
                        border-radius: 8px;
                        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;

                        @media (min-width: 768px) {
                            width: 80%;
                            padding: 20px;
                        }
                    }

                    .tagList {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 4px;
                        margin-top: 8px;

                        @media (min-width: 768px) {
                            margin-top: 12px;
                        }

                        :global(.arco-tag) {
                            padding: 2px 6px;
                            background-color: #f2f3f5;
                            border-radius: 4px;
                            border: none;
                            font-weight: 400;
                            font-size: 10px;
                            line-height: 16px;
                            color: #4e5969;

                            @media (min-width: 768px) {
                                padding: 4px 8px;
                                font-size: 12px;
                                line-height: 18px;
                            }
                        }
                    }

                    .previewTitle {
                        font-weight: 600;
                        font-size: 12px;
                        line-height: 20px;
                        color: #333333;

                        @media (min-width: 768px) {
                            font-size: 14px;
                            line-height: 24px;
                        }
                    }

                    .previewParagraph {
                        font-weight: 400;
                        font-size: 10px;
                        line-height: 18px;
                        color: #5c5c5c;
                        margin-bottom: 16px;

                        @media (min-width: 768px) {
                            font-size: 12px;
                            line-height: 20px;
                            margin-bottom: 28px;
                        }
                    }

                    .imagePlaceholder {
                        width: 100%;
                        height: 100px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        border-radius: 8px;
                        background-color: #fcfcfc;
                        border: 1px solid #ebebeb;
                        margin-bottom: 6px;

                        @media (min-width: 768px) {
                            height: 144px;
                            margin-bottom: 8px;
                        }
                    }

                    .placeholderIcon {
                        font-size: 24px;
                        color: #c9cdd4;
                    }

                    .fileItem {
                        background-color: #fef9f0;
                        padding: 2px 6px;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        align-self: flex-start;
                        margin-bottom: 8px;

                        :global(.arco-typography) {
                            font-weight: 400;
                            font-size: 10px;
                            line-height: 16px;
                            color: #df9f3f;
                        }
                    }
                }

                :global(.arco-card-body) {
                    padding: 0;
                    height: 300px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-color: #fcfcfc;
                    position: relative;

                    @media (min-width: 768px) {
                        height: 350px;
                    }
                }
            }

            // 空白卡片特殊边框样式
            .emptyTemplateCard {
                border: 1px dashed #c9cdd4;
                
                &:hover {
                    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
                }
            }

            .cardInfo {
                display: flex;
                padding: 16px;
                background-color: #ffffff;
                position: absolute;
                width: 100%;
                bottom: 0;
                box-sizing: border-box;
                border: 1px solid #ebebeb;
                border-bottom-right-radius: 8px;
                border-bottom-left-radius: 8px;
                justify-content: space-between;
                align-items: center;

                @media (min-width: 768px) {
                    padding: 24px;
                }

                .cardTitle {
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 24px;
                    color: #333333;
                    margin-bottom: 4px;
                }

                .cardDescription {
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 20px;
                    color: #adadad;
                }
            }

            // 空白卡片信息区域特殊边框样式
            .emptyCardInfo {
                border: 1px dashed #c9cdd4;
            }
        }

        .templateHover {
            .useButton {
                padding: 8px 16px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #4455f2;
                border-radius: 8px;
                font-weight: 500;
                font-size: 14px;
                line-height: 24px;
                color: #ffffff;
                opacity: 0;
                transition: opacity 0.3s;
                height: 40px;

                @media (min-width: 768px) {
                    padding: 8px 24px;
                }

                &:hover {
                    background-color: #4152e9 !important;
                }
            }
        }
    }

    :global(.arco-modal-header) {
        padding: 0;
        height: auto;
        border-bottom: none;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 28px;
            color: #333333;
            text-align: left;
            padding-bottom: 12px;
            border-bottom: 1px solid #ebebeb;

            @media (min-width: 768px) {
                font-size: 20px;
                line-height: 32px;
                padding-bottom: 16px;
            }
        }
    }

    :global(.arco-modal-content) {
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 16px;
        top: 16px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);

        @media (min-width: 768px) {
            right: 32px;
            top: 32px;
        }
    }
}