/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
  /* 对于基于 WebKit 的浏览器 */
}

/* 对于IE和Edge */
html {
  -ms-overflow-style: none;
  /* IE 和 Edge */
}

/* 对于Firefox */
* {
  scrollbar-width: none;
  /* Firefox */
}

.container {
  display: flex;
  height: calc(100vh - 115px);
  flex-direction: column;
  flex: 1;
  overflow: auto;
  position: relative;

  .tabs {
    :global(.arco-tabs-header-nav::before) {
      display: none;
    }

    :global(.arco-tabs-header) {
      width: 100%;
    }

    :global(.arco-tabs-header-ink) {
      display: none;
    }

    :global(.arco-tabs-header-title) {
      padding: 0;
      font-weight: 600;
      font-size: 20px;
      color: #a6a6a6;
      margin-left: 7px !important;
      width: 100%;
      padding-bottom: 16px;
      border-bottom: 1px solid RGBA(0, 0, 0, 0.08);

      &:hover {
        color: #a6a6a6;
      }
    }

    :global(.arco-tabs-header-title-active) {
      color: #333333;

      &:hover {
        color: #333333;
      }
    }

    :global(.arco-tabs-content) {
      padding-top: 24px;
    }
  }

  :global(.arco-popover-content) {
    padding: 0 12px 8px 12px;

    :global(.arco-popover-inner-content) {
      p {
        cursor: pointer;
        padding-left: 8px;
        display: flex;
        align-items: center;
        height: 34px !important;
        width: 104px !important;
        text-align: left;
        color: RGBA(0, 0, 0, 0.65);
        border-radius: 4px;

        &:hover {
          background-color: RGBA(0, 0, 0, 0.02);
        }
      }

      .red {
        color: RGBA(213, 73, 65, 0.95) !important;
      }
    }
  }

  .customContainer {
    width: 100%;
    padding: 8px 0 0 8px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    height: calc(100vh - 180px);
    overflow: hidden;
    position: relative;

    .leftContainer {
      width: calc(51% - 24px);
      height: 95%;
      border-right: 1px solid RGBA(0, 0, 0, 0.08);
      padding-right: 24px;
      position: relative;
      overflow-y: auto;
      margin-top: 8px;

      .titleRow {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;

        .titleContent {
          display: flex;
          flex-direction: column;
          gap: 6px;

          .subtitle {
            font-size: 14px;
            font-weight: 600;
            color: RGBA(0, 0, 0, 0.65);
          }

          .subtitlePlaceholder {
            font-size: 12px;
            font-weight: 400;
            color: RGBA(0, 0, 0, 0.35);
          }
        }

        .addApplication {
          width: 80px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid RGBA(0, 0, 0, 0.08);
          border-radius: 4px;
          cursor: pointer;
          background-color: transparent;
          padding: 0 2px;

          :global(.arco-icon) {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            flex-shrink: 0;
          }

          .operateText {
            font-size: 14px;
            font-weight: 400;
            color: RGBA(0, 0, 0, 0.65) !important;
            line-height: 20px;
            display: flex;
            align-items: center;
            height: 100%;
          }

          &:hover {
            background-color: RGBA(0, 0, 0, 0.02);
          }
        }
      }

      .chooseRowBox {
        cursor: pointer;
        height: 56px;
        width: calc(100% - 24px);
        border-radius: 4px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px;

        .chooseRowCol {
          display: flex;
          justify-content: flex-start;
          align-items: center;

          &.left {
            .chooseRowColImg {
              img {
                width: 32px;
                height: 32px;
              }
            }

            .chooseName {
              font-size: 14px;
              font-weight: 600;
              color: RGBA(0, 0, 0, 0.65);
              margin-left: 8px;
            }

            .chooseCount {
              font-size: 14px;
              font-weight: 400;
              color: RGBA(0, 0, 0, 0.35);
              margin-left: 24px;

              &.count {
                color: RGBA(68, 85, 242, 0.95);
              }
            }
          }

          &.right {
            .operateText {
              font-size: 12px;
              font-weight: 500;
              color: RGBA(0, 0, 0, 0.65) !important;
            }
          }
        }
      }

      .selectRowBox {
        height: 40px;
        width: 100%;
        border-radius: 4px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        display: flex;
        justify-content: space-between;
        align-items: center;

        :global(.arco-select-view) {
          background: transparent;
          height: 40px;
          width: 100%;
          padding-right: 6px;

          :global(.arco-select-suffix) {
            width: 20px;
          }
        }
      }

      .selectedItemContainer {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        width: 100%;
        border-radius: 8px;
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
        margin-top: 8px;
        background-color: #fff;

        .selectedItemList {
          box-sizing: border-box;
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 8px;
          padding: 8px;
          border: 1px solid RGBA(0, 0, 0, 0.08);
          border-radius: 4px;
          min-height: 48px;
          background-color: rgba(0, 0, 0, 0.01);
          transition: all 0.3s ease;
          border-radius: 8px;

          &:hover {
            border-color: RGBA(0, 0, 0, 0.15);
          }

          .selectedItemRow {
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: 8px;
            border: 1px solid RGBA(0, 0, 0, 0.08);
            background-color: #FFFFFF;
            flex: 1;
            margin: 0;
            width: calc(100% - 48px);

            .selectedItemCol {
              display: flex;
              align-items: center;
              gap: 8px;
              width: 100%;

              .agentIcon {
                width: 24px;
                height: 24px;
                border-radius: 4px;
                object-fit: cover;
              }

              .selectedItemText {
                width: 150px;
                font-size: 14px;
                color: RGBA(0, 0, 0, 0.65);
                line-height: 20px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .selectedItemName {
                font-size: 14px;
                color: RGBA(0, 0, 0, 0.65);
                line-height: 24px;
                font-weight: 500;
              }

              .selectedItemTextContent {
                flex: 1;
                font-size: 14px;
                color: RGBA(0, 0, 0, 0.32);
                line-height: 20px;
                font-weight: 400;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .deleteIcon {
                width: 24px;
                height: 24px;
                cursor: pointer;
                opacity: 0.65;
                transition: opacity 0.2s ease;

                &:hover {
                  opacity: 1;
                }
              }
            }
          }
        }
      }

      .channelSelect {
        width: 30%;

        :global(.arco-select-view) {
          background-color: RGBA(0, 0, 0, 0.01);
          border-radius: 8px;
          border: 1px solid RGBA(0, 0, 0, 0.08);
        }
      }

      .apiEndpoint {
        width: 70%;
        height: 40px;
        resize: none;
        padding: 8px;
        background-color: white;
        border-radius: 8px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        font-size: 14px;

        &::placeholder {
          color: RGBA(0, 0, 0, 0.16);
        }
      }

      .teamName {
        width: 70%;
        height: 40px;
        resize: none;
        padding: 8px;
        background-color: white;
        border-radius: 8px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        font-size: 14px;

        &::placeholder {
          color: RGBA(0, 0, 0, 0.16);
        }
      }

      .teamIdContainer {
        position: relative;
        width: 100%;

        .teamId {
          width: 100%;
          height: 40px;
          resize: none;
          padding: 8px;
          background-color: white;
          border-radius: 8px;
          border: 1px solid RGBA(0, 0, 0, 0.08);
          font-size: 14px;

          &::placeholder {
            color: RGBA(0, 0, 0, 0.16);
          }

          &.error {
            border-color: #F53F3F;
          }

          &:disabled {
            color: RGBA(0, 0, 0, 0.35);
            cursor: not-allowed;
            
            &::placeholder {
              color: RGBA(0, 0, 0, 0.25);
            }
          }
        }
      }

      .teamIdWithButton {
        padding-right: 60px;
      }

      .teamIdButtons {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;

        .teamIdGenerateButton {
          color: RGBA(0, 0, 0, 0.64);
          padding: 0 4px;
          height: 24px;
          min-width: 24px;

          &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &:hover {
            background-color: white;
          }
        }
      }

      .teamSecretContainer {
        position: relative;
        width: 100%;

        .teamSecret {
          width: 100%;
          height: 40px;
          resize: none;
          padding: 8px;
          background-color: white;
          border-radius: 8px;
          border: 1px solid RGBA(0, 0, 0, 0.08);
          font-size: 14px;

          &::placeholder {
            color: RGBA(0, 0, 0, 0.16);
          }

          &.error {
            border-color: #F53F3F;
          }
        }
      }

      .teamSecretWithPassword {
        padding-right: 80px;
        font-family: inherit;

        &.hidden {
          font-family: password;
          -webkit-text-security: disc;
        }
      }

      .teamSecretButtons {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        gap: 8px;

        .teamSecretButton {
          color: #86909C;
          padding: 0;
          height: 24px;
          min-width: 24px;

          &:hover {
            background-color: white;
          }
        }

        .teamSecretDivider {
          width: 1px;
          height: 16px;
          background-color: #d9d9d9;
          margin: 0;
        }

        .teamSecretGenerateButton {
          color: RGBA(0, 0, 0, 0.64);
          padding: 0 4px;
          height: 24px;
          min-width: 24px;

          &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          &:hover {
            background-color: white;
          }
        }
      }

      .publishButton {
        width: 98px;
        height: 40px;
        background: #4455F2;
        border-radius: 8px;
        border: none;
        font-size: 14px;
        font-weight: 500;
        color: #FFFFFF;

        &:hover {
          background: #3344E1;
        }

        &:disabled {
          background: RGBA(0, 0, 0, 0.15);
          color: RGBA(0, 0, 0, 0.35);
          cursor: not-allowed;
        }
      }

      .publishButtonPublished {
        width: 98px;
        height: 40px;
        background: RGBA(0, 0, 0, 0.08);
        border-radius: 8px;
        border: none;
        font-size: 14px;
        font-weight: 500;
        color: RGBA(0, 0, 0, 0.35);
        cursor: not-allowed;

        &:hover {
          background: RGBA(0, 0, 0, 0.08);
          color: RGBA(0, 0, 0, 0.35);
        }

        &:disabled {
          background: RGBA(0, 0, 0, 0.08);
          color: RGBA(0, 0, 0, 0.35);
          cursor: not-allowed;
        }
      }
    }

    .rightContainer {
      width: calc(49% - 34px);
      height: 100%;
      padding-left: 24px;
      overflow-y: auto;
      margin-top: 8px;

      :global(.arco-col-19) {
        width: 100%;
        max-width: 100%;
        flex: 0 0 100%;
      }

      :global(.arco-form) {
        width: 100%;
      }

      :global(.arco-form-item) {
        width: 100%;
      }

      .title {
        margin-top: 8px;
        padding: 0 16px;
        width: calc(100% - 32px);
        height: 40px;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        background-color: RGBA(235, 235, 235, 1);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .titleContent {
        display: flex;
        flex-direction: column;

        .subtitle {
          font-size: 14px;
          font-weight: 600;
          color: RGBA(0, 0, 0, 0.65);
        }

        .subtitlePlaceholder {
          font-size: 12px;
          font-weight: 400;
          color: RGBA(0, 0, 0, 0.35);
        }
      }

      .textarea {
        width: 100%;
        height: 350px;
        resize: none;
        padding: 10px;
        background-color: white;
        border-radius: 8px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        font-size: 14px;
      }

      .textarea2 {
        width: 30%;
        height: 20px;
        resize: none;
        padding: 10px;
        background-color: RGBA(0, 0, 0, 0.01);
        border-radius: 8px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        font-size: 14px;
      }

    }
  }
}

.toggleButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  margin: 0px -8px -8px -8px;
  background: #ffffff;
  border-top: 1px solid RGBA(0, 0, 0, 0.08);
  border-radius: 0 0 6px 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 8px;
  box-sizing: border-box;
  height: 40px;

  &:hover {
    border-color: RGBA(0, 0, 0, 0.15);
    background: #fafbfc;
  }

  .toggleText {
    font-size: 12px;
    color: #86909c;
    font-weight: 400;
  }

  .toggleArrow {
    display: flex;
    align-items: center;
    transition: transform 0.2s ease;
  }

  &:hover {
    .toggleArrow {
      transform: translateY(1px);
    }
  }
}