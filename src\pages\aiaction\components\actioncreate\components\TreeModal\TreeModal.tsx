import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
  Modal,
  Tree,
  Checkbox,
  Image,
  Input,
  Button,
  Spin,
  Tabs,
} from '@arco-design/web-react';
import styles from './style/index.module.less';
import { IconDown, IconSearch } from '@arco-design/web-react/icon';
import group from '@/assets/application/folderIcon.png';
import agent from '@/assets/application/agentIcon1.png';
import timeSequenceCard from '@/assets/application/timeSequenceCardIcon1.png';
import workflow from '@/assets/application/workflowIcon.png';
import IconKnowledge from '@/assets/knowledge/IconKnowledge.png';
import fileIcon from '@/assets/application/fileIcon.png';
import useLocale from '@/utils/useLocale';
import Text from '@arco-design/web-react/es/Typography/text';
import ColComponent from '@arco-design/web-react/es/Grid/col';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import ButtonComponent from '@arco-design/web-react/es/Button';
import IconClose from '@/assets/application/IconCloseModel.svg';

const { TabPane } = Tabs;

const TreeModal = ({
  type,
  title,
  visible,
  onClose,
  treeData,
  checkedIds,
  onCheck,
  onConfirm,
  onSearch,
  loading,
  taskAgentData,
  taskWorkflowData,
  setTreeData,
  setCheckedIds,
  taskAgentSetting,
  taskWorkflowSetting,
}) => {
  const locale = useLocale();
  const [searchValue, setSearchValue] = useState('');
  const [activeTab, setActiveTab] = useState('agent'); // 默认显示智能体 Tab
  const searchTimerRef = useRef(null);

  useEffect(() => {
    if (!visible) {
      setSearchValue('');
      setActiveTab('agent'); // 重置 Tab
      if (searchTimerRef.current) {
        clearTimeout(searchTimerRef.current);
      }
    }
  }, [visible]);

  const handleSearchChange = (value) => {
    setSearchValue(value);

    if (searchTimerRef.current) {
      clearTimeout(searchTimerRef.current);
    }

    if (!visible) return;

    searchTimerRef.current = setTimeout(() => {
      if (onSearch) {
        onSearch(value);
      }
    }, 300);
  };

  const findNodeById = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  const selectedCount = useMemo(() => {
    const currentData =
      type === 'task'
        ? activeTab === 'agent'
          ? taskAgentSetting
          : taskWorkflowSetting
        : checkedIds;
    return currentData.length;
  }, [checkedIds, type, activeTab, taskAgentSetting, taskWorkflowSetting]);

  const getAllChildIds = (node) => {
    let ids = [];
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        ids.push(child.id);
        ids = ids.concat(getAllChildIds(child));
      });
    } else if (node.childrenData && node.childrenData.length > 0) {
      node.childrenData.forEach((child) => {
        ids.push(child.id);
        ids = ids.concat(getAllChildIds(child));
      });
    }
    return ids;
  };

  const updateParentStatus = (nodeId, newCheckedIds) => {
    const node = findNodeById(treeData, nodeId);
    if (!node?.parentId) return;

    const parent = findNodeById(treeData, node.parentId);
    if (!parent?.children) return;

    const allChecked = parent.children.every(
      (child) =>
        newCheckedIds.includes(child.id) &&
        getAllChildIds(child).every((cid) => newCheckedIds.includes(cid))
    );

    let updatedIds = [...newCheckedIds];
    if (allChecked && !updatedIds.includes(parent.id)) {
      updatedIds.push(parent.id);
    } else if (!allChecked && updatedIds.includes(parent.id)) {
      updatedIds = updatedIds.filter((id) => id !== parent.id);
    }

    if (JSON.stringify(updatedIds) !== JSON.stringify(newCheckedIds)) {
      updateParentStatus(parent.id, updatedIds);
      return updatedIds;
    }
    return newCheckedIds;
  };

  const handleCheck = (node, checked) => {
    let newCheckedIds = [...checkedIds];
    const childIds = getAllChildIds(node);

    if (checked) {
      newCheckedIds = Array.from(
        new Set([...newCheckedIds, node.id, ...childIds])
      );
    } else {
      newCheckedIds = newCheckedIds.filter(
        (id) => id !== node.id && !childIds.includes(id)
      );
    }

    newCheckedIds = updateParentStatus(node.id, newCheckedIds) || newCheckedIds;

    if (node.level === 3) {
      const parent = findNodeById(treeData, node.parentId);
      newCheckedIds =
        updateParentStatus(parent.id, newCheckedIds) || newCheckedIds;
    }
    onCheck(Array.from(new Set(newCheckedIds)));
  };

  const isIndeterminate = (node) => {
    if (!node.childrenData) return false;

    const ids = getAllChildIds(node);
    if (ids.every((element) => checkedIds.includes(element))) {
      return false;
    }

    return node.childrenData.some(
      (child) =>
        checkedIds.includes(child.id) ||
        (child.childrenData && isIndeterminate(child))
    );
  };

  const renderTitle = (node) => {
    const isLeaf = !node.childrenData;
    const checked = checkedIds.includes(node.id);
    const indeterminate = isIndeterminate(node);
    const currentType =
      type === 'task'
        ? activeTab === 'agent'
          ? 'task-agent'
          : 'task-workflow'
        : type;

    return (
      <div className={styles.customTreeRow}>
        <div>
          {currentType === 'agent' || currentType === 'task-agent' ? (
            <Image
              className={styles.customIcon}
              src={node.level === 1 ? group : agent}
            />
          ) : currentType === 'workflow' || currentType === 'task-workflow' ? (
            <Image
              className={styles.customIcon}
              src={node.level === 1 ? group : workflow}
            />
          ) : currentType === 'knowledge' ? (
            <Image
              className={styles.customIcon}
              src={
                node.level === 1
                  ? IconKnowledge
                  : node.level === 2
                  ? group
                  : fileIcon
              }
            />
          ) : (
            <Image className={styles.customIcon} src={timeSequenceCard} />
          )}
          {node.title}
        </div>
        <Checkbox
          checked={checked}
          indeterminate={!isLeaf && indeterminate}
          onChange={(checked) => handleCheck(node, checked)}
          className={styles.customCheckbox}
        />
      </div>
    );
  };

  const handleTabChange = (key) => {
    setActiveTab(key);
    setSearchValue(''); // 清空搜索
    if (key === 'agent') {
      setTreeData(taskAgentData);
      setCheckedIds(taskAgentSetting.map((item) => item.id));
    } else if (key === 'workflow') {
      setTreeData(taskWorkflowData);
      setCheckedIds(taskWorkflowSetting.map((item) => item.id));
    }
  };

  const footer = (
    <>
      <ColComponent className={styles.customFooterRow}>
        <RowComponent>
          <Text className={styles.selectedCount}>
            {locale['menu.application.info.setting.selected'].replace(
              '{count}',
              selectedCount
            )}
          </Text>
        </RowComponent>
        <RowComponent>
          <ButtonComponent onClick={onClose} className={styles.cancelBut}>
            <Text>
              {locale['menu.application.template.setting.operate.cancel']}
            </Text>
          </ButtonComponent>
          <ButtonComponent
            onClick={() => onConfirm(checkedIds)}
            className={styles.addBut}
          >
            <Text>{locale['menu.application.confirm']}</Text>
          </ButtonComponent>
        </RowComponent>
      </ColComponent>
    </>
  );

  return (
    <Modal
      title={title}
      visible={visible}
      onOk={onClose}
      onCancel={onClose}
      closeIcon={<IconClose />}
      className={styles.customModal}
      footer={footer}
    >
      {type === 'task' && (
        <Tabs
          activeTab={activeTab}
          onChange={handleTabChange}
          className={styles.tabs}
        >
          <TabPane
            key="agent"
            title={locale['menu.application.agent'] || '智能体'}
          />
          <TabPane
            key="workflow"
            title={locale['menu.application.workflow'] || '工作流'}
          />
        </Tabs>
      )}
      <Input
        className={styles.searchBox}
        prefix={<IconSearch />}
        placeholder={locale['menu.application.header.basic.search.placeholder']}
        value={searchValue}
        onChange={handleSearchChange}
        onClear={() => {
          setSearchValue('');
          onSearch && onSearch('');
        }}
        allowClear
      />
      <div style={{ minHeight: 200, position: 'relative' }}>
        <Spin loading={loading} style={{ display: 'block' }}>
          {!treeData || treeData.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '16px 0' }}>
              {searchValue ? '没有找到匹配的结果' : '暂无数据'}
            </div>
          ) : (
            <Tree
              icons={{ switcherIcon: <IconDown /> }}
              treeData={treeData}
              renderTitle={renderTitle}
              checkable={false}
            />
          )}
        </Spin>
      </div>
    </Modal>
  );
};

export default TreeModal;
