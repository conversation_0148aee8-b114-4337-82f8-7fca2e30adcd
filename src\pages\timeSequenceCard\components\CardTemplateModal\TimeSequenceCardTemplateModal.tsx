import React, { useEffect, useState } from 'react';
import { Modal, Card, Typography, Space, Button, Grid, Tag } from '@arco-design/web-react';
import IconPlay from '@/assets/application/IconPlay.svg';
import IconImagePlus from '@/assets/application/IconImagePlus.svg';
import IconVideoPlus from '@/assets/application/IconVideoPlus.svg';
import IconSoundPlus from '@/assets/application/IconSoundPlus.svg';
import IconSmilePlus from '@/assets/application/IconSmilePlus.svg';
import IconClose from '@/assets/application/IconClose.svg';
import IconSmile from '@/assets/application/IconSmile.svg';
import IconTitle from '@/assets/application/IconTitle.svg';
import IconFont from '@/assets/application/IconFont.svg';
import IconTag from '@/assets/application/IconTag.svg';
import IconFile from '@/assets/application/IconFile.svg';
import IconImage from '@/assets/application/IconImage.svg';
import IconVideo from '@/assets/application/IconVideo.svg';
import IconSound from '@/assets/application/IconSound.svg';
import IconLink from '@/assets/application/IconLink.svg';
import IconEmptyCard from '@/assets/application/IconEmptyCard.svg';
import IconSmileEmptyColor from '@/assets/application/IconSmileEmptyColor.svg';
import styles from './style/timeSequenceCardTemplateModal.module.less';

const { Title, Text } = Typography;
const { Row, Col } = Grid;

// 定义组件类型
const componentTypes = {
    ICON: 'icon',
    TITLE: 'title',
    TEXT: 'text',
    TAG: 'tag',
    FILE: 'file',
    IMAGE: 'image',
    VIDEO: 'video',
    AUDIO: 'audio',
    LINK: 'link'
};

// 生成统一格式ID的辅助函数
const generateComponentId = (type, existingItems = []) => {
    // 统计当前类型已有的数量
    const existingCount = existingItems.filter(item => item.type === type).length;
    const nextNumber = existingCount + 1;
    return `${type}_${nextNumber.toString().padStart(3, '0')}`;
};

// 定义模板及其结构项
const cardTemplates = [
    {
        id: 'empty-card',
        name: '空白卡片',
        description: '从空白卡片开始',
        useCustomPreview: true,
        items: [],
    },
    {
        id: 'qa-card',
        name: '问答卡片',
        description: '多格式总结',
        useCustomPreview: true,
        items: [
            {
                id: 'image_001',
                type: componentTypes.IMAGE,
                name: '图片',
                description: '图片',
                icon: <IconImage />,
            },
            {
                id: 'title_001',
                type: componentTypes.TITLE,
                name: '标题',
                description: '标题',
                icon: <IconTitle />,
            },
            {
                id: 'text_001',
                type: componentTypes.TEXT,
                name: '文字',
                description: '文字',
                icon: <IconFont />,
            },
            {
                id: 'tag_001',
                type: componentTypes.TAG,
                name: '标签',
                description: '标签',
                icon: <IconTag />,
            },
        ],
    },
    {
        id: 'doc-card',
        name: '文档卡片',
        description: '精准分析',
        useCustomPreview: true,
        items: [
            {
                id: 'file_001',
                type: componentTypes.FILE,
                name: '文档',
                description: '文档',
                icon: <IconFile />,
            },
            {
                id: 'title_001',
                type: componentTypes.TITLE,
                name: '标题',
                description: '标题',
                icon: <IconTitle />,
            },
            {
                id: 'text_001',
                type: componentTypes.TEXT,
                name: '文字',
                description: '文字',
                icon: <IconFont />,
            },
            {
                id: 'image_001',
                type: componentTypes.IMAGE,
                name: '图片',
                description: '图片',
                icon: <IconImage />,
            },
            {
                id: 'text_002',
                type: componentTypes.TEXT,
                name: '文字',
                description: '文字',
                icon: <IconFont />,
            },
            {
                id: 'tag_001',
                type: componentTypes.TAG,
                name: '标签',
                description: '标签',
                icon: <IconTag />,
            },
        ],
    },
    {
        id: 'media-card',
        name: '素材卡片',
        description: 'AI采集',
        useCustomPreview: true,
        items: [
            {
                id: 'icon_001',
                type: componentTypes.ICON,
                name: '图标',
                description: '图标',
                icon: <IconSmile />,
            },
            {
                id: 'title_001',
                type: componentTypes.TITLE,
                name: '标题',
                description: '标题',
                icon: <IconTitle />,
            },
            {
                id: 'text_001',
                type: componentTypes.TEXT,
                name: '文字',
                description: '文字',
                icon: <IconFont />,
            },
            {
                id: 'image_001',
                type: componentTypes.IMAGE,
                name: '图片',
                description: '图片',
                icon: <IconImage />,
            },
            {
                id: 'text_002',
                type: componentTypes.TEXT,
                name: '文字',
                description: '文字',
                icon: <IconFont />,
            },
            {
                id: 'video_001',
                type: componentTypes.VIDEO,
                name: '视频',
                description: '视频',
                icon: <IconVideo />,
            },
            {
                id: 'text_003',
                type: componentTypes.TEXT,
                name: '文字',
                description: '文字',
                icon: <IconFont />,
            },
            {
                id: 'audio_001',
                type: componentTypes.AUDIO,
                name: '音频',
                description: '音频',
                icon: <IconSound />,
            },
            {
                id: 'text_004',
                type: componentTypes.TEXT,
                name: '文字',
                description: '文字',
                icon: <IconFont />,
            },
        ],
    },
];

// 为每个项目生成唯一ID的函数 - 更新为使用统一格式
const prepareTemplateItems = (items) => {
    // 创建一个临时数组来追踪ID生成
    const processedItems = [];
    
    return items.map(item => {
        // 确保存在description字段，如果不存在则使用name或type作为默认值
        const description = item.description || item.name || `${item.type}组件`;
        
        // 使用统一的ID生成逻辑，基于已处理的items
        const newId = generateComponentId(item.type, processedItems);
        
        const processedItem = {
            ...item,
            id: newId,
            description: description
        };
        
        // 将处理后的item添加到临时数组，供下一个item的ID生成使用
        processedItems.push(processedItem);
        
        return processedItem;
    });
};

interface TimeSequenceCardTemplateModalProps {
    visible: boolean;
    onClose: () => void;
    onUseTemplate: (items: any[]) => void;
}

const TimeSequenceCardTemplateModal: React.FC<TimeSequenceCardTemplateModalProps> = ({
    visible,
    onClose,
    onUseTemplate
}) => {
    const [isMobile, setIsMobile] = useState(false);

    // 检测屏幕尺寸变化
    useEffect(() => {
        const checkIsMobile = () => {
            setIsMobile(window.innerWidth < 768);
        };

        checkIsMobile();
        window.addEventListener('resize', checkIsMobile);

        return () => {
            window.removeEventListener('resize', checkIsMobile);
        };
    }, []);

    const handleUseTemplate = (template) => {
        const preparedItems = prepareTemplateItems(template.items);
        onUseTemplate(preparedItems);
        onClose();
    };

    // 为每种模板类型渲染自定义预览的函数
    const renderTemplatePreview = (template) => {
        switch (template.id) {
            case 'empty-card':
                return (
                    <div className={styles.emptyCardTemplatePreview}>
                        <div className={styles.emptyCardIcon}><IconEmptyCard /></div>
                    </div>
                );
            case 'qa-card':
                return (
                    <div className={styles.qaCardTemplatePreview}>
                        <div className={styles.imagePlaceholder}>
                            <div className={styles.placeholderIcon}>
                                <IconImagePlus />
                            </div>
                        </div>
                        <Space direction='vertical' size={4}>
                            <div className={styles.previewTitle}>这是标题</div>
                            <div className={styles.previewParagraph}>这是一段文字</div>
                        </Space>
                        <div className={styles.tagList}>
                            <Tag>这是标签</Tag>
                            <Tag>这是标签</Tag>
                            <Tag>这是标签</Tag>
                        </div>
                    </div>
                );
            case 'doc-card':
                return (
                    <div className={styles.docCardTemplatePreview}>
                        <div className={styles.fileItem}>
                            <Text>文档.pdf</Text>
                        </div>
                        <Space direction='vertical' size={4}>
                            <div className={styles.previewTitle}>这是标题</div>
                            <div className={styles.previewParagraph}>这是一段文字</div>
                        </Space>
                        <div className={styles.imagePlaceholder}>
                            <div className={styles.placeholderIcon}>
                                <IconImagePlus />
                            </div>
                        </div>
                    </div>
                );
            case 'media-card':
                return (
                    <div className={styles.mediaCardTemplatePreview}>
                        <div className={styles.icon}>
                            <IconSmileEmptyColor />
                        </div>
                        <Space direction='vertical' size={4}>
                            <div className={styles.previewTitle}>这是标题</div>
                            <div className={styles.previewParagraph}>这是一段文字</div>
                        </Space>
                        <div className={styles.imagePlaceholder}>
                            <div className={styles.placeholderIcon}>
                                <IconImagePlus />
                            </div>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <Modal
            visible={visible}
            title="创建"
            onCancel={onClose}
            footer={null}
            className={styles.templateModal}
            style={{ width: isMobile ? '95%' : '90%', maxWidth: 1200 }}
            closeIcon={<IconClose />}
        >
            <div className={styles.templateContainer}>
                <Row gutter={[24, 24]}>
                    {cardTemplates.map((template) => (
                        <Col xs={24} sm={24} md={12} lg={12} xl={12} key={template.id}>
                            <div className={styles.cardWrapper}>
                                <Card className={`${styles.templateCard} ${template.id === 'empty-card' ? styles.emptyTemplateCard : ''}`}>
                                    <div className={styles.templatePreview}>
                                        {renderTemplatePreview(template)}
                                    </div>
                                </Card>
                                <div className={`${styles.cardInfo} ${template.id === 'empty-card' ? styles.emptyCardInfo : ''}`}>
                                    <Space direction='vertical' size={0}>
                                        <Text className={styles.cardTitle}>{template.name}</Text>
                                        <Text className={styles.cardDescription}>{template.description}</Text>
                                    </Space>

                                    <div className={styles.templateHover}>
                                        <Button
                                            type="primary"
                                            size="large"
                                            onClick={() => handleUseTemplate(template)}
                                            className={styles.useButton}
                                        >
                                            使用
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </Col>
                    ))}
                </Row>
            </div>
        </Modal>
    );
};

export default TimeSequenceCardTemplateModal; 