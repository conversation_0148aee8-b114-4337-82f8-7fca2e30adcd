.acpTools {
    display: flex;
    flex-direction: column;

    .headerText {
        font-weight: 500;
        font-size: 20px;
        line-height: 32px;
        color: #333333;
        margin-bottom: 8px;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f5;

        .addToolBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            background-color: #4d5ef3;
            font-weight: 500;
            font-size: 14px;
            line-height: 24px;
            color: #ffffff;
            border-radius: 8px;
            transition: all 0.3s;

            &:hover {
                background-color: #3144f1;
            }
        }

        //搜索输入框
        :global(.arco-input-inner-wrapper) {
            padding: 8px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            ::placeholder {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                color: #d6d6d6;
            }

            :global(.arco-input) {
                padding-top: 0;
                padding-bottom: 0;
                padding-left: 8px;
            }
        }

        //筛选select
        :global(.arco-select-size-default.arco-select-single .arco-select-view) {
            padding: 8px;
            height: auto;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            background-color: #ffffff;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            :global(.arco-select-prefix) {
                margin-right: 4px;
            }

            :global(.arco-select-view-input) {
                &::placeholder {
                    color: #d6d6d6;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                }
            }
        }

        .toolNumber {
            color: #adadad;
            font-size: 14px;
            font-weight: 400;
            white-space: nowrap;
        }
    }

    .content {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 16px;

        .toolCard {
            border: 1px solid #f5f5f5;
            border-radius: 8px;
            padding: 20px 24px;
            transition: all 0.3s;
            min-height: 180px;

            &:hover {
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

                .triggerBtn {
                    opacity: 1;
                }
            }

            :global(.arco-card-body) {
                padding: 0;
                display: flex;
                flex-direction: column;
                width: 100%;
                height: 100%;
            }

            .toolInfo {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 100%;

                .nameWrapper {
                    display: flex;
                    align-items: center;

                    .icon {
                        width: 48px;
                        height: 48px;
                        border-radius: 8px;

                        svg {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .name {
                        font-size: 20px;
                        font-weight: 600;
                        color: #333333;
                        cursor: pointer;
                    }
                }

                .toolDetails {
                    display: flex;
                    flex-direction: column;

                    :global(.arco-typography) {
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 24px;
                        color: #5c5c5c;
                    }

                    .acpType {
                        align-self: flex-start;
                        padding: 2px 6px;
                        border: 1px solid #ebebeb;
                        border-radius: 4px;
                        font-weight: 400;
                        font-size: 12px;
                        color: #5c5c5c;
                    }

                    .acpDetail {
                        color: #5c5c5c;
                        font-size: 12px;
                        font-weight: 400;
                    }

                    .acpCommand,
                    .acpArgument,
                    .acpUrl {
                        color: #5c5c5c;
                        font-size: 14px;
                        font-weight: 400;
                    }
                }

                .toolFooter {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 16px;

                    .metaText {
                        :global(.arco-typography) {
                            font-weight: 400;
                            font-size: 14px;
                            color: #adadad; // 统一字体颜色
                        }
                    }
                }
            }

            .triggerBtn {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 4px;
                background-color: #ffffff;
                border-radius: 8px;
                border: 1px solid #f5f5f5;
                opacity: 0;

                &:hover {
                    background: #f5f5f5;
                }
            }
        }
    }

    .loadingContainer,
    .errorContainer,
    .emptyContainer {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 200px;
    }
}

.confirmDeleteModal {
    position: relative;
    padding: 32px;
    width: 480px;
    border-radius: 8px;

    .modalContent {
        display: flex;
        flex-direction: column;

        .modalContentText {
            font-weight: 400;
            font-size: 14px;
            color: #5c5c5c;
        }
    }

    .modalFooter {
        margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .cancelDeleteBtn,
        .confirmDeleteBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 14px;
        }

        .cancelDeleteBtn {
            background-color: #fafafa;
            color: #5c5c5c;
        }

        .confirmDeleteBtn {
            background-color: #4b5cf2;
        }
    }

    :global(.arco-modal-header) {
        padding: 0;
        height: auto;
        border-bottom: none;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            color: #333333;
            margin-bottom: 24px;
            text-align: left;
        }
    }

    :global(.arco-modal-content) {
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 32px;
        top: 32px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
        background-color: #e9e9ff;
        color: #4d5ef3;
    }
}

.acpDetailModal {
    position: relative;
    padding: 32px;
    width: 640px;
    border-radius: 8px;

    .modalContent {
        display: flex;
        flex-direction: column;
        width: 100%;

        .iconContainer {
            display: flex;
            margin-bottom: 15px;

            .acpIconWrapper {
                width: 48px;
                height: 48px;
                border-radius: 8px;

                .acpIcon {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }

    .modalFooter {
        margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .cancelBtn,
        .saveBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 14px;
            height: 40px;
        }

        .cancelBtn {
            background-color: #fafafa;
            color: #5c5c5c;
        }

        .saveBtn {
            background-color: #4b5cf2;
        }
    }

    .required {
        font-weight: 400;
        font-size: 12px;
        color: #4b5cf2;
    }

    :global(.arco-form-label-item) {
        font-weight: 600;
        font-size: 14px;
        color: #5c5c5c;
    }

    :global(.arco-modal-header) {
        padding: 0;
        height: auto;
        border-bottom: none;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            color: #333333;
            margin-bottom: 24px;
            text-align: left;
        }
    }

    :global(.arco-form-item) {
        margin-bottom: 24px;

        :global(.arco-form-item > .arco-form-label-item) {
            font-weight: 600;
            font-size: 14px;
            line-height: 24px;
            color: #595959;
        }
    }

    :global(.arco-input) {
        padding: 8px 12px;
        border: 1px solid #ebebeb;
        border-radius: 4px;
        background-color: transparent;

        &::placeholder {
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
        }
    }

    :global(.arco-modal-content) {
        min-height: 160px;
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 32px;
        top: 32px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
        background-color: #e9e9ff;
        color: #4d5ef3;
    }

    :global(.arco-select) {
        background-color: transparent;
        border: 1px solid #ebebeb;
        border-radius: 4px;
    }
}

.deleteBtn {
    padding: 4px 8px;
    border-radius: 4px;
    width: 60px;
    display: flex;
    justify-content: flex-start;
    background-color: #ffffff !important;
    color: #d54941 !important;

    &:hover {
        background-color: #f5f5f5 !important;
    }
}

:global(.arco-popover-content-right) {
    color: var(--color-text-2);
    background-color: #ffffff;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.12);
    max-width: none;
    font-size: 14px;
    border-radius: 4px;
    line-height: 1.5715;
    box-sizing: border-box;
    border: none;
    padding: 8px;
}


// 连接类型卡片样式
.connectionTypeCards {
    display: flex;
    gap: 16px;
    width: 100%;

    .connectionTypeCard {
        border: 1px solid #ebebeb;
        border-radius: 8px;
        padding: 12px;
        width: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;
        background-color: var(--color-bg-2);
        transition: all 0.2s;

        &:hover {
            border-color: #4455f2;
        }
    }

    .connectionTypeCardChecked {
        border-color: #4455f2;
        background-color: #f8f8ff;

        .radioMark {
            border-color: #4455f2;
            background-color: #ffffff;

            .radioMarkDot {
                opacity: 1;
                transform: scale(1);
            }
        }
    }

    .radioMark {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid #ebebeb;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        transition: all 0.2s;

        .radioMarkDot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #4455f2;
            opacity: 0;
            transform: scale(0);
            transition: all 0.2s;
        }
    }

    :global(.arco-radio) {
        padding-left: 0;
    }

    .connectionTypeContent {
        flex: 1;
    }
}