ARG VERSION=18.20.4

# 构建阶段
FROM ai4c-tcr.tencentcloudcr.com/infra/node:${VERSION} AS builder
WORKDIR /app
COPY package.json yarn.lock ./
# COPY .husky/ ./.husky/
RUN npm install
COPY . .
COPY .env.production.atb-admin .env.production
RUN npm run build

# 部署阶段
FROM ai4c-tcr.tencentcloudcr.com/infra/nginx:stable-alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY devops/*.css /usr/local/etc/nginx/custom_styles/
COPY devops/nginx.conf /etc/nginx/conf.d/default.conf
# 设置时区
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' > /etc/timezone
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]