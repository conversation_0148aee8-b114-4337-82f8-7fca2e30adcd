import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { Card, Typography, Form, Button, Space, Popover, List, Grid, Tag, Switch, Modal, Message, Tooltip, Input, Spin, Tabs } from '@arco-design/web-react';
import { IconDragDotVertical } from '@arco-design/web-react/icon';
import IconCloseTag from '@/assets/application/IconCloseTag.svg';
import IconSmile from '@/assets/application/IconSmile.svg';
import IconCustom from '@/assets/application/Iconcustom.svg';
import IconTitle from '@/assets/application/IconTitle.svg';
import IconFont from '@/assets/application/IconFont.svg';
import IconTag from '@/assets/application/IconTag.svg';
import IconFile from '@/assets/application/IconFile.svg';
import IconImage from '@/assets/application/IconImage.svg';
import IconVideo from '@/assets/application/IconVideo.svg';
import IconSound from '@/assets/application/IconSound.svg';
import IconLink from '@/assets/application/IconLink.svg';
import IconImagePlus from '@/assets/application/IconImagePlus.svg';
import IconVideoPlus from '@/assets/application/IconVideoPlus.svg';
import IconSoundPlus from '@/assets/application/IconSoundPlus.svg';
import IconSmilePlus from '@/assets/application/IconSmilePlus.svg';
import IconPlay from '@/assets/application/IconPlay.svg';
import PlayCircleOutlined from '@/assets/application/PlayCircleOutlined.svg';
import PlusCircleOutlined from '@/assets/application/PlusCircleOutlined.svg';
import StructureActive from '@/assets/application/structureActive.svg';
import Structure from '@/assets/application/structure.svg';
import CodeActive from '@/assets/application/codeActive.svg';
import Code from '@/assets/application/code.svg';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { GlobalState } from '@/store/index';
import { ReactSortable } from 'react-sortablejs';
import styles from '../style/index.module.less';
import CardComponentsModal from '../../CardTemplateModal/CardComponentsModal';
import { isEqual } from 'lodash';

const { Paragraph, Text } = Typography;
const { Col, Row } = Grid;
const { TextArea } = Input;


// Define component types for structure
const componentTypes = {
    ICON: 'icon',
    TITLE: 'title',
    TEXT: 'text',
    TAG: 'tag',
    FILE: 'file',
    IMAGE: 'image',
    VIDEO: 'video',
    AUDIO: 'audio',
    LINK: 'link',
    CUSTOM: 'custom'
};

// Get icon for component type 
const getIconForType = (type) => {
    switch (type) {
        case componentTypes.ICON:
            return <IconSmile />;
        case componentTypes.TITLE:
            return <IconTitle />;
        case componentTypes.TEXT:
            return <IconFont />;
        case componentTypes.TAG:
            return <IconTag />;
        case componentTypes.FILE:
            return <IconFile />;
        case componentTypes.IMAGE:
            return <IconImage />;
        case componentTypes.VIDEO:
            return <IconVideo />;
        case componentTypes.AUDIO:
            return <IconSound />;
        case componentTypes.LINK:
            return <IconLink />;
        case componentTypes.CUSTOM:
            return <IconCustom />; // 使用默认图标，或者可以换成其他合适的图标
        default:
            return <IconSmile />;
    }
};

// 获取组件的标准化显示名称
const getComponentDisplayName = (type) => {
    switch (type) {
        case componentTypes.ICON:
            return '图标';
        case componentTypes.TITLE:
            return '标题';
        case componentTypes.TEXT:
            return '文本';
        case componentTypes.TAG:
            return '标签';
        case componentTypes.FILE:
            return '文档';
        case componentTypes.IMAGE:
            return '图片';
        case componentTypes.VIDEO:
            return '视频';
        case componentTypes.AUDIO:
            return '音频';
        case componentTypes.LINK:
            return '链接';
        case componentTypes.CUSTOM:
            return '自定义';
        default:
            return '组件';
    }
};

// 创建标准ID格式的辅助函数 - 更新为递增编号格式
const createStandardId = (type, existingItems = []) => {
    // 统计当前类型已有的数量
    const existingCount = existingItems.filter(item => item.type === type).length;
    const nextNumber = existingCount + 1;
    return `${type}_${nextNumber.toString().padStart(3, '0')}`;
};

// 获取当前时间的ISO字符串
const getCurrentTimeString = () => {
    return new Date().toISOString();
};

// 根据组件类型生成对应的parts数据结构
const getTypeSpecificData = (type, id) => {
    const currentTime = getCurrentTimeString();
    
    switch (type) {
        case 'icon':
            return {
                type: "text",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                text: "这是一个图标",
                part_sort: -1,
                metadata: {
                    text_type: "icon"
                }
            };
        case 'title':
            return {
                type: "text",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                text: "这是一个标题",
                part_sort: -1,
                metadata: {
                    text_type: "title"
                }
            };
        case 'text':
            return {
                type: "text",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                text: "这是一个文本",
                part_sort: -1,
                metadata: {
                    text_type: "text"
                }
            };
        case 'tag':
            return {
                type: "text",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                text: "这是一个标签",
                part_sort: -1,
                metadata: {
                    text_type: "tag"
                }
            };
        case 'link':
            return {
                type: "text",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                text: "这是一个链接",
                part_sort: -1,
                metadata: {
                    text_type: "link"
                }
            };
        case 'file':
            return {
                type: "file",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                file: {
                    name: "这是一个文件",
                    mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    bytes: null,
                    uri: null,
                    size: null
                },
                part_sort: -1,
                metadata: {
                    file_type: "document"
                }
            };
        case 'image':
            return {
                type: "file",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                file: {
                    name: "这是一个图片",
                    mimeType: "image/png",
                    bytes: null,
                    uri: null,
                    size: null,
                },
                part_sort: -1,
                metadata: {
                    file_type: "image"
                }
            };
        case 'video':
            return {
                type: "file",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                file: {
                    name: "这是一个视频",
                    mimeType: "video/mp4",
                    bytes: null,
                    uri: null,
                    size: null
                },
                part_sort: -1,
                metadata: {
                    file_type: "video"
                }
            };
        case 'audio':
            return {
                type: "file",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                file: {
                    name: "这是一个音频",
                    mimeType: "audio/mpeg",
                    bytes: null,
                    uri: null,
                    size: null
                },
                part_sort: -1,
                metadata: {
                    file_type: "audio"
                }
            };
        case 'custom':
            return {
                type: "data",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                data: {
                    key1: "value1",
                    key2: "value2",
                    key3: "value3"
                },
                part_sort: -1,
                metadata: {
                    data_type: "custom"
                }
            };
        default:
            return {
                type: "data",
                id: id,
                parent_id: null,
                created_time: currentTime,
                generated_by: "system",
                data: {
                    key1: "value1",
                    key2: "value2",
                    key3: "value3"
                },
                part_sort: -1,
                metadata: {
                    data_type: "custom"
                }
            };
    }
};

// 简化组件项用于内部结构显示
const simplifyItemForStructure = (item) => {
    return {
        id: item.id,
        type: item.componentType || item.type,
        name: getComponentDisplayName(item.componentType || item.type),
        description: item.description || `${getComponentDisplayName(item.componentType || item.type)}组件`,
        icon: getIconForType(item.componentType || item.type),
        partData: item // 保存完整的part数据
    };
};

interface TimeSequenceCardConfigurationProps {
    onFinish?: () => void;
    loading?: boolean;
    onBack?: () => void;
    isEditMode?: boolean;
    onToggleEditMode?: () => void;
    isDetailMode?: boolean;
    onSave?: () => void;
    submitting?: boolean;
}

// 定义ref的类型
export interface TimeSequenceCardConfigurationRef {
    getCurrentJsonData: () => any;
}

const TimeSequenceCardConfiguration = forwardRef<TimeSequenceCardConfigurationRef, TimeSequenceCardConfigurationProps>(({
    onFinish,
    loading = false,
    onBack,
    isEditMode = true,
    onToggleEditMode,
    isDetailMode = false,
    onSave,
    submitting = false
}, ref) => {
    const [popoverVisible, setPopoverVisible] = useState(false);
    const [templateModalVisible, setTemplateModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState('preview');
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [form] = Form.useForm(); // 添加表单实例
    const isUpdatingFromRedux = useRef(false);    // 添加标志来防止循环更新
    const prevStructureItems = useRef(null);
    const localStructureItems = useRef([]);
    const sortableUpdateTimeoutRef = useRef(null);
    const timeSequenceCardDetail = useSelector((state: GlobalState) => state.timeSequenceCardDetail);    // 从Redux获取时序卡片详情数据
    const structureItems = timeSequenceCardDetail?.structureItems || [];
    const basicInfo = timeSequenceCardDetail?.basicInfo || {};
    const isReadOnly = isDetailMode && !isEditMode;    // 计算只读状态 - 只有在详情模式且非编辑状态时表单才禁用
    const [jsonEditorValue, setJsonEditorValue] = useState('');    // 添加JSON编辑状态
    const [jsonError, setJsonError] = useState(null);
    const jsonChangeTimeoutRef = useRef(null);    // 使用useRef和useEffect添加防抖处理

    // 当Redux中的结构项数据变化时，更新本地状态
    useEffect(() => {
        if (timeSequenceCardDetail && timeSequenceCardDetail.structureItems) {
            // 检查数据是否有实质变化
            if (!isEqual(prevStructureItems.current, timeSequenceCardDetail.structureItems)) {
                prevStructureItems.current = timeSequenceCardDetail.structureItems;
                localStructureItems.current = [...timeSequenceCardDetail.structureItems];

                // 更新JSON编辑器的值
                try {
                    const currentJsonData = getCurrentJsonData();
                    if (currentJsonData) {
                        setJsonEditorValue(JSON.stringify(currentJsonData, null, 2));
                    } else {
                        setJsonEditorValue('{\n  "type": "object",\n  "properties": {\n    "parts": []\n  },\n  "required": ["parts"]\n}');
                    }
                    setJsonError(null); // 重置错误状态
                } catch (error) {
                    console.error('更新JSON编辑器失败:', error);
                    setJsonError(error.message);
                }
            }
        }
    }, [timeSequenceCardDetail]);

    // 更新JSON编辑器的值 - 仅在视图模式变化时更新
    useEffect(() => {
        if (viewMode === 'code') {
            try {
                const currentJsonData = getCurrentJsonData();
                if (currentJsonData) {
                    setJsonEditorValue(JSON.stringify(currentJsonData, null, 2));
                } else {
                    setJsonEditorValue('{\n  "type": "object",\n  "properties": {\n    "parts": []\n  },\n  "required": ["parts"]\n}');
                }
                setJsonError(null); // 重置错误状态
            } catch (error) {
                console.error('切换到代码视图时更新JSON失败:', error);
                setJsonError(error.message);
            }
        }
    }, [viewMode]);

    // 处理JSON编辑器的内容变化
    const handleJsonChange = (value) => {
        setJsonEditorValue(value);

        // 清除之前的定时器
        if (jsonChangeTimeoutRef.current) {
            clearTimeout(jsonChangeTimeoutRef.current);
        }

        // 设置新的定时器（防抖处理）
        jsonChangeTimeoutRef.current = setTimeout(() => {
            // 验证JSON
            try {
                const parsedJson = JSON.parse(value);
                setJsonError(null);

                // 自动应用有效的JSON更改
                if (parsedJson && parsedJson.properties) {
                    applyJsonChanges(parsedJson);
                } else if (!parsedJson.properties) {
                    setJsonError('JSON格式错误：必须包含properties字段');
                }
            } catch (error) {
                setJsonError(error.message);
            }

            jsonChangeTimeoutRef.current = null;
        }, 1000); // 1秒防抖延迟
    };

    // 组件卸载时清除定时器
    useEffect(() => {
        return () => {
            if (jsonChangeTimeoutRef.current) {
                clearTimeout(jsonChangeTimeoutRef.current);
            }
        };
    }, []);

    // 应用JSON编辑器的更改到结构项
    const applyJsonChanges = (parsedSchema = null) => {
        try {
            // 如果没有传入解析好的JSON，则尝试解析当前编辑器中的值
            const jsonToApply = parsedSchema || JSON.parse(jsonEditorValue);

            if (jsonToApply && jsonToApply.properties && jsonToApply.properties.parts) {
                const parts = jsonToApply.properties.parts;
                const newItems = [];

                // 遍历parts数组，构建结构项数组
                parts.forEach((part, index) => {
                    if (part) {
                        // 从part数据中提取组件类型
                        let componentType = 'text'; // 默认类型
                        
                        if (part.type === 'text' && part.metadata && part.metadata.text_type) {
                            componentType = part.metadata.text_type;
                        } else if (part.type === 'file' && part.metadata && part.metadata.file_type) {
                            // 根据file_type映射到组件类型
                            switch (part.metadata.file_type) {
                                case 'image':
                                    componentType = 'image';
                                    break;
                                case 'video':
                                    componentType = 'video';
                                    break;
                                case 'audio':
                                    componentType = 'audio';
                                    break;
                                case 'document':
                                default:
                                    componentType = 'file';
                                    break;
                            }
                        } else if (part.type === 'data') {
                            // 检查data类型的metadata，如果是custom则映射为custom类型
                            if (part.metadata && part.metadata.data_type === 'custom') {
                                componentType = 'custom';
                            } else {
                                componentType = 'data';
                            }
                        }

                        // 根据类型获取对应的图标
                        const icon = getIconForType(componentType);

                        // 使用part中的id，如果没有则生成符合格式的id
                        let itemId = part.id;
                        if (!itemId || !itemId.match(/^[a-z]+_\d{3}$/)) {
                            // 如果没有ID或ID格式不正确，基于已有的newItems生成新的ID
                            itemId = createStandardId(componentType, newItems);
                        }

                        newItems.push({
                            id: itemId,
                            type: componentType,
                            name: getComponentDisplayName(componentType),
                            description: `${getComponentDisplayName(componentType)}组件`,
                            icon: icon,
                            partData: part // 保存完整的part数据
                        });
                    }
                });

                // 更新到Redux
                updateStructureItems(newItems);
            }
        } catch (error) {
            setJsonError(error.message);
        }
    };

    // 清除全局状态中的模板数据
    const clearTemplate = () => {
        return {
            type: 'update-selected-template-items',
            payload: { selectedTemplateItems: null },
        };
    };

    // 获取结构项JSON字符串
    const getItemsJsonString = () => {
        // 构建新的数据结构
        const schema = {
            type: "object",
            properties: {
                parts: []
            },
            required: ["parts"]
        };

        // 使用本地结构项数据，将其转换为parts格式
        const items = timeSequenceCardDetail?.structureItems || [];
        
        // 将结构项转换为parts数组
        schema.properties.parts = items.map((item) => {
            // 如果item已经有partData，直接使用；否则根据type生成
            if (item.partData) {
                return item.partData;
            } else {
                // 生成新的part数据，使用item的id
                const id = item.id || createStandardId(item.type, items);
                return getTypeSpecificData(item.type, id);
            }
        });

        return JSON.stringify(schema, null, 2);
    };

    // 获取当前的完整JSON对象（用于外部调用）
    const getCurrentJsonData = () => {
        try {
            // 首先检查是否有timeSequenceCardDetail数据
            if (!timeSequenceCardDetail) {
                console.warn('TimeSequenceCardDetail为空，无法生成JSON数据');
                return null;
            }

            const structureItems = timeSequenceCardDetail.structureItems || [];
            
            // 如果没有结构项，返回空的有效结构
            if (structureItems.length === 0) {
                return {
                    type: "object",
                    properties: {
                        parts: []
                    },
                    required: ["parts"]
                };
            }

            // 构建parts数组
            const parts = structureItems.map((item) => {
                // 如果item已经有partData，直接使用
                if (item.partData) {
                    return item.partData;
                }
                
                // 否则根据type生成新的part数据
                const id = item.id || createStandardId(item.type, structureItems);
                return getTypeSpecificData(item.type, id);
            }).filter(Boolean); // 过滤掉空值

            // 返回标准格式的schema
            return {
                type: "object",
                properties: {
                    parts: parts
                },
                required: ["parts"]
            };
        } catch (error) {
            console.error('获取JSON数据失败:', error);
            console.warn('TimeSequenceCardDetail状态:', timeSequenceCardDetail);
            return null;
        }
    };

    // 使用useImperativeHandle暴露方法给父组件
    useImperativeHandle(ref, () => ({
        getCurrentJsonData
    }), [timeSequenceCardDetail]);

    // Component list (same as in original file)
    const componentList = [
        { id: 'icon', icon: <IconSmile />, text: '图标', type: componentTypes.ICON },
        { id: 'title', icon: <IconTitle />, text: '标题', type: componentTypes.TITLE },
        { id: 'text', icon: <IconFont />, text: '文本', type: componentTypes.TEXT },
        { id: 'tag', icon: <IconTag />, text: '标签', type: componentTypes.TAG },
        { id: 'file', icon: <IconFile />, text: '文档', type: componentTypes.FILE },
        { id: 'image', icon: <IconImage />, text: '图片', type: componentTypes.IMAGE },
        { id: 'video', icon: <IconVideo />, text: '视频', type: componentTypes.VIDEO },
        { id: 'audio', icon: <IconSound />, text: '音频', type: componentTypes.AUDIO },
        { id: 'link', icon: <IconLink />, text: '链接', type: componentTypes.LINK },
        { id: 'custom', icon: <IconCustom />, text: '自定义', type: componentTypes.CUSTOM },
    ];

    // Render preview component based on type
    const renderPreviewItem = (item) => {
        switch (item.type) {
            case componentTypes.ICON:
                return (
                    <div key={item.id} className={styles.icon}>
                        <IconSmilePlus />
                    </div>
                );
            case componentTypes.TITLE:
                return (
                    <Typography.Title key={item.id} className={styles.previewTitle}>
                        这是标题
                    </Typography.Title>
                );
            case componentTypes.TEXT:
                return (
                    <Paragraph key={item.id} className={styles.previewParagraph}>
                        这是一段文字
                    </Paragraph>
                );
            case componentTypes.TAG:
                return (
                    <div key={item.id} className={styles.tagList}>
                        <Tag>这是标签</Tag>
                        <Tag>这是标签</Tag>
                        <Tag>这是标签</Tag>
                    </div>
                );
            case componentTypes.FILE:
                return (
                    <div key={item.id} className={styles.fileItem}>
                        <Text>文档.pdf</Text>
                    </div>
                );
            case componentTypes.IMAGE:
                return (
                    <div key={item.id} className={styles.imagePlaceholder}>
                        <div className={styles.placeholderIcon}>
                            <IconImagePlus />
                        </div>
                    </div>
                );
            case componentTypes.VIDEO:
                return (
                    <div key={item.id} className={styles.videoPlaceholder}>
                        <div className={styles.placeholderIcon}>
                            <IconVideoPlus />
                        </div>
                        <div className={styles.videoPlayBtn}>
                            <IconPlay />
                        </div>
                    </div>
                );
            case componentTypes.AUDIO:
                return (
                    <div key={item.id} className={styles.audioPlaceholder}>
                        <div className={styles.placeholderIcon}>
                            <IconSoundPlus />
                        </div>
                        <div className={styles.audioPlayBtn}>
                            <IconPlay />
                        </div>
                    </div>
                );
            case componentTypes.LINK:
                return (
                    <div key={item.id} className={styles.linkItem}>
                        <Text>.com</Text>
                    </div>
                );
            case componentTypes.CUSTOM:
                return (
                    <div key={item.id} className={styles.customContainer}>
                        <div className={styles.customDataRow}>
                            <div className={styles.keyValuePair}>
                                <div className={styles.inputGroup}>
                                    <div className={styles.inputBox}>键</div>
                                </div>
                                <div className={styles.inputGroup}>
                                    <div className={styles.inputBox}>值</div>
                                </div>
                            </div>
                        </div>
                        <div className={styles.customDataRow}>
                            <div className={styles.keyValuePair}>
                                <div className={styles.inputGroup}>
                                    <div className={styles.inputBox}>键</div>
                                </div>
                                <div className={styles.inputGroup}>
                                    <div className={styles.inputBox}>值</div>
                                </div>
                            </div>
                        </div>
                    </div>
                );
            default:
                return null;
        }
    };

    // 更新Redux状态中的结构项
    const updateStructureItems = (newItems) => {
        // 如果是从Redux更新触发的变化，不要再次更新Redux
        if (!isEditMode || !timeSequenceCardDetail) {
            return;
        }

        // 检查结构项是否有实际变化
        if (!isEqual(newItems, timeSequenceCardDetail.structureItems)) {
            dispatch({
                type: 'update-timeSequence-card-detail',
                payload: {
                    timeSequenceCardDetail: {
                        ...timeSequenceCardDetail,
                        structureItems: newItems
                    }
                },
            });
        }
    };

    // 处理ReactSortable的列表变化
    const handleSortableChange = (newItems) => {
        localStructureItems.current = newItems;

        // 使用防抖更新Redux，避免拖拽过程中频繁更新
        if (sortableUpdateTimeoutRef.current) {
            clearTimeout(sortableUpdateTimeoutRef.current);
        }

        sortableUpdateTimeoutRef.current = setTimeout(() => {
            updateStructureItems(newItems);
            sortableUpdateTimeoutRef.current = null;
        }, 300);
    };

    // Add structure item 
    const addStructureItem = (component) => {
        // 生成唯一ID，传入当前已有的结构项
        const uniqueId = createStandardId(component.type, structureItems);
        
        // 生成part数据
        const partData = getTypeSpecificData(component.type, uniqueId);
        const standardName = getComponentDisplayName(component.type);

        const newItem = {
            id: uniqueId, // 使用统一格式的ID
            type: component.type,
            name: standardName,
            description: `${standardName}组件`,
            icon: getIconForType(component.type),
            color: component.color,
            partData: partData // 保存完整的part数据
        };

        // 更新Redux状态
        updateStructureItems([...structureItems, newItem]);
        setPopoverVisible(false);
    };

    // Remove structure item 
    const removeStructureItem = (id) => {
        const newItems = structureItems.filter(item => item.id !== id);
        updateStructureItems(newItems);
    };

    // Handle using templates 
    const handleUseTemplate = (templateItems) => {
        // 当前已有的组件项
        const currentItems = structureItems || [];
        
        // 准备要添加的新组件项
        const newItems = [];
        
        // 创建一个临时的items数组，用于计算ID编号
        const tempItems = [...currentItems];
        
        // 对模板项进行标准化处理
        templateItems.forEach(item => {
            const type = item.type;
            
            // 生成唯一ID，基于当前的tempItems
            const uniqueId = createStandardId(type, tempItems);
            
            // 生成part数据
            const partData = getTypeSpecificData(type, uniqueId);
            
            const newItem = {
                ...item,
                id: uniqueId, // 使用统一格式的ID
                name: getComponentDisplayName(type), // 使用标准化名称
                description: item.description || `${getComponentDisplayName(type)}组件`,
                partData: partData // 保存完整的part数据
            };
            
            newItems.push(newItem);
            // 将新项添加到临时数组，以便下一个item的ID计算正确
            tempItems.push(newItem);
        });
        
        // 追加模板项到现有项
        updateStructureItems([...currentItems, ...newItems]);
    };

    // Handle click on "使用模板" button
    const handleOpenTemplateModal = () => {
        // 直接打开模板选择对话框
        setTemplateModalVisible(true);
    };

    // 渲染代码视图
    const renderCodeView = () => {
        return (
            <div className={styles.codeViewContainer}>
                <TextArea
                    className={styles.jsonEditor}
                    value={jsonEditorValue}
                    onChange={handleJsonChange}
                    disabled={isReadOnly}
                    style={{
                        height: jsonError ? 'calc(100% - 30px)' : '100%',
                        fontFamily: 'SFMono-Regular, Consolas, Liberation Mono, Menlo, Courier, monospace',
                        fontSize: '14px',
                        lineHeight: '1.6',
                        resize: 'none',
                        backgroundColor: '#ffffff',
                    }}
                    placeholder="请输入有效的JSON格式数据。修改后会自动应用更改。"
                />
                {jsonError && (
                    <div style={{
                        padding: '5px 10px',
                        color: '#ff4d4f',
                        fontSize: '12px',
                        backgroundColor: '#fff2f0',
                        borderRadius: '8px',
                        marginTop: '2px',
                        borderTop: '1px solid #ff4d4f',
                        borderLeft: '1px solid #ff4d4f',
                        borderRight: '1px solid #ff4d4f',
                        borderBottom: '1px solid #ff4d4f'
                    }}>
                        错误: {jsonError}
                    </div>
                )}
            </div>
        );
    };

    // Component list popover content
    const componentContent = (
        <div className={styles.componentPopover}>
            <List
                dataSource={componentList}
                render={(item, index) => (
                    <List.Item
                        key={index}
                        className={styles.componentItem}
                        onClick={() => addStructureItem(item)}
                    >
                        <Space size={8} className={styles.listItem}>
                            {item.icon}
                            <span>{item.text}</span>
                        </Space>
                    </List.Item>
                )}
            />
        </div>
    );

    // 处理创建按钮点击事件
    const handleCreateTimeSequenceCard = async () => {
        try {
            // 获取 is_default 字段的值
            const isDefault = form.getFieldValue('is_default') || false;

            // 更新Redux状态中的is_default字段
            if (timeSequenceCardDetail) {
                dispatch({
                    type: 'update-timeSequence-card-detail',
                    payload: {
                        timeSequenceCardDetail: {
                            ...timeSequenceCardDetail,
                            basicInfo: {
                                ...timeSequenceCardDetail.basicInfo,
                                is_default: isDefault
                            }
                        }
                    },
                });
            }

            // 如果有完成回调，调用它
            if (onFinish) {
                onFinish();
                return;
            }
        } catch (error) {
            console.error('准备提交数据失败:', error);
            Message.error('准备提交数据失败: ' + (error.message || '未知错误'));
        }
    };

    // 修改保存函数
    const handleSave = async () => {
        try {
            // 获取 is_default 字段的值
            const isDefault = form.getFieldValue('is_default') || false;

            // 更新Redux状态中的is_default字段
            if (timeSequenceCardDetail) {
                dispatch({
                    type: 'update-timeSequence-card-detail',
                    payload: {
                        timeSequenceCardDetail: {
                            ...timeSequenceCardDetail,
                            basicInfo: {
                                ...timeSequenceCardDetail.basicInfo,
                                is_default: isDefault
                            }
                        }
                    },
                });
            }

            // 调用保存回调
            if (onSave) {
                onSave();
            }
        } catch (error) {
            console.error('保存失败:', error);
            Message.error('保存失败: ' + (error.message || '未知错误'));
        }
    };

    // 修改编辑按钮的处理
    const handleEditButtonClick = () => {
        if (isEditMode) {
            // 已在编辑模式，点击表示保存
            handleSave();
        } else {
            // 未在编辑模式，点击表示切换到编辑模式
            if (onToggleEditMode) onToggleEditMode();
        }
    };

    // 渲染加载状态
    if (loading) {
        return (
            <div className={styles.loadingContainer}>
                <Spin tip="加载中..." />
            </div>
        );
    }

    return (
        <div>
            <Row gutter={[48, 0]} className={styles.cardContainer}>
                <Col span={12} style={{ height: '100%' }}>
                    <Card bordered={false} style={{ height: '100%' }}>
                        <Form
                            form={form}
                            labelCol={{ span: 24 }}
                            wrapperCol={{ span: 24 }}
                            style={{ height: '100%' }}
                            disabled={isReadOnly}
                        >
                            <Row justify="space-between" align="center" style={{ marginBottom: '8px' }}>
                                <Space className={styles.templatePlaceholder} direction="vertical" size={'mini'}>
                                    <Text style={{ color: '#5c5c5c', fontWeight: '600', fontSize: '14px' }}>卡片结构</Text>
                                    <Text className={styles.labelExact}>自定义时序卡片模板</Text>
                                </Space>
                                <Space className={styles.templateActions}>
                                    <Popover
                                        position="bl"
                                        content={componentContent}
                                        trigger="click"
                                        popupVisible={popoverVisible}
                                        onVisibleChange={setPopoverVisible}
                                        disabled={isReadOnly}
                                    >
                                        <Button type="text" disabled={isReadOnly}>添加</Button>
                                    </Popover>
                                    <Button
                                        type="text"
                                        onClick={handleOpenTemplateModal}
                                        disabled={isReadOnly}
                                    >
                                        选择组件
                                    </Button>
                                    <Button.Group>
                                        <Button
                                            type="text"
                                            icon={viewMode === 'preview' ? <StructureActive /> : <Structure />}
                                            style={{
                                                backgroundColor: viewMode === 'preview' ? '#fafafa' : 'transparent',
                                                borderRadius: '6px 6px 6px 6px',
                                            }}
                                            onClick={() => setViewMode('preview')}
                                        />
                                        <Button
                                            type="text"
                                            icon={viewMode === 'code' ? <CodeActive /> : <Code />}
                                            style={{
                                                backgroundColor: viewMode === 'code' ? '#fafafa' : 'transparent',
                                                borderRadius: '6px 6px 6px 6px',
                                            }}
                                            onClick={() => setViewMode('code')}
                                        />
                                    </Button.Group>
                                </Space>
                            </Row>
                            <div className={`${styles.structureContainer} ${viewMode === 'code' ? styles.codeViewMode : ''}`}>
                                {structureItems.length > 0 ? (
                                    viewMode === 'preview' ? (
                                        <div className={styles.selectedItems}>
                                            <ReactSortable
                                                list={structureItems}
                                                setList={handleSortableChange}
                                                handle=".drag-handle"
                                                animation={150}
                                                className={styles.selectedList}
                                                disabled={isReadOnly}
                                            >
                                                {structureItems.map((item) => (
                                                    <Row key={item.id} className={styles.selectedItemRow}>
                                                        <Col className={styles.selectedItemCol}>
                                                            <Space>
                                                                <IconDragDotVertical className={`${styles.dragHandle} drag-handle`} />
                                                                <div className={styles.itemIconWrapper}>
                                                                    {item.icon}
                                                                </div>
                                                                <Text className={styles.selectedItemText}>{getComponentDisplayName(item.type)}</Text>
                                                            </Space>
                                                            {!isReadOnly && (
                                                                <IconCloseTag
                                                                    className={styles.deleteIcon}
                                                                    onClick={() => removeStructureItem(item.id)}
                                                                />
                                                            )}
                                                        </Col>
                                                    </Row>
                                                ))}
                                            </ReactSortable>
                                        </div>
                                    ) : (
                                        // 代码视图
                                        renderCodeView()
                                    )
                                ) : (
                                    <div className={styles.emptyStructure}>
                                        <div className={styles.structurePlaceholder}>
                                            <PlusCircleOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
                                        </div>
                                    </div>
                                )}
                            </div>
                        </Form>
                        <CardComponentsModal
                            visible={templateModalVisible}
                            onClose={() => setTemplateModalVisible(false)}
                            onUseTemplate={handleUseTemplate}
                        />
                    </Card>
                </Col>

                {/* Right Preview Area */}
                <Col span={12} style={{ borderLeft: '1px solid #ebebeb', height: '100%' }}>
                    <Card className={styles.previewCard} bordered={false} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                        <Typography.Title className={styles.previewCardTitle}>参考效果</Typography.Title>
                        <div className={styles.previewContainer}>
                            {structureItems.length > 0 ? (
                                <div className={styles.previewContent}>
                                    {structureItems.map(item => renderPreviewItem(item))}
                                </div>
                            ) : (
                                <div className={styles.emptyPreview}>
                                    <div className={styles.previewPlaceholder}>
                                        <div className={styles.placeholderIcon}>
                                            <PlayCircleOutlined />
                                        </div>
                                        <Typography.Text type="secondary" style={{ fontSize: '12px', color: '#adadad' }}>时序卡片预览</Typography.Text>
                                    </div>
                                </div>
                            )}
                        </div>
                    </Card>
                </Col>
            </Row>
            <div className={styles.formActions}>
                <Space>
                    <Button
                        className={styles.cancelBtn}
                        onClick={() => {
                            if (onBack) {
                                onBack();
                            } else {
                                // 清除全局状态中的模板数据
                                dispatch(clearTemplate());
                                // 返回到列表页面
                                navigate('/application/timeSequenceCard');
                            }
                        }}
                        disabled={submitting}
                    >
                        {isDetailMode ? '返回' : '取消'}
                    </Button>
                    {!isDetailMode && (
                        <Button
                            className={styles.confirmBtn}
                            type="primary"
                            onClick={handleCreateTimeSequenceCard}
                            loading={submitting}
                        >
                            创建
                        </Button>
                    )}
                    {isDetailMode && (
                        <Button
                            className={styles.editBtn}
                            type="primary"
                            onClick={handleEditButtonClick}
                            loading={submitting}
                        >
                            {isEditMode ? '保存' : '编辑'}
                        </Button>
                    )}
                </Space>
            </div>
        </div>
    );
});

export default TimeSequenceCardConfiguration;