// export const host = import.meta.env.VITE_API_URL;
export const host = '/api';

export const endpoints = {
  // role
  roleOptionsUrl: `${host}/role/options`,
  rolesUrl: `${host}/roles`,
  roleDetailUrl: `${host}/role/{id}/details`,
  roleUpdateUrl: `${host}/role`,

  // user
  tokenUrl: `${host}/token`,
  myInfoUrl: `${host}/user/me`,
  usersUrl: `${host}/users`,
  userDetailUrl: `${host}/user/{id}/details`,
  userUpdateUrl: `${host}/user`,
  usrCreationUrl: `${host}/user`,
  userAvatarUrl: `${host}/user/avatar`,
  getUserAvatarUrl: `${host}/user/avatar`,

  // setting
  settingListUrl: `${host}/settings`,
  settingDetailUrl: `${host}/setting/{id}`,

  // plugin
  pluginListUrl: `${host}/plugins`,
  pluginMenuUrl: `${host}/plugin/menu`,
  pluginInstallUrl: `${host}/plugin/{id}/install`,
  pluginRemoveUrl: `${host}/plugin/{id}/remove`,

  // agent
  agentSettingUrl: `${host}/agent/settings`,
  agentListUrl: `${host}/agents`,
  agentUrl: `${host}/agent`,
  agentRefreshUrl: `${host}/refresh-agents`,
  agentUtilityOptionsUrl: `${host}/agent/utility/options`,
  agentUtilityLabelOptionsUrl: `${host}/agent/utility/labels`,
  agentRuleOptionsUrl: `${host}/agent/rule/options`,
  agentLabelOptionsUrl: `${host}/agent/labels`,
  agentAIPrompt: `${host}/text/generate-instruction`,
  agentAcpToolsUrl: `${host}/agent/{agentId}/acptools`,
  agentNameCheckUrl: `${host}/agent/name`,

  // agent task
  agentTaskListUrl: `${host}/agent/tasks`,
  agentTaskDetailUrl: `${host}/agent/{agentId}/task/{taskId}`,

  // agent instruct
  instructCompletionUrl: `${host}/instruct/{agentId}`,

  // router
  routerSettingUrl: `${host}/router/settings`,

  // conversation
  conversationInitUrl: `${host}/conversation/{agentId}`,
  conversationMessageUrl: `${host}/conversation/{agentId}/{conversationId}`,
  notificationUrl: `${host}/conversation/{conversationId}/notification`,
  conversationsUrl: `${host}/conversations`,
  conversationCountUrl: `${host}/conversations/count`,
  conversationDeletionUrl: `${host}/conversation/{conversationId}`,
  conversationDetailUrl: `${host}/conversation/{conversationId}`,
  conversationAttachmentUrl: `${host}/conversation/{conversationId}/files/{messageId}/{source}`,
  conversationUserUrl: `${host}/conversation/{conversationId}/user`,
  conversationDialogsUrl: `${host}/conversation/{conversationId}/dialogs`,
  conversationMessageDeletionUrl: `${host}/conversation/{conversationId}/message/{messageId}`,
  conversationMessageUpdateUrl: `${host}/conversation/{conversationId}/update-message`,
  conversationTagsUpdateUrl: `${host}/conversation/{conversationId}/update-tags`,
  fileUploadUrl: `${host}/agent/{agentId}/conversation/{conversationId}/upload`,
  pinConversationUrl: `${host}/agent/{agentId}/conversation/{conversationId}/dashboard`,

  // LLM
  llmListUrl: `${host}/llm-models`,
  llmTypesListUrl: `${host}/llm-model-types`,
  llmCreateUrl: `${host}/llm-model/save`,
  llmDeleteUrl: `${host}/llm-model/{id}/delete`,
  llmkeyListUrl: `${host}/llm-model-keys`,
  llmkeyByProviderUrl: `${host}/llm-model-keys/{provider}`,
  llmkeyCreateUrl: `${host}/llm-model-key/save`,
  llmkeyUpdateUrl: `${host}/llm-model-key/save`,
  llmkeyDeleteUrl: `${host}/llm-model-key/{id}/delete`,
  llmConfigListUrl: `${host}/llm-model-configs`,
  llmConfigCreateUrl: `${host}/llm-model-config/save`,
  llmConfigDeleteUrl: `${host}/llm-model-config/{id}/delete`,

  // LLM provider
  llmProvidersUrl: `${host}/llm-providers`,
  llmProvidersListUrl: `${host}/llm-provider/list`,
  llmProviderModelsUrl: `${host}/llm-provider/{provider}/models`,
  llmProviderModelsByIdUrl: `${host}/llm-models/{llmProviderId}`,
  llmProviderDeleteUrl: `${host}/llm-provider/{id}/delete`,
  llmProviderCreateUrl: `${host}/llm-provider/save`,
  llmProviderUpdateUrl: `${host}/llm-provider/save`,

  // logging
  loggingFullLogUrl: `${host}/logger/full-log`,
  loggingContentLogUrl: `${host}/logger/conversation/{conversationId}/content-log`,
  loggingStateLogUrl: `${host}/logger/conversation/{conversationId}/state-log`,

  // knowledge base
  vectorCollectionExistUrl: `${host}/knowledge/vector/{collection}/exist`,
  vectorCollectionsUrl: `${host}/knowledge/vector/collections`,
  vectorKnowledgePageListUrl: `${host}/knowledge/vector/{collection}/page`,
  vectorKnowledgeSearchUrl: `${host}/knowledge/vector/{collection}/search`,
  vectorKnowledgeCreateUrl: `${host}/knowledge/vector/{collection}/create`,
  vectorKnowledgeUpdateUrl: `${host}/knowledge/vector/{collection}/update`,
  vectorKnowledgeDeleteUrl: `${host}/knowledge/vector/{collection}/data/{id}`,
  vectorKnowledgeDeleteAllUrl: `${host}/knowledge/vector/{collection}/data`,
  vectorKnowledgeUploadUrl: `${host}/knowledge/vector/{collection}/upload`,
  vectorCollectionCreateUrl: `${host}/knowledge/vector/create-collection`,
  vectorCollectionDeleteUrl: `${host}/knowledge/vector/{collection}/delete-collection`,
  vectorCollectionUpdateUrl: `${host}/knowledge/vector/{collection}/update-collection`,

  // RAGFlow knowledge base
  kb_list: `${host}/kb/list`,

  graphKnowledgeSearchUrl: `${host}/knowledge/graph/search`,

  knowledgeDocumentUploadUrl: `${host}/knowledge/document/{collection}/upload`,
  knowledgeDocumentDeleteUrl: `${host}/knowledge/document/{collection}/delete/{fileId}`,
  knowledgeDocumentDeleteAllUrl: `${host}/knowledge/document/{collection}/delete`,
  knowledgeDocumentPageListUrl: `${host}/knowledge/document/{collection}/page`,
  knowledgeDocumentDetailUrl: `${host}/knowledge/document/{collection}/file/{fileId}`,

  knowledgeLabelsUrl: `${host}/knowledge/vector/labels`,

  // 获取keycloak配置
  authConfigUrl: `${host}/authConfig`,
  // chathub
  chatHubUrl: `${host}/chatHub`,

  // dashboard
  dashboardSettingUrl: `${host}/dashboard/components`,
  dashConversationInstructionUrl: `${host}/dashboard/component/conversation?userId={userId}`,

  // Google geocode api
  addressUrl: `${host}/address/options`,

  // workflow
  userDefinitionsUrl: `${host}/workflow/user-definitions`,
  workflowList: `${host}/elsa/api/workflow-definitions`,
  addWorkflow: `${host}/elsa/api/workflow-definitions`,
  deleteWorkflow: `${host}/elsa/api/workflow-definitions/{id}`,
  isWorkflowExist: `${host}/elsa/api/workflow-definitions/validation/is-name-unique`,
  // workflowList: `http://localhost:3000/elsa/api/workflow-definitions`,
  // addWorkflow: `http://localhost:3000/elsa/api/workflow-definitions`,
  // deleteWorkflow: `http://localhost:3000/elsa/api/workflow-definitions/{id}`,
  // isWorkflowExist: `http://localhost:3000/elsa/api/workflow-definitions/validation/is-name-unique`,

  //Artifact
  artifactListUrl: `${host}/artifact/{conversationId}/items`,
  artifactDetailUrl: `${host}/artifact/{conversationId}/{id}`,
  artifactCreateUrl: `${host}/artifact`,

  // 时序卡片元数据
  timeSequenceCardMetadataListUrl: `${host}/artifact_metadata_item/paginated`,
  timeSequenceCardMetadataCreateUrl: `${host}/artifact_metadata_item`,
  timeSequenceCardMetadataDetailUrl: `${host}/artifact_metadata_item/{id}`,
  timeSequenceCardMetadataUpdateUrl: `${host}/artifact_metadata_item/{id}`,
  timeSequenceCardMetadataDeleteUrl: `${host}/artifact_metadata_item/{code}/{isDeleted}`,
  timeSequenceCardContentSchemaUrl: `${host}/artifact_metadata_item/content_json_schema`,
  timeSequenceCardContentSchemaValidateUrl: `${host}/artifact_metadata_item/content_json_schema/validate`,
  timeSequenceCardMetadataToggleUrl: `${host}/artifact_metadata_item/{code}/{isEnabled}`,
  timeSequenceCardContentExampleUrl: `${host}/artifact_metadata_item/content/example`,

  // AcpServer
  acpServerSaveUrl: `${host}/acp_server/save`,
  acpServerTransportTypesUrl: `${host}/acp_server/transport_types`,
  acpServerListUrl: `${host}/acp_server/list`,
  acpServerDetailUrl: `${host}/acp_server/{serverId}`,
  acpServerDeleteUrl: `${host}/acp_server/{serverId}`,
  acpServerNameCheckUrl: `${host}/acp_server/name/{name}`,
  acpServerToolsUrl: `${host}/acp_server/tools/{id}`,
  acpServerPingUrl: `${host}/acp_server/ping`,
  acpServerAvailableUrl: `${host}/acp_server/available/{id}`,
  acpServerToolsListUrl: `${host}/acp_server/tools/{id}`,

  // application
  applicationUrl: `${host}/application`,
  applicationListUrl: `${host}/applications`,
  applicationDetailUrl: `${host}/application/{id}`,
  applicationCreateUrl: `${host}/application`,
  applicationBindAgentsUrl: `${host}/application/bind-agents`,
  applicationBindArtifactsUrl: `${host}/application/bind-artifacts`,
  applicationLabelUrl: `${host}/application/labels`,
  applicationImportUrl: `${host}/application/import/zip`,
  applicationNameCheckUrl: `${host}/application/name`,
  applicationPublishUrl: `${host}/application/publish/{applicationId}`,
  applicationExportUrl: `${host}/application/{applicationId}/export/zip`,
};
