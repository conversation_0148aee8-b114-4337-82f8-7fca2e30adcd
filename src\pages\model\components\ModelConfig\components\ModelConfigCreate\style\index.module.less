.addModelContainer {
    height: calc(100vh - 88px); // 确保容器占满视口高度
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    .addModelTitle {
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f5;
    }

    .modelIcon {
        font-weight: 600;
        font-size: 14px;
        color: #5c5c5c;
    }

    .required {
        font-weight: 400;
        font-size: 12px;
        color: #4b5cf2;
    }

    .Card {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        border-left: 1px solid #ebebeb;
    }

    .idInput,
    .nameInput,
    .embeddingDimensionInput,
    .temperatureInput,
    .deployNameInput,
    .modelVersionInput,
    .apiVersionInput,
    .groupInput {
        padding: 8px 12px;
        background-color: transparent;
        border-radius: 8px;
        border: 1px solid #ebebeb;

        &::placeholder {
            color: #d6d6d6;
        }

        &:hover {
            background-color: #fafafa;
            border-color: #ebebeb;
        }
    }

    .Switchlabel {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: #333333;
    }

    .SwitchTitle {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #adadad;
    }

    .tagFormItem {
        padding-bottom: 24px;
        border-bottom: 1px solid #ebebeb;
        
        // 添加标签列表样式
        .selectedItemList {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 8px;
            background-color: #fcfcfc;
            border: 1px solid #f5f5f5;
            border-radius: 8px;
        }

        .selectedItemRow {
            width: 100%;

            :global(.arco-input) {
                padding: 0;
                border: none;
                border-radius: 0;
                background-color: transparent;
            }

            :global(.arco-input-inner-wrapper) {
                padding: 8px 8px 8px 12px;
                border: 1px solid #f5f5f5;
                border-radius: 8px;
                background-color: #ffffff;

                &:hover {
                    background-color: #ffffff;
                }
            }
        }

        .deleteIcon {
            cursor: pointer;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    /* Tag 样式 */
    .tagContainer {
        background-color: #fcfcfc;
        padding: 8px;
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        display: flex;
        flex-wrap: wrap;
        margin-top: 8px;
        width: 100%;

        .tagItem {
            height: 40px;
            width: 100%;
            padding: 8px 12px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #5c5c5c;
        }
    }

    .addTagBtn {
        cursor: pointer;
        padding: 8px 24px;
        background-color: #ffffff !important;
        border: 1px solid #ebebeb !important;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;
    }

    .labelExact {
        font-weight: 400;
        font-size: 12px;
        color: #adadad;
    }

    .upload {
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #ebebeb;
        border-radius: 4px;
        padding: 8px 16px 8px 8px;
        font-weight: 400;
        font-size: 14px;
        color: #5c5c5c;
    }

    .addModelHeader {
        display: flex;
        margin-bottom: 24px;
        width: 100%;

        .iconAndName {
            display: flex;
            align-items: center;
            width: 100%;

            .addModelIconWrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 72px;
                height: 72px;
                border-radius: 8px;
                background-color: #f5f5f5;
                flex-shrink: 0;

                .addModelIcon {
                    width: 72px;
                    height: 72px;
                }
            }

            .divider {
                width: 1px;
                height: 72px;
                background-color: #f5f5f5;
                margin: 0 24px;
            }

            .nameFormContainer {
                flex: 1;

                .nameFormItem {
                    margin-bottom: 0;
                }
            }
        }
    }

    .switchContainer {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        .switchLeftContent {
            display: flex;
            flex-direction: column;
        }

        .switchRightContent {
            display: flex;
            justify-content: flex-end;
        }
    }

    .requiredIcon {
        color: #4455f2;
        font-size: 15px;
        line-height: 16px;
        margin-left: 4px;
        position: relative;
        top: -1px;
        font-weight: bold;
    }

    :global(.arco-form-item) {
        margin-bottom: 24px;

        // 隐藏 Arco Design 默认的必填标识
        :global(.arco-form-item-symbol) {
            display: none;
        }
    }

    :global(.arco-form-label-item > label) {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: #333333;
    }

    :global(.arco-space-item) {
        margin-bottom: 0;
        margin-right: 0 !important;
    }

    :global(.arco-tag-checked) {
        padding: 8px 16px 8px 8px;
        background-color: white;
        border-radius: 4px;
        border: 1px solid #ebebeb;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    :global(.arco-tag-icon) {
        box-sizing: border-box;
        height: 24px;
        width: 24px;
    }

    :global(.arco-card-size-default .arco-card-body) {
        padding: 0;
    }

    :global(.arco-switch-checked) {
        background-color: #4b5cf2 !important;
    }

    :global(.arco-switch) {
        background-color: #d6d6d6;
    }

    :global(.arco-input-inner-wrapper) {
        padding: 8px;
        background-color: transparent;
        border-radius: 8px;
        border: 1px solid #ebebeb;
        transition: all 0.3s;

        :global(.arco-input) {
            padding-top: 0;
            padding-bottom: 0;
            padding-left: 8px;
        }

        &:hover {
            background-color: #fafafa;
            border-color: #ebebeb;
        }

        ::placeholder {
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
        }
    }

    :global(.arco-input.arco-input-size-large) {
        padding: 8px 12px;
        background-color: transparent;
        border: 1px solid #ebebeb;
        border-radius: 8px;

        ::placeholder {
            font-weight: 400;
            font-size: 14px;
            color: #d6d6d6;
        }

        :global(.arco-input) {
            padding-top: 0;
            padding-bottom: 0;
            padding-left: 8px;
        }
    }

    :global(.arco-form) {
        height: 100%;
    }

    :global(.arco-row) {
        height: 100%;
    }

    // :global(.arco-col-12) {
    //     height: 100%;
    // }

    :global(.arco-select-size-default.arco-select-single .arco-select-view) {
        padding: 8px 12px;
        height: auto;
        line-height: 24px;
        font-weight: 400;
        font-size: 14px;
        color: #d6d6d6;
        border-radius: 8px;
        border: 1px solid #ebebeb;
        background-color: transparent;

        &:hover {
            background-color: #fafafa;
            border-color: #ebebeb;
        }

        :global(.arco-select-prefix) {
            margin-right: 4px;
        }

        :global(.arco-select-view-input) {
            &::placeholder {
                color: #d6d6d6;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
            }
        }

        :global(.arco-select-view-value) {
            &::placeholder {
                color: #d6d6d6;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
            }
        }
    }

    .cancelButton,
    .submitButton {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px 24px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        transition: all 0.3s;
    }

    .cancelButton {
        background-color: #ffffff;
        border: 1px solid #ebebeb;
        color: #5c5c5c;
        margin-right: 8px;

        &:hover {
            background-color: #fafafa !important;
            border-color: #f5f5f5 !important;
        }
    }

    .submitButton {
        background-color: #4b5cf2;
        color: #ffffff;

        &:hover {
            background-color: #4152e9 !important;
            color: #ffffff !important;
        }
    }

    .submitButtonDisabled {
        background-color: #c3c8fa !important;
        color: #ffffff !important;
        cursor: not-allowed;

        &:hover {
            background-color: #c3c8fa !important;
        }
    }

    /* 覆盖 Arco Design 默认的禁用按钮样式 */
    :global(.arco-btn.arco-btn-primary[disabled]) {
        background-color: #c3c8fa !important;
        color: #ffffff !important;
        border-color: #c3c8fa !important;
    }
}