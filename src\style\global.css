@import 'nprogress/nprogress.css';

html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 14px;
  background-color: var(--color-bg-1);
}

.chart-wrapper .bizcharts-tooltip {
  background: linear-gradient(304.17deg,
      rgba(253, 254, 255, 0.6) -6.04%,
      rgba(244, 247, 252, 0.6) 85.2%) !important;
  border-radius: 6px;
  backdrop-filter: blur(10px);
  padding: 8px !important;
  width: 180px !important;
  opacity: 1 !important;
}

body[arco-theme='dark'] .chart-wrapper .bizcharts-tooltip {
  background: linear-gradient(304.17deg,
      rgba(90, 92, 95, 0.6) -6.04%,
      rgba(87, 87, 87, 0.6) 85.2%) !important;
  backdrop-filter: blur(10px);
  border-radius: 6px;
  box-shadow: none !important;
}

.arco-menu-inline-header {
  line-height: 48px !important;
}

/* .custom-menu-div {
  width: 87%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #a6a6a6;
} */
.custom-menu-div.selected {
  font-weight: 600 !important;
  color: #333333;
}

.arco-menu-inline-header .arco-menu-icon-suffix {
  padding: 6px 6px;
  border-radius: 4px;
}

.arco-menu-inline-header:hover .arco-menu-icon-suffix:hover {
  background-color: rgba(87, 87, 87, 0.06);
}

.arco-menu-inline-header:hover .arco-menu-icon-suffix:hover svg {
  color: #000000 !important;
}

strong {
  display: inline-block;
}