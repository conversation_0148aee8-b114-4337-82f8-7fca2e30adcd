import React, { useState, useEffect, useRef } from 'react';
import {
    Card,
    Button,
    Input,
    Typography,
    Space,
    Select,
    Spin,
    Modal,
    Form,
    Message,
    Popover,
    Grid,
} from '@arco-design/web-react';
import IconSearch from '@/assets/model/IconSearch.svg';
import IconSupplier from '@/assets/model/IconSupplier.svg';
import IconSupplier2 from '@/assets/model/IconSupplier2.svg';
import IconSupplier3 from '@/assets/model/IconSupplier3.svg';
import IconSupplier4 from '@/assets/model/IconSupplier4.svg';
import IconSupplierPlus from '@/assets/model/IconSupplierPlus.svg';
import IconClose from '@/assets/model/IconClose.svg';
import IconAction from '@/assets/model/IconAction.svg';
import IconSortType from '@/assets/model/IconSortType.svg';
import IconEmptyModel from '@/assets/model/IconEmptyModel.svg';
import styles from './style/index.module.less';
import {
    getLlmProviderList,
    createLlmProvider,
    updateLlmProvider,
    deleteLlmProvider,
} from '@/lib/services/llm-model-service';

const { Text, Paragraph } = Typography;
const Option = Select.Option;
const FormItem = Form.Item;
const { Row } = Grid;

// 创建一个禁用默认必填标记的表单项组件
const FormItemWithoutRequiredMark = (props) => {
    return <FormItem {...props} requiredSymbol={false} />;
};

// 创建自定义标签组件，与SecretKey组件保持一致
const CustomLabel = ({ label, required }) => {
    return (
        <Space>
            <span>{label}</span>
            {required && <Text className={styles.requiredIcon}>*</Text>}
        </Space>
    );
};

function Supplier() {
    const [suppliers, setSuppliers] = useState([]);
    const [filteredSuppliers, setFilteredSuppliers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [modalMode, setModalMode] = useState('view'); // 'view' for "供应商详情", 'create' for "新增供应商"
    const [selectedSupplier, setSelectedSupplier] = useState(null);
    const [form] = Form.useForm(); // 创建Form实例
    const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
    const [supplierToDelete, setSupplierToDelete] = useState(null);
    const [searchText, setSearchText] = useState('');
    const [sortType, setSortType] = useState('createTime');
    const [formValid, setFormValid] = useState(false);
    const [isEditing, setIsEditing] = useState(false); // 是否处于编辑状态
    const [formHasChanged, setFormHasChanged] = useState(false); // 表单是否发生变化
    const originalFormValuesRef = useRef({}); // 使用useRef存储原始表单值
    const supplierIcons = [
        IconSupplier,
        IconSupplier2,
        IconSupplier3,
        IconSupplier4,
    ];

    // 获取供应商图标的函数
    const getSupplierIcon = (id) => {
        const supplier = suppliers.find((s) => s.id === id);
        if (supplier?.logo) {
            return (
                <img src={supplier.logo} alt="logo" style={{ width: 24, height: 24 }} />
            );
        }
        // 使用简单的哈希函数生成索引
        const hash = id
            .split('')
            .reduce((acc, char) => acc + char.charCodeAt(0), 0);
        const index = hash % supplierIcons.length;
        const IconComponent = supplierIcons[index];
        return <IconComponent />;
    };

    useEffect(() => {
        fetchSuppliers();
    }, []);

    // 当suppliers或搜索/排序条件改变时，更新过滤后的供应商列表
    useEffect(() => {
        filterAndSortSuppliers();
    }, [suppliers, searchText, sortType]);

    const fetchSuppliers = async () => {
        try {
            setLoading(true);
            const data = await getLlmProviderList();

            // 处理获取到的数据，添加标签信息
            const formattedSuppliers = data.map((supplier) => ({
                id: supplier.id,
                name: supplier.name || supplier.provider, // 使用 name 或 provider 作为显示名称
                description:
                    supplier.description || `${supplier.provider} 提供的模型服务`,
                logo: supplier.logo,
                provider: supplier.provider,
                // tags: getProviderTags(supplier.provider),
                createdTime: supplier.createdTime,
                updatedTime: supplier.updatedTime,
            }));

            setSuppliers(formattedSuppliers);
            setError(null);
        } catch (err) {
            console.error('获取供应商列表失败:', err);
            setError('获取供应商数据失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    };

    // 筛选和排序供应商列表
    const filterAndSortSuppliers = () => {
        let result = [...suppliers];

        // 搜索过滤
        if (searchText) {
            const lowerCaseSearch = searchText.toLowerCase();
            result = result.filter(
                (supplier) =>
                    supplier.name.toLowerCase().includes(lowerCaseSearch) ||
                    supplier.description.toLowerCase().includes(lowerCaseSearch) ||
                    supplier.provider.toLowerCase().includes(lowerCaseSearch)
            );
        }

        // 排序
        switch (sortType) {
            case 'createTime':
                result.sort(
                    (a, b) =>
                        new Date(b.createdTime || 0).getTime() -
                        new Date(a.createdTime || 0).getTime()
                );
                break;
            case 'updateTime':
                result.sort((a, b) => {
                    // 处理 updatedTime 可能为 null 的情况
                    const timeA = a.updatedTime ? new Date(a.updatedTime).getTime() : 0;
                    const timeB = b.updatedTime ? new Date(b.updatedTime).getTime() : 0;
                    return timeB - timeA;
                });
                break;
            case 'name':
                result.sort((a, b) => a.name.localeCompare(b.name));
                break;
            default:
                break;
        }

        setFilteredSuppliers(result);
    };

    // 验证表单，检查部署名称是否已输入
    const validateForm = () => {
        const provider = form.getFieldValue('provider');
        if (provider && provider.trim() !== '') {
            setFormValid(true);
        } else {
            setFormValid(false);
        }
    };

    // 检查表单是否发生变化
    const checkFormChanged = (allValues) => {
        const currentValues = allValues || form.getFieldsValue();
        const originalValues = originalFormValuesRef.current;

        // 深度比较对象
        const hasChanged = JSON.stringify(currentValues) !== JSON.stringify(originalValues);
        setFormHasChanged(hasChanged);
        return hasChanged;
    };

    // 处理表单字段变化
    const handleFormValuesChange = (changedValues, allValues) => {
        if ('provider' in changedValues) {
            validateForm();
        }

        // 只有在编辑模式下才检查表单是否发生变化
        if (modalMode === 'view' && isEditing) {
            checkFormChanged(allValues);
        }
    };

    // 处理编辑按钮点击
    const handleEdit = () => {
        setIsEditing(true);
        // 保存当前表单值作为原始值，用于后续比较是否有变化
        originalFormValuesRef.current = JSON.parse(
            JSON.stringify(form.getFieldsValue())
        );
        setFormHasChanged(false);
    };

    // 处理排序变化
    const handleSort = (value) => {
        setSortType(value);
    };

    // 处理搜索
    const handleSearch = (value) => {
        setSearchText(value);
    };

    // 处理"添加供应商"按钮
    const handleAddSupplier = () => {
        form.resetFields(); // 重置表单字段
        setModalMode('create');
        setFormValid(false); // 重置表单验证状态
        setModalVisible(true);
    };

    // 处理"查看"供应商详情
    const handleViewSupplier = (supplier) => {
        const formValues = {
            displayName: supplier.name || '',
            provider: supplier.provider || '',
            description: supplier.description || '',
        };

        form.setFieldsValue(formValues);
        setSelectedSupplier(supplier);
        setModalMode('view');
        setFormValid(true); // 查看模式下表单默认有效
        setIsEditing(false); // 初始为非编辑状态
        originalFormValuesRef.current = JSON.parse(JSON.stringify(formValues)); // 设置原始表单值
        setFormHasChanged(false); // 重置表单变化状态
        setModalVisible(true);
    };

    // 处理删除确认
    const handleDeleteConfirm = (supplier) => {
        setSupplierToDelete(supplier);
        setConfirmDeleteVisible(true);
    };

    // 确认删除
    const handleConfirmDelete = async () => {
        if (supplierToDelete) {
            try {
                const success = await deleteLlmProvider(supplierToDelete.id);
                if (success) {
                    await fetchSuppliers(); // 重新获取供应商列表
                    Message.success('供应商删除成功！');
                } else {
                    Message.error('删除供应商失败，请重试！');
                }
            } catch (error) {
                console.error('删除供应商失败:', error);
                Message.error('删除供应商失败，请检查网络或联系管理员！');
            } finally {
                setConfirmDeleteVisible(false);
                setSupplierToDelete(null);
            }
        }
    };

    // 取消删除
    const handleCancelDelete = () => {
        setConfirmDeleteVisible(false);
        setSupplierToDelete(null);
    };

    // 处理取消按钮
    const handleCancel = () => {
        if (isEditing && modalMode === 'view') {
            // 如果在编辑状态，取消时恢复原始值
            // form.setFieldsValue(originalFormValuesRef.current);
            setModalVisible(false);
            setIsEditing(false);
            setFormHasChanged(false);
        } else {
            // 关闭模态框
            setModalVisible(false);
            setSelectedSupplier(null);
            setIsEditing(false);
            setFormHasChanged(false);
            form.resetFields();
        }
    };

    // 处理表单提交
    // const handleSubmit = () => {
    //     // 如果不是编辑状态，则切换到编辑状态
    //     if (modalMode === 'view' && !isEditing) {
    //         handleEdit();
    //         return;
    //     }

    //     // 如果表单没有变化，不执行保存
    //     if (modalMode === 'view' && isEditing && !formHasChanged) {
    //         return;
    //     }

    //     form
    //         .validate()
    //         .then(async (values) => {
    //             try {
    //                 if (modalMode === 'create') {
    //                     // 构造创建供应商的请求数据
    //                     const requestData = {
    //                         name: values.displayName,
    //                         provider: values.provider,
    //                         description: values.description,
    //                     };

    //                     // 调用创建供应商API
    //                     const success = await createLlmProvider(requestData);
    //                     if (success) {
    //                         await fetchSuppliers(); // 重新获取供应商列表
    //                         Message.success('供应商创建成功！');
    //                         setModalVisible(false);
    //                         setSelectedSupplier(null);
    //                         form.resetFields();
    //                     } else {
    //                         Message.error('创建供应商失败，请重试！');
    //                     }
    //                 } else if (modalMode === 'view' && isEditing) {
    //                     // 构造更新供应商的请求数据
    //                     const requestData = {
    //                         id: selectedSupplier.id,
    //                         name: values.displayName,
    //                         provider: values.provider,
    //                         description: values.description,
    //                     };

    //                     // 调用更新供应商API
    //                     const success = await updateLlmProvider(requestData);
    //                     if (success) {
    //                         await fetchSuppliers(); // 重新获取供应商列表
    //                         Message.success('供应商更新成功！');
    //                         // 更新原始值引用
    //                         originalFormValuesRef.current = JSON.parse(
    //                             JSON.stringify(values)
    //                         );
    //                         setIsEditing(false);
    //                         setFormHasChanged(false);
    //                         setModalVisible(false); // 关闭模态框
    //                         setSelectedSupplier(null); // 清除选中的供应商
    //                     } else {
    //                         Message.error('更新供应商失败，请重试！');
    //                     }
    //                 }
    //             } catch (error) {
    //                 console.error(
    //                     modalMode === 'create' ? '创建供应商失败:' : '更新供应商失败:',
    //                     error
    //                 );
    //                 Message.error(
    //                     `${modalMode === 'create' ? '创建' : '更新'
    //                     }供应商失败，请检查网络或联系管理员！`
    //                 );
    //             }
    //         })
    //         .catch((errors) => {
    //             console.log('Validation errors:', errors);
    //             Message.error('请检查表单字段！');
    //         });
    // };

    // 格式化日期函数
    const formatDate = (dateString) => {
        if (!dateString) return ''; // 处理空值
        const date = new Date(dateString);
        const year = date.getUTCFullYear();
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const day = String(date.getUTCDate()).padStart(2, '0');
        return `${year}/${month}/${day}`;
    };

    // 渲染内容
    const renderContent = () => {
        if (loading) {
            return (
                <div className={styles.loadingContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ padding: 24 }}>
                            <Spin tip="加载中..." />
                        </div>
                    </Space>
                </div>
            );
        }

        if (error) {
            return (
                <div className={styles.errorContainer}>
                    <Text type="error">{error}</Text>
                </div>
            );
        }

        if (filteredSuppliers.length === 0) {
            return (
                <div className={styles.emptyContainer}>
                    <Space direction='vertical' size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <IconEmptyModel style={{ width: 80, height: 80 }} />
                        <Text type="secondary">{searchText ? '未找到匹配的供应商' : '暂无供应商数据'}</Text>
                    </Space>
                </div>
            );
        }

        return (
            <>
                {filteredSuppliers.map((supplier, index) => (
                    <Card key={supplier.id || index} className={styles.supplierCard} onClick={() => handleViewSupplier(supplier)}>
                        <div className={styles.supplierInfo}>
                            <div className={styles.cardContent}>
                                <Space direction='vertical' size={8}>
                                    <Space className={styles.nameWrapper} size={12}>
                                        <span className={styles.icon}>
                                            {getSupplierIcon(supplier.id)}
                                        </span>
                                        <Text className={styles.name}>{supplier.name}</Text>
                                    </Space>
                                    <Paragraph className={styles.description}>
                                        {supplier.description}
                                    </Paragraph>
                                </Space>
                            </div>
                            <Row className={styles.cardFooter}>
                                <Space size={4} className={styles.metaInfo}>
                                    <Text>@AI4C</Text>
                                    <Text>|</Text>
                                    <Text>
                                        {supplier.updatedTime
                                            ? `更新时间：${formatDate(supplier.updatedTime)}`
                                            : `创建时间：${formatDate(supplier.createdTime)}`}
                                    </Text>
                                </Space>
                                <Popover
                                    trigger="click"
                                    position="right"
                                    className={styles.popoverContent}
                                    content={
                                        <Space className={styles.popoverContent} direction='vertical' size={'mini'}>
                                            <Button
                                                className={`${styles.actionBtn} ${styles.deleteBtn}`}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    handleDeleteConfirm(supplier)
                                                }
                                                }
                                            >
                                                删除
                                            </Button>
                                        </Space>
                                    }
                                >
                                    <Button
                                        className={styles.triggerBtn}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                        }}>
                                        <IconAction />
                                    </Button>
                                </Popover>
                            </Row>
                        </div>
                    </Card>
                ))}
            </>
        );
    };

    return (
        <div className={styles.supplier}>
            <div className={styles.header}>
                <Button
                    type="primary"
                    className={styles.addSupplierBtn}
                    // onClick={handleAddSupplier}
                    style={{ visibility: 'hidden' }}
                >
                    新增供应商
                </Button>
                <Space size={'small'}>
                    <Text className={styles.supplierNumber}>
                        共 {filteredSuppliers.length} 个供应商
                    </Text>
                    <Input
                        prefix={<IconSearch />}
                        placeholder="AI搜索..."
                        style={{ width: 240 }}
                        onChange={(value) => handleSearch(value)}
                        allowClear
                    />
                    <Select
                        prefix={<IconSortType />}
                        placeholder="按创建时间排序"
                        style={{ width: 160 }}
                        onChange={handleSort}
                        value={sortType}
                    >
                        <Option value="createTime">按创建时间排序</Option>
                        <Option value="updateTime">按更新时间排序</Option>
                        <Option value="name">按名称排序</Option>
                    </Select>
                </Space>
            </div>

            <div className={styles.content}>
                {renderContent()}
            </div>

            {/* 供应商详情/新增供应商模态框 */}
            <Modal
                visible={modalVisible}
                title={modalMode === 'create' ? '新增供应商' : '详情'}
                onCancel={handleCancel}
                closeIcon={<IconClose />}
                className={styles.supplierDetailModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <Form form={form} autoComplete="off" layout="vertical" onValuesChange={handleFormValuesChange} requiredSymbol={false}>
                        <Row className={styles.supplierHeader}>
                            <div className={styles.iconAndName}>
                                <div className={styles.supplierIconWrapper}>
                                    <IconSupplierPlus className={styles.supplierIcon} />
                                </div>
                                <div className={styles.divider}></div>
                                <div className={styles.nameFormContainer}>
                                    <FormItem
                                        label="名称"
                                        field="displayName"
                                        className={styles.nameFormItem}
                                        style={{ marginBottom: '0px' }}
                                    >
                                        <Input
                                            placeholder="请输入"
                                            disabled={modalMode === 'view' && !isEditing}
                                        />
                                    </FormItem>
                                </div>
                            </div>
                        </Row>

                        <FormItemWithoutRequiredMark
                            label={<CustomLabel label="部署名称" required={true} />}
                            field="provider"
                            rules={[{ required: true, message: '请输入部署名称' }]}
                        >
                            <Input
                                placeholder="请输入"
                                disabled={modalMode === 'view' && !isEditing}
                            />
                        </FormItemWithoutRequiredMark>

                        <FormItemWithoutRequiredMark
                            label={<CustomLabel label="描述" required={false} />}
                            field="description"
                        >
                            <Input.TextArea
                                placeholder="用简单明了的话描述~"
                                rows={4}
                                maxLength={500}
                                className={styles.descriptionInput}
                                showWordLimit
                                disabled={modalMode === 'view' && !isEditing}
                            />
                        </FormItemWithoutRequiredMark>
                    </Form>
                </div>
                <div className={styles.modalFooter}>
                    <Button onClick={handleCancel} className={styles.cancelBtn}>
                        {isEditing && modalMode === 'view' ? '取消' : '取消'}
                    </Button>
                    {/* <Button
                        type="primary"
                        onClick={handleSubmit}
                        className={`${styles.saveBtn} ${(modalMode === 'create' && !formValid) ||
                            (modalMode === 'view' && isEditing && !formHasChanged)
                            ? styles.disabledBtn : ''
                            }`}
                        disabled={(modalMode === 'create' && !formValid) ||
                            (modalMode === 'view' && isEditing && !formHasChanged)}
                    >
                        {modalMode === 'create' ? '创建' : (isEditing ? '保存' : '编辑')}
                    </Button> */}
                </div>
            </Modal>

            {/* 删除确认弹窗 */}
            <Modal
                visible={confirmDeleteVisible}
                title="删除供应商"
                onCancel={handleCancelDelete}
                closeIcon={<IconClose />}
                className={styles.confirmDeleteModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <Text className={styles.modalContentText}>
                        {`供应商 ${supplierToDelete?.name || ''
                            } 将被删除，请确认您是否要删除？`}
                    </Text>
                </div>
                <div className={styles.modalFooter}>
                    <Space>
                        <Button
                            onClick={handleCancelDelete}
                            className={styles.cancelDeleteBtn}
                        >
                            取消
                        </Button>
                        <Button
                            type="primary"
                            status="danger"
                            onClick={handleConfirmDelete}
                            className={styles.confirmDeleteBtn}
                        >
                            删除
                        </Button>
                    </Space>
                </div>
            </Modal>
        </div>
    );
}

export default Supplier;
