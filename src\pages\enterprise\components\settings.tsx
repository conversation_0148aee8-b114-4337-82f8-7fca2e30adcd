import React from 'react';
import { Card, Typography, Form, Input, Button, Space } from '@arco-design/web-react';
import styles from './style/index.module.less';

const { Title, Text } = Typography;
const FormItem = Form.Item;

function EnterpriseSettings() {
    const [form] = Form.useForm();

    const handleSave = () => {
        form.validate().then((values) => {
            console.log('企业设置保存:', values);
            // 这里可以处理保存设置的逻辑
        }).catch((errors) => {
            console.log('表单验证失败:', errors);
        });
    };

    return (
        <div className={styles.container}>
            <Card className={styles.infoCard}>
                <Title heading={4}>企业设置</Title>
            </Card>

            <Card className={styles.settingsCard} style={{ marginTop: 16 }}>
                <Form form={form} layout="vertical" initialValues={{
                    enterpriseName: '智用研究院',
                    description: '专注于人工智能应用研究的科研机构',
                    website: 'https://ai4c.cn',
                    email: '<EMAIL>'
                }}>
                    <FormItem label="企业名称" field="enterpriseName" rules={[{ required: true, message: '请输入企业名称' }]}>
                        <Input placeholder="请输入企业名称" />
                    </FormItem>

                    <FormItem label="企业描述" field="description">
                        <Input.TextArea placeholder="请输入企业描述" />
                    </FormItem>

                    <FormItem label="企业网站" field="website">
                        <Input placeholder="请输入企业网站" />
                    </FormItem>

                    <FormItem label="联系邮箱" field="email" rules={[{ required: true, message: '请输入联系邮箱' }]}>
                        <Input placeholder="请输入联系邮箱" />
                    </FormItem>

                    <FormItem>
                        <Space size="large">
                            <Button type="primary" onClick={handleSave}>
                                保存设置
                            </Button>
                            <Button>
                                取消
                            </Button>
                        </Space>
                    </FormItem>
                </Form>
            </Card>
        </div>
    );
}

export default EnterpriseSettings; 