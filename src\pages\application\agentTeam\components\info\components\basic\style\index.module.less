  /* 全局样式控制 */
:global(body) {
  overflow: hidden;
}

  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
    /* 对于基于 WebKit 的浏览器 */
  }

  /* 对于IE和Edge */
  html {
    -ms-overflow-style: none;
    /* IE 和 Edge */
  }

  /* 对于Firefox */
  * {
    scrollbar-width: none;
    /* Firefox */
  }

  :global(.arco-tabs-content) {
    background-color: #FFFFFF;
    width: 100%;
    height: 100vh;
    padding-top: 0px !important;
  }

  .container {
    display: flex;
    height: calc(100vh - 115px);
    flex-direction: column;
    flex: 1;
    overflow: auto;
    position: relative;

    .tabs {
      :global(.arco-tabs-header-nav::before) {
        display: none;
      }

      :global(.arco-tabs-header) {
        width: 100%;
      }

      :global(.arco-tabs-header-ink) {
        display: none;
      }

      :global(.arco-tabs-header-title) {
        padding: 0;
        font-weight: 600;
        font-size: 20px;
        color: #a6a6a6;
        margin-left: 7px !important;
        width: 100%;
        padding-bottom: 16px;
        border-bottom: 1px solid RGBA(0, 0, 0, .08);

        &:hover {
          color: #a6a6a6;
        }
      }

      :global(.arco-tabs-header-title-active) {
        color: #333333;

        &:hover {
          color: #333333;
        }
      }

      :global(.arco-tabs-content) {
        background-color: #FFFFFF;
        width: 100%;
        height: 100vh;
      }
    }

    :global(.arco-popover-content) {
      padding: 0 12px 8px 12px;

      :global(.arco-popover-inner-content) {
        p {
          cursor: pointer;
          padding-left: 8px;
          display: flex;
          align-items: center;
          height: 34px !important;
          width: 104px !important;
          text-align: left;
          color: RGBA(0, 0, 0, .65);
          border-radius: 4px;

          &:hover {
            background-color: RGBA(0, 0, 0, .02);
          }
        }

        .red {
          color: RGBA(213, 73, 65, .95) !important;
        }
      }
    }

    .subtitle {
      font-size: 14px;
      font-weight: 600;
      color: RGBA(0, 0, 0, .65);
    }

    .subtitlePlaceholder {
      font-size: 12px;
      font-weight: 400;
      color: RGBA(0, 0, 0, .35);
    }

    .customContainer {
      width: 100%;
      padding: 8px 0 0 8px;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      .inputrow {
        width: 100%;
        border-radius: 4px;
        border: 1px solid RGBA(0, 0, 0, .08);
        height: 40px;
      }

      .container {
        width: calc(51% - 24px);
        height: calc(100vh - 200px);
        padding-right: 24px;
        position: relative;

        .operateBox {
          margin-top: 10px;
          border-radius: 4px;
          border: 1px dashed RGBA(0, 0, 0, .16);
          height: 92px;
          display: flex;
          padding: 0 24px;
          width: calc(100% - 48px);
          justify-content: space-between;
          align-items: center;

          .operateTitle {
            font-size: 16px;
            font-weight: 600;
            color: RGBA(0, 0, 0, .8);
          }

          .operateSubtitle {
            font-size: 12px;
            font-weight: 400;
            color: RGBA(0, 0, 0, .35);
          }

          .operateBut {
            padding: 0;
            border: 0;
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 12px;
            background-color: RGBA(248, 248, 254, 1);

            .icon {
              font-size: 6px;
            }
          }

          .operateButInit {
            padding: 0;
            border: 0;
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 12px;
            background-color: RGBA(13, 41, 254, .95);

            .icon {
              font-size: 6px;
            }
          }

          .operateText {
            width: 80px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;

            .text {
              font-size: 14px;
              font-weight: 400;
              color: RGBA(0, 0, 0, .65);
            }
          }
        }

        .customBlock {
          width: 100%;
          margin-bottom: 0px;

          :global(.arco-form-item) {
            margin-bottom: 8px;

            :global(.arco-input-tag) {
              background-color: #FFFFFF !important;
              border-radius: 4px;
              border: 1px solid RGBA(0, 0, 0, .08);
              height: 40px;

              :global(.arco-input-tag-view) {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 40px;
              }

              :global(.arco-input-tag-inner) {
                input {
                  color: RGBA(0, 0, 0, .8);
                  font-size: 14px;

                  &::placeholder {
                    font-size: 14px;
                    font-weight: 400;
                    color: RGBA(0, 0, 0, .16);
                  }
                }
              }

              :global(.arco-input-tag-suffix) {
                border-radius: 12px;
                color: RGBA(0, 0, 0, .35);
                width: 24px;
                height: 24px;
                display: flex;
                justify-content: center;
                align-items: center;
                padding-right: 0 !important;
                font-size: 13px;

                &:hover {
                  background-color: RGBA(0, 0, 0, .02);
                  color: RGBA(0, 0, 0, .65);
                  cursor: pointer;
                }
              }
            }
          }

          .addLabelBut {
            border-radius: 4px !important;
            border: 1px solid RGBA(0, 0, 0, .08) !important;
            height: 40px;
            color: RGBA(0, 0, 0, .65);
            font-size: 14px;
            font-weight: 400;

            &:disabled {
              color: RGBA(0, 0, 0, .35);
            }
          }
        }
      }
    }

    .labelContainer {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      width: 50%;
      border-radius: 8px;
      flex: none;
      order: 1;
      align-self: stretch;
      flex-grow: 0;
      margin-top: 8px;


      .selectedItemList {
        box-sizing: border-box;
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 8px;
        border-radius: 4px;
        min-height: 48px;
        background-color: RGBA(0, 0, 0, 0.01);
        transition: all 0.3s ease;
        border: 1px solid RGBA(0, 0, 0, .08);
        border-radius: 8px;

        &:hover {
          border-color: RGBA(0, 0, 0, 0.15);
        }

        .selectedItemRow {
          width: 100%;
          display: flex;
          align-items: center;
          border-radius: 8px;

          .selectedItemCol {
            display: flex;
            align-items: center;
            gap: 8px;
            width: 100%;

            :global(.arco-input-inner-wrapper) {
              height: 40px;
              border-radius: 8px;
              background-color: #fff;
              border: 1px solid RGBA(0, 0, 0, .08);
            }

            .deleteIcon {
              width: 24px;
              height: 24px;
              cursor: pointer;
              opacity: 0.65;
              transition: opacity 0.2s ease;

              &:hover {
                opacity: 1;
              }
            }
          }
        }
      }
    }

    .titleRow {
      margin-top: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      width: 50%;

      .titleContent {
        display: flex;
        flex-direction: column;
        gap: 6px;
      }

      .switchContainer {
        display: flex;
        align-items: center;
        gap: 8px;
        height: 40px;
        width: 76px;
        justify-content: center;
        border-radius: 8px;
        background-color: transparent;
        padding: 0 2px;

        :global(.arco-switch) {
          min-width: 40px;
          height: 24px;
        }

        :global(.arco-switch-checked) {
          background-color: RGBA(13, 41, 254, 0.95) !important;
        }
      }

      .addLabelBut {
        width: 76px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        border-radius: 8px;
        cursor: pointer;
        background-color: transparent;
        padding: 0 2px;

        :global(.arco-icon) {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          flex-shrink: 0;
        }

        .operateText {
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          display: flex;
          align-items: center;
          height: 100%;
        }

        &:hover {
          background-color: RGBA(0, 0, 0, 0.02);
        }
      }
    }

    .iconContainer {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 50%;

      .nameContainer {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        :global(.arco-form-item) {
          width: 100%;
          margin-bottom: 0px;

          :global(.arco-form-item-control-children) {
            width: 100%;
          }

          :global(.arco-input) {
            width: 100%;
          }
        }
      }

      .divider {
        width: 1px;
        height: 72px;
        background-color: rgba(0, 0, 0, 0.08);
        margin: 0 24px;
        flex-shrink: 0;
      }
    }

    .previewBox {
      width: 72px;
      height: 72px;
      border: 1px solid #e5e6eb;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      position: relative;
      background-color: #f7f8fa;
      cursor: pointer;
      transition: all 0.3s;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);
        opacity: 0;
        transition: opacity 0.3s;
        z-index: 1;
      }

      &:hover {
        border-color: #165dff;
        box-shadow: 0 0 8px rgba(22, 93, 255, 0.1);

        &::before {
          opacity: 1;
        }

        .uploadHoverIcon {
          opacity: 1;
        }
      }
    }

    .iconWrapper {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      &:hover {
        .removeIcon {
          opacity: 1;
        }
      }
    }

    .uploadHoverIcon {
      position: absolute;
      transition: all 0.3s;
      z-index: 2;
      width: 24px;
      height: 24px;
      object-fit: contain;
      opacity: 0;
    }

    .removeIcon {
      position: absolute;
      top: 8px;
      right: 8px;
      opacity: 0;
      transition: opacity 0.2s;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      padding: 4px;
      z-index: 1;

      &:hover {
        background-color: #fff;
        color: rgb(var(--danger-6));
      }
    }

    .placeholderBox {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      &:hover {
        .plusIcon {
          transform: scale(1.1);
        }
      }
    }

    .plusIcon {
      width: 100%;
      height: 100%;
      color: #86909c;
      transition: transform 0.3s;
    }

    .loadingWrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      color: #86909c;
      font-size: 12px;
    }

    .uploadButton {
      min-width: 80px;
      background-color: #fff !important;
      border: 1px solid #e5e6eb !important;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
    }
  }