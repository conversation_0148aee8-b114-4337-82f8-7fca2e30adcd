.customModal {
  width: 640px;
  padding: 32px;

  .tabs {

    :global(.arco-tabs-header-nav::before) {
      display: none;
    }

    :global(.arco-tabs-header-ink) {
      display: none;
    }

    :global(.arco-tabs-header-title) {
      padding: 0;
      font-weight: 600;
      font-size: 18px;

      color: #a6a6a6;

      &:hover {
        color: #a6a6a6;
      }
    }

    :global(.arco-tabs-header-title-active) {
      color: #333333;

      &:hover {
        color: #333333;
      }
    }

    :global(.arco-tabs-content) {
      padding-top: 24px;
    }

    :global(.arco-tabs-header-nav-line.arco-tabs-header-nav-horizontal > .arco-tabs-header-scroll .arco-tabs-header-title:first-of-type) {
      margin-left: 0;
    }

    :global(.arco-tabs-header-nav-line .arco-tabs-header-title) {
      margin: 0 12px;
    }

  }

  :global(.arco-modal-header) {
    padding: 0;
    border-bottom: 0;
    height: auto;

    :global(.arco-modal-title) {
      font-weight: 600;
      font-size: 18px;
      line-height: 24px;
      color: #333333;
      text-align: left;
    }
  }

  :global(.arco-modal-close-icon) {
    right: 38px;
    top: 38px;
  }

  :global(.arco-modal-content) {
    padding: 0;
    margin-bottom: 24px;

    :global(.arco-tree) {
      border-radius: 4px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      padding: 8px;
      max-height: 400px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 3px;
      }
    }
  }

  :global(.arco-modal-footer) {
    padding: 0;
    border-top: 0;
  }

  :global(.arco-tree) {
    border-radius: 4px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    padding: 8px;
  }

  :global(.arco-tree-node) {
    padding: 0 8px;

    &:hover {
      border-radius: 4px;
      background-color: rgba(252, 252, 252, 1);
    }

    :global(.arco-tree-node-title) {
      width: 100%;

      &:hover {
        background: transparent;
      }
    }

    :global(.arco-tree-node-switcher) {
      height: 54px;
    }
  }

  .customTreeRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 40px;
    margin-top: 4px;

    &:hover {
      background: transparent;
    }

    .customIcon {
      margin-right: 8px;

      :global(.arco-image-img) {
        width: 24px;
        height: 24px;
      }
    }
  }

  .customCheckbox {
    :global(.arco-checkbox-mask) {
      border-radius: 12px;
      height: 20px;
      width: 20px;
      // background-color: #4455f2;
    }

    :global(.arco-checkbox-checked .arco-checkbox-mask) {
      background-color: #4455f2;
    }


  }

  .searchBox {
    width: 320px;
    height: 40px;
    border-radius: 4px;
    outline: none;
    border: 1px solid RGBA(0, 0, 0, 0.08);
    font-size: 14px;
    font-weight: 400;
    background-color: #ffffff;
    margin-bottom: 8px;
    margin-top: 24px;

    :global(.arco-input-inner-wrapper) {
      background-color: #ffffff;
    }

    input::placeholder {
      color: RGBA(0, 0, 0, 0.65);
    }
  }

  .customFooterRow {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .cancelBut {
      border-radius: 4px;
      background-color: RGBA(0, 0, 0, 0.02);
      margin-right: 8px;
      height: 40px;
      width: 76px;

      span {
        font-weight: 600;
        color: RGBA(0, 0, 0, 0.65);
      }
    }

    .selectedCount {
      font-size: 14px;
      font-weight: 600;
      color: RGBA(0, 0, 0, 0.65);
    }

    .addBut {
      border-radius: 4px;
      background-color: RGBA(68, 85, 242, 0.04);
      height: 40px;
      width: 76px;

      span {
        font-weight: 600;
        color: RGBA(68, 85, 242, 0.95);
      }
    }
  }
}