import postcss from 'postcss';
import type { Plugin } from 'postcss';

/**
 * 这是我们的核心：一个自定义的 PostCSS 插件。
 * @param {object} opts - 插件选项，我们让 layer 的名字可以自定义。
 * @returns {import('postcss').Plugin}
 */
export const postCssPluginArcoLayer = (
  opts: {
    layer?: 'components' | 'utilities' | 'base' | 'arco';
  } = {}
): Plugin => {
  const layerName = opts.layer || 'components'; // 默认层级是 'components'
  const ARCO_DESIGN_PACKAGE = '@arco-design/web-react';
  const layerOrder = 'properties, theme, base, components, arco, utilities';
  return {
    postcssPlugin: 'postcss-plugin-arco-layer', // 插件名称
    // 使用 Root 钩子，它会在 postcss 处理每个 CSS 文件时执行
    Root(root) {
      const filePath = root.source?.input.file;

      // 关键判断：只处理来自 @arco-design/web-react 包内部的样式文件
      if (filePath && filePath.includes(ARCO_DESIGN_PACKAGE)) {
        // 保证选择css选择层级顺序
        const layerRuleOrder = postcss.atRule({
          name: 'layer',
          params: layerOrder,
        });
        // 创建一个新的 @layer 规则，例如 @layer components
        const layerRule = postcss.atRule({ name: 'layer', params: layerName });

        // 创建一个副本，因为当我们移动节点时，root.nodes 会被修改
        const nodes = root.nodes.slice();

        // 清空原始的 root
        root.removeAll();

        // 将所有节点添加到 layer 规则中
        layerRule.append(...nodes);

        // 将包含所有样式规则的 layer 添加回 root
        root.append(layerRule);
        root.prepend(layerRuleOrder);
      }
    },
  };
};
