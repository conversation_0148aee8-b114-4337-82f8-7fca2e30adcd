.avatar {
  width: 72px;
  height: 72px;
}

.userInfoContainer {
  :global(.arco-input) {
    padding: 8px 12px;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;

    &::placeholder {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #5c5c5c;
    }
  }

  :global(.arco-input-disabled) {
    color: #5c5c5c !important;
    -webkit-text-fill-color: #5c5c5c;
    background-color: #fafafa;
  }

  :global(.arco-card-body) {
    padding: 0;
  }

  :global(.arco-form-item) {
    margin-bottom: 16px;
  }

  :global(.arco-form-item-label) {
    font-weight: 500;
    color: #333333;
  }

  :global(.arco-form-label-item > label) {
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
    color: #333333;
  }


  .accountSection {

    .DeleteAccount {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 24px;
      border: 1px solid #ebebeb;
      border-radius: 8px;
      background-color: transparent;
      height: 40px;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      color: #d54941;
    }

    :global(.arco-btn-secondary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
      color: #d54941;
      background-color: #fef8f8;
      border-color: #ebebeb;
    }
  }
}


.userInfoHeader {
  display: flex;
  margin-bottom: 24px;
  width: 100%;

  :global(.arco-form-item) {
    margin-bottom: 0;
  }

  .avatarAndName {
    display: flex;
    align-items: center;
    width: 100%;

    .avatarWrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 72px;
      height: 72px;
      border-radius: 8px;
      flex-shrink: 0;
      position: relative;
      transition: all 0.2s ease;

      .uploadingOverlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .uploadingText {
          color: #ffffff;
          font-size: 12px;
          font-weight: 500;
        }
      }

      .hoverOverlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.2s ease;
        cursor: pointer;
        pointer-events: none; // 让遮罩不阻挡点击事件
        
        .hoverIcon {
          width: 20px;
          height: 20px;
          margin-bottom: 4px;
          fill: #ffffff;
        }
        
        .hoverText {
          color: #ffffff;
          font-size: 12px;
          font-weight: 500;
          text-align: center;
        }
      }

      &:hover .hoverOverlay {
        opacity: 1;
      }

      // 当有其他遮罩层时，隐藏hover遮罩
      .uploadingOverlay ~ .hoverOverlay {
        display: none;
      }
    }

    .divider {
      width: 1px;
      height: 72px;
      background-color: #f5f5f5;
      margin: 0 24px;
    }

    .nameFormContainer {
      flex: 1;
      position: relative;

      :global(.arco-form-item-wrapper) {
        width: 100%;
      }

      :global(.arco-input-wrapper) {
        margin-top: 8px;
      }

      :global(.arco-input) {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
      }

      :global(.arco-input-disabled) {
        color: #5c5c5c !important;
      }
    }

    .EditButton {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 24px;
      border: 1px solid #ebebeb;
      border-radius: 8px;
      height: 40px;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      color: #5c5c5c;
      margin-left: 8px;
      align-self: flex-end;
    }

    :global(.arco-btn-text:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
      color: #5c5c5c;
      background-color: #fafafa;
      border-color: #ebebeb;
    }
  }
}

.editNameModal {
  :global(.arco-input) {
    padding: 0 !important;
    border: none !important;
    border-radius: 0px !important;
    background-color: transparent;
    width: 100%;

    &::placeholder {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #d6d6d6;
    }
  }

  :global(.arco-input-inner-wrapper) {
    padding: 8px 12px;
    background-color: transparent;
    border: 1px solid #ebebeb;
    border-radius: 8px;

    :global(.arco-input-word-limit) {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #adadad;
    }
  }

  :global(.arco-modal-title) {
    margin-bottom: 16px !important;
  }

  .saveNameBtn {
    background-color: #4455f2;
    color: #ffffff;

    &:hover {
      background-color: #3a4ae0;
      color: #ffffff;
    }
  }

  .disabledSaveBtn {
    background-color: #c3c8fa !important;
    color: #ffffff !important;
    cursor: not-allowed;

    &:hover {
      background-color: #c3c8fa !important;
    }
  }

  :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
    background-color: #4152e9;
    color: #ffffff;
  }
}

.editNameModal,
.confirmDeleteModal {
  position: relative;
  padding: 24px;
  width: 480px;
  border-radius: 16px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

  .modalContent {
    display: flex;
    flex-direction: column;
    width: 100%;

    .modalContentText {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #adadad;
      margin-bottom: 16px;
    }

    .emailConfirmSection {
      display: flex;
      flex-direction: column;
      margin-bottom: 24px;
      width: 100%;

      .emailConfirmLabel {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: #333333;
        margin-bottom: 8px;
      }

      .emailFormItem {
        margin-bottom: 0;
        width: 100%;
      }
    }
  }

  .modalFooter {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .saveNameBtn,
    .cancelDeleteBtn,
    .confirmDeleteBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 18px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
    }

    .cancelDeleteBtn {
      background-color: #ffffff;
      color: #5c5c5c;
      border: 1px solid #ebebeb;

      &:hover {
        background-color: #fafafa;
        border-color: #ebebeb;
      }
    }

    .confirmDeleteBtn {
      background-color: #d54941;
      color: #ffffff;

      &:hover {
        background-color: #cd463e;
        color: #ffffff;
      }
    }

    .disabledDeleteBtn {
      background-color: rgba(213, 73, 65, 0.5) !important;
      color: #ffffff !important;
      cursor: not-allowed;

      &:hover {
        background-color: rgba(213, 73, 65, 0.5) !important;
      }
    }
  }

  /* 添加全局样式确保输入框占满宽度 */

  /* 覆盖栅格宽度 */
  :global(.arco-form-item .arco-col-19) {
    width: 100%;
    flex: 0 0 100%;
  }

  :global(.arco-form) {
    width: 100%;
  }

  :global(.arco-form-item) {
    width: 100%;
  }

  :global(.arco-form-item-control) {
    width: 100%;
  }

  :global(.arco-input-wrapper) {
    width: 100%;
  }

  :global(.arco-input) {
    padding: 8px 12px;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    background-color: transparent;
    width: 100%;

    &::placeholder {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #d6d6d6;
    }
  }

  :global(.arco-modal-header) {
    padding: 0 !important;
    height: auto;
    border-bottom: none !important;

    :global(.arco-modal-title) {
      font-weight: 600;
      font-size: 18px;
      line-height: 24px;
      color: #333333;
      margin-bottom: 4px;
      text-align: left;
    }
  }

  :global(.arco-modal-content) {
    padding: 0;
    width: 100%;
    height: 100%;
  }

  :global(.arco-modal-footer) {
    display: none;
  }

  :global(.arco-modal-close-icon) {
    position: absolute;
    right: 24px;
    top: 24px;
    font-size: 12px;
    cursor: pointer;
    color: var(--color-text-1);
  }

  :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
    background-color: #cd463e;
    color: #ffffff;
  }
}

:global {
  .arco-modal-mask {
    background: rgba(0, 0, 0, 0.08);
  }
}