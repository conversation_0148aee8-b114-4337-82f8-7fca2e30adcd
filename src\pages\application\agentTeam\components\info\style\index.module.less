.customContainer {
  .tabs {
    :global(.arco-tabs-header-nav::before) {
        display: none;
    }
    :global(.arco-tabs-header) {
      width: 100%;
      border-bottom: 1px solid RGBA(0, 0, 0, .08);
    }
    :global(.arco-tabs-header-ink) {
        display: none;
    }
  
    :global(.arco-tabs-header-title) {
      padding: 0;
      font-weight: 600;
      font-size: 20px;
      color: RGBA(0, 0, 0, .35);
      margin-left: 0px !important;
      padding-bottom: 16px;
      &:hover {
        color: RGBA(0, 0, 0, .8);
        background: none !important;
      }
    }
  
    :global(.arco-tabs-header-title-active) {
        color: #333333;
  
        &:hover {
            color: #333333;
        }
    }
  
    :global(.arco-tabs-content) {
        padding-top: 24px;
    }
  }

  .footer {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: #FFFFFF;
    border-top: 1px solid RGBA(0, 0, 0, .08);
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .operateButGroup {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 8px;
      margin: 16px 24px 24px 0;

      .text {
        font-size: 14px;
        font-weight: 600;
      }

      .but {
        border-radius: 8px;
        width: 76px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .cancelBut {
        background-color: RGBA(250, 250, 250, 1);
        border: 1px solid RGBA(0, 0, 0, .1);

        &:hover {
          background-color: RGBA(245, 245, 245, 1);
        }

        .text {
          color: RGBA(0, 0, 0, .65);
        }
      }

      .createBut {
        background-color: RGBA(13, 41, 254, .95);

        &:hover {
          background-color: RGBA(13, 41, 254, 1);
          box-shadow: 0 4px 8px rgba(13, 41, 254, 0.2);
        }

        .text {
          color: white;
        }
      }
    }
  }
}