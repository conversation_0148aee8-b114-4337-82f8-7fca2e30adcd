version: '1.0'
name: pipeline-20250319-1
displayName: pipeline-20250319-1
triggers:
  trigger: manual
  push:
    branches:
      precise:
        - test_v1.0
stages:
  - name: stage-3a8873c6
    displayName: 编译
    strategy: naturally
    trigger: auto
    executor:
      - qiu-tianfu
      - greenshade
    steps:
      - step: build@nodejs
        name: build_nodejs
        displayName: Nodejs 构建
        nodeVersion: 20.10.0
        commands:
          - '# 设置NPM源，提升安装速度'
          - npm config set registry https://registry.npmmirror.com
          - ''
          - '# 执行编译命令'
          - npm install && npm run build
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./dist
        caches:
          - ~/.npm
          - ~/.yarn
        notify: []
        strategy:
          retry: '0'
