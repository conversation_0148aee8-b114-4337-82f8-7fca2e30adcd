.aiAction {
    display: flex;
    flex-direction: column;

    .headerText {
        font-weight: 600;
        font-size: 20px;
        line-height: 32px;
        color: #333333;
        margin-bottom: 8px;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f5;

        .addActionBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 24px;
            background-color: #4d5ef3;
            font-weight: 500;
            font-size: 14px;
            line-height: 24px;
            color: #ffffff;
            border-radius: 8px;
            transition: all 0.3s;

            &:hover {
                background-color: #3144f1;
            }
        }

        //搜索输入框
        :global(.arco-input-inner-wrapper) {
            padding: 8px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            ::placeholder {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                color: #d6d6d6;
            }

            :global(.arco-input) {
                padding-top: 0;
                padding-bottom: 0;
                padding-left: 8px;
            }
        }

        //筛选select
        :global(.arco-select-size-default.arco-select-single .arco-select-view) {
            padding: 8px;
            height: auto;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            background-color: #ffffff;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            :global(.arco-select-prefix) {
                margin-right: 4px;
            }

            :global(.arco-select-view-input) {
                &::placeholder {
                    color: #d6d6d6;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                }
            }
        }

        .actionNumber {
            color: #a6a6a6;
            font-size: 14px;
            font-weight: 400;
            white-space: nowrap;
        }
    }

    .content {
        height: calc(100vh - 205px);
        min-height: 300px;
        overflow-y: auto;
        // 使用 grid 布局替代 flex 布局
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 16px;
        grid-auto-rows: min-content;
        position: relative;
        will-change: transform;
        box-sizing: border-box;

        // 添加媒体查询，处理小屏幕设备
        @media screen and (max-width: 1200px) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media screen and (max-width: 992px) {
            grid-template-columns: repeat(2, 1fr);
        }

        @media screen and (max-width: 576px) {
            grid-template-columns: repeat(1, 1fr);
        }

        .actionCard {
            border: 1px solid #f5f5f5;
            height: 240px;
            box-sizing: border-box;
            cursor: pointer;
            border-radius: 8px;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            min-height: 200px;
            transition: all 0.3s;

            // 添加响应式内边距
            @media screen and (max-width: 1400px) {
                padding: 16px 20px;
            }

            @media screen and (max-width: 1200px) {
                padding: 14px 16px;
            }

            &:hover {
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

                .triggerBtn {
                    opacity: 1;
                }
            }

            :global(.arco-card-body) {
                padding: 0;
                display: flex;
                flex-direction: column;
                width: 100%;
                height: 100%;
            }

            .actionInfo {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 100%;

                .nameWrapper {
                    display: flex;
                    align-items: center;

                    .icon {
                        width: 48px;
                        height: 48px;
                        border-radius: 8px;

                        svg {
                            width: 48px;
                            height: 48px;
                        }
                    }

                    .name {
                        font-size: 16px;
                        font-weight: 600;
                        color: #333333;
                    }
                }

                .cardDescription {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    line-height: 1.5;
                }

                .tags {
                    display: flex;
                    flex-wrap: wrap;

                    :global(.arco-btn) {
                        border-radius: 4px;
                        padding: 2px 6px;
                        font-size: 12px;
                        background-color: transparent;
                        border: 1px solid #00000014;
                        color: #000000A3;
                        font-weight: 400;
                    }
                }
            }

            :global(.arco-space-item) {
                :global(.arco-typography) {
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 20px;
                    color: #adadad;
                }
            }

            .triggerBtn {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 4px;
                background-color: #ffffff;
                border-radius: 8px;
                border: 1px solid #f5f5f5;
                opacity: 0;

                &:hover {
                    background: #fafafa;
                    border-color: #f5f5f5;
                }
            }
        }
        
        // 添加空状态容器样式
        .emptyContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            grid-column: 1 / -1;
            background-color: #ffffff;

            :global(.arco-space) {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .emptyText {
                font-weight: 500;
                font-size: 14px;
                line-height: 24px;
                color: #5c5c5c;
                text-align: center;
            }
        }

        // 建设中状态容器
        .buildingContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            grid-column: 1 / -1;
            background-color: #ffffff;

            :global(.arco-space) {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .buildingText {
                font-weight: 500;
                font-size: 14px;
                line-height: 24px;
                color: #5c5c5c;
                text-align: center;
            }
        }
    }
    
    // 添加加载状态容器样式和错误容器样式
    .loadingContainer, .errorContainer {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        grid-column: 1 / -1;
        background-color: #ffffff;
        
        :global(.arco-spin) {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
}

.confirmDeleteModal {
    position: relative;
    padding: 24px;
    width: 480px;
    border-radius: 16px;

    .modalContent {
        display: flex;
        flex-direction: column;

        .modalContentText {
            font-weight: 400;
            font-size: 14px;
            color: #5c5c5c;
        }
    }

    .modalFooter {
        margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .cancelDeleteBtn,
        .confirmDeleteBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 18px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
        }

        .cancelDeleteBtn {
            background-color: #ffffff;
            color: #5c5c5c;
            border: 1px solid #ebebeb;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }
        }

        .confirmDeleteBtn {
            background-color: #d54941;
            color: #ffffff;

            &:hover {
                background-color: #cd463e;
                color: #ffffff;
            }
        }
    }

    :global(.arco-modal-header) {
        padding: 0;
        height: auto;
        border-bottom: none;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            color: #333333;
            margin-bottom: 16px;
            text-align: left;
        }
    }

    :global(.arco-modal-content) {
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 24px;
        top: 24px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
        background-color: #e9e9ff;
        color: #4d5ef3;
    }
}   

.deleteBtn {
    padding: 4px 8px;
    border-radius: 4px;
    width: 144px;
    display: flex;
    justify-content: flex-start;
    background-color: #ffffff !important;
    color: #333333 !important;

    &:hover {
        background: #f5f5f5 !important;
    }
}



:global(.arco-trigger-arrow.arco-popover-arrow) {
    display: none;
}

:global(.arco-popover-content-right) {
    color: var(--color-text-2);
    background-color: #ffffff;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.12);
    max-width: none;
    font-size: 14px;
    border-radius: 8px;
    line-height: 1.5715;
    box-sizing: border-box;
    border: none;
    padding: 8px;
    border: 1px solid #f5f5f5   ;
}

:global {
    .arco-modal-mask {
        background: rgba(0, 0, 0, 0.16);
    }
}