.supplier {
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f5;
        box-sizing: border-box;

        .addSupplierBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            background-color: #4455f2;
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
            border-radius: 8px;
            transition: all 0.3s;
            height: 40px;

            &:hover {
                background-color: #4152e9;
            }
        }

        //搜索输入框
        :global(.arco-input-inner-wrapper) {
            padding: 8px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            ::placeholder {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                color: #d6d6d6;
            }

            :global(.arco-input) {
                padding-top: 0;
                padding-bottom: 0;
                padding-left: 8px;
            }
        }

        //筛选select
        :global(.arco-select-size-default.arco-select-single .arco-select-view) {
            padding: 8px;
            height: auto;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            background-color: #ffffff;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            :global(.arco-select-prefix) {
                margin-right: 4px;
            }

            :global(.arco-select-view-input) {
                &::placeholder {
                    color: #d6d6d6;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                }
            }
        }

        .supplierNumber {
            color: #adadad;
            font-size: 14px;
            font-weight: 400;
            white-space: nowrap;
        }
    }

    .content {
        height: calc(100vh - 205px);
        min-height: 300px;
        overflow-y: auto;
        // 修改为固定四列布局
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 16px;
        grid-auto-rows: min-content;
        position: relative;
        will-change: transform;
        box-sizing: border-box;

        // 添加媒体查询，处理小屏幕设备
        @media screen and (max-width: 1200px) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media screen and (max-width: 992px) {
            grid-template-columns: repeat(2, 1fr);
        }

        @media screen and (max-width: 576px) {
            grid-template-columns: repeat(1, 1fr);
        }

        .supplierCard {
            border: 1px solid #f5f5f5;
            border-radius: 8px;
            padding: 20px 24px;
            transition: all 0.3s;
            min-height: 200px;
            height: 240px;
            box-sizing: border-box;
            cursor: pointer;
            width: 100%;
            max-width: 100%;
            overflow: hidden;

            @media screen and (max-width: 1400px) {
                padding: 16px 20px;
            }

            @media screen and (max-width: 1200px) {
                padding: 14px 16px;
            }

            &:hover {
                box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

                .triggerBtn {
                    opacity: 1;
                }
            }

            :global(.arco-card-body) {
                padding: 0;
                display: flex;
                flex-direction: column;
                width: 100%;
                height: 100%;
            }

            .supplierInfo {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 100%;

                .nameWrapper {
                    display: flex;
                    align-items: center;

                    :global(.arco-space-item) {
                        .icon {
                            width: 48px;
                            height: 48px;
                            border-radius: 8px;

                            svg {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }

                    .name {
                        font-weight: 600;
                        font-size: 16px;
                        line-height: 24px;
                        color: #333333;
                        cursor: pointer;
                    }
                }

                .description {
                    color: #5c5c5c;
                    margin-bottom: 0;
                    font-size: 14px;
                    font-weight: 400;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    line-height: 1.5;
                    word-break: break-word;
                }

                .tags {
                    display: flex;
                    gap: 8px;
                    flex-wrap: wrap;

                    :global(.arco-btn) {
                        border-radius: 4px;
                        padding: 2px 6px;
                        font-size: 12px;
                        background-color: transparent;
                        border: 1px solid #ebebeb;
                        color: #595959;
                        font-weight: 400;
                    }
                }

                .metaInfo {
                    color: #595959;
                    font-size: 14px;

                    :global(.arco-typography) {
                        color: #adadad;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 20px;
                    }
                }
            }

            .cardFooter {
                display: flex;
                justify-content: space-between;
            }

            .triggerBtn {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 4px;
                background-color: #ffffff;
                border-radius: 8px;
                border: 1px solid #f5f5f5;
                opacity: 0;

                &:hover {
                    background: #fafafa;
                }
            }
        }
        
        // 添加空状态容器样式
        .emptyContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            grid-column: 1 / -1;
            background-color: #ffffff;

            :global(.arco-space) {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            :global(.arco-typography) {
                font-weight: 500;
                font-size: 14px;
                line-height: 24px;
                color: #5c5c5c;
                text-align: center;
            }
        }
    }
    
    // 添加加载状态容器样式和错误容器样式
    .loadingContainer, .errorContainer {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        grid-column: 1 / -1;
        background-color: #ffffff;
        
        :global(.arco-spin) {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
}

.confirmDeleteModal {
    position: relative;
    padding: 24px;
    width: 480px;
    border-radius: 16px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);


    .modalContent {
        display: flex;
        flex-direction: column;

        .modalContentText {
            font-weight: 400;
            font-size: 14px;
            color: #5c5c5c;
        }
    }

    .modalFooter {
        margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .cancelDeleteBtn,
        .confirmDeleteBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 18px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
        }

        .cancelDeleteBtn {
            background-color: #ffffff;
            color: #5c5c5c;
            border: 1px solid #ebebeb;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }
        }

        .confirmDeleteBtn {
            background-color: #d54941;
            color: #ffffff;

            &:hover {
                background-color: #cd463e;
                color: #ffffff;
            }
        }
    }

    :global(.arco-modal-header) {
        padding: 0 !important;
        height: auto;
        border-bottom: none !important;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            color: #333333;
            margin-bottom: 16px;
            text-align: left;
        }
    }

    :global(.arco-modal-content) {
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 24px;
        top: 24px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
        background-color: #cd463e;
        color: #ffffff;
    }
}

.supplierDetailModal {
    position: relative;
    padding: 24px;
    width: 640px;
    border-radius: 16px;
    border: 1px solid #f5f5f5;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

    .supplierHeader {
        display: flex;
        margin-bottom: 24px;
        width: 100%;

        .iconAndName {
            display: flex;
            align-items: center;
            width: 100%;

            .supplierIconWrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 72px;
                height: 72px;
                border-radius: 8px;
                background-color: #f5f5f5;
                flex-shrink: 0;

                .supplierIcon {
                    width: 72px;
                    height: 72px;
                }
            }

            .divider {
                width: 1px;
                height: 72px;
                background-color: #f5f5f5;
                margin: 0 24px;
            }

            .nameFormContainer {
                flex: 1;
        
                .nameFormItem {
                    margin-bottom: 0;
                }
            }
        }
    }

    .modalContent {
        display: flex;
        flex-direction: column;
    }

    .modalFooter {
        // margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .cancelBtn,
        .saveBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            line-height: 24px;
            height: 40px;
        }

        .cancelBtn {
            background-color: #ffffff;
            color: #5c5c5c;
            border: 1px solid #ebebeb;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }
        }

        .saveBtn {
            background-color: #4455f2;
            color: #ffffff;
            
            &:hover:not(.disabledBtn) {
                background-color: #4152e9;
            }
        }

        .disabledBtn {
            background-color: #c3c8fa !important;
            color: #ffffff !important;
            cursor: not-allowed;
            
            &:hover {
                background-color: #c3c8fa !important;
            }
        }
    }

    .requiredIcon {
        color: #4455f2;
        font-size: 15px;
        line-height: 16px;
        margin-left: 4px;
        position: relative;
        top: -1px;
        font-weight: bold;
    }

    :global(.arco-form-label-item) {
        font-weight: 600;
        font-size: 14px;
        color: #333333;
        line-height: 24px;
    }

    :global(.arco-space-item) {
        margin-right: 0 !important;
    }

    :global(.arco-modal-header) {
        padding: 0;
        height: auto;
        border-bottom: none;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 20px;
            line-height: 32px;
            color: #333333;
            margin-bottom: 16px;
            text-align: left;
        }
    }

    :global(.arco-form-item) {
        margin-bottom: 24px;

        :global(.arco-form-item > .arco-form-label-item) {
            font-weight: 600;
            font-size: 14px;
            color: #333333;
            line-height: 24px;
        }
    }

    :global(.arco-form-item) {
        :global(.arco-form-label-item > label) {
            font-weight: 600;
            font-size: 14px;
            color: #333333;
            line-height: 24px;
        }
    }

    :global(.arco-input) {
        padding: 8px 12px;
        border: 1px solid #ebebeb;
        border-radius: 8px;
        background-color: transparent;

        &::placeholder {
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
        }
    }

    :global(.arco-input-disabled) {
        background-color: #fcfcfc;
    }

    :global(.arco-modal-content) {
        min-height: 160px;
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 32px;
        top: 32px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
        background-color: #4152e9;
        color: #ffffff;
    }

    :global(.arco-textarea) {
        background-color: transparent;
        min-height: 64px;
        border: 1px solid #ebebeb;
        border-radius: 8px;
        resize: none;

        &::placeholder {
            color: #d6d6d6;
        }
    }

    :global(.arco-textarea-disabled) {
        background-color: #fcfcfc;
    }

    :global(.arco-textarea-word-limit) {
        color: #adadad;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
    }
}

.deleteBtn {
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    padding: 4px 8px;
    border-radius: 4px;
    width: 144px;
    display: flex;
    justify-content: flex-start;
    background-color: #ffffff !important;
    color: #333333 !important;

    &:hover {
        background-color: #f5f5f5 !important;
    }
}

:global(.arco-popover-content-right) {
    color: var(--color-text-2);
    background-color: #ffffff;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.12);
    max-width: none;
    font-size: 14px;
    border-radius: 8px;
    line-height: 1.5715;
    box-sizing: border-box;
    border: none;
    padding: 8px;
    border: 1px solid #f5f5f5;
}

:global {
    .arco-modal-mask {
        background: rgba(0, 0, 0, 0.08);
    }
}