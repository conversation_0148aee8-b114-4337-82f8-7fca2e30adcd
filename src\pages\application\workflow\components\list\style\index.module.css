/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
  /* 对于基于 WebKit 的浏览器 */
}
/* 对于IE和Edge */
html {
  -ms-overflow-style: none;
  /* IE 和 Edge */
}
/* 对于Firefox */
* {
  scrollbar-width: none;
  /* Firefox */
}
:global(.arco-popover-content) {
  padding: 0 12px 8px 12px;
}
:global(.arco-popover-content) :global(.arco-popover-inner-content) p {
  cursor: pointer;
  padding-left: 8px;
  display: flex;
  align-items: center;
  height: 34px !important;
  width: 104px !important;
  text-align: left;
  color: rgba(0, 0, 0, 0.65);
  border-radius: 4px;
}
:global(.arco-popover-content) :global(.arco-popover-inner-content) p:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
:global(.arco-popover-content) :global(.arco-popover-inner-content) .black {
  color: rgba(0, 0, 0, 0.8) !important;
  background: rgba(0, 0, 0, 0.02) !important;
}
:global(.arco-popover-content) :global(.arco-popover-inner-content) .black:hover {
  background: rgba(0, 0, 0, 0.04) !important;
}
.customModal {
  padding: 8px;
}
.customModal :global(.arco-modal-header) {
  border: none;
}
.customModal :global(.arco-modal-header) :global(.arco-modal-title) {
  text-align: left !important;
  font-size: 18px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.8);
}
.customModal :global(.arco-modal-content) {
  padding-top: 14px;
}
.customModal :global(.arco-modal-content) :global(.arco-form) :global(.arco-form-item) {
  display: flex;
  flex-direction: column;
}
.customModal :global(.arco-modal-content) :global(.arco-form) :global(.arco-form-item) .label {
  font-size: 14px;
  font-weight: 600;
  display: inline-block;
  padding-bottom: 6px;
  color: rgba(0, 0, 0, 0.65);
}
.customModal :global(.arco-modal-content) :global(.arco-form) :global(.arco-form-item) .subLabel {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.35);
  padding-left: 6px;
}
.customModal :global(.arco-modal-content) :global(.arco-form) :global(.arco-form-item) .subLabel2 {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.35);
  padding: 0 0 6px 0;
}
.customModal :global(.arco-modal-content) :global(.arco-form) :global(.arco-form-item) :global(.arco-form-label-item) {
  text-align: left !important;
  flex-basis: auto !important;
}
.customModal :global(.arco-modal-content) :global(.arco-form) :global(.arco-form-item) :global(.arco-form-label-item) :global(.arco-form-item-symbol) {
  display: none !important;
}
.customModal :global(.arco-modal-content) :global(.arco-form) :global(.arco-form-item) :global(.arco-input) {
  background-color: #ffffff !important;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  height: 40px;
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
}
.customModal :global(.arco-modal-content) :global(.arco-form) :global(.arco-form-item) :global(.arco-input)::placeholder {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.16);
}
.customModal :global(.arco-modal-content) :global(.arco-form) :global(.arco-btn) {
  height: 40px;
  width: 80px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  color: rgba(0, 0, 0, 0.65);
}
.customModal :global(.arco-modal-content) :global(.arco-form) :global(.arco-btn) span {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
}
.customModal :global(.arco-modal-content) :global(.arco-form) .customBlock {
  width: 100%;
}
.customModal :global(.arco-modal-content) :global(.arco-form) .customBlock :global(.arco-form-item) {
  margin-bottom: 8px;
}
.customModal :global(.arco-modal-content) :global(.arco-form) .customBlock :global(.arco-form-item) :global(.arco-input-tag) {
  background-color: #ffffff !important;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  height: 40px;
}
.customModal :global(.arco-modal-content) :global(.arco-form) .customBlock :global(.arco-form-item) :global(.arco-input-tag) :global(.arco-input-tag-view) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
}
.customModal :global(.arco-modal-content) :global(.arco-form) .customBlock :global(.arco-form-item) :global(.arco-input-tag) :global(.arco-input-tag-inner) input {
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
}
.customModal :global(.arco-modal-content) :global(.arco-form) .customBlock :global(.arco-form-item) :global(.arco-input-tag) :global(.arco-input-tag-inner) input::placeholder {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.16);
}
.customModal :global(.arco-modal-content) :global(.arco-form) .customBlock :global(.arco-form-item) :global(.arco-input-tag) :global(.arco-input-tag-suffix) {
  border-radius: 12px;
  color: rgba(0, 0, 0, 0.35);
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-right: 0 !important;
  font-size: 13px;
}
.customModal :global(.arco-modal-content) :global(.arco-form) .customBlock :global(.arco-form-item) :global(.arco-input-tag) :global(.arco-input-tag-suffix):hover {
  background-color: rgba(0, 0, 0, 0.02);
  color: rgba(0, 0, 0, 0.65);
  cursor: pointer;
}
.customModal :global(.arco-modal-footer) {
  border: none;
}
.customContainer {
  display: flex;
  flex-direction: column;
  padding-top: 8px;
  will-change: transform;
  -webkit-overflow-scrolling: touch;
  position: relative;
}
.customContainer .addBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 24px;
  background-color: #4d5ef3;
  font-weight: 600;
  font-size: 14px;
  color: #ffffff;
  border-radius: 8px;
  transition: all 0.3s;
  height: 40px;
}
.customContainer .addBtn:hover {
  background-color: #3144f1;
}
.customContainer .loadingContainer {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  z-index: 1;
}
.customContainer .loadingContainer .loadingText {
  margin-top: 12px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 14px;
}
.customContainer .emptyContainer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.customContainer .emptyContainer svg {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
}
.customContainer .emptyContainer .emptyText {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.45);
  line-height: 22px;
}
.customContainer .butFont {
  font-size: 14px;
  font-weight: 600;
}
.customContainer .blueBut {
  background-color: rgba(68, 85, 242, 0.95);
  color: #ffffff;
  border-radius: 8px;
  width: 104px;
  height: 40px;
}
.customContainer .blueBut:hover {
  color: #ffffff !important;
  background-color: #4756df !important;
}
.customContainer .normalBut {
  background-color: rgba(68, 85, 242, 0.04);
  color: rgba(68, 85, 242, 0.95);
  border-radius: 4px;
  width: 104px;
  height: 40px;
}
.customContainer .normalBut:hover {
  color: rgba(68, 85, 242, 0.95) !important;
  background-color: #f0f1fe !important;
}
.customContainer .rowEndCenter {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.customContainer .countAppText {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.35);
  margin-right: 16px;
}
.customContainer .searchBox {
  width: 240px;
  height: 40px;
  border-radius: 8px;
  outline: none;
  border: 1px solid rgba(0, 0, 0, 0.08);
  font-size: 14px;
  font-weight: 400;
  background-color: #ffffff;
  margin-right: 16px;
  transition: all 0.2s ease;
}
.customContainer .searchBox:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
.customContainer .searchBox:focus {
  background-color: #ffffff;
}
.customContainer .searchBox :global(.arco-input-inner-wrapper) {
  background-color: transparent;
  border-radius: 8px;
}
.customContainer .searchBox input::placeholder {
  color: rgba(0, 0, 0, 0.25);
}
.customContainer .selectBox {
  height: 40px;
  width: 160px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}
.customContainer .selectBox:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
.customContainer .selectBox:focus-within {
  background-color: #ffffff;
}
.customContainer .selectBox :global(.arco-select-view) {
  height: 40px;
  width: 160px;
  border-radius: 8px;
  background-color: transparent;
  display: flex;
  align-items: center;
}
.customContainer .selectBox :global(.arco-select-view-value) {
  color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
}
.customContainer .selectBox :global(.arco-select-view-value[title='']) {
  color: rgba(0, 0, 0, 0.25);
}
.customContainer .customCardBox {
  height: calc(100vh - 160px);
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  padding-top: 16px;
  margin-bottom: 100px;
  position: relative;
  will-change: transform;
  align-content: flex-start;
}
.customContainer .customCardBox .rowStartCenter {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}
.customContainer .customCardBox .customCard {
  margin-bottom: 0;
  margin-right: 0;
  min-width: 240px;
  height: 240px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  cursor: pointer;
}
.customContainer .customCardBox .customCard .folderIcon {
  max-width: 48px;
  max-height: 48px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  padding-top: 0;
}
.customContainer .customCardBox .customCard .folderIcon img {
  max-width: 48px;
  max-height: 48px;
}
.customContainer .customCardBox .customCard .name {
  color: rgba(0, 0, 0, 0.8);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  word-break: break-word;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-height: 48px;
  padding-top: 0;
  display: flex;
  align-items: center;
}
.customContainer .customCardBox .customCard .groupName {
  color: rgba(0, 0, 0, 0.8);
  font-size: 20px;
  font-weight: 600;
}
.customContainer .customCardBox .customCard .tag {
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  font-weight: 400;
  font-size: 12px;
  margin-right: 4px;
  color: rgba(0, 0, 0, 0.65);
}
.customContainer .customCardBox .customCard .description {
  font-size: 14px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.65);
  line-height: 24px;
  height: 48px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
.customContainer .customCardBox .customCard .footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: absolute;
  bottom: 40px;
  left: 24px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  width: calc(100% - 48px);
  z-index: 1;
}
.customContainer .customCardBox .customCard .footer .hoverContainer {
  display: flex;
  gap: 8px;
  align-items: center;
  min-width: 0;
  transition: all 0.2s ease;
  position: absolute;
  right: 0;
  opacity: 1;
  width: 100%;
}
.customContainer .customCardBox .customCard .footer .hoverContainer .metaText {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.32);
  line-height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  margin-right: 8px;
}
.customContainer .customCardBox .customCard .footer .hoverContainer .iconMoreContainer {
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}
.customContainer .customCardBox .customCard .footer .hoverContainer .iconMoreContainer:active {
  background-color: rgba(0, 0, 0, 0.04);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.customContainer .customCardBox .customCard .footer .hoverContainer .iconMoreContainer.active {
  background-color: rgba(0, 0, 0, 0.04);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.customContainer .customCardBox .customCard .options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 16px;
  left: 16px;
  right: 16px;
  padding: 5px;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: #fff;
  z-index: 1;
}
.customContainer .customCardBox .customCard .options:global(.arco-icon) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.customContainer .customCardBox .customCard .options .iconMoreContainer {
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}
.customContainer .customCardBox .customCard .options .iconMoreContainer:active {
  background-color: rgba(0, 0, 0, 0.04);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.customContainer .customCardBox .customCard .options .iconMoreContainer.active {
  background-color: rgba(0, 0, 0, 0.04);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.profileTag {
  display: inline-block;
  padding: 2px 8px;
  margin-right: 8px;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  cursor: default;
}
.profileTag:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
.tooltipProfilesContainer {
  padding: 4px;
  max-width: 200px;
}
.tooltipProfileItem {
  padding: 4px 8px;
  margin-bottom: 4px;
  background-color: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tooltipProfileItem:last-child {
  margin-bottom: 0;
}
.backToTop {
  position: fixed;
  left: 55%;
  bottom: 40px;
  width: 130px;
  transform: translateX(-50%);
  height: 38px;
  border-radius: 24px;
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 9999;
  gap: 8px;
}
.backToTop span {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.64);
  font-weight: 500;
}
.backToTop:hover {
  background: #f2f3f5;
}
.backToTop.hidden {
  opacity: 0;
  pointer-events: none;
}
.subtitleTitle {
  font-size: 20px;
  font-weight: 500;
  line-height: 32px;
  color: rgba(0, 0, 0, 0.8);
}
:global(.arco-form-item-wrapper) {
  width: 100%;
  flex: 1;
}
.iconContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
}
.iconContainer .nameContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}
.iconContainer .nameContainer :global(.arco-form-item) {
  width: 100%;
  margin-bottom: 0px;
}
.iconContainer .nameContainer :global(.arco-form-item) :global(.arco-form-item-control-children) {
  width: 100%;
}
.iconContainer .nameContainer :global(.arco-form-item) :global(.arco-input) {
  width: 100%;
}
.iconContainer .divider {
  width: 1px;
  height: 72px;
  background-color: rgba(0, 0, 0, 0.08);
  margin: 0 24px;
  flex-shrink: 0;
}
.titleRow {
  margin-top: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  width: 100%;
}
.titleRow .titleContent {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.titleRow .titleContent .subtitlePlaceholder {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.35);
}
.titleRow .switchContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
  width: 76px;
  justify-content: center;
  border-radius: 8px;
  background-color: transparent;
  padding: 0 2px;
}
.titleRow .switchContainer :global(.arco-switch) {
  min-width: 40px;
  height: 24px;
}
.titleRow .switchContainer :global(.arco-switch-checked) {
  background-color: rgba(13, 41, 254, 0.95) !important;
}
.titleRow .addLabelBut {
  width: 76px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  cursor: pointer;
  background-color: transparent;
  padding: 0 2px;
}
.titleRow .addLabelBut :global(.arco-icon) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}
.titleRow .addLabelBut .operateText {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  display: flex;
  align-items: center;
  height: 100%;
}
.titleRow .addLabelBut:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
.labelContainer {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  width: 100%;
  border-radius: 8px;
  flex: none;
  order: 1;
  align-self: stretch;
  flex-grow: 0;
  margin-top: 8px;
}
.labelContainer .selectedItemList {
  box-sizing: border-box;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  border-radius: 4px;
  min-height: 48px;
  background-color: rgba(0, 0, 0, 0.01);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 8px;
}
.labelContainer .selectedItemList:hover {
  border-color: rgba(0, 0, 0, 0.15);
}
.labelContainer .selectedItemList .selectedItemRow {
  width: 100%;
  display: flex;
  align-items: center;
  border-radius: 8px;
}
.labelContainer .selectedItemList .selectedItemRow .selectedItemCol {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}
.labelContainer .selectedItemList .selectedItemRow .selectedItemCol :global(.arco-input-inner-wrapper) {
  height: 40px;
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.08);
}
.labelContainer .selectedItemList .selectedItemRow .selectedItemCol .deleteIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  opacity: 0.65;
  transition: opacity 0.2s ease;
}
.labelContainer .selectedItemList .selectedItemRow .selectedItemCol .deleteIcon:hover {
  opacity: 1;
}
.operateButGroup {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  margin: 0 24px 24px 0;
}
.operateButGroup .text {
  font-size: 14px;
  font-weight: 600;
}
.operateButGroup .but {
  border-radius: 8px;
  width: 76px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.operateButGroup .cancelBut {
  background-color: #fafafa;
}
.operateButGroup .cancelBut .text {
  color: rgba(0, 0, 0, 0.65);
}
.operateButGroup .cancelBut:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
.operateButGroup .createBut {
  background-color: #4455F2;
  transition: all 0.3s ease;
}
.operateButGroup .createBut .text {
  color: white;
}
.operateButGroup .createBut:hover {
  background-color: #3144f1;
}
.operateButGroup .createBut.disabled {
  opacity: 0.32;
  cursor: not-allowed;
  pointer-events: none;
  background-color: #4455F2;
}
.operateButGroup .createBut[disabled] {
  opacity: 0.32;
  cursor: not-allowed;
  pointer-events: none;
  background-color: #4455F2;
}
:global(.arco-modal-footer) {
  padding: 0;
  border-top: none;
}
