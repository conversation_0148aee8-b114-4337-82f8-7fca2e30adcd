const menu_i18n = {
    'en-US': {
        'menu.welcome': 'Welcome',
        'menu.dashboard': '首页',
        'menu.acp': 'ACP Server',
        'menu.acp.serverdetail': 'ACP Server详情',
        'menu.model': '模型',
        'menu.model.create': '新增模型',
        'menu.knowledge': '知识库',
        'menu.plugin': '插件',
        'menu.plugin.detail': '插件详情',
        'menu.sdk': 'SDK',
        'menu.sdk.detail': 'SDK详情',
        'menu.usage': '使用统计',
        'menu.usage.detail': '使用统计详情',
        'menu.enterprise': '组织',
        'menu.knowledge.project': '项目组',
        'menu.knowledge.project.folder': '文件组',
        'menu.knowledge.project.folder.detail': '文档',
        'menu.knowledge.list': '文件列表',
        'menu.knowledge.list.detail': '文档',
        'menu.user': '账户',
        'menu.application': 'Agent Team',
        'menu.application.sequenceCard': '时序卡片',
        'menu.application.timeSequenceCard': '时序卡片',
        'menu.application.agent': '智能体',
        'menu.application.workflow': '工作流',
        'menu.application.aiaction': 'AI action',
        'menu.list': 'List',
        'menu.result': 'Result',
        'menu.exception': 'Exception',
        'menu.form': 'Form',
        'menu.profile': 'Profile',
        'menu.visualization': 'Data Visualization',
        'menu.exception.403': '403',
        'menu.exception.404': '404',
        'menu.exception.500': '500',
        'menu.timeSequenceCard': '时序卡片',
        'menu.timeSequenceCard.create': '新增时序卡片',
        'menu.timeSequenceCard.detail': '时序卡片详情',
        'menu.aiaction': 'AI action',
        'menu.aiaction.create': '新增AI action',
        'menu.aiaction.detail': 'AI action详情',
        'menu.workflow': '工作流',
        'menu.workflow.detail': '工作流详情',
        'menu.agent': '智能体',
        'menu.agent.list': '智能体列表',
        'menu.agent.info': '智能体详情',
        'menu.agent.create': '新增智能体',
        'menu.model.list': '模型列表',
        'menu.model.detail': '模型详情',
        'menu.model.configdetail': '模型配置详情',
        'menu.model.configcreate': '新增模型配置'
    },
    'zh-CN': {
        'menu.welcome': 'Welcome',
        'menu.dashboard': '仪表盘',
        'menu.acp': 'ACP Server',
        'menu.acp.serverdetail': 'ACP Server详情',
        'menu.model': '模型',
        'menu.model.create': '新增模型',
        'menu.knowledge': '知识库',
        'menu.plugin': '插件',
        'menu.plugin.detail': '插件详情',
        'menu.sdk': 'SDK',
        'menu.sdk.detail': 'SDK详情',
        'menu.usage': '使用统计',
        'menu.usage.detail': '使用统计详情',
        'menu.enterprise': '组织',
        'menu.user': '账户',
        'menu.application': 'Agent Team',
        'menu.application.sequenceCard': '时序卡片',
        'menu.application.timeSequenceCard': '时序卡片',
        'menu.application.agent': '智能体',
        'menu.application.workflow': '工作流',
        'menu.application.aiaction': 'AI action',
        'menu.list': '列表页',
        'menu.result': '结果页',
        'menu.exception': '异常页',
        'menu.form': '表单页',
        'menu.profile': '详情页',
        'menu.visualization': '数据可视化',
        'menu.exception.403': '403',
        'menu.exception.404': '404',
        'menu.exception.500': '500',
        'menu.timeSequenceCard': '时序卡片',    
        'menu.timeSequenceCard.create': '新增时序卡片',
        'menu.timeSequenceCard.detail': '时序卡片详情',
        'menu.aiaction': 'AI action',
        'menu.aiaction.create': '新增AI action',
        'menu.aiaction.detail': 'AI action详情',
        'menu.workflow': '工作流',
        'menu.workflow.detail': '工作流详情',
        'menu.agent': '智能体',
        'menu.agent.list': '智能体列表',
        'menu.agent.info': '智能体详情',
        'menu.agent.create': '新增智能体',
        'menu.model.list': '模型列表',
        'menu.model.detail': '模型详情',
        'menu.model.configdetail': '模型配置详情',
        'menu.model.configcreate': '新增模型配置'
    }
};

export default menu_i18n; 