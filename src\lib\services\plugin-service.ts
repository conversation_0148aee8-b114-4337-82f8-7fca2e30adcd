// plugins.ts
import axiosInstance from './interceptors';
import { PluginListParams, PluginListResponse, PluginUploadParams, PluginUploadResponse } from '@/types/pluginType';
import { endpoints } from './api-endpoints';

/**
 * 获取插件列表
 * @param params 查询参数
 * @returns Promise<PluginListResponse>
 */
export async function getPluginList(params: PluginListParams = {}): Promise<PluginListResponse> {
    try {
        const response = await axiosInstance.get(endpoints.pluginListUrl, {
            params: {
                'pager.page': params.pager?.page || 1,
                'pager.size': params.pager?.size || 100,
                'pager.count': params.pager?.count || 0,
                'page.sort': params.pager?.sort || null,
                'page.order': params.pager?.order || 'asc',
                'page.offset': params.pager?.offset || 0,
                'page.returnTotal': params.pager?.returnTotal || false,
                'names': params.names || null,
                'similarName': params.similarName || null,
                useHook: params.useHook || true,
            },
        });
        return response.data;
    } catch (error) {
        console.error('获取插件列表失败:', error);
        throw error;
    }
}

/**
 * 上传插件
 * @param file 插件文件对象
 * @returns Promise<ApiResponse>
 */
export async function uploadPlugin({ file }: PluginUploadParams): Promise<PluginUploadResponse> {
    try {
        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);

        // 发送请求
        const response = await axiosInstance.post('', formData);

        return response.data;
    } catch (error) {
        console.error('上传插件失败:', error);
        throw error;
    }
}


/**
 * 获取插件详细信息
 * @param pluginId 插件ID
 * @returns Promise<{code: number, message: string, data: Plugin | null}>
 */
export async function getPluginDetails(pluginId: string): Promise<{ code: number, message: string, data: any | null }> {
    try {
        const response = await axiosInstance.get('/plugins/details', {
            params: {
                pluginId
            }
        });

        return response.data;
    } catch (error) {
        console.error('获取插件详情失败:', error);
        throw error;
    }
}
