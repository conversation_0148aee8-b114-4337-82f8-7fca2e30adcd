.container {
    .customContainer {
        width: 100%;
        padding: 8px 0 0 8px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        padding-top: 16px;

        .divider {
            width: 1px;
            height: calc(100vh - 180px);
            background-color: rgba(0, 0, 0, 0.08);
            margin: 0 24px;
        }

        .leftContainer {
            height: calc(100vh - 180px);
            display: flex;
            flex-direction: column;
            background: #fff;
            width: 51%;

            .subtitle {
                font-size: 14px;
                font-weight: 400;
                color: RGBA(0, 0, 0, .35);
                margin-left: 24px;
            }

            .liteIcon {
                width: 24px;
                height: 24px;
                margin-right: 8px;
            }

            .textRow {
                height: 40px;
                width: calc(100% - 14px);
                border-radius: 4px;
                border: 1px solid RGBA(0, 0, 0, .08);
                display: flex;
                justify-content: flex-start;
                align-items: center;
                padding-left: 12px;
                color: RGBA(0, 0, 0, .8);
                font-size: 14px;
                font-weight: 400;
            }

            .chatContainer {
                display: flex;
                flex-direction: column;
                height: calc(100% - 8px);
                background: rgba(0, 0, 0, 0.01);
                position: relative;
                width: 100%;
                border: 1px solid rgba(0, 0, 0, 0.1);
                margin-top: 8px;
                border-radius: 8px;
                overflow: hidden;
            }

            .messageList {
                flex: 1;
                overflow-y: auto;
                padding: 0 16px;
                padding-bottom: 80px;
                margin-bottom: 72px;
            }

            .messageItem {
                display: flex;
                align-items: flex-start;
                justify-content: center;
                width: 100%;
                margin-bottom: 16px;

                &:last-child {
                    margin-bottom: 0;
                }

                &.userMessage {
                    justify-content: flex-end;

                    .messageContent {
                        background: rgba(0, 0, 0, 0.04);
                        font-weight: 400;
                        font-size: 16px;
                        line-height: 24px;
                        letter-spacing: 0%;
                        color: #000000CC;
                        border-radius: 16px;
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                    }
                }

                &.aiMessage {
                    justify-content: flex-start;

                    .messageContent {
                        background: transparent;
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 16px;
                        line-height: 24px;
                        letter-spacing: 0%;
                        color: #000000CC;
                        border-radius: 4px;
                    }
                }
            }

            .avatar {
                width: 24px;
                height: 24px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                background: none;
            }

            .messageMain {
                flex: 0 1 auto;
            }

            .messageHeader {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }

            .messageName {
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 14px;
                line-height: 24px;
                letter-spacing: 0%;
                color: #000000CC;
                margin-left: 8px;
            }

            .messageContent {
                padding: 8px 12px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 22px;
                color: #1D2129;
                min-height: 20px;
                box-sizing: border-box;
            }

            .messageText {
                word-break: break-word;
                
                // markdown 内容样式
                :global {
                    p {
                        margin: 8px 0;
                        line-height: 1.6;
                    }

                    h1, h2, h3, h4, h5, h6 {
                        margin-top: 12px;
                        margin-bottom: 6px;
                        font-weight: 600;
                        line-height: 1.25;
                    }

                    h1 { font-size: 18px; }
                    h2 { font-size: 16px; }
                    h3 { font-size: 15px; }
                    h4 { font-size: 14px; }

                    code {
                        padding: 2px 4px;
                        background-color: rgba(0, 0, 0, 0.04);
                        border-radius: 3px;
                        font-family: Consolas, Monaco, 'Andale Mono', monospace;
                        font-size: 13px;
                    }

                    pre {
                        padding: 8px 12px;
                        background-color: rgba(0, 0, 0, 0.04);
                        border-radius: 4px;
                        overflow-x: auto;
                        margin: 8px 0;

                        code {
                            background: none;
                            padding: 0;
                        }
                    }

                    ul, ol {
                        padding-left: 20px;
                        margin: 6px 0;

                        li {
                            margin: 2px 0;
                        }
                    }

                    blockquote {
                        margin: 8px 0;
                        padding: 0 8px;
                        color: rgba(0, 0, 0, 0.65);
                        border-left: 3px solid rgba(0, 0, 0, 0.1);
                    }

                    a {
                        color: #1890ff;
                        text-decoration: none;

                        &:hover {
                            text-decoration: underline;
                        }
                    }

                    img {
                        max-width: 100%;
                        height: auto;
                    }

                    table {
                        width: 100%;
                        border-collapse: collapse;
                        margin: 8px 0;
                        font-size: 12px;

                        th, td {
                            border: 1px solid #ddd;
                            padding: 4px 8px;
                            text-align: left;
                        }

                        th {
                            background-color: #f5f5f5;
                        }
                    }
                }
            }

            .messageTime {
                font-size: 12px;
                color: #999;
                margin-top: 4px;
            }

            .inputContainer {
                padding: 8px 16px;
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                z-index: 10;
                display: flex;
                align-items: center;
                gap: 8px;

                .leftButton {
                    width: 40px;
                    height: 40px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #FFFFFF;
                    border: 1px solid #E5E6EB;
                    cursor: pointer;
                    flex-shrink: 0;

                    &:hover {
                        background: rgba(0, 0, 0, 0.02);
                    }
                }

                .inputWrapper {
                    display: flex;
                    gap: 8px;
                    align-items: center;
                    position: relative;
                    width: 100%;
                    border: 1px solid #E5E6EB;
                    border-radius: 8px;
                    padding: 8px;
                    background-color: #FFFFFF;

                    :global(.ant-input) {
                        flex: 1;
                        resize: none;
                        padding: 8px;
                        height: 40px !important;
                        border: 0;
                        font-size: 14px;

                        &:focus {
                            box-shadow: none;
                        }
                    }

                    .leftButton {
                        width: 40px;
                        height: 40px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: #FFFFFF;
                        border: 0;
                        cursor: pointer;
                        flex-shrink: 0;

                        &:hover {
                            background: rgba(0, 0, 0, 0.02);
                        }
                    }

                    .plusButton {
                        position: absolute;
                        left: 16px;
                        top: 50%;
                        transform: translateY(-50%);
                        height: 32px;
                        width: 32px;
                        padding: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border: none;
                        background: none;
                        cursor: pointer;
                    }

                    .sendButton {
                        position: absolute;
                        right: 8px;
                        top: 50%;
                        transform: translateY(-50%);
                        height: 32px;
                        width: 32px;
                        padding: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border: none;
                        background: none;
                        cursor: pointer;
                    }
                }
            }

            .createTitle {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 40px;
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                letter-spacing: 0%;
                color: #00000059;
                margin-bottom: 8px;
            }
        }

        .rightContainer {
            width: 49%;

            .tabs {
                span {
                    cursor: pointer;
                    width: 52px;
                    height: 32px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 14px;
                    font-weight: 600;
                    color: RGBA(0, 0, 0, .35);
                    border-radius: 4px;
                    margin-right: 4px;

                    &.selected {
                        color: RGBA(0, 0, 0, .8);
                        background-color: RGBA(0, 0, 0, .04);
                        border-radius: 4px;
                    }
                }
            }

            .contentBox {
                .subtitle {
                    margin-top: 24px;
                    font-size: 14px;
                    font-weight: 600;
                    color: RGBA(0, 0, 0, .65);
                    margin-bottom: 8px;
                }

                .content {
                    width: calc(100% - 32px);
                    border-radius: 4px;
                    border: 1px solid RGBA(0, 0, 0, .08);
                    overflow-y: auto;
                    padding: 16px;
                    display: flex;
                    justify-content: flex-start;
                    align-items: flex-start;
                    align-content: flex-start;

                    .row {
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        height: 26px;

                        .col {
                            font-size: 14px;
                            display: flex;
                            justify-content: flex-start;
                            align-items: center;
                            width: 25%;
                        }
                    }

                    :global(.arco-collapse) {
                        width: 100%;
                    }

                    .collapse {
                        margin-bottom: 16px;

                        .collapseItem {
                            :global(.arco-collapse-item-header) {
                                height: 36px;
                                display: flex;
                                align-items: center;
                                justify-content: flex-start;

                                :global(.arco-collapse-item-header-title) {
                                    line-height: 36px;
                                    height: 36px;
                                    font-size: 12px;
                                    font-weight: 400;
                                    color: RGBA(0, 0, 0, .65);
                                    // 超过一行显示省略号
                                    display: -webkit-box;
                                    -webkit-line-clamp: 1;
                                    line-clamp: 1;
                                    -webkit-box-orient: vertical;
                                    overflow: hidden;
                                }
                            }
                        }

                        .messageId {
                            font-size: 12px;
                            font-weight: 400;
                            color: RGBA(0, 0, 0, .35);
                            margin-bottom: 4px;
                        }

                        .createdAt {
                            font-size: 12px;
                            font-weight: 400;
                            color: RGBA(0, 0, 0, .35);
                        }
                    }

                    .paramsTable {
                        width: 100%;

                        .paramName {
                            color: rgba(0, 0, 0, 0.65);
                            padding: 4px 8px;
                            font-size: 14px;
                        }

                        .paramValue {
                            padding: 4px 8px;
                            font-size: 14px;

                            &.cost {
                                color: #00b42a;
                                font-weight: 500;
                            }

                            &.temperature {
                                color: #f53f3f;
                                font-weight: 500;
                            }

                            &.model {
                                color: #165dff;
                                font-weight: 500;
                            }

                            &.normal {
                                color: rgba(0, 0, 0, 0.65);
                            }
                        }
                    }
                }
            }

            .logBox {
                .analyzeItem {
                    width: 100%;
                    display: flex;
                    justify-content: flex-start;
                    align-items: flex-start;
                    flex-direction: column;
                    margin-bottom: 16px;

                    .analyzeItemContent {
                        width: calc(100% - 24px);
                        padding: 6px 12px;
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;
                        font-size: 14px;
                        font-weight: 400;
                        color: RGBA(0, 0, 0, .65);
                        background-color: RGBA(43, 164, 113, .04);
                        border-radius: 4px;

                        .markdownWrapper {
                            width: 100%;
                            transition: max-height 0.3s ease-in-out;

                            :global {

                                h1,
                                h2,
                                h3,
                                h4,
                                h5,
                                h6 {
                                    margin-top: 16px;
                                    margin-bottom: 8px;
                                    font-weight: 600;
                                    line-height: 1.25;
                                }

                                h1 {
                                    font-size: 20px;
                                }

                                h2 {
                                    font-size: 18px;
                                }

                                h3 {
                                    font-size: 16px;
                                }

                                h4 {
                                    font-size: 14px;
                                }

                                p {
                                    margin: 8px 0;
                                    line-height: 1.6;
                                }

                                code {
                                    padding: 2px 4px;
                                    background-color: rgba(0, 0, 0, 0.04);
                                    border-radius: 3px;
                                    font-family: Consolas, Monaco, 'Andale Mono', monospace;
                                    font-size: 13px;
                                }

                                pre {
                                    padding: 12px;
                                    background-color: rgba(0, 0, 0, 0.04);
                                    border-radius: 4px;
                                    overflow-x: auto;

                                    code {
                                        background: none;
                                        padding: 0;
                                    }
                                }

                                ul,
                                ol {
                                    padding-left: 24px;
                                    margin: 8px 0;

                                    li {
                                        margin: 4px 0;
                                    }
                                }

                                blockquote {
                                    margin: 8px 0;
                                    padding: 0 12px;
                                    color: rgba(0, 0, 0, 0.65);
                                    border-left: 4px solid rgba(0, 0, 0, 0.1);
                                }

                                a {
                                    color: #1890ff;
                                    text-decoration: none;

                                    &:hover {
                                        text-decoration: underline;
                                    }
                                }

                                img {
                                    max-width: 100%;
                                    height: auto;
                                }
                            }
                        }

                        .buttomRow {
                            width: 100%;
                            display: flex;
                            justify-content: flex-end;
                            align-items: center;

                            .showMore {
                                color: RGBA(0, 0, 0, .35);

                                &:hover {
                                    color: RGBA(0, 0, 0, .8);
                                }
                            }
                        }
                    }

                    .analyzeItemOneline {
                        height: 36px;
                        font-size: 12px;
                        font-weight: 400;
                        display: flex;
                        justify-content: flex-start;
                        align-items: center;

                        .analyzeItemName {
                            font-size: 12px;
                            font-weight: 600;
                            color: RGBA(0, 0, 0, .65);
                            margin-right: 16px;
                        }

                        .analyzeItemTime {
                            font-size: 12px;
                            font-weight: 400;
                            color: RGBA(0, 0, 0, .35);
                        }
                    }

                    .analyzeItemId {
                        font-size: 12px;
                        font-weight: 400;
                        color: RGBA(0, 0, 0, .35);
                        margin-top: 4px;
                    }
                }
            }
        }
    }
}