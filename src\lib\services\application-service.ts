import axiosInstance from './interceptors';
// import { AxiosResponse } from 'axios';
// import { ApplicationListParams, ApplicationListResponse } from '@/types/application';
import { endpoints } from './api-endpoints';
import { AgentResponse } from './agent-service';

// 定义应用列表请求参数接口
export interface ApplicationListParams {
    Pager?: {
        Page: number;
        Size: number;
    };
    ApplicationName?: string;
    label?: string;
}

export interface ApplicationResponse {
    id: string;
    name: string;
    description: string;
    profile: string;
    welcome: string;
    type: string;
    provider: string;
    channel: string;
    iconUrl: string | null;
    installed: boolean;
    labels: Array<string>;
    isPublished: boolean;
    disabled: boolean;
    createdDateTime: string;
    updatedDateTime: string;
    agent: AgentResponse;
    artifacts: any | null;
    openapi_auth_options: any | null;
}

// 定义应用列表响应数据接口
export interface ApplicationListResponse {
    items: Array<ApplicationResponse>;
    count: number;
}

/**
 * 获取应用列表
 * @param params 查询参数
 * @returns Promise<ApplicationListResponse>
 */
export async function getApplicationList(params: ApplicationListParams = {}): Promise<ApplicationListResponse> {
    try {
        const response = await axiosInstance.get(endpoints.applicationListUrl, {
            params: {
                'Pager.Page': params.Pager?.Page || 1,
                'Pager.Size': params.Pager?.Size || 12,
                'ApplicationName': params.ApplicationName || '',
                'labels': params.label || undefined,
            },
        });
        return response.data;
    } catch (error) {
        console.error('获取应用列表失败:', error);
        throw error;
    }
}

/**
 * 创建应用
 * @param data 应用数据
 */
export async function createApplication(applicationData: ApplicationResponse): Promise<ApplicationResponse> {
    const response = await axiosInstance.post(endpoints.applicationUrl, applicationData);
    return response.data;
}

/**
 * 更新应用
 * @param id 应用ID
 * @param data 更新数据
 */
export async function updateApplication(id: string, data: {
    id: string;
    name?: string;
    description?: string;
    type?: string;
    provider?: string;
    channel?: string;
    iconUrl: string | null;
    installed?: boolean;
    isPublished?: boolean;
    disabled?: boolean;
    labels?: string[];
    welcome?: string;
    openapi_auth_options?: any;
}) {
    return axiosInstance.put(endpoints.applicationDetailUrl.replace('{id}', id), data);
}

/**
 * 删除应用
 * @param id 应用ID
 */
export async function deleteApplication(id: string) {
    return axiosInstance.delete(endpoints.applicationDetailUrl.replace('{id}', id));
}

/**
 * 获取应用详情
 * @param id 应用ID
 */
export async function getApplicationDetail(id: string): Promise<ApplicationResponse> {
    const response = await axiosInstance.get(endpoints.applicationDetailUrl.replace('{id}', id));
    return response.data;
}

/**
 * 发布应用
 * @param id 应用ID
 */
export async function publishApplication(id: string) {
    return axiosInstance.patch(endpoints.applicationPublishUrl.replace('{applicationId}', id));
}

/**
 * 绑定应用到代理
 */
export async function bindAgents(data: {
    applicationId: string;
    agentIds: string[];
}) {
    return axiosInstance.post(endpoints.applicationBindAgentsUrl, data);
}

/**
 * 绑定时序卡片到应用
 */
export async function bindArtifactsCards(data: {
    applicationId: string;
    artifactIds: string[];
}) {
    return axiosInstance.post(endpoints.applicationBindArtifactsUrl, data);
}

/**
 * 获取应用标签
 */
export async function getApplicationLabels() {
    return axiosInstance.get(endpoints.applicationLabelUrl);
}

/**
 * 导入应用
 */
export async function importApplication(data: {
    file: File;
    import_type: string;
}) {
    const formData = new FormData();
    formData.append('file', data.file);
    formData.append('import_type', data.import_type);

    return axiosInstance.post(endpoints.applicationImportUrl, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}

/**
 * 检查应用名称是否存在
 * @param name 应用名称
 */
export async function checkApplicationName(name: string) {
    return axiosInstance.get(endpoints.applicationNameCheckUrl, {
        params: {
            applicationName: name
        }
    });
}

/**
 * 导出应用
 */
export async function exportApplication(id: string) {
    return axiosInstance.get(endpoints.applicationExportUrl.replace('{applicationId}', id), {
        responseType: 'arraybuffer'
    });
}