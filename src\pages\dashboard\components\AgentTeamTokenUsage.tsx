import React, { useEffect, useRef, useState } from 'react';
import { Card, Spin, Select, Button, Space } from '@arco-design/web-react';
import { Column } from '@antv/g2plot';
import styles from './style/AgentTeamTokenUsage.module.less';
import IconAgentTeamTokenUsage from '@/assets/dashboard/AgentTeamTokenUsage.svg'

const ButtonGroup = Button.Group;
const Option = Select.Option;

type DataType = {
    date: string;
    count: number;
};

type PeriodType = 'week' | 'month' | 'quarter';

const AgentTeamTokenUsage = () => {
    const chartRef = useRef<Column | null>(null);
    const containerRef = useRef<HTMLDivElement | null>(null);
    const [loading, setLoading] = useState(false);
    const [chartHeight, setChartHeight] = useState(300); // 默认高度
    const [selectedTeam, setSelectedTeam] = useState('全部 Agent Team');
    const [period, setPeriod] = useState<PeriodType>('quarter');
    const [chartInitialized, setChartInitialized] = useState(false);

    // 季度数据
    const quarterData: DataType[] = [
        { date: '第1季度', count: 3800 },
        { date: '第2季度', count: 2700 },
        { date: '第3季度', count: 3900 },
        { date: '第4季度', count: 4200 },
    ];

    // 月度数据
    const monthData: DataType[] = [
        { date: '1月', count: 1200 },
        { date: '2月', count: 900 },
        { date: '3月', count: 1700 },
        { date: '4月', count: 800 },
        { date: '5月', count: 1100 },
        { date: '6月', count: 900 },
        { date: '7月', count: 1300 },
        { date: '8月', count: 1200 },
        { date: '9月', count: 1400 },
        { date: '10月', count: 1600 },
        { date: '11月', count: 1300 },
        { date: '12月', count: 1300 },
    ];

    // 周数据
    const weekData: DataType[] = [
        { date: '第1周', count: 250 },
        { date: '第2周', count: 350 },
        { date: '第3周', count: 280 },
        { date: '第4周', count: 380 },
        { date: '第5周', count: 330 },
        { date: '第6周', count: 290 },
        { date: '第7周', count: 350 },
        { date: '第8周', count: 310 },
    ];

    const getDataByPeriod = () => {
        switch (period) {
            case 'week':
                return weekData;
            case 'month':
                return monthData;
            case 'quarter':
            default:
                return quarterData;
        }
    };

    const updateChartSize = () => {
        if (!containerRef.current) return;

        // 根据容器宽度计算适当的高度
        const parent = containerRef.current.parentElement;
        if (!parent) return;
        const containerWidth = parent.clientWidth;

        // 使用比例来确定高度，并确保最小高度为200px
        const calculatedHeight = Math.max(containerWidth * 0.33, 200);

        setChartHeight(calculatedHeight);

        // 如果图表已经初始化，更新其尺寸
        if (chartRef.current) {
            chartRef.current.changeSize(containerWidth, calculatedHeight);
        }
    };

    const initChart = () => {
        if (!containerRef.current || chartInitialized) return;

        // 获取当前周期的数据
        const currentData = getDataByPeriod();

        // 找出最大值，用于设置Y轴刻度
        const maxValue = Math.max(...currentData.map(item => item.count));
        // 计算合适的Y轴最大值，避免顶部空白过多
        const yAxisMax = Math.ceil(maxValue * 1.1);
        
        const column = new Column(containerRef.current, {
            data: currentData,
            padding: [20, 10, 20, 20],
            xField: 'date',
            yField: 'count',
            color: '#4455f2',
            autoFit: true,
            xAxis: {
                grid: null,
                line: {
                    style: {
                        stroke: '#f5f5f5', // 设置X轴线条的颜色
                    },
                },
                label: {
                    style: {
                        fill: '#adadad', // 文字颜色
                        fontSize: 12, // 文字大小
                        fontWeight: 'normal', // 文字粗细
                    },
                    offset: 8, // 与轴线的距离
                    autoRotate: false, // 禁止自动旋转
                    autoHide: true, // 自动隐藏重叠的标签
                },
            },
            yAxis: {
                grid: {
                    line: {
                        style: {
                            stroke: '#f5f5f5',
                        },
                    },
                },
                // 设置固定刻度，避免重复
                min: 0,
                max: yAxisMax,
                // 指定固定的刻度间隔，保证刻度均匀且不重复
                tickCount: 5, // 减少刻度数量，避免拥挤
                label: {
                    formatter: (val) => {
                        const value = parseFloat(val);
                        // 确保0显示为0
                        if (value === 0) return '0';
                        // 1000及以上的值用k表示
                        if (value >= 1000) {
                            return `${(value / 1000).toFixed(0)}k`;
                        }
                        // 小于1000的值直接显示
                        return `${value}`;
                    },
                }
            },
            columnStyle: {
                radius: [4, 4, 4, 4], // 柱状图圆角
            },
            tooltip: {
                showMarkers: false,
                formatter: (datum) => {
                    return {
                        name: '用量',
                        value: datum.count,
                    };
                }
            },
            state: {
                active: {
                    style: {
                        fill: '#7b7ff7', // 悬浮时的颜色
                    },
                },
            },
            // 添加柱状图的间距设置
            columnWidthRatio: 0.6, // 增加柱状图宽度占比
            minColumnWidth: 20, // 最小柱宽
            maxColumnWidth: 40, // 最大柱宽
            height: chartHeight,
        });

        chartRef.current = column;
        column.render();
        setChartInitialized(true);
    };

    // 当周期或团队选择改变时更新图表数据
    useEffect(() => {
        if (chartRef.current && chartInitialized) {
            chartRef.current.changeData(getDataByPeriod());
        }
    }, [period, selectedTeam]);

    // 初始化图表并处理窗口大小变化
    useEffect(() => {
        // 初始设置
        updateChartSize();
        initChart();

        // 添加窗口大小变化监听
        const handleResize = () => {
            updateChartSize();
        };

        window.addEventListener('resize', handleResize);

        // 清理函数
        return () => {
            window.removeEventListener('resize', handleResize);
            if (chartRef.current) {
                chartRef.current.destroy();
                chartRef.current = null;
                setChartInitialized(false);
            }
        };
    }, []);

    // 团队选项列表
    const teamOptions = [
        { label: '全部 Agent Team', value: '全部 Agent Team' },
        { label: 'Agent Team1', value: 'Agent Team1' },
        { label: 'Agent Team2', value: 'Agent Team2' },
        { label: 'Agent Team3', value: 'Agent Team3' },
        { label: 'Agent Team4', value: 'Agent Team4' },
    ];

    // 处理团队选择变化
    const handleTeamChange = (value) => {
        setSelectedTeam(value);
        // 这里可以添加筛选逻辑，根据所选团队过滤数据
    };

    // 处理周期选择变化
    const handlePeriodChange = (value: PeriodType) => {
        setPeriod(value);
    };

    return (
        <Card
            title={
                <div className={styles.cardHeader}>
                    <div className={styles.HeaderTag}>
                        <IconAgentTeamTokenUsage/>
                        <div className={styles.title}>Agent Team Token用量统计</div>
                    </div>
                    <div className={styles.HeaderControls}>
                        <Space size={12}>
                            <Select
                                placeholder="选择 Agent Team"
                                value={selectedTeam}
                                onChange={handleTeamChange}
                                style={{ width: 200 }}
                                allowClear={false}
                            >
                                {teamOptions.map((option) => (
                                    <Option key={option.value} value={option.value}>
                                        {option.label}
                                    </Option>
                                ))}
                            </Select>
                            <ButtonGroup>
                                <Button 
                                    type={period === 'week' ? 'primary' : 'default'} 
                                    onClick={() => handlePeriodChange('week')}
                                >
                                    周
                                </Button>
                                <Button 
                                    type={period === 'month' ? 'primary' : 'default'} 
                                    onClick={() => handlePeriodChange('month')}
                                >
                                    月
                                </Button>
                                <Button 
                                    type={period === 'quarter' ? 'primary' : 'default'} 
                                    onClick={() => handlePeriodChange('quarter')}
                                >
                                    季度
                                </Button>
                            </ButtonGroup>
                        </Space>
                    </div>
                </div>
            }
            className={styles.card}
        >
            <Spin loading={loading} style={{ width: '100%' }}>
                <div
                    ref={containerRef}
                    className={styles.chart}
                    style={{ height: `${chartHeight}px` }}
                />
            </Spin>
        </Card>
    );
};

export default AgentTeamTokenUsage; 