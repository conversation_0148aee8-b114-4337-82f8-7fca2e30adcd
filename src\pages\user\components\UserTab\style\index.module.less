// @import '@/style/theme/index.less';

.userTabContainer {
    width: 100%;


    .tabs {
        :global(.arco-tabs-header-nav::before) {
            display: none;
        }

        :global(.arco-tabs-header-ink) {
            display: none;
        }

        :global(.arco-tabs-header-title) {
            padding: 0;
            font-weight: 600;
            font-size: 20px;
            color: #a6a6a6;
            margin: 0 12px;

            &:hover {
                color: #a6a6a6;
            }
        }

        :global(.arco-tabs-header-title-active) {
            color: #333333;

            &:hover {
                color: #333333;
            }
        }

        :global(.arco-tabs-content) {
            padding-top: 16px;
        }

        :global(.arco-tabs-header-nav-line.arco-tabs-header-nav-horizontal > .arco-tabs-header-scroll .arco-tabs-header-title:first-of-type) {
            margin-left: 0;
        }

        :global(.arco-tabs-header-wrapper) {
            padding-bottom: 16px;
            border-bottom: 1px solid #f5f5f5;
        }
    }
}