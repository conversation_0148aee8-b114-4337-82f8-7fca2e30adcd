import { useState, useEffect } from 'react';
import {
    Card,
    Grid,
    Typography,
    Upload,
    Input,
    Select,
    InputNumber,
    Button,
    Space,
    Tag,
    Switch,
    Form,
    Message,
} from '@arco-design/web-react';
import IconModel from '@/assets/model/ModelIocn.svg';
import IconTagClose from '@/assets/model/IconTagClose.svg';
import styles from './style/index.module.less';
import {
    getLlmTypeList,
    getLlmProviderList,
    createLlmModelConfig,
    getLlmModelKeys,
    getLlmProviderModelList,
    getLlmProviderModelListById,
    getLlmModelKeysByProvider
} from '@/lib/services/llm-model-service';
import { useNavigate } from 'react-router-dom';

const { Row, Col } = Grid;
const { Text, Title } = Typography;
const FormItem = Form.Item;

// 创建一个不显示必填图标的 Form.Item
const FormItemWithoutRequiredMark = (props) => {
    const { children, ...rest } = props;
    return (
        <Form.Item
            {...rest}
            requiredSymbol={false} // 禁用 Arco Design 默认的必填标记
        >
            {children}
        </Form.Item>
    );
};

// 自定义的必填图标组件
const RequiredIcon = () => <span className={styles.requiredIcon}>*</span>;

const CustomLabel = ({ label, required }) => {
    return (
        <Space>
            <span>{label}</span>
            {required && <RequiredIcon />}
        </Space>
    );
};

const AddModelConfig = () => {
    const [form] = Form.useForm();
    const navigate = useNavigate();
    const [tags, setTags] = useState([]);
    const [modelTypes, setModelTypes] = useState([]);
    const [loading, setLoading] = useState(false);
    const [providers, setProviders] = useState([]);
    const [providersLoading, setProvidersLoading] = useState(false);
    const [modelKeys, setModelKeys] = useState([]);
    const [modelKeysLoading, setModelKeysLoading] = useState(false);
    const [formValid, setFormValid] = useState(false);
    const [providerModels, setProviderModels] = useState([]);
    const [providerModelsLoading, setProviderModelsLoading] = useState(false);
    const [selectedModelType, setSelectedModelType] = useState(null);

    // 验证必填字段
    const validateForm = () => {
        try {
            // 获取当前表单值
            const values = form.getFieldsValue();
            // 必填字段列表
            const requiredFields = [
                'deployName',
                'provider',
                'modelType',
                'model', // 新增模型选择作为必填字段
                'llmModelKeyId',
                'promptCost',
                'completionCost',
                'temperature',
            ];

            // 检查所有必填字段是否都已填写
            let allFieldsFilled = requiredFields.every((field) => {
                const value = values[field];
                return value !== undefined && value !== null && value !== '';
            });

            // 如果模型类型是Chat(2)或Text(1)，则maxTokens也是必填的
            if (values.modelType === 1 || values.modelType === 2) {
                const maxTokensValid = values.maxTokens !== undefined &&
                    values.maxTokens !== null &&
                    values.maxTokens !== '' &&
                    values.maxTokens > 0;
                allFieldsFilled = allFieldsFilled && maxTokensValid;
            }

            // 如果模型类型是Embedding(4)，则embeddingDimension也是必填的
            if (values.modelType === 4) {
                const embeddingValid = values.embeddingDimension !== undefined &&
                    values.embeddingDimension !== null &&
                    values.embeddingDimension !== '' &&
                    parseInt(values.embeddingDimension, 10) > 0;
                allFieldsFilled = allFieldsFilled && embeddingValid;
            }

            // 设置表单验证状态
            setFormValid(allFieldsFilled);
        } catch (error) {
            // 发生错误时设置为未验证通过
            setFormValid(false);
        }
    };

    // 清空表单自动填充的字段
    const clearAutoFilledFields = () => {
        form.setFieldsValue({
            model: undefined,
            llmModelKeyId: undefined, // 清空模型密钥
            modelId: '',
            modelVersion: '',
            apiVersion: '',
            modelType: undefined,
            promptCost: undefined,
            completionCost: undefined,
            maxTokens: undefined,
            embeddingDimension: undefined,
            temperature: undefined
        });
    };

    // 根据所选模型填充表单字段
    const fillFormFieldsByModel = (modelId) => {
        // 查找选中的模型
        const selectedModel = providerModels.find(model => model.id === modelId);

        if (selectedModel) {
            // 自动填充表单字段
            form.setFieldsValue({
                modelId: selectedModel.modelId, // 填充modelId字段
                modelVersion: selectedModel.version, // 填充版本字段
                apiVersion: selectedModel.apiVersion, // 填充API版本字段
                modelType: selectedModel.type, // 填充模型类型字段
                // 可以根据需要添加更多字段的自动填充
                promptCost: selectedModel.promptCost,
                completionCost: selectedModel.completionCost,
                maxTokens: selectedModel.maxTokens,
                embeddingDimension: selectedModel.dimension,
                temperature: selectedModel.temperature,

            });
        }
    };

    // 表单值变化时触发验证
    const handleValuesChange = (changedValues) => {
        // 当供应商变化时，获取该供应商的模型列表和模型密钥列表
        if ('provider' in changedValues && changedValues.provider) {
            // 根据选中的供应商ID获取对应的供应商对象
            const selectedProvider = providers.find(
                (p) => p.id === changedValues.provider
            );

            // 使用ID获取模型列表
            fetchProviderModels(changedValues.provider);

            // 使用provider名称获取模型密钥列表
            if (selectedProvider && selectedProvider.provider) {
                fetchModelKeysByProvider(selectedProvider.provider);
            }
        }

        // 当选择模型时，自动填充相关字段
        if ('model' in changedValues && changedValues.model) {
            fillFormFieldsByModel(changedValues.model);
        }

        // 如果模型类型发生变化，更新状态以触发重新渲染
        if ('modelType' in changedValues) {
            setSelectedModelType(changedValues.modelType);
        }

        validateForm();
    };

    // 获取供应商模型列表
    const fetchProviderModels = async (providerId) => {
        // 清空之前自动填充的数据
        clearAutoFilledFields();

        setProviderModelsLoading(true);
        try {
            // 使用新的函数通过供应商ID获取模型列表
            const response = await getLlmProviderModelListById(providerId);
            if (response && Array.isArray(response)) {
                const enabledModels = response.filter(model => model.isEnabled === true);
                setProviderModels(enabledModels);
                // setProviderModels(response);
            } else {
                Message.error('获取供应商模型列表失败');
            }
        } catch (error) {
            console.error('Failed to fetch provider models:', error);
            Message.error('获取供应商模型列表失败，请稍后重试');
        } finally {
            setProviderModelsLoading(false);
        }
    };

    // 根据供应商获取模型密钥列表
    const fetchModelKeysByProvider = async (providerName) => {
        setModelKeysLoading(true);
        try {
            // 使用供应商名称(而不是ID)获取密钥列表
            const response = await getLlmModelKeysByProvider(providerName);
            if (response && Array.isArray(response)) {
                setModelKeys(response);
            } else {
                Message.error('获取供应商模型密钥列表失败');
            }
        } catch (error) {
            console.error('Failed to fetch provider model keys:', error);
            Message.error('获取供应商模型密钥列表失败，请稍后重试');
        } finally {
            setModelKeysLoading(false);
        }
    };

    // 初始检查表单状态
    useEffect(() => {
        // 首次加载时验证表单
        validateForm();
    }, []);

    // 添加模型标签
    const handleAddTag = () => {
        if (tags.length >= 3) {
            Message.warning('标签最多添加3个');
            return;
        }
        // 直接添加一个空标签，然后自动聚焦到它
        setTags([...tags, '']);
    };

    // 处理标签变化
    const handleTagChange = (newTags) => {
        setTags(newTags);
    };

    // 删除标签
    const handleTagClose = (index) => {
        const newTags = [...tags];
        newTags.splice(index, 1);
        setTags(newTags);
    };

    // 创建模型配置
    const handleSubmit = async () => {
        try {
            const values = await form.validate();

            // 如果对外展示名称为空，则使用模型部署名称作为默认值
            const name =
                values.name && values.name.trim() ? values.name : values.deployName;

            // 过滤掉空标签
            const nonEmptyTags = tags.filter((tag) => tag.trim() !== '');

            // 根据选择的 provider id 获取对应的供应商ID
            // 由于选择框已经直接使用了供应商ID，不需要再查找
            const llmProviderId = values.provider || '';

            // 根据选择的 llmModelKeyId 查找对应的 id
            const selectedModelKey = modelKeys.find(
                (k) => k.id === values.llmModelKeyId
            );
            const llmModelKeyId = selectedModelKey
                ? selectedModelKey.id
                : values.llmModelKeyId || '';

            // 根据选择的模型查找对应的id（与其他ID获取方式保持一致的写法）
            const selectedModel = providerModels.find(
                (m) => m.id === values.model
            );
            const llmModelId = selectedModel
                ? selectedModel.id
                : values.model || '';

            // 构造请求体，包含 LlmModelConfigCreateRequest 所需的字段
            const requestData = {
                id: values.id || '',
                llmProviderId: llmProviderId, // 直接使用选择框中的ID
                llmModelId: llmModelId, // 使用所选模型的 id
                modelId: values.modelId || '', // 使用自动填充的模型ID
                icon: values.icon || '',
                name: name || '',
                deployName: values.deployName || '',
                version: values.modelVersion || '',
                apiVersion: values.apiVersion || '',
                type: values.modelType || 0,
                tags: nonEmptyTags.length > 0 ? nonEmptyTags : null,
                multiModal: values.multiModal || false,
                imageGeneration: values.allowGenerateImage || false,
                promptCost: values.promptCost || 0,
                completionCost: values.completionCost || 0,
                dimension: values.embeddingDimension ? parseInt(values.embeddingDimension, 10) : null,
                maxTokens: values.maxTokens !== undefined && values.maxTokens !== '' ? values.maxTokens : null,
                temperature: parseFloat(values.temperature) || 1,
                isEnabled: values.isEnabled || false,
                llmModelKeyId: llmModelKeyId, // 使用模型密钥对应的 id
                group: values.group || null,
            };

            console.log('提交的 requestData:', requestData); // 调试用，验证数据
            const response = await createLlmModelConfig(requestData);

            if (response) {
                Message.success('模型配置创建成功');
                form.resetFields();
                setTags([]);
                navigate('/model');
            } else {
                Message.error('模型配置创建失败');
            }
        } catch (error) {
            console.log('创建模型配置失败:', error);
            Message.error('模型配置创建失败，请检查输入或稍后重试');
        }
    };

    // 获取模型类型
    useEffect(() => {
        const fetchModelTypes = async () => {
            setLoading(true);
            try {
                const response = await getLlmTypeList();
                if (response) {
                    setModelTypes(response);
                } else {
                    Message.error('获取模型类型失败');
                }
            } catch (error) {
                console.error('Failed to fetch model types:', error);
                Message.error('获取模型类型失败，请稍后重试');
            } finally {
                setLoading(false);
            }
        };
        fetchModelTypes();
    }, []);

    // 获取供应商ID列表
    useEffect(() => {
        const fetchProviders = async () => {
            setProvidersLoading(true);
            try {
                const response = await getLlmProviderList();
                if (response && Array.isArray(response)) {
                    setProviders(response); // 保留完整对象数组
                } else {
                    Message.error('获取供应商ID列表失败');
                }
            } catch (error) {
                console.error('Failed to fetch providers:', error);
                Message.error('获取供应商ID列表失败，请稍后重试');
            } finally {
                setProvidersLoading(false);
            }
        };
        fetchProviders();
    }, []);

    // 获取模型密钥ID列表
    // useEffect(() => {
    //     const fetchModelKeys = async () => {
    //         setModelKeysLoading(true);
    //         try {
    //             const response = await getLlmModelKeys();
    //             if (response && Array.isArray(response)) {
    //                 setModelKeys(response); // 保留完整对象数组
    //             } else {
    //                 Message.error('获取模型密钥列表失败');
    //             }
    //         } catch (error) {
    //             console.error('Failed to fetch model keys:', error);
    //             Message.error('获取模型密钥列表失败，请稍后重试');
    //         } finally {
    //             setModelKeysLoading(false);
    //         }
    //     };
    //     fetchModelKeys();
    // }, []);

    // 取消创建模型
    const handleCancel = () => {
        navigate('/model', { state: { fromModel: true } });
    };

    return (
        <div className={styles.addModelContainer}>
            <Title
                heading={4}
                className={styles.addModelTitle}
                style={{ marginBottom: '24px' }}
            >
                配置模型
            </Title>
            <Form
                form={form}
                autoComplete="off"
                layout="vertical"
                onValuesChange={handleValuesChange}
                requiredSymbol={false}
            >
                <Row gutter={[40, 0]}>
                    <Col span={12}>
                        <Card bordered={false} style={{ padding: '0px' }}>
                            <Row className={styles.addModelHeader}>
                                <div className={styles.iconAndName}>
                                    <div className={styles.addModelIconWrapper}>
                                        <IconModel className={styles.addModelIcon} />
                                    </div>
                                    <div className={styles.divider}></div>
                                    <div className={styles.nameFormContainer}>
                                        <FormItemWithoutRequiredMark
                                            label="对外展示名称"
                                            field="name"
                                            style={{ marginBottom: '0px' }}
                                        >
                                            <Input
                                                placeholder="请输入"
                                                className={styles.nameInput}
                                            />
                                        </FormItemWithoutRequiredMark>
                                    </div>
                                </div>
                            </Row>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark
                                        label={<CustomLabel label="部署名称" required={true} />}
                                        field="deployName"
                                        rules={[{ required: true, message: '请输入模型部署名称' }]}
                                    >
                                        <Input
                                            placeholder="请输入"
                                            className={styles.deployNameInput}
                                        />
                                    </FormItemWithoutRequiredMark>
                                </Col>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark label="ID" field="modelId">
                                        <Input placeholder="请输入" className={styles.idInput} />
                                    </FormItemWithoutRequiredMark>
                                </Col>
                            </Row>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark label="组" field="group">
                                        <Input placeholder="请输入" className={styles.groupInput} />
                                    </FormItemWithoutRequiredMark>
                                </Col>
                            </Row>
                            <FormItemWithoutRequiredMark className={styles.tagFormItem}>
                                <Row
                                    justify="space-between"
                                    align="center"
                                    style={{ marginBottom: '8px' }}
                                >
                                    <Space direction="vertical" size={'mini'}>
                                        <Text
                                            style={{
                                                color: '#5c5c5c',
                                                fontWeight: '600',
                                                fontSize: '14px',
                                            }}
                                        >
                                            标签
                                        </Text>
                                        <Text className={styles.labelExact}>标签最多添加3个</Text>
                                    </Space>
                                    <Button
                                        className={styles.addTagBtn}
                                        onClick={handleAddTag}
                                    >
                                        添加
                                    </Button>
                                </Row>
                                {tags.length > 0 && (
                                    <div className={styles.selectedItemList}>
                                        {tags.map((tag, index) => (
                                            <Row
                                                key={`tag-${index}`}
                                                className={styles.selectedItemRow}
                                            >
                                                <Input
                                                    autoFocus={tag === ''}
                                                    value={tag}
                                                    onChange={(value) => {
                                                        if (value && value.length > 20) {
                                                            return;
                                                        }
                                                        const newTags = [...tags];
                                                        newTags[index] = value;
                                                        handleTagChange(newTags);
                                                    }}
                                                    onKeyDown={(e) => {
                                                        if (e.key === 'Enter') {
                                                            e.preventDefault();
                                                            e.stopPropagation();
                                                        }
                                                    }}
                                                    placeholder={`标签 ${index + 1}`}
                                                    suffix={
                                                        <IconTagClose
                                                            className={styles.deleteIcon}
                                                            onClick={() => handleTagClose(index)}
                                                        />
                                                    }
                                                />
                                            </Row>
                                        ))}
                                    </div>
                                )}
                            </FormItemWithoutRequiredMark>
                            <div>
                                <div className={styles.switchContainer}>
                                    <Space
                                        className={styles.switchLeftContent}
                                        direction="vertical"
                                        size={4}
                                    >
                                        <Text className={styles.Switchlabel}>是否启用</Text>
                                        <Text className={styles.SwitchTitle}>模型控制</Text>
                                    </Space>
                                    <div className={styles.switchRightContent}>
                                        <FormItemWithoutRequiredMark
                                            field="isEnabled"
                                            triggerPropName="checked"
                                            style={{ margin: 0 }}
                                        >
                                            <Switch />
                                        </FormItemWithoutRequiredMark>
                                    </div>
                                </div>
                                <div className={styles.switchContainer}>
                                    <Space
                                        className={styles.switchLeftContent}
                                        direction="vertical"
                                        size={4}
                                    >
                                        <Text className={styles.Switchlabel}>
                                            是否允许发送图片/视频
                                        </Text>
                                        <Text className={styles.SwitchTitle}>模型控制</Text>
                                    </Space>
                                    <div className={styles.switchRightContent}>
                                        <FormItemWithoutRequiredMark
                                            field="multiModal"
                                            triggerPropName="checked"
                                            style={{ margin: 0 }}
                                        >
                                            <Switch />
                                        </FormItemWithoutRequiredMark>
                                    </div>
                                </div>
                                <div className={styles.switchContainer}>
                                    <Space
                                        className={styles.switchLeftContent}
                                        direction="vertical"
                                        size={4}
                                    >
                                        <Text className={styles.Switchlabel}>是否允许生成图片</Text>
                                        <Text className={styles.SwitchTitle}>模型控制</Text>
                                    </Space>
                                    <div className={styles.switchRightContent}>
                                        <FormItemWithoutRequiredMark
                                            field="allowGenerateImage"
                                            triggerPropName="checked"
                                            style={{ margin: 0 }}
                                        >
                                            <Switch />
                                        </FormItemWithoutRequiredMark>
                                    </div>
                                </div>
                            </div>
                        </Card>
                    </Col>

                    <Col span={12} className={styles.Card}>
                        <Card bordered={false}>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark
                                        label={<CustomLabel label="供应商" required={true} />}
                                        field="provider"
                                        rules={[{ required: true, message: '请选择供应商' }]}
                                    >
                                        <Select placeholder="请选择" loading={providersLoading}>
                                            {providers.map((item) => (
                                                <Select.Option key={item.id} value={item.id}>
                                                    {item.provider}
                                                </Select.Option>
                                            ))}
                                        </Select>
                                    </FormItemWithoutRequiredMark>
                                </Col>
                            </Row>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark
                                        label={<CustomLabel label="模型" required={true} />}
                                        field="model"
                                        rules={[{ required: true, message: '请选择模型' }]}
                                    >
                                        <Select placeholder="请选择" loading={providerModelsLoading} disabled={!form.getFieldValue('provider')}>
                                            {providerModels.map((item) => (
                                                <Select.Option key={item.id} value={item.id}>
                                                    {item.name || item.id}
                                                </Select.Option>
                                            ))}
                                        </Select>
                                    </FormItemWithoutRequiredMark>
                                </Col>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark
                                        label={<CustomLabel label="密钥" required={true} />}
                                        field="llmModelKeyId"
                                        rules={[{ required: true, message: '请选择模型密钥' }]}
                                    >
                                        <Select placeholder="请选择" loading={modelKeysLoading} disabled={!form.getFieldValue('provider')}>
                                            {modelKeys.map((item) => (
                                                <Select.Option key={item.id} value={item.id}>
                                                    {item.llmProvider ? (item.name ? `${item.llmProvider}(${item.name})` : item.llmProvider) : '未知供应商'}
                                                </Select.Option>
                                            ))}
                                        </Select>
                                    </FormItemWithoutRequiredMark>
                                </Col>
                            </Row>

                            <Row gutter={16}>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark
                                        label={<CustomLabel label="模型类型" required={true} />}
                                        field="modelType"
                                        rules={[{ required: true, message: '请选择模型类型' }]}
                                    >
                                        <Select placeholder="请选择" loading={loading}>
                                            {modelTypes && Object.entries(modelTypes).map(([key, value]) => (
                                                <Select.Option key={value} value={value}>{key}</Select.Option>
                                            ))}
                                        </Select>
                                    </FormItemWithoutRequiredMark>
                                </Col>
                            </Row>

                            <Row gutter={16}>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark label="模型版本" field="modelVersion">
                                        <Input placeholder="请输入" className={styles.modelVersionInput} />
                                    </FormItemWithoutRequiredMark>
                                </Col>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark label="API版本" field="apiVersion">
                                        <Input placeholder="请输入" className={styles.apiVersionInput} />
                                    </FormItemWithoutRequiredMark>
                                </Col>
                            </Row>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark
                                        label={<CustomLabel label="输入成本（每千Token/元）" required={true} />}
                                        field="promptCost"
                                        rules={[{ required: true, message: '请输入输入成本（每千Token/元）' }]}
                                    >
                                        <InputNumber min={0} placeholder="0" />
                                    </FormItemWithoutRequiredMark>
                                </Col>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark
                                        label={<CustomLabel label="输出成本（每千Token/元）" required={true} />}
                                        field="completionCost"
                                        rules={[{ required: true, message: '请输入输出成本（每千Token/元）' }]}
                                    >
                                        <InputNumber min={0} placeholder="0" />
                                    </FormItemWithoutRequiredMark>
                                </Col>
                            </Row>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark
                                        label={
                                            <CustomLabel
                                                label="最大输出（Token数）"
                                                required={selectedModelType === 1 || selectedModelType === 2}
                                            />
                                        }
                                        field="maxTokens"
                                        rules={[
                                            {
                                                validator: (value, callback) => {
                                                    const modelType = selectedModelType;
                                                    // 如果是Chat(2)或Text(1)类型
                                                    if (modelType === 1 || modelType === 2) {
                                                        if (value === undefined || value === null || value === '') {
                                                            callback('当模型类型为Chat或Text时，最大输出Token数必填');
                                                        } else if (value === 0) {
                                                            callback('最大输出Token数不能为0');
                                                        } else {
                                                            callback();
                                                        }
                                                    } else {
                                                        // 其他类型，如果有值则不能为0
                                                        if (value !== undefined && value !== null && value !== '' && value === 0) {
                                                            callback('最大输出Token数不能为0');
                                                        } else {
                                                            callback();
                                                        }
                                                    }
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            min={0}
                                            placeholder={
                                                selectedModelType === 1 || selectedModelType === 2
                                                    ? "请输入（必填）"
                                                    : "请输入（可选）"
                                            }
                                        />
                                    </FormItemWithoutRequiredMark>
                                </Col>
                            </Row>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark
                                        label={
                                            <CustomLabel
                                                label="嵌入维度"
                                                required={selectedModelType === 4}
                                            />
                                        }
                                        field="embeddingDimension"
                                        rules={[
                                            {
                                                validator: (value, callback) => {
                                                    const modelType = selectedModelType;
                                                    // 如果是Embedding(4)类型
                                                    if (modelType === 4) {
                                                        if (value === undefined || value === null || value === '') {
                                                            callback('当模型类型为Embedding时，嵌入维度必填');
                                                        } else {
                                                            const numValue = parseInt(value, 10);
                                                            if (isNaN(numValue) || numValue <= 0) {
                                                                callback('嵌入维度不能为0');
                                                            } else {
                                                                callback();
                                                            }
                                                        }
                                                    } else {
                                                        // 其他类型，如果有值则不能为0
                                                        if (value !== undefined && value !== null && value !== '') {
                                                            const numValue = parseInt(value, 10);
                                                            if (isNaN(numValue) || numValue <= 0) {
                                                                callback('嵌入维度不能为0');
                                                            } else {
                                                                callback();
                                                            }
                                                        } else {
                                                            callback();
                                                        }
                                                    }
                                                }
                                            }
                                        ]}
                                    >
                                        <Input
                                            placeholder={
                                                selectedModelType === 4
                                                    ? "请输入（必填）"
                                                    : "请输入（可选）"
                                            }
                                            className={styles.embeddingDimensionInput}
                                        />
                                    </FormItemWithoutRequiredMark>
                                </Col>
                            </Row>
                            <Row gutter={16}>
                                <Col span={12}>
                                    <FormItemWithoutRequiredMark
                                        label={<CustomLabel label="Temperature" required={true} />}
                                        field="temperature"
                                        rules={[
                                            { required: true, message: '请输入Temperature' },
                                            {
                                                validator: (value, callback) => {
                                                    if (value !== undefined && value !== null && value !== '') {
                                                        const numValue = parseFloat(value);
                                                        if (isNaN(numValue) || numValue <= 0 || numValue > 1) {
                                                            callback('Temperature必须是0-1之间的数字，且不能为0');
                                                        } else {
                                                            callback();
                                                        }
                                                    } else {
                                                        callback();
                                                    }
                                                }
                                            }
                                        ]}
                                    >
                                        <InputNumber
                                            min={0}
                                            max={1}
                                            step={0.1}
                                            placeholder="请输入（0.1-1）"
                                        />
                                    </FormItemWithoutRequiredMark>
                                </Col>
                            </Row>
                        </Card>
                    </Col>
                </Row>

            </Form>
            <Space style={{ display: 'flex', justifyContent: 'flex-end', paddingTop: '16px', marginTop: '16px', borderTop: '1px solid #f5f5f5' }} size={8}>
                <Button onClick={handleCancel} className={styles.cancelButton}>取消</Button>
                <Button
                    onClick={handleSubmit}
                    className={`${styles.submitButton} ${!formValid ? styles.submitButtonDisabled : ''}`}
                    disabled={!formValid}
                >
                    创建
                </Button>
            </Space>
        </div>
    );
};

export default AddModelConfig;
