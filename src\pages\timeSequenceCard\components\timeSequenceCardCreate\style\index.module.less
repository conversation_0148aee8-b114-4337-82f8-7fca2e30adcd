// style/index.module.less

.timeSequenceCardLayout {
  // min-height: 100vh;

  .cardTabs {
    :global(.arco-tabs-header-wrapper) {
      padding-bottom: 16px;
      margin-bottom: 16px;
      border-bottom: 1px solid #f5f5f5;
    }

    :global(.arco-tabs-header-nav::before) {
      display: none;
    }

    :global(.arco-tabs-header-ink) {
      display: none;
    }

    :global(.arco-tabs-header-title) {
      padding: 0;
      font-weight: 600;
      font-size: 20px;
      color: #a6a6a6;

      &:hover {
        color: #a6a6a6;
      }
    }

    :global(.arco-tabs-header-title-active) {
      color: #333333;

      &:hover {
        color: #333333;
      }
    }

    :global(.arco-tabs-content) {
      padding-top: 0px;
    }

    :global(.arco-tabs-header-nav-line.arco-tabs-header-nav-horizontal > .arco-tabs-header-scroll .arco-tabs-header-title:first-of-type) {
      margin-left: 0;
    }

    :global(.arco-tabs-header-nav-line .arco-tabs-header-title) {
      margin: 0 12px;
    }
  }


  .TimeSequenceCardBasicInfo {


    :global(.arco-card-body) {
      padding: 0;
    }

    :global(.arco-form-item) {
      margin-bottom: 24px;
    }

    :global(.arco-form-label-item) {
      font-weight: 600;
      font-size: 14px;
      color: #5c5c5c;
      line-height: 24px;
      text-align: left;
    }

    :global(.arco-input.arco-input-size-default) {
      padding: 8px 12px;
      background-color: transparent;
      border: 1px solid #ebebeb;
      border-radius: 8px;

      &::placeholder {
        font-weight: 400;
        font-size: 14px;
        color: #d6d6d6;
      }

      :global(.arco-input) {
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 8px;
      }
    }

    // 输入框禁用状态
    :global(.arco-input-disabled) {
      background-color: #fcfcfc !important;
      border-color: #ebebeb !important;
      color: #adadad !important;
      cursor: not-allowed !important;

      &::placeholder {
        color: #adadad !important;
      }
    }

    // 按钮禁用状态
    :global(.arco-btn-secondary.arco-btn-disabled) {
      background-color: #ffffff;
      color: #adadad;
      cursor: not-allowed;

      &:hover {
        background-color: #ffffff;
      }
    }

    :global(.arco-textarea) {
      background-color: transparent;
      min-height: 88px;
      border: 1px solid #ebebeb;
      border-radius: 8px;
      padding: 8px 12px;
    }

    :global(.arco-textarea-disabled) {
      background-color: #fcfcfc;
    }

    :global(.arco-textarea-word-limit) {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #adadad;
    }

    :global(.arco-switch) {
      background-color: #d6d6d6;
    }

    //Switch组件样式
    :global(.arco-switch-checked) {
      background-color: #4b5cf2;
    }

    // 非编辑模式下关闭的Switch背景颜色
    :global(.arco-switch[disabled]:not(.arco-switch-checked)) {
      background-color: #e8e8e8;
      opacity: 1;
    }

    :global(.arco-switch[disabled]) {
      background-color: #c3c8fa;
    }



    .basicInfoHeader {
      display: flex;
      margin-bottom: 24px;
      width: 100%;

      .iconAndName {
        display: flex;
        align-items: center;
        width: 100%;

        .IconWrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 72px;
          height: 72px;
          border-radius: 8px;
          background-color: #f5f5f5;
          flex-shrink: 0;

          .Icon {
            width: 72px;
            height: 72px;
          }
        }

        .divider {
          width: 1px;
          height: 72px;
          background-color: #f5f5f5;
          margin: 0 24px;
        }

        .nameFormContainer {
          flex: 1;

          .nameFormItem {
            margin-bottom: 0;
          }
        }
      }
    }

    .descriptionInput {
      max-height: 88px;
      resize: none;
      /* 禁用调整大小 */

      &::placeholder {
        font-weight: 400;
        font-size: 14px;
        color: #d6d6d6;
      }
    }

    .labelExact {
      font-weight: 400;
      font-size: 12px;
      color: #adadad;
    }

    // 添加标签列表样式
    .tagFormItem {
      .selectedItemList {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 8px;
        background-color: #fcfcfc;
        border: 1px solid #f5f5f5;
        border-radius: 8px;

        .selectedItemRow {

          // 输入框禁用状态
          :global(.arco-input-disabled) {
            background-color: transparent !important;
          }
        }
      }

      .selectedItemRow {
        width: 100%;

        :global(.arco-input) {
          padding: 0;
          border: none;
          border-radius: 0;
          background-color: transparent;
        }

        :global(.arco-input-inner-wrapper) {
          padding: 8px 8px 8px 12px;
          border: 1px solid #f5f5f5;
          border-radius: 8px;
          background-color: #ffffff;

          &:hover {
            background-color: #ffffff;
          }
        }
      }

      .deleteIcon {
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .disabledDeleteIcon {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
  }
}

.selectedItemRow {
  .selectedItemCol {
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s;
    padding: 8px 12px;
    border-radius: 8px;
    background-color: #ffffff;
    border: 1px solid #f5f5f5;

    &:hover {
      background-color: #fcfcfc;
      cursor: pointer;
    }

    .itemIconWrapper {
      width: 24px;
      height: 24px;

      .svg {
        width: 100%;
        height: 100%;
      }
    }

    .selectedItemText {
      font-weight: 500;
      font-size: 14px;
      color: #5c5c5c;
    }
  }
}

.cardContainer {
  height: calc(100vh - 230px);

  :global(.arco-switch) {
    background-color: #d6d6d6;
  }

  :global(.arco-switch-checked) {
    background-color: #4455f2;
  }

  :global(.arco-card-body) {
    padding: 0;
    height: 100%;
  }

  .labelExact {
    font-weight: 400;
    font-size: 12px;
    color: #adadad;
  }
}

.pageContainer {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 165px);
  overflow: hidden;
}

.contentWrapper {
  flex-grow: 1;
  overflow-y: auto;
}

.formRow {
  height: 100%;
}

.TimeSequenceCardBasicInfo {
  height: 100%;
}

/* Tag styles */
.tagContainer {
  background-color: #fcfcfc;
  padding: 8px;
  border-radius: 8px;
  border: 1px solid #f5f5f5;
  display: flex;
  flex-wrap: wrap;
  margin-top: 8px;
  width: 100%;

  .tagItem {
    height: 40px;
    width: 100%;
    padding: 8px 12px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #f5f5f5;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #5c5c5c;
  }
}

// 必填图标样式
.requiredIcon {
  color: #4455f2;
  font-size: 15px;
  line-height: 16px;
  margin-left: 4px;
  position: relative;
  top: 2px;
  font-weight: bold;
}

.switchContainer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;

  .switchLeftContent {
    display: flex;
    flex-direction: column;
  }

  .switchRightContent {
    display: flex;
    justify-content: flex-end;
  }
}

.Switchlabel {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #333333;
}

.SwitchTitle {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #adadad;
}

.addTagBtn {
  cursor: pointer;
  padding: 8px 24px;
  background-color: #ffffff !important;
  border: 1px solid #ebebeb !important;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #5c5c5c;

  &:hover {
    background-color: #fcfcfc !important;
    border-color: #ebebeb !important;
  }
}

/* Form styles */
.structureContainer {
  padding: 12px;
  border: 1px solid #ebebeb;
  border-radius: 8px;
  transition: all 0.3s;
  background-color: #fcfcfc;
  position: relative;
  height: 100%;
  overflow-y: auto;
  flex-grow: 1;

  // 代码视图模式下的样式
  &.codeViewMode {
    background-color: #ffffff;
  }

  .emptyStructure {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
  }

  .selectedList {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  /* 代码视图容器 */
  .codeViewContainer {
    height: 100%;
    width: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;

    :global(.arco-textarea) {
      border-radius: 8px;
      padding: 0;
      color: #5c5c5c;
      background-color: #ffffff;
    }

    :global(.arco-textarea-disabled) {
      background-color: #fcfcfc;
      color: #5c5c5c;
    }

    /* 去除textarea聚焦时的边框样式 */
    :global(.arco-textarea:focus),
    :global(.arco-textarea-focus) {
      box-shadow: none !important;
      border-color: transparent !important;
      outline: none !important;
    }

    /* 应用修改按钮样式 */
    :global(.arco-btn-primary) {
      background-color: #4b5cf2;
      border-color: #4b5cf2;
      border-radius: 8px;

      &:hover {
        background-color: #4152e9;
        border-color: #4152e9;
      }
    }
  }
}

.placeholderContent {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;
}

.placeholderText {
  color: #ccc;
}

.templateActions {
  Button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 24px;
    border-radius: 8px;
    border: 1px solid #ebebeb !important;
    background-color: #ffffff;
    transition: all 0.3s;
    height: 40px;

    &:hover {
      background-color: #fafafa !important;
    }

    span {
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      color: #5c5c5c;
    }
  }

  :global(.arco-btn-group) {
    display: flex;
    padding: 4px;
    border-radius: 8px;
    border: 1px solid #ebebeb;
    gap: 4px;

    button {
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      border: none !important;
    }
  }
}

.componentPopover {
  .componentHeader {
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    color: #adadad;
    padding: 2px 4px;
    border-bottom: 1px solid #f5f5f5;
    margin-bottom: 4px;
  }

  :global(.arco-list-item:not(:last-child)) {
    border-bottom: none;
  }

  :global(.arco-list-content) {
    :global(.arco-list-item) {
      padding: 0 !important;
      cursor: pointer;
      border-radius: 4px;

      span {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;
      }

      :hover {
        background-color: #fafafa;
      }
    }
  }

  :global(.arco-space-item) {
    height: 24px;
  }

  .listItem {
    display: flex;
    align-items: center;
    padding: 4px;
  }
}

:global(.arco-popover-content.arco-popover-content-bl) {
  padding: 8px;
  width: 200px;
  border: none;
  border: 1px solid #f5f5f5;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
}

:global(.arco-trigger-arrow.arco-popover-arrow) {
  display: none;
}

.formActions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #f5f5f5;

  .cancelBtn,
  .confirmBtn,
  .editBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    height: 40px;
  }

  .cancelBtn {
    background-color: #ffffff;
    color: #5c5c5c;
    border: 1px solid #ebebeb;

    &:hover {
      background-color: #fafafa !important;
      border-color: #ebebeb !important;
    }
  }

  .confirmBtn {
    background-color: #4455f2;
    color: #ffffff;

    &:hover {
      color: #ffffff !important;
    }

    &:hover:not(.disabledBtn) {
      background-color: #4152e9;
    }
  }

  .editBtn {
    background-color: #4455f2;
    color: #ffffff;

    &:hover {
      color: #ffffff !important;
    }

    &:hover:not(.disabledBtn) {
      background-color: #4152e9;
    }
  }
}

/* Preview styles */
.previewCard {
  height: 100%;
  display: flex;
  flex-direction: column;

  :global(.arco-card-body) {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
  }

  .previewCardTitle {
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    margin-bottom: 8px;
    flex-shrink: 0;
  }

  .previewContainer {
    background-color: #fcfcfc;
    border-radius: 8px;
    transition: all 0.3s;
    border: 1px solid #ebebeb;
    display: flex;
    justify-content: center;
    padding: 30px 0;
    padding-top: 80px;
    overflow-y: auto;
    box-sizing: border-box;
    position: relative;
    height: 100%;
    flex: 1;

    .previewContent {
      display: flex;
      flex-direction: column;
      width: 60%;
      min-height: min-content;
      background-color: #ffffff;
      box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.12);
      padding: 32px;
      border-radius: 8px;
      margin: 0 auto;
      margin-bottom: 30px;
      /* 添加底部边距，防止最后一个元素贴边 */
      height: auto;
      /* 允许高度自适应内容 */
      overflow: visible;
      /* 保证内容不被裁剪 */
      max-width: 500px;
      /* 限制最大宽度 */

      @media screen and (max-width: 1200px) {
        width: 80%;
      }

      .icon {
        margin-bottom: 16px;
      }

      .tagList {
        display: flex;
        margin-bottom: 16px;
        gap: 4px;

        :global(.arco-tag) {
          padding: 6px 10px;
          background-color: #ffffff;
          border-radius: 4px;
          border: 1px solid #ebebeb;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          color: #5c5c5c;
        }
      }

      .previewTitle {
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        margin-bottom: 16px;
      }

      .previewParagraph {
        font-weight: 400;
        font-size: 16px;
        color: #5c5c5c;
        margin-bottom: 16px;
      }

      .audioPlaceholder {
        height: 80px;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 16px;
        background-color: #fcfcfc;
        border: 1px solid #ebebeb;
        position: relative;
        border-radius: 4px;
      }

      .videoPlayBtn,
      .audioPlayBtn {
        width: 40px;
        height: 40px;
        background-color: #ffffff;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
        position: absolute;
        bottom: 16px;
        left: 16px;
      }

      .emptyPreview {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        border: 1px dashed #e5e6eb;
        border-radius: 4px;
        width: 100%;
      }

      .placeholderIcon {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
      }

      .imagePlaceholder,
      .videoPlaceholder {
        width: 100%;
        height: 234px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        margin-bottom: 16px;
        transition: all 0.3s;
        background-color: #fcfcfc;
        border: 1px solid #ebebeb;
        position: relative;
      }

      .linkItem {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        background-color: #f7fcfa;
        border-radius: 4px;
        padding: 6px 10px;
        align-self: flex-start;

        :global(.arco-typography) {
          color: #2ba471;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
        }
      }

      .fileItem {
        background-color: #fef9f0;
        padding: 6px 10px;
        border-radius: 4px;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        align-self: flex-start;

        :global(.arco-typography) {
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          color: #df9f3f;
        }
      }

      // 自定义组件样式
      .customContainer {
        width: 100%;
        margin-bottom: 16px;
        border: 1px solid #ebebeb;
        border-radius: 8px;
        overflow: hidden;
        background-color: #fcfcfc;

        .customDataRow {
          display: flex;
          border-bottom: 1px solid #ebebeb;

          &:last-child {
            border-bottom: none;
          }

          .keyValuePair {
            display: flex;
            width: 100%;

            .inputGroup {
              flex: 1;
              border-right: 1px solid #ebebeb;
              position: relative;

              &:last-child {
                border-right: none;
              }

              .inputBox {
                padding: 8px 12px;
                background-color: #fcfcfc;
                min-height: 20px;
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
                color: #d6d6d6;
                position: relative;
              }
            }
          }
        }
      }
    }

    .emptyPreview {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
    }
  }
}

.confirmModal {
  position: relative;
  padding: 24px;
  width: 480px;
  border-radius: 16px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

  .modalContent {
    display: flex;
    flex-direction: column;

    .modalContentText {
      font-weight: 400;
      font-size: 14px;
      color: #5c5c5c;
    }
  }

  .modalFooter {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .cancelDeleteBtn,
    .confirmDeleteBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 24px;
      border-radius: 8px;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }

    .cancelDeleteBtn {
      background-color: #ffffff;
      border: 1px solid #ebebeb;
      color: #5c5c5c;

      &:hover {
        background-color: #fafafa;
        border-color: #ebebeb;
      }
    }

    .confirmDeleteBtn {
      background-color: #4b5cf2;
      color: #ffffff;
    }
  }

  :global(.arco-modal-header) {
    padding: 0;
    height: auto;
    border-bottom: none;

    :global(.arco-modal-title) {
      font-weight: 600;
      font-size: 18px;
      line-height: 24px;
      color: #333333;
      margin-bottom: 24px;
      text-align: left;
    }
  }

  :global(.arco-modal-content) {
    padding: 0;
    width: 100%;
    height: 100%;
  }

  :global(.arco-modal-footer) {
    display: none;
  }

  :global(.arco-modal-close-icon) {
    position: absolute;
    right: 32px;
    top: 32px;
    font-size: 12px;
    cursor: pointer;
    color: var(--color-text-1);
  }

  :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
    background-color: #4152e9;
    color: #ffffff;
  }
}

:global(.arco-modal-mask) {
  background: rgba(0, 0, 0, 0.08) !important;

}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .cardContainer {
    flex-direction: column;
  }

  :global(.arco-col) {
    width: 100%;
    margin-bottom: 16px;
  }
}

//编辑确认Model
.confirmEditingModal {
  position: relative;
  padding: 24px;
  width: 480px;
  border-radius: 16px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

  :global(.arco-modal-header) {
    padding: 0;
    height: auto;
    border-bottom: none;

    :global(.arco-modal-title) {
      font-weight: 600;
      font-size: 20px;
      line-height: 32px;
      color: #333333;
      margin-bottom: 16px;
      text-align: left;
    }
  }

  :global(.arco-modal-content) {
    padding: 0;
    width: 100%;
    height: 100%;
  }

  :global(.arco-modal-footer) {
    display: none;
  }

  :global(.arco-modal-close-icon) {
    position: absolute;
    right: 24px;
    top: 24px;
    cursor: pointer;
  }

  .modalContent {
    display: flex;
    flex-direction: column;

    .modalContentText {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #5c5c5c;
    }
  }

  .modalFooter {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .cancelEditingBtn,
    .confirmEditingBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      line-height: 24px;
      height: 40px;
      transition: all 0.3s;
    }

    .cancelEditingBtn {
      background-color: #ffffff;
      color: #5c5c5c;
      border: 1px solid #ebebeb;

      &:hover {
        background-color: #fafafa;
        border-color: #ebebeb;
      }
    }

    .confirmEditingBtn {
      background-color: #4455f2;
    }
  }

  :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
    background-color: #4152e9;
    color: #ffffff;
  }
}