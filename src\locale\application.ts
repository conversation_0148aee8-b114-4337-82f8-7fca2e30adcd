const application_i18n = {
  'en-US': {
    'menu.application': 'Agent Team',
    'menu.application.sequenceCard': '时序卡片',
    'menu.application.import': '导入',
    'menu.application.upload.type.error': '请上传zip格式的文件',
    'menu.application.upload.type.support': '支持.zip格式，文件最大支持5MB',
    'menu.application.upload.size.error': '文件大小不能超过5MB',
    'menu.application.delete.title': '删除Agent Team',
    'menu.application.delete.content': '已绑定的智能体将被一并删除，请确定是否删除？',
    'menu.application.search.placeholder': '搜索Agent Team名称',
    'menu.application.info': 'Agent Team详情',
    'menu.application.createApp': '创建Agent Team',
    'menu.application.createTimeSequenceCardTemplate': '创建时序卡片',
    'menu.application.editTimeSequenceCardTemplate': '编辑模版',
    'menu.application.createAgent': '创建智能体',
    'menu.application.createGroup': '创建分组',
    'menu.application.header.appList': 'Agent Team 列表',
    'menu.application.header.search.placeholder': 'AI搜索...',
    'menu.application.timeSequenceCard.header.search.placeholder': 'AI搜索...',
    'menu.application.header.basic.search.placeholder': 'AI搜索...',
    'menu.application.appListCount': '共 {count} 个Agent Team',
    'menu.application.timeSequenceCardCount': '共 {count} 个时序卡片',
    'menu.application.opreate.move': '迁移',
    'menu.application.opreate.del': '删除',
    'menu.application.opreate.save': '保存',
    'menu.application.opreate.create': '创建',
    'menu.application.opreate.del.comfirm.title': '删除确认',
    'menu.application.opreate.del.comfirm.content':
      '它无法撤销。请确认您是否要删除？',
    'menu.application.opreate.errMsg': '操作失败，请刷新重试！',
    'menu.application.opreate.okMsg': '操作成功！',
    'menu.application.create.group.title': '分组设置',
    'menu.application.create.group.form.name': '名称',
    'menu.application.create.group.form.subName': '(必填)',
    'menu.application.create.group.form.name.placeholder': '请输入名称',
    'menu.application.create.group.form.description': '描述',
    'menu.application.create.group.form.description.placeholder':
      '用简单明了的话描述～',
    'menu.application.create.group.form.label': '标签',
    'menu.application.create.group.form.label.desc': '标签最多添加3个',
    'menu.application.create.group.form.label.placeholder': '标签{index}',
    'menu.application.create.group.form.label.addbutton': '添加',
    'menu.application.create.openingSpeech': '我是你的AI助手，请告诉我你的问题或者需要我提供什么帮助',
    'menu.application.create.start': '开始创建Agent Team',
    'menu.application.create.back': '返回',
    'menu.application.create.role.me': '我',
    'menu.application.create.role.agent': '创建助手',
    'menu.application.create.saysomething': '说点什么...',
    'menu.application.create.title': 'Agent Team名称：',
    'menu.application.create.desc': '描述：',
    'menu.application.create.label': '标签：',
    'menu.application.create.agent': '智能体：',
    'menu.application.create.workflow': '工作流：',
    'menu.application.create.knowledge': '知识库：',
    'menu.application.create.knowledge.filecount': '共 {count} 个文件',
    'menu.application.appCount': '{count} Agent Team',
    'menu.application.agentCount': '{count} 智能体',
    'menu.application.header.agentList': '智能体列表',
    'menu.application.header.template': '创建',
    'menu.application.header.templateList': '时序卡片列表',
    'menu.application.header.workflowList': '工作流列表',
    'menu.application.agent': '智能体',
    'menu.application.agent.delete.title': '删除智能体',
    'menu.application.agent.delete.content': '它无法撤销。请确认您是否要删除？',
    'menu.application.agent.count': '共 {count} 个智能体',
    'menu.application.agent.info': '智能体详情',
    'menu.application.agent.create.title': '智能体名称：',
    'menu.application.agent.create.type': '智能体类型：',
    'menu.application.agent.create.model': '模型：',
    'menu.application.agent.create.tools': '工具：',
    'menu.application.agent.create.plugin': '插件：',
    'menu.application.timeSequenceCard': '时序卡片',
    'menu.application.timeSequenceCard.fetch.error': '获取时序卡片列表失败',
    'menu.application.workflow': '工作流',
    'menu.application.workflow.count': '共 {count} 工作流',
    'menu.application.workflow.detail': '工作流详情',
    'menu.application.workflow.delete.title': '删除工作流',
    'menu.application.workflow.search.placeholder': '搜索工作流名称',
    'menu.application.template.setting.names': '命名',
    'menu.application.template.setting.adds': '添加',
    'menu.application.confirm': '确认',
    'menu.application.template.setting.perview': '预览',
    'menu.application.template.setting.placeholder.namessubtitle':
      '设置模板的名称',
    'menu.application.template.setting.operate.img': '图片',
    'menu.application.template.setting.operate.img.placeholder': '文档缩略图',
    'menu.application.template.setting.operate.label': '标签',
    'menu.application.template.setting.operate.label.placeholder': '文档关键词',
    'menu.application.template.setting.operate.icon': '图标',
    'menu.application.template.setting.operate.icon.placeholder':
      '不同文档个性化',
    'menu.application.template.setting.operate.word': '富文本',
    'menu.application.template.setting.operate.word.placeholder':
      '展示文档信息',
    'menu.application.template.setting.operate.cancel': '取消',
    'menu.application.template.setting.operate.create': '创建',
    'menu.application.info.header.basic': '基础信息',
    'menu.application.info.header.settings': '配置',
    'menu.application.info.header.testing': '测试',
    'menu.application.info.basic.names': '名称',
    'menu.application.info.basic.icon': '图标',
    'menu.application.info.basic.placeholder.names': '设置Agent Team的名字',
    'menu.application.info.basic.descript': '描述',
    'menu.application.info.basic.placeholder.descript': '用简单明了的话描述',
    'menu.application.info.basic.switchStatus': '是否启用',
    'menu.application.info.basic.appStatus': 'Agent Team 状态',
    'menu.application.info.basic.label': '标签',
    'menu.application.info.basic.placeholder.label': '标签至多添加3个',
    'menu.application.info.setting.addSequenceCard': '添加时序卡片',
    'menu.application.info.setting.placeholder.addSequenceCard': '选择时序卡片模版',
    'menu.application.info.setting.openingSpeech': '开场白',
    'menu.application.info.setting.placeholder.openingSpeech':
      '自定义开场白文案',
    'menu.application.info.setting.addAgent': '添加智能体',
    'menu.application.info.setting.placeholder.addAgent': '添加已有智能体',
    'menu.application.info.setting.addWorkflow': '添加工作流',
    'menu.application.info.setting.placeholder.addWorkflow': '添加已有工作流',
    'menu.application.info.setting.addKnowledge': '添加知识库',
    'menu.application.info.setting.addDocument': '添加文档库',
    'menu.application.info.setting.placeholder.addKnowledge': '添加已有知识库',
    'menu.application.info.setting.addTimeSequenceCard': '选择时序卡片',
    'menu.application.info.setting.placeholder.addTimeSequenceCard': '选择时序卡片模版',
    'menu.application.info.setting.tools': '系统工具',
    'menu.application.info.setting.placeholder.addTools': '添加已有工具',
    'menu.application.info.setting.AcpSetting': 'ACP配置',
    'menu.application.info.setting.placeholder.addAcpTools': '添加tools和资源',
    'menu.application.info.setting.promptTemplate': '提示词模板',
    'menu.application.info.setting.placeholder.addPromptTemplate': '添加提示词模板',
    'menu.application.info.setting.response': '响应',
    'menu.application.info.setting.placeholder.addResponse': '添加响应',
    'menu.application.info.setting.response.prefix': '前缀',
    'menu.application.info.setting.response.purpose': '用途',
    'menu.application.info.setting.response.content': '内容',
    'menu.application.info.setting.function': '函数',
    'menu.application.info.setting.placeholder.addFunction': '添加函数',
    'menu.application.info.setting.plugin': '插件',
    'menu.application.info.setting.addPlugin': '添加插件',
    'menu.application.info.setting.placeholder.addPlugin': '添加已有插件',
    'menu.application.info.setting.inputChannel': '选择渠道',
    'menu.application.info.setting.inputChannel.platform': '选择发布平台',
    'menu.application.info.setting.placeholder.chooseChannel':
      '渠道',
    'menu.application.info.setting.chooseChannel.wechat': '微信',
    'menu.application.info.setting.chooseChannel.dd': '钉钉',
    'menu.application.info.setting.chooseChannel.web': 'web',
    'menu.application.info.setting.addFile': '文件',
    'menu.application.info.setting.unselected': '未选择',
    'menu.application.info.setting.selected': '已选择：{count} 项',
    'menu.application.info.testing.title': '对话',
    'menu.application.info.testing.agent.title': 'Agent Team测试',
    'menu.application.info.testing.start': '开始测试',
    'menu.application.info.testing.role.agent': '测试助手',
    'menu.application.info.testing.buttom.statisticians': '统计',
    'menu.application.info.testing.buttom.log': '日志',
    'menu.application.info.testing.testAgent': '测试智能体',
    'menu.application.info.testing.testApplication': '测试应用',
    'menu.application.info.testing.content.state': '当前状态',
    'menu.application.info.testing.content.historyState': '历史状态',
    'menu.application.info.testing.content.params': '参数',
    'menu.application.info.testing.content.analyze': '分析',
    'menu.application.info.testing.content.routing': '路由',
    'menu.application.info.testing.content.rebot': '聊天机器人',
    'menu.application.agent.search.placeholder': '搜索智能体名称',
    'menu.application.agent.sort.placeholder': '排序方式',
    'menu.application.agent.search.tags': '选择标签',
    'menu.application.agent.sort.desc': '降序',
    'menu.application.agent.sort.asc': '升序',
    'menu.application.agent.info.basic.placeholder.names': '设置智能体的名字',
    'menu.application.agent.info.setting.layout': '编排',
    'menu.application.agent.info.setting.promptTemplate': '提示词',
    'menu.application.agent.info.setting.placeholder.layout': '人设与回复逻辑',
    'menu.application.agent.info.setting.placeholder.textarea':
      '使用自然语言填写智能体的设定，功能与工作流程',
    'menu.application.agent.info.setting.type': '智能体类型',
    'menu.application.agent.info.setting.placeholder.type': '选择智能体类型',
    'menu.application.agent.info.setting.chooseModel': '选择模型',
    'menu.application.agent.info.setting.placeholder.chooseModel': '选择已有模型',
    'menu.application.status.disabled': '禁用',
    'menu.application.status.enabled': '启用',
    'menu.application.knowledge.create': '创建文档库',
    'menu.application.knowledge.name': '知识库名称',
    'menu.application.knowledge.name.required': '请输入知识库名称',
    'menu.application.knowledge.name.maxLength': '知识库名称最多50个字符',
    'menu.application.knowledge.name.placeholder': '请输入知识库名称',
    'menu.application.knowledge.delete.title': '删除知识库',
    'menu.application.knowledge.delete.content': '它无法撤销。请确认您是否要删除？',
    'menu.application.aiaction': 'AI action',
    ' menu.application.aiaction.create': '创建',
    'menu.application.info.header.routingMap': '路由图',
    'menu.application.agent.info.setting.routeRule': '路由规则',
    'menu.application.agent.info.setting.placeholder.routeRule': '选择路由规则',
    'menu.application.knowledge.search.placeholder': '搜索知识库名称',
    'menu.application.knowledge.file.list': '文件列表',
    'menu.application.knowledge.file.list.question': 'Q&A列表',
    'menu.application.knowledge.file.setting': '设置',
    'menu.application.knowledge.file.upload': '上传文档',
    'menu.application.knowledge.file.upload.question': '新建Q&A',
    'menu.application.knowledge.file.search.placeholder': '搜索文件',
    'menu.application.knowledge.file.content': '文档内容',
    'menu.application.knowledge.file.question': 'Question',
    'menu.application.knowledge.file.answer': 'Answer',
    'menu.application.knowledge.file.info': '信息',
    'menu.application.knowledge.file.size': '大小',
    'menu.application.knowledge.file.createTime': '创建时间',
    'menu.application.knowledge.file.updateTime': '更新时间',
    'menu.application.knowledge.file.createUser': '创建人',
    'menu.application.knowledge.file.name': '文档名称',
    'menu.application.knowledge.file.documentDetail': '文档详情',
    'menu.application.knowledge.file.qandaDetail': 'Q&A详情',
    'menu.application.knowledge.file.upload.clickOrDrag': '点击/拖拽文件',
    'menu.application.knowledge.file.upload.support': '支持.txt格式, 文件大小不得超过512KB',
    'menu.application.knowledge.file.createQanda': '创建问答',
    'menu.application.knowledge.file.empty': '未找到文件',
    'menu.application.knowledge.file.delete': '确认删除',
    'menu.application.knowledge.file.delete.confirm': '确定要删除这个文件吗？此操作不可恢复。',
    'menu.application.knowledge.empty': '暂无知识库',
    'menu.application.knowledge.create.input': '请输入',
    'menu.application.knowledge.createQanda': '创建Q&A库',
    'menu.application.info.testing.clean.title': '清除会话',
    'menu.application.info.testing.clean.content': '会话历史清除后无法恢复，请确认是否清除？',
    'menu.workflow.header.workflowList': '工作流列表',
    'menu.application.opreate.export': '导出',
    'menu.application.opreate.export.success': '导出成功',
    'menu.application.opreate.export.errMsg': '导出失败',
  },
  'zh-CN': {
    'menu.application': 'Agent Team',
    'menu.application.import': '导入',
    'menu.application.upload.type.error': '请上传zip格式的文件',
    'menu.application.upload.type.support': '支持.zip格式，文件最大支持5MB',
    'menu.application.upload.size.error': '文件大小不能超过5MB',
    'menu.application.sequenceCard': '时序卡片',
    'menu.application.delete.title': '删除Agent Team',
    'menu.application.delete.content': '已绑定的智能体将被一并删除，请确定是否删除？',
    'menu.application.search.placeholder': '搜索Agent Team名称',
    'menu.application.info': 'Agent Team详情',
    'menu.application.createApp': '创建Agent Team',
    'menu.application.createTimeSequenceCardTemplate': '创建时序卡片',
    'menu.application.editTimeSequenceCardTemplate': '编辑模版',
    'menu.application.createAgent': '创建智能体',
    'menu.application.createGroup': '创建分组',
    'menu.application.header.appList': 'Agent Team 列表',
    'menu.application.header.search.placeholder': 'AI搜索...',
    'menu.application.timeSequenceCard.header.search.placeholder': 'AI搜索...',
    'menu.application.header.basic.search.placeholder': '搜索名称...',
    'menu.application.appListCount': '共 {count} 个Agent Team',
    'menu.application.timeSequenceCardCount': '共 {count} 个时序卡片',
    'menu.application.opreate.move': '迁移',
    'menu.application.opreate.del': '删除',
    'menu.application.opreate.save': '保存',
    'menu.application.opreate.create': '创建',
    'menu.application.opreate.del.comfirm.title': '删除确认',
    'menu.application.opreate.del.comfirm.content':
      '它无法撤销。请确认您是否要删除？',
    'menu.application.opreate.errMsg': '操作失败，请刷新重试！',
    'menu.application.opreate.okMsg': '操作成功！',
    'menu.application.create.group.title': '分组设置',
    'menu.application.create.group.form.name': '名称',
    'menu.application.create.group.form.subName': '(必填)',
    'menu.application.create.group.form.name.placeholder': '请输入名称',
    'menu.application.create.group.form.description': '描述',
    'menu.application.create.group.form.description.placeholder':
      '用简单明了的话描述～',
    'menu.application.create.group.form.label': '标签',
    'menu.application.create.group.form.label.desc': '标签最多添加3个',
    'menu.application.create.group.form.label.placeholder': '标签{index}',
    'menu.application.create.group.form.label.addbutton': '添加',
    'menu.application.create.openingSpeech': '我是你的AI助手，请告诉我你的问题或者需要我提供什么帮助',
    'menu.application.create.start': '开始创建Agent Team',
    'menu.application.create.back': '返回',
    'menu.application.create.role.me': '我',
    'menu.application.create.role.agent': '创建助手',
    'menu.application.create.saysomething': '说点什么...',
    'menu.application.create.title': 'Agent Team名称：',
    'menu.application.create.desc': '描述：',
    'menu.application.create.label': '标签：',
    'menu.application.create.agent': '智能体：',
    'menu.application.create.workflow': '工作流：',
    'menu.application.create.knowledge': '知识库：',
    'menu.application.create.knowledge.filecount': '共 {count} 个文件',
    'menu.application.appCount': '{count} Agent Team',
    'menu.application.agentCount': '{count} 智能体',
    'menu.application.header.agentList': '智能体列表',
    'menu.application.header.template': '创建',
    'menu.application.header.templateList': '时序卡片列表',
    'menu.application.header.workflowList': '工作流列表',
    'menu.workflow.header.workflowList': '工作流列表',
    'menu.application.agent': '智能体',
    'menu.application.agent.delete.title': '删除智能体',
    'menu.application.agent.delete.content': '它无法撤销。请确认您是否要删除？',
    'menu.application.agent.count': '共 {count} 个智能体',
    'menu.application.agent.search.placeholder': '搜索智能体名称',
    'menu.application.agent.sort.placeholder': '排序方式',
    'menu.application.agent.search.tags': '选择标签',
    'menu.application.agent.sort.desc': '降序',
    'menu.application.agent.sort.asc': '升序',
    'menu.application.agent.create.title': '智能体名称：',
    'menu.application.agent.create.type': '智能体类型：',
    'menu.application.agent.create.model': '模型：',
    'menu.application.agent.create.tools': '工具：',
    'menu.application.agent.create.plugin': '插件：',
    'menu.application.timeSequenceCard': '时序卡片',
    'menu.application.timeSequenceCard.fetch.error': '获取时序卡片列表失败',
    'menu.application.workflow': '工作流',
    'menu.application.workflow.count': '共 {count} 工作流',
    'menu.application.workflow.detail': '工作流详情',
    'menu.application.workflow.delete.title': '删除工作流',
    'menu.application.workflow.search.placeholder': '搜索工作流名称',
    'menu.application.template.setting.names': '命名',
    'menu.application.template.setting.adds': '添加',
    'menu.application.confirm': '确认',
    'menu.application.template.setting.perview': '预览',
    'menu.application.template.setting.placeholder.namessubtitle':
      '设置模板的名称',
    'menu.application.template.setting.operate.img': '图片',
    'menu.application.template.setting.operate.img.placeholder': '文档缩略图',
    'menu.application.template.setting.operate.label': '标签',
    'menu.application.template.setting.operate.label.placeholder': '文档关键词',
    'menu.application.template.setting.operate.icon': '图标',
    'menu.application.template.setting.operate.icon.placeholder':
      '不同文档个性化',
    'menu.application.template.setting.operate.word': '富文本',
    'menu.application.template.setting.operate.word.placeholder':
      '展示文档信息',
    'menu.application.template.setting.operate.cancel': '取消',
    'menu.application.template.setting.operate.create': '创建',
    'menu.application.info.header.basic': '基础信息',
    'menu.application.info.header.settings': '配置',
    'menu.application.info.header.testing': '测试',
    'menu.application.info.basic.names': '名称',
    'menu.application.info.basic.placeholder.names': '设置Agent Team的名字',
    'menu.application.info.basic.icon': '图标',
    'menu.application.info.basic.descript': '描述',
    'menu.application.info.basic.placeholder.descript': '用简单明了的话描述',
    'menu.application.info.basic.switchStatus': '是否启用',
    'menu.application.info.basic.appStatus': 'Agent Team 状态',
    'menu.application.info.basic.label': '标签',
    'menu.application.info.basic.placeholder.label': '标签至多添加三个',
    'menu.application.info.setting.addSequenceCard': '添加时序卡片',
    'menu.application.info.setting.placeholder.addSequenceCard': '选择时序卡片模版',
    'menu.application.info.setting.openingSpeech': '开场白',
    'menu.application.info.setting.placeholder.openingSpeech':'自定义开场白文案',
    'menu.application.info.setting.tools': '系统工具',
    'menu.application.info.setting.placeholder.addTools': '添加已有工具',
    'menu.application.info.setting.AcpSetting': 'ACP配置',
    'menu.application.info.setting.placeholder.addAcpTools': '添加tools和资源',
    'menu.application.info.setting.promptTemplate': '提示词模板',
    'menu.application.info.setting.placeholder.addPromptTemplate': '添加提示词模板',
    'menu.application.info.setting.response': '响应',
    'menu.application.info.setting.placeholder.addResponse': '添加响应',
    'menu.application.info.setting.response.prefix': '前缀',
    'menu.application.info.setting.response.purpose': '用途',
    'menu.application.info.setting.response.content': '内容',
    'menu.application.info.setting.function': '函数',
    'menu.application.info.setting.placeholder.addFunction': '添加函数',
    'menu.application.info.setting.addAgent': '添加智能体',
    'menu.application.info.setting.placeholder.addAgent': '添加已有智能体',
    'menu.application.info.setting.addWorkflow': '添加工作流',
    'menu.application.info.setting.placeholder.addWorkflow': '添加已有工作流',
    'menu.application.info.setting.addKnowledge': '添加知识库',
    'menu.application.info.setting.addDocument': '添加文档库',
    'menu.application.info.setting.placeholder.addKnowledge': '添加已有知识库',
    'menu.application.info.setting.addTimeSequenceCard': '选择时序卡片',
    'menu.application.info.setting.placeholder.addTimeSequenceCard':
      '选择时序卡片模版',
    'menu.application.info.setting.inputChannel': '选择渠道',
    'menu.application.info.setting.inputChannel.platform': '选择发布平台',
    'menu.application.info.setting.placeholder.chooseChannel':
      '渠道',
    'menu.application.info.setting.chooseChannel.wechat': '微信',
    'menu.application.info.setting.chooseChannel.dd': '钉钉',
    'menu.application.info.setting.chooseChannel.web': 'web',
    'menu.application.info.setting.addFile': '文件',
    'menu.application.info.setting.unselected': '未选择',
    'menu.application.info.setting.selected': '已选择：{count} 项',
    'menu.application.info.testing.content.properties': '当前属性',
    'menu.application.info.testing.content.historyState': '历史状态',
    'menu.application.info.testing.testAgent': '测试智能体',
    'menu.application.info.testing.testApplication': '测试应用',
    'menu.application.info.testing.agent.title': 'Agent Team测试',
    'menu.application.info.testing.start': '开始测试',
    'menu.application.info.testing.role.agent': '测试助手',
    'menu.application.status.disabled': '禁用',
    'menu.application.status.enabled': '启用',
    'menu.application.knowledge.create': '创建文档库',
    'menu.application.knowledge.name': '知识库名称',
    'menu.application.knowledge.name.required': '请输入知识库名称',
    'menu.application.knowledge.name.maxLength': '知识库名称最多50个字符',
    'menu.application.knowledge.name.placeholder': '请输入知识库名称',
    'menu.application.knowledge.delete.title': '删除知识库',
    'menu.application.knowledge.delete.content': '它无法撤销。请确认您是否要删除？',
    'menu.application.aiaction.create': '创建',
    'menu.application.info.header.routingMap': '路由图',
    'menu.application.agent.info.setting.routeRule': '路由规则',
    'menu.application.agent.info.setting.placeholder.routeRule': '选择路由规则',
    'menu.application.agent.info.setting.promptTemplate': '提示词',
    'menu.application.knowledge.search.placeholder': '搜索知识库名称',
    'menu.application.knowledge.file.list': '文件列表',
    'menu.application.knowledge.file.setting': '设置',
    'menu.application.knowledge.file.upload': '上传文档',
    'menu.application.knowledge.file.search.placeholder': '搜索文件',
    'menu.application.knowledge.file.content': '文档内容',
    'menu.application.knowledge.file.question': 'Question',
    'menu.application.knowledge.file.answer': 'Answer',
    'menu.application.knowledge.file.info': '信息',
    'menu.application.knowledge.file.size': '大小',
    'menu.application.knowledge.file.createTime': '创建时间',
    'menu.application.knowledge.file.updateTime': '更新时间',
    'menu.application.knowledge.file.createUser': '创建人',
    'menu.application.knowledge.file.name': '文档名称',
    'menu.application.knowledge.file.documentDetail': '文档详情',
    'menu.application.knowledge.file.qandaDetail': 'Q&A详情',
    'menu.application.knowledge.file.upload.clickOrDrag': '点击/拖拽文件',
    'menu.application.knowledge.file.upload.support': '支持.txt格式, 文件大小不得超过512KB',
    'menu.application.knowledge.file.createQanda': '创建问答',
    'menu.application.knowledge.file.empty': '未找到文件',
    'menu.application.knowledge.file.delete': '确认删除',
    'menu.application.knowledge.file.delete.confirm': '确定要删除这个文件吗？此操作不可恢复。',
    'menu.application.knowledge.empty': '暂无知识库',
    'menu.application.knowledge.create.input': '请输入',
    'menu.application.info.testing.clean.title': '清除会话',
    'menu.application.info.testing.clean.content': '会话历史清除后无法恢复，请确认是否清除？',
    'menu.application.opreate.export': '导出',
    'menu.application.opreate.export.success': '导出成功',
    'menu.application.opreate.export.errMsg': '导出失败',
  },
};

export default application_i18n;
