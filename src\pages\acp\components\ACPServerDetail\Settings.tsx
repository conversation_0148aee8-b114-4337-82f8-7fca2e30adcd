import React, { useState, useEffect } from 'react';
import { Form, Button, Input, Typography, Space, Message, Grid, Switch, Slider, Radio, Select } from '@arco-design/web-react';
import { saveAcpServer, checkAcpServerAvailable, getTransportTypes } from '@/lib/services/acp-server-service';
import { AcpServerResponse, TransportType } from '@/types/acpServerType';
import IconDeleteTransportOptions from '@/assets/acp/IconDeleteTransportOptions.svg';
import styles from './style/index.module.less';

const { Text } = Typography;
const FormItem = Form.Item;
const { Row } = Grid;

// 创建一个禁用默认必填标记的表单项组件
const FormItemWithoutRequiredMark = (props) => {
    return <FormItem {...props} requiredSymbol={false} />;
};

// 创建自定义标签组件
const CustomLabel = ({ label, required }) => {
    return (
        <Space>
            <span>{label}</span>
            {required && <Text className={styles.requiredIcon}>*</Text>}
        </Space>
    );
};

// 定义HTTP传输模型接口
interface HttpTransportModel {
    name?: string;
    connection_timeout_seconds?: number;
    additional_headers?: { [key: string]: string };
}

interface SettingsProps {
    acpServer: AcpServerResponse | null;
    onSave?: (updatedServer: AcpServerResponse) => void;
    existingAcps?: AcpServerResponse[];  // 用于检查重名
    onTestConnectionComplete?: () => Promise<void>;  // 测试连接完成的回调（成功或失败都会调用）
}

function Settings({ acpServer, onSave, existingAcps = [], onTestConnectionComplete }: SettingsProps) {
    const [form] = Form.useForm();
    const [isEditing, setIsEditing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [isTestingConnection, setIsTestingConnection] = useState(false);
    // Query Params和Additional Headers相关状态
    const [queryParamsList, setQueryParamsList] = useState<{ key: string, value: string }[]>([]);
    const [additionalHeaders, setAdditionalHeaders] = useState<{ key: string, value: string }[]>([]);
    // 客户端传输项相关状态
    const [useStreamingHttp, setUseStreamingHttp] = useState(false);
    const [clientName, setClientName] = useState('');
    const [connectionTimeout, setConnectionTimeout] = useState(0);
    // 添加传输类型状态
    const [transportTypes, setTransportTypes] = useState<TransportType>({});

    // 获取传输类型列表
    const fetchTransportTypes = async () => {
        try {
            const types = await getTransportTypes();
            setTransportTypes(types);
        } catch (error) {
            console.error('获取传输类型列表失败:', error);
        }
    };

    // 只在组件挂载时获取传输类型
    useEffect(() => {
        fetchTransportTypes();
    }, []);

    // 初始化表单数据
    useEffect(() => {
        if (acpServer) {
            // 初始化客户端传输项
            const httpModel = (acpServer.http_transport_model || {}) as HttpTransportModel;
            const clientNameValue = httpModel.name || '';
            const connectionTimeoutValue = httpModel.connection_timeout_seconds || 0;

            console.log('初始化ACP服务器数据:', {
                acpServer,
                httpModel,
                clientName: clientNameValue,
                connectionTimeout: connectionTimeoutValue
            });

            // 设置状态变量
            setClientName(clientNameValue);
            setConnectionTimeout(connectionTimeoutValue);

            form.setFieldsValue({
                name: acpServer.name || '',
                description: acpServer.description || '',
                transport_type: acpServer.transport_type || 1,
                location: acpServer.location || '',
                client_name: clientNameValue, // 确保表单字段也设置了客户端名称
            });

            // 初始化Query Params
            const queryParams = acpServer.query_params || {};
            const queryParamsArray = Object.entries(queryParams).map(([key, value]) => ({ key, value: String(value) }));
            setQueryParamsList(queryParamsArray);

            // 初始化Additional Headers
            const additionalHeadersObj = httpModel.additional_headers || {};
            const additionalHeadersArray = Object.entries(additionalHeadersObj).map(([key, value]) => ({ key, value: String(value) }));
            setAdditionalHeaders(additionalHeadersArray);
        }
    }, [acpServer, form]);

    // 检查名称是否重复
    const validateName = (value: string, callback: any) => {
        if (!value) {
            callback();
            return;
        }

        // 如果名称与当前ACP的名称相同，则不检查重复
        if (acpServer && value === acpServer.name) {
            callback();
            return;
        }

        // 检查是否存在同名的ACP
        const isDuplicate = existingAcps.some(acp =>
            acp.name === value && acp.id !== acpServer?.id
        );

        if (isDuplicate) {
            callback('该名称已存在，请使用其他名称');
        } else {
            callback();
        }
    };

    // 添加处理query param的函数
    const handleAddQueryParam = () => {
        setQueryParamsList([...queryParamsList, { key: '', value: '' }]);
    };

    // 处理删除query param
    const handleDeleteQueryParam = (index) => {
        const newParams = [...queryParamsList];
        newParams.splice(index, 1);
        setQueryParamsList(newParams);
    };

    // 处理更新query param的键值
    const handleQueryParamChange = (index, field, value) => {
        const newParams = [...queryParamsList];
        newParams[index][field] = value;
        setQueryParamsList(newParams);
    };

    // 在组件中添加以下函数来处理additional headers的添加
    const handleAddAdditionalHeader = () => {
        setAdditionalHeaders([...additionalHeaders, { key: '', value: '' }]);
    };

    // 处理删除additional header
    const handleDeleteAdditionalHeader = (index) => {
        const newHeaders = [...additionalHeaders];
        newHeaders.splice(index, 1);
        setAdditionalHeaders(newHeaders);
    };

    // 处理更新additional header的键值
    const handleAdditionalHeaderChange = (index, field, value) => {
        const newHeaders = [...additionalHeaders];
        newHeaders[index][field] = value;
        setAdditionalHeaders(newHeaders);
    };

    // 处理编辑按钮点击
    const handleEdit = () => {
        setIsEditing(true);
    };

    // 处理取消编辑
    const handleCancel = () => {
        setIsEditing(false);
        // 重置表单和状态
        if (acpServer) {
            // 重置客户端传输项
            const httpModel = (acpServer.http_transport_model || {}) as HttpTransportModel;
            const clientNameValue = httpModel.name || '';
            const connectionTimeoutValue = httpModel.connection_timeout_seconds || 0;

            // 设置状态变量
            setClientName(clientNameValue);
            setConnectionTimeout(connectionTimeoutValue);

            form.setFieldsValue({
                name: acpServer.name || '',
                description: acpServer.description || '',
                transport_type: acpServer.transport_type || 1,
                location: acpServer.location || '',
                client_name: clientNameValue, // 确保表单字段也设置了客户端名称
            });

            // 重置Query Params
            const queryParams = acpServer.query_params || {};
            const queryParamsArray = Object.entries(queryParams).map(([key, value]) => ({ key, value: String(value) }));
            setQueryParamsList(queryParamsArray);

            // 重置Additional Headers
            const additionalHeadersObj = httpModel.additional_headers || {};
            const additionalHeadersArray = Object.entries(additionalHeadersObj).map(([key, value]) => ({ key, value: String(value) }));
            setAdditionalHeaders(additionalHeadersArray);
        }
    };

    // 静默测试连接（不显示消息提示）
    const silentTestConnection = async () => {
        if (!acpServer?.id) {
            return;
        }

        try {
            await checkAcpServerAvailable(acpServer.id);
            // 静默测试，不显示任何消息
        } catch (error) {
            console.error('静默测试连接失败:', error);
            // 静默测试，不显示任何消息
        } finally {
            // 测试完成后刷新ACP工具数据
            if (onTestConnectionComplete) {
                try {
                    await onTestConnectionComplete();
                } catch (refreshError) {
                    console.error('刷新ACP工具数据失败:', refreshError);
                }
            }
        }
    };

    // 处理手动测试连接（显示消息提示）
    const handleTestConnection = async () => {
        if (!acpServer?.id) {
            Message.error('无法获取ACP服务器ID');
            return;
        }

        setIsTestingConnection(true);

        try {
            const result = await checkAcpServerAvailable(acpServer.id);
            if (result.success) {
                Message.success(result.message || '测试连接成功');
            } else {
                Message.error(result.message || '测试连接失败，请检查配置');
            }
        } catch (error) {
            console.error('测试连接失败:', error);
            Message.error('测试连接失败，请检查网络连接');
        } finally {
            setIsTestingConnection(false);
            // 无论测试连接成功还是失败，都刷新ACP工具数据
            if (onTestConnectionComplete) {
                try {
                    await onTestConnectionComplete();
                } catch (refreshError) {
                    console.error('刷新ACP工具数据失败:', refreshError);
                }
            }
        }
    };

    // 处理保存设置
    const handleSave = async () => {
        try {
            const values = await form.validate();
            setIsSaving(true);

            console.log('保存表单数据:', {
                values,
                clientName,
                connectionTimeout
            });

            // 将queryParamsList数组转换为对象格式
            const queryParamsObj: { [key: string]: string } = {};
            queryParamsList.forEach(param => {
                if (param.key && param.value) {
                    queryParamsObj[param.key] = param.value;
                }
            });

            // 将additionalHeaders数组转换为对象格式
            const additionalHeadersObj: { [key: string]: string } = {};
            additionalHeaders.forEach(header => {
                if (header.key && header.value) {
                    additionalHeadersObj[header.key] = header.value;
                }
            });

            // 构建更新数据
            const updateData = {
                id: acpServer?.id,
                name: values.name,
                description: values.description,
                transport_type: values.transport_type, // 使用表单中的transport_type值
                location: values.location,
                is_available: acpServer?.is_available || false, // 保持原有的可用性状态
                query_params: queryParamsObj,
                http_transport_model: {
                    // use_streamable_http: useStreamingHttp,
                    name: clientName, // 使用状态变量中的clientName
                    connection_timeout_seconds: connectionTimeout,
                    additional_headers: additionalHeadersObj
                }
            };

            // 调用更新API
            const result = await saveAcpServer(updateData);

            if (result) {
                Message.success('ACP Server更新成功！');
                setIsEditing(false);

                // 构建更新后的服务器对象
                const updatedServer: AcpServerResponse = {
                    ...acpServer,
                    name: values.name,
                    description: values.description,
                    transport_type: values.transport_type, // 更新transport_type
                    location: values.location,
                    query_params: queryParamsObj,
                    http_transport_model: {
                        // use_streamable_http: useStreamingHttp,
                        name: clientName, // 使用状态变量中的clientName
                        connection_timeout_seconds: connectionTimeout,
                        additional_headers: additionalHeadersObj
                    },
                    updated_time: new Date().toISOString(), // 更新时间
                };

                // 调用回调函数通知父组件更新
                if (onSave) {
                    onSave(updatedServer);
                }

                // 配置保存后进行静默测试连接，确保服务器状态是最新的
                await silentTestConnection();
            }
        } catch (error) {
            console.error('更新ACP失败:', error);
            if (error.message && error.message.includes('验证')) {
                // 表单验证错误，不显示错误消息，让表单自己处理
                return;
            }
            Message.error('更新ACP失败，请检查网络或联系管理员！');
        } finally {
            setIsSaving(false);
        }
    };

    return (
        <div className={styles.settingsContainer}>
            <Form
                form={form}
                layout="vertical"
                className={styles.settingsForm}
                requiredSymbol={false}
            >
                <FormItemWithoutRequiredMark
                    label={<CustomLabel label="名称" required={true} />}
                    field="name"
                    rules={[
                        { required: true, message: '请输入名称' },
                        { validator: validateName }
                    ]}
                    validateTrigger={['onChange', 'onBlur']}
                >
                    <Input disabled={!isEditing} placeholder="请输入" />
                </FormItemWithoutRequiredMark>

                <FormItemWithoutRequiredMark
                    label={<CustomLabel label="描述" required={true} />}
                    field="description"
                    rules={[{ required: true, message: '请输入描述' }]}
                >
                    <Input.TextArea
                        disabled={!isEditing}
                        placeholder="用简单明了的话描述~"
                        rows={4}
                        maxLength={100}
                        showWordLimit
                    />
                </FormItemWithoutRequiredMark>

                <FormItemWithoutRequiredMark
                    label="连接类型"
                    field="transport_type"
                >
                    {/* 使用Select组件渲染传输类型选项*/}
                    <Select placeholder="请选择连接类型" style={{ width: '100%' }} disabled={!isEditing}>
                        {Object.entries(transportTypes).map(([name, value]) => {
                            // 获取中文描述
                            const getDisplayName = (key: string) => {
                                switch (key) {
                                    case 'auto_detect_http':
                                        return '自动检测(auto_detect_http)';
                                    case 'streamable_http':
                                        return '可流式传输的HTTP(streamable_http)';
                                    case 'sse':
                                        return '服务器发送事件(sse)';
                                    default:
                                        return key;
                                }
                            };

                            return (
                                <Select.Option key={name} value={value}>
                                    {getDisplayName(name)}
                                </Select.Option>
                            );
                        })}
                    </Select>
                </FormItemWithoutRequiredMark>

                <FormItemWithoutRequiredMark
                    label={<CustomLabel label="URL" required={true} />}
                    field="location"
                    rules={[{ required: true, message: '请输入URL' }]}
                >
                    <Input disabled={!isEditing} placeholder="请输入" />
                </FormItemWithoutRequiredMark>

                {/* 添加Query Params模块 */}
                <FormItemWithoutRequiredMark className={styles.queryParamsFormItem}>
                    <Row justify="space-between" align="center" style={{ marginBottom: queryParamsList.length > 0 ? '8px' : '0' }}>
                        <Space className={styles.templatePlaceholder} direction="vertical" size={'mini'}>
                            <Text style={{ color: '#333333', fontWeight: '600', fontSize: '14px' }}>参数</Text>
                            <Text className={styles.labelExact}>URL扩展参数</Text>
                        </Space>
                        {/* {isEditing && ( */}
                        <Button
                            className={styles.addTagBtn}
                            onClick={handleAddQueryParam}
                            disabled={!isEditing}
                        >
                            添加
                        </Button>
                        {/* )} */}
                    </Row>
                    {queryParamsList.length > 0 && (
                        <div className={styles.queryParamsContainer} style={{ border: '1px solid #f5f5f5', borderRadius: '8px', padding: '8px', marginTop: '8px', backgroundColor: '#fafafa' }}>
                            <Row className={styles.queryParamsHeader} style={{ marginBottom: '8px', padding: '0 16px' }}>
                                <div style={{ display: 'flex', width: '100%', gap: '8px' }}>
                                    <Text style={{ flex: 1, color: '#adadad', fontSize: '14px' }}>名称</Text>
                                    <Text style={{ flex: 1, color: '#adadad', fontSize: '14px' }}>值</Text>
                                    {isEditing && <div style={{ width: '24px' }}></div>}
                                </div>
                            </Row>

                            <div className={styles.selectedItemList}>
                                {queryParamsList.map((param, index) => (
                                    <Row key={`query-param-${index}`} className={styles.selectedItemRow} align="center" style={{ marginBottom: '8px', padding: '0 8px' }}>
                                        <div style={{ display: 'flex', width: '100%', gap: '8px', alignItems: 'center' }}>
                                            <div style={{ flex: 1, position: 'relative' }}>
                                                <Input
                                                    value={param.key}
                                                    disabled={!isEditing}
                                                    autoFocus={isEditing && index === queryParamsList.length - 1}
                                                    onChange={(value) => {
                                                        if (value && value.length > 50) {
                                                            return;
                                                        }
                                                        handleQueryParamChange(index, 'key', value);
                                                    }}
                                                    placeholder="例如: additionalProp1"
                                                    style={{ width: '100%' }}
                                                />
                                                {isEditing && (
                                                    <div style={{
                                                        position: 'absolute',
                                                        right: '12px',
                                                        bottom: '8px',
                                                        fontSize: '14px',
                                                        color: '#adadad'
                                                    }}>
                                                        {param.key ? param.key.length : 0}/50
                                                    </div>
                                                )}
                                            </div>
                                            <div style={{ flex: 1, position: 'relative' }}>
                                                <Input
                                                    value={param.value}
                                                    disabled={!isEditing}
                                                    onChange={(value) => {
                                                        if (value && value.length > 50) {
                                                            return;
                                                        }
                                                        handleQueryParamChange(index, 'value', value);
                                                    }}
                                                    placeholder="例如: string"
                                                    style={{ width: '100%' }}
                                                />
                                                {isEditing && (
                                                    <div style={{
                                                        position: 'absolute',
                                                        right: '12px',
                                                        bottom: '8px',
                                                        fontSize: '14px',
                                                        color: '#adadad'
                                                    }}>
                                                        {param.value ? param.value.length : 0}/50
                                                    </div>
                                                )}
                                            </div>
                                            {/* {isEditing && ( */}
                                            <Button
                                                className={styles.deleteOptionBtn}
                                                icon={<IconDeleteTransportOptions />}
                                                disabled={!isEditing}
                                                onClick={() => {
                                                    handleDeleteQueryParam(index);
                                                }}
                                            />
                                            {/* )} */}
                                        </div>
                                    </Row>
                                ))}
                            </div>
                        </div>
                    )}
                </FormItemWithoutRequiredMark>

                {/* 客户端传输项模块 */}
                <div className={styles.clientTransportSection}>
                    <Text style={{ color: '#333333', fontWeight: '500', fontSize: '14px', marginBottom: '24px', padding: '4px 10px', borderRadius: '8px', backgroundColor: '#f7f7f7', display: 'inline-block' }}>
                        HTTP客户端传输选项
                    </Text>

                    {/* 使用可流式传输的HTTP */}
                    {/* <FormItemWithoutRequiredMark style={{ marginBottom: '24px' }} field="use_streamable_http">
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Text style={{ color: '#333333', fontWeight: '600', fontSize: '14px' }}>
                                使用可流式传输的HTTP
                            </Text>
                            <Switch
                                checked={useStreamingHttp}
                                disabled={!isEditing}
                                onChange={setUseStreamingHttp}
                            />
                        </div>
                    </FormItemWithoutRequiredMark> */}

                    {/* 客户端名称 */}
                    <FormItemWithoutRequiredMark
                        label={<CustomLabel label="名称" required={false} />}
                        field="client_name"
                    >
                        <Input
                            placeholder="请输入"
                            disabled={!isEditing}
                            value={clientName}
                            onChange={(value) => {
                                console.log('设置客户端名称:', value);
                                setClientName(value);
                            }}
                        />
                    </FormItemWithoutRequiredMark>

                    {/* 初始连接超时时间 */}
                    <FormItemWithoutRequiredMark
                        label="初始连接超时时间"
                        field="connection_timeout_seconds"
                    >
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }} className={styles.connectionTimeoutContainer}>
                            <Input
                                style={{ width: '120px' }}
                                disabled={!isEditing}
                                value={connectionTimeout.toString()}
                                onChange={(value) => {
                                    const numValue = parseInt(value) || 0;
                                    if (numValue >= 0 && numValue <= 60) {
                                        setConnectionTimeout(numValue);
                                    }
                                }}
                                placeholder="0"
                            />
                            <div style={{ flex: 1, paddingLeft: '16px', paddingRight: '16px' }} className={styles.sliderContainer}>
                                <Slider
                                    value={connectionTimeout}
                                    disabled={!isEditing}
                                    onChange={(value) => {
                                        if (typeof value === 'number') {
                                            setConnectionTimeout(value);
                                        }
                                    }}
                                    min={0}
                                    max={60}
                                    step={1}
                                    style={{ width: '100%' }}
                                />
                            </div>
                        </div>
                    </FormItemWithoutRequiredMark>

                    {/* 添加Additional Headers模块 */}
                    <FormItemWithoutRequiredMark className={styles.additionalHeadersFormItem} style={{ marginBottom: '0px' }} field="additional_headers">
                        <Row justify="space-between" align="center" style={{ marginBottom: additionalHeaders.length > 0 ? '8px' : '0' }}>
                            <Space className={styles.templatePlaceholder} direction="vertical" size={'mini'}>
                                <Text style={{ color: '#333333', fontWeight: '600', fontSize: '14px' }}>自定义HTTP请求头</Text>
                            </Space>
                            {/* {isEditing && ( */}
                            <Button
                                className={styles.addTagBtn}
                                onClick={handleAddAdditionalHeader}
                                disabled={!isEditing}
                            >
                                添加
                            </Button>
                            {/* )} */}
                        </Row>

                        {additionalHeaders.length > 0 && (
                            <div className={styles.additionalHeadersContainer} style={{ border: '1px solid #f5f5f5', borderRadius: '8px', padding: '8px', marginTop: '8px', backgroundColor: '#fafafa' }}>
                                <Row className={styles.additionalHeadersHeader} style={{ marginBottom: '8px', padding: '0 16px' }}>
                                    <div style={{ display: 'flex', width: '100%', gap: '8px' }}>
                                        <Text style={{ flex: 1, color: '#adadad', fontSize: '14px' }}>名称</Text>
                                        <Text style={{ flex: 1, color: '#adadad', fontSize: '14px' }}>值</Text>
                                        {isEditing && <div style={{ width: '24px' }}></div>}
                                    </div>
                                </Row>

                                <div className={styles.selectedItemList}>
                                    {additionalHeaders.map((option, index) => (
                                        <Row key={`additional-header-${index}`} className={styles.selectedItemRow} align="center" style={{ marginBottom: '8px', padding: '0 8px' }}>
                                            <div style={{ display: 'flex', width: '100%', gap: '8px', alignItems: 'center' }}>
                                                <div style={{ flex: 1, position: 'relative' }}>
                                                    <Input
                                                        value={option.key}
                                                        disabled={!isEditing}
                                                        autoFocus={isEditing && index === additionalHeaders.length - 1}
                                                        onChange={(value) => {
                                                            if (value && value.length > 99) {
                                                                return;
                                                            }
                                                            handleAdditionalHeaderChange(index, 'key', value);
                                                        }}
                                                        placeholder="例如: additionalProp1"
                                                        style={{ width: '100%' }}
                                                    />
                                                    {isEditing && (
                                                        <div style={{
                                                            position: 'absolute',
                                                            right: '12px',
                                                            bottom: '8px',
                                                            fontSize: '14px',
                                                            color: '#adadad'
                                                        }}>
                                                            {option.key ? option.key.length : 0}/99
                                                        </div>
                                                    )}
                                                </div>
                                                <div style={{ flex: 1, position: 'relative' }}>
                                                    <Input
                                                        value={option.value}
                                                        disabled={!isEditing}
                                                        onChange={(value) => {
                                                            // if (value && value.length > 50) {
                                                            //     return;
                                                            // }
                                                            handleAdditionalHeaderChange(index, 'value', value);
                                                        }}
                                                        placeholder="例如: string"
                                                        style={{ width: '100%' }}
                                                    />
                                                    {isEditing && (
                                                        <div style={{
                                                            position: 'absolute',
                                                            right: '12px',
                                                            bottom: '8px',
                                                            fontSize: '14px',
                                                            color: '#adadad'
                                                        }}>
                                                            {option.value ? option.value.length : 0}
                                                        </div>
                                                    )}
                                                </div>
                                                {/* {isEditing && ( */}
                                                <Button
                                                    className={styles.deleteOptionBtn}
                                                    icon={<IconDeleteTransportOptions />}
                                                    onClick={() => {
                                                        handleDeleteAdditionalHeader(index);
                                                    }}
                                                    disabled={!isEditing}
                                                />
                                                {/* )} */}
                                            </div>
                                        </Row>
                                    ))}
                                </div>
                            </div>
                        )}
                    </FormItemWithoutRequiredMark>
                </div>
            </Form>
            <div className={styles.formFooter}>
                <div className={styles.leftActions}>
                    <Button
                        className={styles.testConnectionButton}
                        onClick={handleTestConnection}
                        loading={isTestingConnection}
                        disabled={isEditing || isSaving}
                    >
                        测试连接
                    </Button>
                </div>
                <div className={styles.rightActions}>
                    {isEditing ? (
                        <>
                            <Button
                                className={styles.cancelButton}
                                onClick={handleCancel}
                                disabled={isSaving}
                            >
                                取消
                            </Button>
                            <Button
                                type="primary"
                                className={styles.saveButton}
                                onClick={handleSave}
                                loading={isSaving}
                            >
                                保存
                            </Button>
                        </>
                    ) : (
                        <Button type="primary" className={styles.editButton} onClick={handleEdit}>
                            编辑
                        </Button>
                    )}
                </div>
            </div>
        </div>
    );
}

export default Settings; 