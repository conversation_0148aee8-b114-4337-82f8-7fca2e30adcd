import axiosInstance from './interceptors';
import { endpoints } from './api-endpoints';
import { WorkflowListParams, WorkflowListResponse } from '@/types/workflowType';

/**
 * 获取工作流列表
 * @param params 查询参数
 * @returns Promise<WorkflowListResponse>
 */
export async function getWorkflowList(
  params: WorkflowListParams = {}
): Promise<WorkflowListResponse> {
  try {
    const response = await axiosInstance.get(endpoints.userDefinitionsUrl, {
      params: {
        'Pager.Page': params.Pager?.Page || 1,
        'Pager.Size': params.Pager?.Size || 16,
        'Pager.Sort': 'updated_datetime',
        'Pager.Order': params.Pager?.Order || null,
      },
    });
    return response.data;
  } catch (error) {
    console.error('获取工作流列表失败:', error);
    throw error;
  }
}

/**
 * 获取Agent Team工作流列表
 * @param params 查询参数
 * @returns Promise<WorkflowListResponse>
 */
export async function getAgentTeamWorkflowList( params){
  try {
    const response = await axiosInstance.get(endpoints.workflowList, {
      params: {
        'Page': params.Pager?.Page || 0,
        'PageSize': params.Pager?.Size || 16,
        'versionOptions': 'Latest',
        'SearchTerm': params.SearchTerm || '',
        'IsSystem': 'False',
        'OrderDirection': 'Ascending',
      },
    });
    return response.data;
  } catch (error) {
    console.error('获取工作流列表失败:', error);
    throw error;
  }
}

/**
 * 检查Agent Team工作流名称是否唯一
 * @param params 参数
 */
export async function isWorkflowExist(name){
  try {
    const response = await axiosInstance.get(endpoints.isWorkflowExist, {
      params: {
        'name': name || '',
      },
    });
    return response.data;
  } catch (error) {
    console.error('检测工作流名称失败:', error);
    throw error;
  }
}

/**
 * 创建Agent Team工作流
 * @param params 添加参数
 */
export async function addAgentTeamWorkflow(params){
  try {
    const response = await axiosInstance.post(endpoints.addWorkflow, params);
    return response.data;
  } catch (error) {
    console.error('新增工作流失败:', error);
    throw error;
  }
}

/**
 * 删除Agent Team工作流
 * @param params 删除参数
 */
export async function deleteAgentTeamWorkflow(id){
  try {
    const url = endpoints.deleteWorkflow.replace('{id}', id);
    const response = await axiosInstance.delete(url);
    return response.data;
  } catch (error) {
    console.error('删除工作流失败:', error);
    throw error;
  }
}

