// src/types/applicationType.ts

// 定义应用列表请求参数接口
export interface ApplicationListParams {
    pager?: {
        page: number;
        size: number;
        count?: number;
    };
    useHook?: boolean;
}

// 定义应用列表响应数据接口
export interface ApplicationListResponse {
    items: Array<{
        id: string;
        name: string;
        description: string;
        type: string;
        provider: string;
        channel: string;
        iconUrl: string;
        installed: boolean;
        isPublished: boolean;
        disabled: boolean;
        createdDateTime: string;
        updatedDateTime: string;
    }>;
    count: number;
}

// 应用项目接口
export interface ApplicationItem {
    id: string;
    name: string;
    description: string;
    type: string;
    provider: string;
    channel: string;
    iconUrl: string;
    installed: boolean;
    isPublished: boolean;
    disabled: boolean;
    createdDateTime: string;
    updatedDateTime: string;
}

// 可以添加更多接口定义，例如创建应用的参数等
export interface CreateApplicationParams {
    name: string;
    description?: string;
    type: string;
}

export interface UpdateApplicationParams {
    name?: string;
    description?: string;
    type?: string;
    disabled?: boolean;
}