import React, { useState, useEffect, useRef } from 'react';
import { Card, Typography, Space, Button, Grid, Divider, Anchor } from '@arco-design/web-react';
import styles from './style/index.module.less';
import IconSDKPlus from '@/assets/sdk/IconSDKPlus.svg';


const { Row, Col } = Grid;
const { Title, Paragraph, Text } = Typography;
const { Link } = Anchor;

function SdkDetail() {
    const [activeSection, setActiveSection] = useState('intro');
    const scrollContainerRef = useRef(null);
    // 模拟SDK数据
    const sdkData = {
        name: '微信小程序版SDK',
        version: '1.0.1',
        updateDate: '2024/06/24',
        icon: '📁',
        iconColor: '#ff9a2a',
        sections: [
            {
                id: 'intro',
                title: '1. 产品概述',
                content: `为解决在非通用型计算设备上使用小程序的诉求，我们推出了小程序硬件框架（Wechat Mini-Program Framework, WMPF）。
WMPF 作为一个运行环境，能让硬件设备(非通用型计算设备) 在缺乏条件运行微信客户端的情况下运行微信小程序，目前已支持安卓设备。
常见的通用型计算设备包括但不限于智能手机、笔记本电脑、台式电脑以及通用平板电脑等。`
            },
            {
                id: 'capability',
                title: '2. 产品能力',
                content: `WMPF 上运行的微信小程序，与手机客户端的微信小程序能力一致。通过 WMPF ，开发者可以将微信平台能力赋能到硬件设备上。
由于非通用硬件的使用场景和能力与手机有所不同，通过 WMPF 小程序支持的能力也与微信客户端的小程序能力有一定差异，详情请见《WMPF 小程序能力说明》。
最低硬件配置：
• 四核 2GHz CPU
• 内存 2GB RAM + 8GB ROM
• 安卓 7.1 及以上`
            },
            {
                id: 'advantage',
                title: '3. 产品优势',
                content: `对于硬件开发者：
• 开发成本低：开发者无需担忧入驻成本，对 App 作开发或维护更新，小程序可以较网页更新，即更即用。
• 内容生态丰富：微信小程序生态中有丰富的内容服务，硬件可以直接运行现成的小程序内容，为用户提供完善的服务。
对于小程序开发者：
• 跨终端运行：仅需完成一次开发，即可实现小程序多端运行，适配成本低。
• 拓展线下场景：小程序获得更多线下场景的流量，通过线下场景增加用户引流至线上。`
            },
            {
                id: 'scenario',
                title: '4. 使用场景',
                content: `WMPF 小程序硬件框架，可以应用在各行各业的安卓系统平板电脑、大屏设备等硬件，提供低成本解耦互动解决方案，可接入设备包括但不限于：
• 智慧零售：收银机 / 货架机 / 导购屏 / 自动贩卖机 / 点餐平板 / 互动广告屏等等
• 家用及娱乐设备：智能冰箱 / 儿童平板 / 跑步机 / 电视机 / KTV 点歌机等
• 公共服务：医院挂号机 / 图书馆终端机 / 美术馆办卡机等
• 办公设备：教育平板 / 会议终端 / 会议投屏等`
            },
            {
                id: 'interface',
                title: '5. 用户界面',
                content: `WMPF 的小程序与手机微信中运行的小程序几乎完全一致，它可以从设备的「桌面」直接启动，但为保护隐私，登录、分享等涉及到用户个人信息的场景需要用户授权。`
            }
        ],
        subSections: [
            { id: 'wechatLogin', title: '微信登录', parentId: 'interface' },
            { id: 'wechatShare', title: '微信分享', parentId: 'interface' },
            { id: 'wechatPay', title: '微信支付', parentId: 'interface' }
        ]
    };

    const handleAnchorChange = (link) => {
        setActiveSection(link.replace('#', ''));
    };

    const renderContent = () => {
        return sdkData.sections.map(section => (
            <div key={section.id} id={section.id} className={styles.contentSection}>
                <Title heading={5} className={styles.sectionTitle}>{section.title}</Title>
                <Paragraph style={{ whiteSpace: 'pre-line' }} className={styles.sectionContent}>{section.content}</Paragraph>
                {/* Render subsections if any */}
                {section.id === 'interface' && sdkData.subSections
                    .filter(sub => sub.parentId === 'interface')
                    .map(sub => (
                        <div key={sub.id} id={sub.id} className={styles.subSection}>
                            <Title heading={6}>· {sub.title}</Title>
                            <Paragraph>此处为{sub.title}的详细内容...</Paragraph>
                        </div>
                    ))}
            </div>
        ));
    };

    return (
        <div className={styles.container}>
            {/* 基础信息模块 */}
            <Row align="center" justify="space-between">
                <Text className={styles.sdkTitle}>开发文档</Text>
                <Space size="small" className={styles.sdkActions}>

                </Space>
                <Button type="primary" className={styles.sdkDownloadBtn}>下载SDK</Button>
            </Row>
            <Divider className={styles.divider} />
            {/* SDK详情模块 */}
            <div className={styles.detailContainer}>
                <Row gutter={40}>
                    {/* 左侧内容区 */}
                    <Col xs={24} md={12} className={styles.contentArea}>
                        <Row className={styles.metaInfo}>
                            <IconSDKPlus />
                            <Space direction='vertical' size={0}>
                                <Title className={styles.title}>SDK</Title>
                                <Space className={styles.meta}>
                                    <Text>版本: 0.0.4</Text>
                                    <Text>|</Text>
                                    <Text>@AI4C</Text>
                                    <Text>|</Text>
                                    <Text>创建时间：2024/10/25</Text>
                                </Space>
                            </Space>
                        </Row>
                        <Card bordered={false}>
                            <div className={styles.detailContent} ref={scrollContainerRef}>
                                {renderContent()}
                            </div>
                        </Card>
                    </Col>

                    {/* 右侧目录区 */}
                    <Col xs={24} md={12} className={styles.tocArea}>
                        <div className={styles.tocSticky}>
                            <Card bordered={false}>
                                <Title heading={5} className={styles.tocTitle} style={{ marginTop: 0, marginBottom: 8 }}>目录</Title>
                                <Anchor
                                    // lineless
                                    onChange={handleAnchorChange}
                                    className={styles.anchor}
                                // currentLink={`#${activeSection}`}
                                >
                                    {sdkData.sections.map(section => (
                                        <Link key={section.id} href={`#${section.id}`} title={section.title.substring(3)} />
                                    ))}
                                    {/* Sub navigation items */}
                                    {sdkData.subSections.map(sub => (
                                        <Link
                                            key={sub.id}
                                            href={`#${sub.id}`}
                                            title={sub.title}
                                            className={styles.subLink}
                                        />
                                    ))}
                                </Anchor>
                            </Card>
                        </div>
                    </Col>
                </Row>
            </div>
        </div>
    );
}

export default SdkDetail;