import { IModalManagerChildrenProps } from '@/pages/knowledge/components/file-manager/src/components/modal-manager';
import { useTranslate } from '@/pages/knowledge/components/file-manager/src/hooks/common-hooks';
import { useGetFolderId } from '@/pages/knowledge/components/file-manager/src/hooks/file-manager-hooks';
import { Button, Form, Input, Modal, Space, Typography } from 'antd';
import { useState } from 'react';
import styles from './index.module.less';
import FileIcon from '@/assets/knowledge/FileIcon.svg';
import IconCloseTag from '@/assets/close.svg';

const { Text } = Typography;

interface IProps extends Omit<IModalManagerChildrenProps, 'showModal'> {
  loading: boolean;
  onOk: (name: string, description?: string, tags?: string[]) => void;
}

const FolderCreateModal = ({ visible, hideModal, loading, onOk }: IProps) => {
  const [form] = Form.useForm();
  const { t } = useTranslate('common');
  const [formValues, setFormValues] = useState<{ name?: string; description?: string }>({});
  const [tags, setTags] = useState<string[]>([]);
  const currentFolderId = useGetFolderId();
  
  // 判断是否为根目录（主页）
  const isRootDirectory = !currentFolderId;

  type FieldType = {
    name?: string;
    description?: string;
  };

  const handleOk = async () => {
    const ret = await form.validateFields();
    const nonEmptyTags = tags.filter((tag) => tag.trim() !== '');
    
    // 如果在二级文件夹中，传递空的描述和标签
    if (isRootDirectory) {
      await onOk(ret.name, ret.description, nonEmptyTags);
    } else {
      await onOk(ret.name, '', []);
    }
    
    // 创建成功后清理表单数据
    form.resetFields();
    setFormValues({});
    setTags([]);
  };

  // 监听表单值变化
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    setFormValues(allValues);
  };

  // 处理Modal关闭
  const handleCancel = () => {
    form.resetFields();
    setFormValues({});
    setTags([]);
    hideModal();
  };

  // 判断确认按钮是否应该禁用
  const isConfirmDisabled = !formValues.name?.trim() || loading;

  // 新增标签输入项
  const showTagInput = () => {
    if (tags.length >= 3) {
      // 这里可以添加提示信息
      return;
    }
    setTags([...tags, '']);
  };

  // 处理标签变化
  const handleTagChange = (newTags: string[]) => {
    setTags(newTags);
  };

  // 自定义footer
  const customFooter = (
    <div className={styles.customFooter}>
      <Button
        className={styles.cancelButton}
        onClick={handleCancel}
      >
        取消
      </Button>
      <Button
        type="primary"
        className={styles.confirmButton}
        loading={loading}
        disabled={isConfirmDisabled}
        onClick={handleOk}
      >
        创建
      </Button>
    </div>
  );

  return (
    // 旧的modal
    // <Modal
    //   title={t('newFolder', { keyPrefix: 'fileManager' })}
    //   open={visible}
    //   onOk={handleOk}
    //   onCancel={hideModal}
    //   okButtonProps={{ loading }}
    //   confirmLoading={loading}
    // >
    //   <Form
    //     name="basic"
    //     labelCol={{ span: 4 }}
    //     wrapperCol={{ span: 20 }}
    //     style={{ maxWidth: 600 }}
    //     autoComplete="off"
    //     form={form}
    //   >
    //     <Form.Item<FieldType>
    //       label={t('name')}
    //       name="name"
    //       rules={[{ required: true, message: t('namePlaceholder') }]}
    //     >
    //       <Input />
    //     </Form.Item>
    //   </Form>
    // </Modal>

    <Modal
      title={'新的文件组'}
      open={visible}
      onCancel={handleCancel}
      footer={customFooter}
      className={styles.folderCreateModal}
    >
      <div className={styles.formContainer}>
        <Form
          name="folderCreateForm"
          layout="vertical"
          autoComplete="off"
          form={form}
          onValuesChange={handleFormValuesChange}
        >
          <div className={styles.topSection}>
            <div className={styles.iconSection}>
              <FileIcon className={styles.folderIcon} />
            </div>
            <div className={styles.divider}></div>
            <div className={styles.nameFormSection}>
              <Form.Item<FieldType>
                label={
                  <span className={styles.labelWithRequired}>
                    名称
                    <span className={styles.requiredStar}>*</span>
                  </span>
                }
                name="name"
                required={false}
                rules={[{ required: true, message: t('namePlaceholder') }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
            </div>
          </div>
          
          {/* 只在主页（根目录）显示描述和标签 */}
          {isRootDirectory && (
            <div className={styles.bottomSection}>
              <Form.Item<FieldType>
                label="描述"
                name="description"
              >
                <Input.TextArea
                  placeholder="请输入"
                  rows={4}
                  maxLength={100}
                />
              </Form.Item>
              
              <Form.Item
                className={styles.tagFormItem}
              >
                <div className={styles.tagHeader}>
                  <Space direction="vertical" size={4}>
                    <Text className={styles.tagLabel}>标签</Text>
                    <Text className={styles.tagHint}>标签至多添加3个</Text>
                  </Space>
                  <Button
                    className={styles.addTagBtn}
                    onClick={showTagInput}
                    disabled={tags.length >= 3}
                  >
                    添加
                  </Button>
                </div>

                {tags.length > 0 && (
                  <div className={styles.selectedItemList}>
                    {tags.map((tag, index) => (
                      <div key={`tag-${index}`} className={styles.selectedItemRow}>
                        <Input
                          autoFocus={tag === ''}
                          value={tag}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value && value.length > 20) {
                              return;
                            }
                            const newTags = [...tags];
                            newTags[index] = value;
                            handleTagChange(newTags);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              e.stopPropagation();
                            }
                          }}
                          placeholder={`标签 ${index + 1}`}
                          suffix={
                            <IconCloseTag
                              className={styles.deleteIcon}
                              onClick={() => {
                                const newTags = [...tags];
                                newTags.splice(index, 1);
                                handleTagChange(newTags);
                              }}
                            />
                          }
                        />
                      </div>
                    ))}
                  </div>
                )}
              </Form.Item>
            </div>
          )}
        </Form>
      </div>
    </Modal>
  );
};

export default FolderCreateModal;
