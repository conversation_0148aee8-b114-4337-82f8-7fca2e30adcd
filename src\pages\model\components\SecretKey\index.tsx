import React, { useState, useEffect, useRef } from 'react';
import { Table, Button, Input, Typography, Space, Select, Modal, Form, Message, Grid, Spin } from '@arco-design/web-react';
import IconSearch from '@/assets/model/IconSearch.svg';
import IconKeyPlus from '@/assets/model/IconKeyPlus.svg';
import IconKey from '@/assets/model/IconKey.svg';
import IconKey2 from '@/assets/model/IconKey2.svg';
import IconKey3 from '@/assets/model/IconKey3.svg';
import IconKey4 from '@/assets/model/IconKey4.svg';
import IconClose from '@/assets/model/IconClose.svg';
import IconSortType from '@/assets/model/IconSortType.svg';
import IconEmptyKey from '@/assets/model/IconEmptyKey.svg';
import styles from './style/index.module.less';
import { getLlmModelKeys, createLlmModelKey, deleteLlmModelKey, updateLlmModelKey, getLlmProviderList } from '@/lib/services/llm-model-service';
import { LlmModelKeyCreateRequest, LlmModelKeyResponse, LlmModelKeyUpdateRequest, LlmProviderResponse } from '@/types/llmModelType';

const { Text } = Typography;
const Option = Select.Option;
const { Row } = Grid;

// 创建一个不显示必填图标的 Form.Item
const FormItemWithoutRequiredMark = (props) => {
    const { children, ...rest } = props;
    return (
        <Form.Item
            {...rest}
            requiredSymbol={false} // 禁用 Arco Design 默认的必填标记
        >
            {children}
        </Form.Item>
    );
};

// 自定义的必填图标组件
const RequiredIcon = () => (
    <span className={styles.requiredIcon}>*</span>
);

// 自定义表单项标签组件
const CustomLabel = ({ label, required }) => {
    return (
        <Space>
            <span>{label}</span>
            {required && <RequiredIcon />}
        </Space>
    );
};

function SecretKey() {
    const [sortType, setSortType] = useState('createTime');
    const [searchValue, setSearchValue] = useState('');
    const [modalVisible, setModalVisible] = useState(false);
    const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
    const [modalMode, setModalMode] = useState('view'); // 'view' for "密钥详情", 'create' for "新增密钥"
    const [selectedKey, setSelectedKey] = useState(null);
    const [keyToDelete, setKeyToDelete] = useState(null); // 记录待删除的密钥
    const [form] = Form.useForm(); // 创建Form实例
    const [modelkeysdata, setModelKeysData] = useState([]);
    const [displayData, setDisplayData] = useState([]);
    const [loading, setLoading] = useState(false);
    const keyIcons = [IconKey, IconKey2, IconKey3, IconKey4];
    const [isEditing, setIsEditing] = useState(false);
    const [formChanged, setFormChanged] = useState(false);
    const originalFormValuesRef = useRef({});
    const [formValid, setFormValid] = useState(false); // 添加表单验证状态
    const [providerNameList, setProviderNameList] = useState<string[]>([]);
    const [providerList, setProviderList] = useState<LlmProviderResponse[]>([]); // 存储完整的供应商列表数据
    const [error, setError] = useState(null); // 添加错误状态

    // 获取密钥图标的函数
    // let counter = 0;
    // const getKeyIcon = () => {
    //     const index = counter % keyIcons.length;
    //     counter++;
    //     const IconComponent = keyIcons[index];
    //     return <IconComponent />;
    // };

    const columns = [
        
        {
            title: '供应商',
            dataIndex: 'provider',
            width: 200,
            render: (text, record) => (
                <div className={styles.keyCell}>
                    <span className={styles.keyIcon}>
                        {/* {getKeyIcon()} */}
                        <IconKey/>
                    </span>
                    <Text className={styles.keyText}>{text}</Text>
                </div>
            ),
        },
        {
            title: '名称',
            dataIndex: 'name',
            width: 200,
            render: (text) => (
                <Text className={styles.keyText}>{text}</Text>
            ),
        },
        {
            title: '创建人',
            dataIndex: 'creator',
            width: 120,
            render: (text) => (
                <Text className={styles.creatorText}>-</Text>
            ),
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 120,
            render: (text) => (
                <Text className={styles.timeText}>{text}</Text>
            ),
        },
        {
            title: '更新时间',
            dataIndex: 'updateTime',
            width: 120,
            render: (text) => (
                <Text className={styles.timeText}>{text}</Text>
            ),
        },
        {
            title: '操作',
            dataIndex: 'operations',
            width: 120,
            render: (_, record) => (
                <Button
                    type="text"
                    className={styles.deleteBtn}
                    onClick={(e) => {
                        e.stopPropagation(); // 阻止事件冒泡，防止触发行点击事件
                        handleDeleteConfirm(record);
                    }}
                >
                    删除
                </Button>
            ),
        },
    ];

    // 在组件挂载时获取密钥列表和供应商列表
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            try {
                // 获取供应商列表，先获取供应商数据以便于后续映射
                const providerListData = await getLlmProviderList();
                setProviderList(providerListData);
                // 从供应商列表中提取 provider 字段
                const providers = providerListData.map(item => item.provider);
                setProviderNameList(providers);

                // 获取密钥列表
                const response = await getLlmModelKeys();
                // 映射接口响应数据到前端 data 格式
                const mappedData = response.map((item) => {
                    // 根据 llmProviderId 查找对应的供应商名称
                    const providerInfo = providerListData.find(provider => provider.id === item.llmProviderId);
                    const providerName = providerInfo ? providerInfo.provider : item.llmProviderId;

                    return {
                        id: item.id || 'Unknown',
                        name: item.name || '-',
                        creator: item.createUserId || '@创建人',
                        createTime: new Date(item.createdTime).toLocaleString(), // 创建时间
                        updateTime: item.updatedTime ? new Date(item.updatedTime).toLocaleString() : '-', // 更新时间
                        provider: providerName, // 显示供应商名称
                        providerId: item.llmProviderId, // 保存供应商ID
                        apiKey: item.apiKey, // API密钥
                        address: item.endpoint, // 接口地址
                        // 保存原始时间用于排序
                        rawCreateTime: new Date(item.createdTime).getTime(),
                        rawUpdateTime: item.updatedTime ? new Date(item.updatedTime).getTime() : 0,
                    };
                });
                setModelKeysData(mappedData);
                setDisplayData(mappedData);
                setError(null);
            } catch (error) {
                console.error('获取数据失败:', error);
                setError('获取数据失败，请刷新重试！');
                Message.error('获取数据失败，请刷新重试！');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []); // 空依赖数组，仅在组件挂载时执行

    // 处理排序和搜索
    useEffect(() => {
        let filteredData = [...modelkeysdata];

        // 执行搜索过滤
        if (searchValue.trim()) {
            const lowerCaseSearch = searchValue.toLowerCase();
            filteredData = filteredData.filter(item =>
                item.provider.toLowerCase().includes(lowerCaseSearch) ||
                item.creator.toLowerCase().includes(lowerCaseSearch) ||
                item.apiKey.toLowerCase().includes(lowerCaseSearch) ||
                item.address.toLowerCase().includes(lowerCaseSearch)
            );
        }

        // 执行排序
        filteredData.sort((a, b) => {
            switch (sortType) {
                case 'createTime':
                    return b.rawCreateTime - a.rawCreateTime; // 创建时间降序
                case 'updateTime':
                    return b.rawUpdateTime - a.rawUpdateTime; // 更新时间降序
                case 'name':
                    return a.provider.localeCompare(b.provider); // 按名称（供应商）字母顺序
                default:
                    return 0;
            }
        });

        setDisplayData(filteredData);
    }, [modelkeysdata, sortType, searchValue]);

    // 处理搜索输入变化
    const handleSearchChange = (value) => {
        setSearchValue(value);
    };

    // 重置编辑状态
    const resetEditState = () => {
        setIsEditing(true);
        // 更新原始表单值引用
        originalFormValuesRef.current = JSON.parse(JSON.stringify(form.getFieldsValue()));
        // 重置表单变化状态
        setFormChanged(false);
    };

    // 处理表单字段变化
    const handleFormValuesChange = (changedValues, allValues) => {
        // 在任何模式下都执行表单验证
        validateForm();

        // 只有在编辑模式下才检查表单是否发生变化
        if (modalMode === 'view' && isEditing) {
            const formHasChanged = JSON.stringify(allValues) !== JSON.stringify(originalFormValuesRef.current);
            setFormChanged(formHasChanged);
        }
    };

    // 验证表单，检查必填字段是否已填写
    const validateForm = () => {
        const values = form.getFieldsValue();
        const requiredFields = ['name', 'provider', 'apiKey', 'address'];
        const allFieldsFilled = requiredFields.every(field => {
            const value = values[field];
            return value !== undefined && value !== null && value !== '';
        });
        setFormValid(allFieldsFilled);
    };

    // 处理"查看"按钮
    const handleView = (record) => {
        const initialValues = {
            name: record.name || '',
            provider: record.provider || '', // 使用provider名称
            apiKey: record.apiKey || '',
            address: record.address || '',
        };
        form.setFieldsValue(initialValues);
        originalFormValuesRef.current = JSON.parse(JSON.stringify(initialValues));
        setSelectedKey(record);
        setModalMode('view');
        setIsEditing(false);
        setFormChanged(false);
        setModalVisible(true);
        // 立即验证表单状态
        setTimeout(() => validateForm(), 0);
    };

    // 处理编辑按钮
    const handleEdit = () => {
        resetEditState();
    };

    // 处理"添加密钥"按钮
    const handleAddKey = () => {
        form.resetFields(); // 重置表单字段
        originalFormValuesRef.current = {};
        setModalMode('create');
        setIsEditing(true);
        setFormChanged(false);
        setModalVisible(true);
        // 立即验证表单状态
        setTimeout(() => validateForm(), 0);
    };

    // 处理删除确认
    const handleDeleteConfirm = (record) => {
        setKeyToDelete(record);
        setConfirmDeleteVisible(true);
    };

    // 确认删除
    const handleConfirmDelete = async () => {
        if (keyToDelete) {
            try {
                const success = await deleteLlmModelKey(keyToDelete.id);
                if (success) {
                    setLoading(true);
                    const response = await getLlmModelKeys();
                    const mappedData = response.map((item: LlmModelKeyResponse) => {
                        // 根据 llmProviderId 查找对应的供应商名称
                        const providerInfo = providerList.find(provider => provider.id === item.llmProviderId);
                        const providerName = providerInfo ? providerInfo.provider : item.llmProviderId;

                        return {
                            id: item.id || 'Unknown',
                            name: item.name || '未命名密钥',
                            creator: item.createUserId || '@创建人',
                            createTime: new Date(item.createdTime).toLocaleString(),
                            updateTime: item.updatedTime ? new Date(item.updatedTime).toLocaleString() : '-',
                            provider: providerName, // 显示供应商名称
                            providerId: item.llmProviderId, // 保存供应商ID
                            apiKey: item.apiKey,
                            address: item.endpoint,
                            rawCreateTime: new Date(item.createdTime).getTime(),
                            rawUpdateTime: item.updatedTime ? new Date(item.updatedTime).getTime() : 0,
                        };
                    });
                    setModelKeysData(mappedData);
                    setLoading(false);
                    Message.success('密钥删除成功！');
                }
            } catch (error) {
                console.error('删除密钥失败:', error);
                setLoading(false);
                Message.error('删除密钥失败，请重试！');
            } finally {
                setConfirmDeleteVisible(false);
                setKeyToDelete(null);
            }
        }
    };

    // 取消删除
    const handleCancelDelete = () => {
        setConfirmDeleteVisible(false);
        setKeyToDelete(null);
    };

    // 处理取消按钮
    const handleCancel = () => {
        setModalVisible(false);
        setSelectedKey(null);
        setIsEditing(false);
        form.resetFields();
    };

    // 处理表单提交
    const handleSubmit = () => {
        // 如果处于查看模式且点击编辑按钮
        if (modalMode === 'view' && !isEditing) {
            handleEdit();
            return;
        }

        // 如果正在编辑但表单没有变化，不执行保存
        if (isEditing && !formChanged && modalMode === 'view') {
            return;
        }

        form.validate().then(async (values) => {
            // 根据选择的provider名称查找对应的provider对象的id
            const selectedProvider = providerList.find(item => item.provider === values.provider);
            const providerId = selectedProvider ? selectedProvider.id : '';

            if (!providerId) {
                Message.error('无法找到对应的供应商ID，请重新选择供应商！');
                return;
            }

            if (modalMode === 'create') {
                // 构造 LlmModelKeyCreateRequest 参数
                const requestData: LlmModelKeyCreateRequest = {
                    id: '',
                    name: values.name, // 添加名称字段
                    apiKey: values.apiKey,
                    llmProviderId: providerId, // 使用供应商ID
                    llmProvider: values.provider, // 添加供应商名称字段
                    endpoint: values.address,
                };

                try {
                    // 调用接口创建新密钥
                    const success = await createLlmModelKey(requestData);
                    if (success) {
                        // 创建成功，重新获取最新列表数据
                        setLoading(true);
                        const response = await getLlmModelKeys();
                        const mappedData = response.map((item: LlmModelKeyResponse) => {
                            // 根据 llmProviderId 查找对应的供应商名称
                            const providerInfo = providerList.find(provider => provider.id === item.llmProviderId);
                            const providerName = providerInfo ? providerInfo.provider : item.llmProviderId;

                            return {
                                id: item.id || 'Unknown',
                                name: item.name || '未命名密钥',
                                creator: item.createUserId || '@创建人',
                                createTime: new Date(item.createdTime).toLocaleString(),
                                updateTime: item.updatedTime ? new Date(item.updatedTime).toLocaleString() : '-',
                                provider: providerName, // 显示供应商名称
                                providerId: item.llmProviderId, // 保存供应商ID
                                apiKey: item.apiKey,
                                address: item.endpoint,
                                rawCreateTime: new Date(item.createdTime).getTime(),
                                rawUpdateTime: item.updatedTime ? new Date(item.updatedTime).getTime() : 0,
                            };
                        });
                        setModelKeysData(mappedData);
                        setLoading(false);
                        Message.success('密钥创建成功！');
                    } else {
                        Message.error('密钥创建失败，请重试！');
                    }
                } catch (error) {
                    console.error('创建密钥失败:', error);
                    setLoading(false);
                    Message.error('密钥创建失败，请检查网络或联系管理员！');
                }
            } else {
                // 更新现有密钥
                const requestData: LlmModelKeyUpdateRequest = {
                    id: selectedKey.id,
                    name: values.name, // 添加名称字段
                    apiKey: values.apiKey,
                    llmProviderId: providerId, // 使用供应商ID
                    llmProvider: values.provider, // 添加供应商名称字段
                    endpoint: values.address,
                }

                try {
                    // 调用接口更新密钥
                    const success = await updateLlmModelKey(requestData);
                    if (success) {
                        // 更新成功，重新获取最新列表数据
                        setLoading(true);
                        const response = await getLlmModelKeys();
                        const mappedData = response.map((item: LlmModelKeyResponse) => {
                            // 根据 llmProviderId 查找对应的供应商名称
                            const providerInfo = providerList.find(provider => provider.id === item.llmProviderId);
                            const providerName = providerInfo ? providerInfo.provider : item.llmProviderId;

                            return {
                                id: item.id,
                                name: item.name || '未命名密钥',
                                creator: item.createUserId || '@创建人',
                                createTime: new Date(item.createdTime).toLocaleString(),
                                updateTime: item.updatedTime ? new Date(item.updatedTime).toLocaleString() : '-',
                                provider: providerName, // 显示供应商名称
                                providerId: item.llmProviderId, // 保存供应商ID
                                apiKey: item.apiKey,
                                address: item.endpoint,
                                rawCreateTime: new Date(item.createdTime).getTime(),
                                rawUpdateTime: item.updatedTime ? new Date(item.updatedTime).getTime() : 0,
                            };
                        });
                        setModelKeysData(mappedData);
                        setLoading(false);
                        Message.success('密钥更新成功！');
                        setIsEditing(false); // 退出编辑模式
                        setFormChanged(false);
                    } else {
                        Message.error('密钥更新失败，请重试！');
                    }

                } catch (error) {
                    console.error('更新密钥失败:', error);
                    setLoading(false);
                    Message.error('密钥更新失败，请检查网络或联系管理员！');
                }
            }
            setModalVisible(false);
            setSelectedKey(null);
            form.resetFields();
        }).catch(errors => {
            console.log('Validation errors:', errors);
            Message.error('请检查表单字段！');
        });
    };

    // 渲染表格内容
    const renderContent = () => {
        if (loading) {
            return (
                <div className={styles.loadingContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ padding: 24 }}>
                            <Spin tip="密钥加载中..." />
                        </div>
                    </Space>
                </div>
            );
        }

        if (error) {
            return (
                <div className={styles.errorContainer}>
                    <Text type="error">{error}</Text>
                </div>
            );
        }

        if (displayData.length === 0) {
            return (
                <div className={styles.emptyContainer}>
                    <Space direction='vertical' size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <IconEmptyKey style={{ width: 80, height: 80 }} />
                        <Text type="secondary">{searchValue ? '未找到匹配的密钥' : '暂无密钥数据'}</Text>
                    </Space>
                </div>
            );
        }

        return (
            <Table
                columns={columns}
                data={displayData}
                border={{
                    wrapper: true,
                    cell: true,
                }}
                scroll={{
                    y: 'calc(100vh - 250px)'
                }}
                className={styles.table}
                pagination={false}
                rowClassName={(record, index) => index % 2 === 0 ? styles.evenRow : styles.oddRow}
                onRow={(record) => {
                    return {
                        onClick: () => handleView(record),
                        style: { cursor: 'pointer' } // 添加鼠标指针样式，提示可点击
                    };
                }}
            />
        );
    };

    return (
        <div className={styles.secretKey}>
            <div className={styles.header}>
                <Button type="primary" className={styles.addKeyBtn} onClick={handleAddKey}>新增密钥</Button>
                <Space size={'small'}>
                    <Text className={styles.keyNumber}>
                        共 {displayData.length} 个密钥
                    </Text>
                    <Input
                        prefix={<IconSearch />}
                        placeholder="AI搜索..."
                        style={{ width: 240 }}
                        value={searchValue}
                        onChange={handleSearchChange}
                        allowClear
                    />
                    <Select
                        prefix={<IconSortType />}
                        placeholder="按创建时间排序"
                        style={{ width: 160 }}
                        value={sortType}
                        onChange={setSortType}
                    >
                        <Option value="createTime">按创建时间排序</Option>
                        <Option value="updateTime">按更新时间排序</Option>
                        <Option value="name">按名称排序</Option>
                    </Select>
                </Space>
            </div>
            <div className={styles.tableContainer}>
                {renderContent()}
            </div>

            {/* 删除确认弹窗 */}
            <Modal
                visible={confirmDeleteVisible}
                title="删除密钥"
                onCancel={handleCancelDelete}
                closeIcon={<IconClose />}
                className={styles.confirmDeleteModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <Text className={styles.modalContentText}>密钥将被删除，请确认您是否要删除？</Text>
                </div>
                <div className={styles.modalFooter}>
                    <Space>
                        <Button onClick={handleCancelDelete} className={styles.cancelDeleteBtn}>取消</Button>
                        <Button type="primary" onClick={handleConfirmDelete} className={styles.confirmDeleteBtn}>
                            删除
                        </Button>
                    </Space>
                </div>
            </Modal>

            {/* 密钥详情/创建密钥模态框 */}
            <Modal
                visible={modalVisible}
                title={modalMode === 'create' ? '新增密钥' : '密钥详情'}
                onCancel={handleCancel}
                closeIcon={<IconClose />}
                className={styles.secretDetailModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <Form
                        form={form}
                        autoComplete="off"
                        layout="vertical"
                        requiredSymbol={false}
                        onValuesChange={handleFormValuesChange}
                    >
                        <Row className={styles.keyHeader}>
                            <div className={styles.iconAndName}>
                                <div className={styles.keyIconWrapper}>
                                    <IconKeyPlus className={styles.keyDetailIcon} />
                                </div>
                                <div className={styles.divider}></div>
                                <div className={styles.nameFormContainer}>
                                    <FormItemWithoutRequiredMark
                                        label={<CustomLabel label="供应商" required={true} />}
                                        field="provider"
                                        rules={[{ required: true, message: '请选择供应商' }]}
                                        className={styles.nameFormItem}
                                        style={{ marginBottom: '0px' }}
                                    >
                                        <Select
                                            placeholder="请选择"
                                            disabled={!isEditing}
                                            loading={loading}
                                            showSearch
                                            allowClear
                                            filterOption={(inputValue, option) =>
                                                option.props.value.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
                                            }
                                            notFoundContent={loading ? '加载中...' : '暂无数据'}
                                        >
                                            {providerNameList.map((provider) => (
                                                <Option key={provider} value={provider}>
                                                    {provider}
                                                </Option>
                                            ))}
                                        </Select>
                                    </FormItemWithoutRequiredMark>
                                </div>
                            </div>
                        </Row>
                        <FormItemWithoutRequiredMark
                            label={<CustomLabel label="名称" required={true} />}
                            field="name"
                            rules={[{ required: true, message: '请输入密钥名称' }]}
                            className={styles.nameFormItem}
                            style={{ marginBottom: '10px' }}
                        >
                            <Input placeholder="请输入" disabled={!isEditing} />
                        </FormItemWithoutRequiredMark>

                        <FormItemWithoutRequiredMark
                            label={<CustomLabel label="apiKey" required={true} />}
                            field="apiKey"
                            rules={[{ required: true, message: '请输入apiKey' }]}
                        >
                            <Input placeholder="请输入" disabled={!isEditing} />
                        </FormItemWithoutRequiredMark>

                        <FormItemWithoutRequiredMark
                            label={<CustomLabel label="接口地址" required={true} />}
                            field="address"
                            rules={[{ required: true, message: '请输入接口地址' }]}
                        >
                            <Input placeholder="请输入" disabled={!isEditing} />
                        </FormItemWithoutRequiredMark>
                    </Form>
                </div>
                <div className={styles.modalFooter}>
                    <Button onClick={handleCancel} className={styles.cancelBtn}>
                        {modalMode === 'view' ? '取消' : '取消'}
                    </Button>
                    <Button
                        type="primary"
                        onClick={handleSubmit}
                        className={`${styles.saveBtn} ${(modalMode === 'create' && !formValid) ||
                                (modalMode === 'view' && isEditing && !formChanged)
                                ? styles.saveBtnDisabled : ''
                            }`}
                        disabled={(modalMode === 'create' && !formValid) ||
                            (modalMode === 'view' && isEditing && !formChanged)}
                    >
                        {modalMode === 'create' ? '创建' : (isEditing ? '保存' : '编辑')}
                    </Button>
                </div>
            </Modal>
        </div>
    );
}

export default SecretKey;