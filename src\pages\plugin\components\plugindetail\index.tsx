import React, { useState, useEffect } from 'react';
import { Tabs } from '@arco-design/web-react';
import styles from './style/index.module.less';
import PluginDetailContent from './components/PluginDetailContent';
import PluginSettingContent from './components/PluginSettingContent';

const TabPane = Tabs.TabPane;

// 主组件
function PluginDetail() {
    const [activeTab, setActiveTab] = useState('setting');
    const [pluginData, setPluginData] = useState(null);

    useEffect(() => {
        // 从localStorage获取插件数据
        const storedPluginData = localStorage.getItem('currentPluginData');
        if (storedPluginData) {
            try {
                const parsedData = JSON.parse(storedPluginData);
                setPluginData(parsedData);
            } catch (error) {
                console.error('解析插件数据失败:', error);
            }
        }
    }, []);

    return (
        <div className={styles.container}>
            <Tabs activeTab={activeTab} onChange={setActiveTab} className={styles.mainTabs}>
                {/* <TabPane key="detail" title="插件详情">
                    <PluginDetailContent pluginData={pluginData} />
                </TabPane> */}
                <TabPane key="setting" title="插件详情">
                    <PluginSettingContent pluginData={pluginData} />
                </TabPane>
            </Tabs>
        </div>
    );
}

export default PluginDetail; 