import {
  KnowledgeRouteKey,
  KnowledgeSearchParams,
} from '@/pages/knowledge/components/file-manager/src/constants/knowledge';
import { useCallback, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

export enum SegmentIndex {
  Second = '2',
  Third = '3',
}

// 兼容 React Router v5 的 useSearchParams 实现
const useSearchParams = () => {
  const location = useLocation();
  
  const searchParams = useMemo(() => {
    return new URLSearchParams(location.search);
  }, [location.search]);

  const setSearchParams = useCallback((params: URLSearchParams) => {
  }, []);

  return [searchParams, setSearchParams] as const;
};

export const useSegmentedPathName = (index: SegmentIndex) => {
  const { pathname } = useLocation();

  const pathArray = pathname.split('/');
  return pathArray[index] || '';
};

export const useSecondPathName = () => {
  return useSegmentedPathName(SegmentIndex.Second);
};

export const useThirdPathName = () => {
  return useSegmentedPathName(SegmentIndex.Third);
};

export const useGetKnowledgeSearchParams = () => {
  const [currentQueryParameters] = useSearchParams();

  return {
    documentId:
      currentQueryParameters.get(KnowledgeSearchParams.DocumentId) || '',
    knowledgeId:
      currentQueryParameters.get(KnowledgeSearchParams.KnowledgeId) || '',
  };
};

export const useNavigateWithFromState = () => {
  const navigate = useNavigate();
  return useCallback(
    (path: string) => {
      navigate(path, { state: { from: path } });
    },
    [navigate],
  );
};

export const useNavigateToDataset = () => {
  const navigate = useNavigate();
  const { knowledgeId } = useGetKnowledgeSearchParams();

  return useCallback(() => {
    navigate(`/knowledge/${KnowledgeRouteKey.Dataset}?id=${knowledgeId}`);
  }, [knowledgeId, navigate]);
};

export const useGetPaginationParams = () => {
  const [currentQueryParameters] = useSearchParams();

  return {
    page: currentQueryParameters.get('page') || 1,
    size: currentQueryParameters.get('size') || 10,
  };
};

export const useSetPaginationParams = () => {
  const [queryParameters] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();

  const setPaginationParams = useCallback(
    (page = 1, pageSize?: number) => {
      const newParams = new URLSearchParams(location.search);
      newParams.set('page', page.toString());
      if (pageSize) {
        newParams.set('size', pageSize.toString());
      }
      navigate(`${location.pathname}?${newParams.toString()}`);
    },
    [history, location],
  );

  return {
    setPaginationParams,
    page: Number(queryParameters.get('page')) || 1,
    size: Number(queryParameters.get('size')) || 10,
  };
};
