import auth, { AuthParams } from '@/utils/authentication';
import { useEffect, useMemo, useState, lazy } from 'react';

export type IRoute = AuthParams & {
  name: string;
  key: string;
  hasShow?: boolean;
  breadcrumb?: boolean; // 当前页是否展示面包屑
  children?: IRoute[];
  ignore?: boolean; // 当前路由是否渲染菜单项，为 true 的话不会在菜单中显示，但可通过路由地址访问。
  path?: string;
  component?: React.LazyExoticComponent<React.FC<any>>;
};

export const routes: IRoute[] = [
  {
    name: 'menu.dashboard',
    key: 'dashboard',
  },
  {
    name: 'menu.application',
    key: 'application',
    hasShow: true,
    children: [
      {
        name: 'menu.application.list',
        key: 'application/list',
        ignore: true,
      },
      {
        name: 'menu.application.create',
        key: 'application/create',
        ignore: true,
        breadcrumb: false,
      },
      {
        name: 'menu.application.info',
        key: 'application/info',
        ignore: true,
      },
    ],
  },
  {
    name: 'menu.agent',
    key: 'agent',
    children: [
      {
        name: 'menu.agent.list',
        key: 'agent/list',
        ignore: true,
      },
      {
        name: 'menu.agent.info',
        key: 'agent/info',
        ignore: true,
      },
      {
        name: 'menu.agent.create',
        key: 'agent/create',
        ignore: true,
        breadcrumb: false,
      },
    ],
  },
  {
    name: 'menu.workflow',
    key: 'workflow',
    children: [
      {
        name: 'menu.workflow.detail',
        key: 'workflow/detail',
        ignore: true,
      },
    ],
  },
  {
    name: 'menu.timeSequenceCard',
    key: 'timeSequenceCard',
    children: [
      {
        name: 'menu.timeSequenceCard.create',
        key: 'timeSequenceCard/create',
        ignore: true,
      },
      {
        name: 'menu.timeSequenceCard.cards',
        key: 'timeSequenceCard/cards',
        ignore: true,
      },
    ],
  },
  {
    name: 'menu.aiaction',
    key: 'aiaction',
    children: [
      {
        name: 'menu.aiaction.create',
        key: 'aiaction/create',
        ignore: true,
      },
    ],
  },
  {
    name: 'menu.acp',
    key: 'acp',
    children: [
      {
        name: 'menu.acp.servers',
        key: 'acp/servers',
        ignore: true,
        breadcrumb: true,
      },
    ],
  },
  {
    name: 'menu.model',
    key: 'model',
    children: [
      {
        name: 'menu.model.create',
        key: 'model/create',
        ignore: true,
        breadcrumb: true,
      },
      {
        name: 'menu.model.list',
        key: '/model/list',
        ignore: true,
        breadcrumb: true,
      },
      {
        name: 'menu.model.detail',
        key: 'model/detail',
        ignore: true,
        breadcrumb: true,
      },
      {
        name: 'menu.model.configdetail',
        key: 'model/configdetail',
        ignore: true,
        breadcrumb: true,
      },
      {
        name: 'menu.model.configcreate',
        key: 'model/configcreate',
        ignore: true,
        breadcrumb: true,
      },
    ],
  },
  {
    name: 'menu.knowledge',
    key: 'knowledge',
    hasShow: true,
    children: [
      {
        name: 'menu.knowledge.list',
        key: 'knowledge/list',
        ignore: true,
        breadcrumb: true,
        children: [
          {
            name: 'menu.knowledge.list.detail',
            key: 'knowledge/list/detail',
            ignore: true,
            breadcrumb: true,
          },
        ],
      },
    ],
  },
  {
    name: 'menu.plugin',
    key: 'plugin',
    children: [
      {
        name: 'menu.plugin.detail',
        key: 'plugin/detail',
        ignore: true,
        breadcrumb: true,
      },
    ],
  },
  {
    name: 'menu.sdk',
    key: 'sdk',
    children: [
      {
        name: 'menu.sdk.detail',
        key: 'sdk/detail',
        ignore: true,
        breadcrumb: true,
      },
    ],
  },
  {
    name: 'menu.usage',
    key: 'usage',
    children: [
      {
        name: 'menu.usage.detail',
        key: 'usage/detail',
        ignore: true,
        breadcrumb: true,
      },
    ],
  },
  {
    name: 'menu.enterprise',
    key: 'enterprise',
    ignore: true,
  },
  {
    name: 'menu.user',
    key: 'user',
    ignore: true,
  },
];

export const getName = (path: string, routes) => {
  return routes.find((item) => {
    const itemPath = `/${item.key}`;
    if (path === itemPath) {
      return item.name;
    } else if (item.children) {
      return getName(path, item.children);
    }
  });
};

export const generatePermission = (role: string) => {
  const actions = role === 'admin' ? ['*'] : ['read'];
  const result = {};
  routes.forEach((item) => {
    if (item.children) {
      item.children.forEach((child) => {
        result[child.name] = actions;
      });
    }
  });
  return result;
};

const useRoute = (userPermission): [IRoute[], string] => {
  const filterRoute = (routes: IRoute[], arr = []): IRoute[] => {
    if (!routes.length) {
      return [];
    }
    for (const route of routes) {
      const { requiredPermissions, oneOfPerm } = route;
      let visible = true;
      if (requiredPermissions) {
        visible = auth({ requiredPermissions, oneOfPerm }, userPermission);
      }
      if (!visible) {
        continue;
      }
      if (route.children && route.children.length) {
        const newRoute = { ...route, children: [] };
        filterRoute(route.children, newRoute.children);
        if (newRoute.children.length) {
          arr.push(newRoute);
        }
      } else {
        arr.push({ ...route });
      }
    }
    return arr;
  };

  const [permissionRoute, setPermissionRoute] = useState(routes);

  useEffect(() => {
    const newRoutes = filterRoute(routes);
    setPermissionRoute(newRoutes);
  }, [JSON.stringify(userPermission)]);

  const defaultRoute = useMemo(() => {
    const first = permissionRoute[0];
    if (first) {
      const firstRoute = first?.children?.[0]?.key || first.key;
      return firstRoute;
    }
    return '';
  }, [permissionRoute]);

  return [permissionRoute, defaultRoute];
};

export default useRoute;
