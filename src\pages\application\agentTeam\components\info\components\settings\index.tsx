import React, { useState, useMemo } from 'react';
import styles from './style/index.module.less';
import useLocale from '@/utils/useLocale';
import { Form, Image, Message, Select, Input, Grid, Button, Modal } from '@arco-design/web-react';
import { IconRefresh } from '@arco-design/web-react/icon';
import IconCloseTag from '@/assets/close.svg';
import AddIcon from '@/assets/application/addIcon.svg';
import { useEffect } from 'react';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import Text from '@arco-design/web-react/es/Typography/text';
import AgentIcon from '@/assets/application/agentIcon1.png';
import TreeModal from './components/TreeModal/TreeModal';
import { ApplicationResponse } from '@/lib/services/application-service';
import { getAgentList } from '@/lib/services/agent-service';
import { useSelector, useDispatch } from 'react-redux';
import { GlobalState } from '@/store/index';
import { useNavigate } from 'react-router-dom';
import IconLeftBar from '@/assets/application/IconLeftBar.svg';
import CloseEye from '@/assets/application/CloseEye2.svg';
import OpenEye from '@/assets/application/OpenEye2.svg';

const { Row, Col } = Grid;
const FormItem = Form.Item;
const TextArea = Input.TextArea;


interface ApplicationSettingsProps {
  applicationData: ApplicationResponse | null;
  loading: boolean;
  onApplicationDataUpdate: (newData: Partial<ApplicationResponse>, agentSetting: any[]) => void;
  isEditing: boolean;
  onPublish: () => Promise<void>;
}

function ApplicationSettings({ applicationData, loading: parentLoading, onApplicationDataUpdate, isEditing, onPublish }: ApplicationSettingsProps) {
  const agentDetailMenuName = useSelector((state: GlobalState) => state.agentDetailMenuName);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isInitialized, setIsInitialized] = useState(false);
  const locale = useLocale();
  const [form] = Form.useForm();

  const [agentData, setAgentData] = useState([]);

  const [loadingData, setLoadingData] = useState({ agent: false });
  const [treeData, setTreeData] = useState<Array<any>>([]);

  const Option = Select.Option;
  const [visibleTreeModal, setVisibleTreeModal] = useState(false);
  const [checkedIds, setCheckedIds] = useState([]);
  const [modalTitle, setModalTitle] = useState('');
  const [modalType, setModalType] = useState('');
  const [initialHasMore, setInitialHasMore] = useState(true);

  // AgentTeam 配置
  const [agentSetting, setAgentSetting] = useState([]);
  const [channel, setChannelSetting] = useState('');
  const [apiEndpoint, setApiEndpoint] = useState('');
  const [teamId, setTeamId] = useState('');
  const [teamSecret, setTeamSecret] = useState('');
  const [welcome, setWelcome] = useState('');

  // 保存初始值用于取消时恢复
  const [initialApiEndpoint, setInitialApiEndpoint] = useState('');
  const [initialApiId, setInitialApiId] = useState('');
  const [initialTeamId, setInitialTeamId] = useState('');
  const [initialTeamSecret, setInitialTeamSecret] = useState('');
  const [initialTeamName, setInitialTeamName] = useState('');
  const [teamName, setTeamName] = useState('');
  const [apiId, setApiId] = useState('');
  const [agentExpanded, setAgentExpanded] = useState(false);
  const [showTeamSecret, setShowTeamSecret] = useState(false);
  const [teamIdExists, setTeamIdExists] = useState(false);
  const [published, setPublished] = useState(false);

  // 智能体排序函数 - 路由智能体始终在第一位
  const sortAgents = (agents: any[]) => {
    return [...agents].sort((a, b) => {
      if (a.type === 'routing' && b.type !== 'routing') return -1;
      if (a.type !== 'routing' && b.type === 'routing') return 1;
      return 0;
    });
  };

  // 生成UUID
  const generateUUID = () => {
    return crypto.randomUUID();
  };

  // 生成32位密码
  const generateRandomPassword = () => {
    const uuid = generateUUID();
    // 去掉连字符，得到32位十六进制字符
    return uuid.replace(/-/g, '');
  };

  // 生成ID
  const generateRandomId = () => {
    const uuid = generateUUID();
    return uuid;
  };

  // 处理生成密码
  const handleGeneratePassword = () => {
    const newPassword = generateRandomPassword();
    setTeamSecret(newPassword);
  };

  // 处理生成ID
  const handleGenerateId = () => {
    const newId = generateRandomId();
    setTeamId(newId);
  };

  // Team ID 输入验证和处理
  const handleTeamIdChange = (value: string) => {
    // 只允许UUID格式的字符：字母、数字和连字符，最大36位
    const filteredValue = value.replace(/[^a-zA-Z0-9-]/g, '').slice(0, 36);
    setTeamId(filteredValue);
  };

  // Team Secret 输入验证和处理
  const handleTeamSecretChange = (value: string) => {
    // 只允许字母、数字和常见特殊字符，最大32位
    const filteredValue = value.replace(/[^a-zA-Z0-9!@#$%^&*()_+=\-\[\]{}|;:,.<>?]/g, '').slice(0, 32);
    setTeamSecret(filteredValue);
  };

  // 验证Team ID格式
  const validateTeamId = (value: string) => {
    if (!value) return false;
    // 验证UUID v4格式：xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
    return /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value);
  };

  // 验证Team Secret格式
  const validateTeamSecret = (value: string) => {
    if (!value) return false;
    return value.length === 32;
  };

  // 获取AT所属智能体
  const fetchATAgent = async () => {
    if (!applicationData?.id) {
      setAgentSetting([]);
      return;
    }

    setLoadingData(prev => ({ ...prev, agent: true }));
    try {
      const params: any = {
        Pager: {
          Page: 1,
          Size: 999,
        },
        applicationId: applicationData.id,
      };

      const response = await getAgentList(params);

      if (response?.items) {
        const selectedAgent = response.items.map(item => ({
          id: item.id,
          name: item.name,
          icon_url: item.icon_url,
          description: item.description,
          applicationId: item.applicationId,
          ...item
        }));
        setAgentSetting(sortAgents(selectedAgent));
      } else {
        setAgentSetting([]);
      }
    } catch (error) {
      console.error('获取AT智能体失败:', error);
      Message.error({
        content: locale['menu.application.agent.fetch.error']
      });
      setAgentSetting([]);
    } finally {
      setLoadingData(prev => ({ ...prev, agent: false }));
    }
  };

  // 获取智能体列表
  const fetchAgentList = async (searchValue?: string, searchLabel?: string, page = 1, size = 16) => {
    try {
      setLoadingData(prev => ({ ...prev, agent: true }));
      const params: any = {
        Pager: {
          Page: page,
          Size: size,
        },
      };

      if (searchValue) {
        params.agent_name = searchValue;
      }
      if (searchLabel) {
        params.label = searchLabel;
      }

      const response = await getAgentList(params);

      if (response) {
        const originalDataCount = response.items.length;
        
        const formattedData = response.items
          .filter(item => !item.applicationId || item.applicationId === applicationData?.id)
          .map(item => ({
            id: item.id,
            title: item.name,
            applicationId: item.applicationId,
            parentId: '',
            level: 1,
            children: [],
            icon_url: item.icon_url,
            creator: item.plugin.name,
            createdTime: item.updated_time,
            description: item.description,
            labels: item.labels
          }));

        if (!searchValue && page === 1) {
          setAgentData(formattedData);
        }
        
        // 返回过滤后的数据和原始数据量信息
        return {
          data: formattedData,
          originalCount: originalDataCount,
          hasMore: originalDataCount >= size // 基于原始数据量判断是否还有更多
        };
      }
      return { data: [], originalCount: 0, hasMore: false };
    } catch (error) {
      console.error('获取智能体列表失败:', error);
      Message.error({
        content: locale['menu.application.agent.fetch.error']
      });
      return { data: [], originalCount: 0, hasMore: false };
    } finally {
      setLoadingData(prev => ({ ...prev, agent: false }));
    }
  };

  const fetchData = async () => {
    try {
      await fetchATAgent();

      setChannelSetting(applicationData.channel);
      setWelcome(applicationData.welcome);

      const currentApiEndpoint = applicationData.openapi_auth_options?.openapi_uri?.base_uri || '';
      const currentApiId = applicationData.openapi_auth_options?.openapi_uri?.agentId || '';
      const currentTeamId = applicationData.openapi_auth_options?.auth_config?.client_id || '';
      const currentTeamSecret = applicationData.openapi_auth_options?.auth_config?.client_secret || '';
      const currentTeamName = applicationData.openapi_auth_options?.auth_config?.client_name || applicationData.name;

      setApiEndpoint(currentApiEndpoint);
      setApiId(currentApiId);
      setTeamId(currentTeamId);
      setTeamSecret(currentTeamSecret);
      setTeamName(currentTeamName);
      setPublished(applicationData.isPublished);

      // 检查Team ID是否已存在
      setTeamIdExists(!!currentTeamId && currentTeamId.length > 0);

      // 保存初始值
      setInitialApiEndpoint(currentApiEndpoint);
      setInitialApiId(currentApiId);
      setInitialTeamId(currentTeamId);
      setInitialTeamSecret(currentTeamSecret);
      setInitialTeamName(currentTeamName);
    } catch (error) {
      console.error('获取数据失败:', error);
    }
  };

  // 在组件挂载时获取数据
  useEffect(() => {
    if (!isInitialized) {
      fetchData();
      setIsInitialized(true);
    }
  }, []);

  useEffect(() => {
    if (!isEditing && isInitialized) {
      // 当从编辑状态切换到非编辑状态时，恢复初始值
      setApiEndpoint(initialApiEndpoint);
      setTeamId(initialTeamId);
      setTeamSecret(initialTeamSecret);
      setTeamName(initialTeamName);
      // 恢复Team ID存在状态
      setTeamIdExists(!!initialTeamId && initialTeamId.length > 0);
      fetchData();
      setIsInitialized(true);
    }
  }, [isEditing]);

  // 监听表单数据变化
  useEffect(() => {
    const updateData = {
      channel: channel,
      welcome: welcome,
      openapi_auth_options: {
        auth_config: {
          client_name: teamName,
          client_id: teamId,
          client_secret: teamSecret,
        },
        openapi_uri: {
          agentId: apiId,
          base_uri: apiEndpoint,
        },
      },
    };

    onApplicationDataUpdate(updateData, agentSetting);

  }, [agentSetting, channel, welcome, teamId, teamSecret, teamName]);


  const openChooseModal = async (type: string) => {
    try {
      // 检查数据是否正在加载
      if (loadingData.agent) {
        Message.warning('数据加载中，请稍候...');
        return;
      }
      let currentData = [];

      if (type === 'agent') {
        const result = await fetchAgentList('', '', 1, 16);
        currentData = result.data;
        setInitialHasMore(result.hasMore);
        setModalTitle(locale['menu.application.info.setting.addAgent']);
        setModalType('agent');
        setCheckedIds(agentSetting.map(item => item.id));

      }

      const sortTreeData = [...currentData].sort((a, b) => {
        const aChecked = checkedIds.includes(a.id);
        const bChecked = checkedIds.includes(b.id);

        if (aChecked && !bChecked) return -1;
        if (!aChecked && bChecked) return 1;
        return 0;
      });

      setTreeData(sortTreeData);

      setTimeout(() => {
        setVisibleTreeModal(true);
      }, 100);
    } catch (error) {
      console.error('打开模态框失败:', error);
      Message.error('打开失败，请重试');
    }
  };

  const handleTreeConfirm = (selectedIds: string[]) => {
    if (!modalType) return;

    const getSelectedItems = (data: any[], ids: string[]) => {
      return data.reduce((acc: any[], item) => {
        if (ids.includes(item.id)) {
          acc.push({
            id: item.id,
            name: item.title,
            ...item
          });
        }
        if (item.children) {
          acc.push(...getSelectedItems(item.children, ids));
        }
        return acc;
      }, []);
    }

    switch (modalType) {
      case 'agent':
        const selectedAgents = getSelectedItems(treeData, selectedIds);
        
        // 获取当前已有的智能体ID
        const currentAgentIds = agentSetting.map(agent => agent.id);
        
        // 找出需要新增的智能体（在selectedIds中但不在当前列表中）
        const agentsToAdd = selectedAgents.filter(agent => !currentAgentIds.includes(agent.id));
        
        // 找出需要移除的智能体（在当前列表中但不在selectedIds中，且不是路由智能体）
        const agentsToKeep = agentSetting.filter(agent => 
          selectedIds.includes(agent.id) || agent.type === 'routing'
        );
        
        // 合并保留的智能体和新增的智能体，然后排序
        const finalAgents = [...agentsToKeep, ...agentsToAdd];
        setAgentSetting(sortAgents(finalAgents));
        break;
    }

  };

  const handleCheck = (selectedIds: string[]) => {
    setCheckedIds(selectedIds);
  };

  const handleModalClose = () => {
    setVisibleTreeModal(false);
    // 关闭模态框时重置数据
    setTreeData([]);
    setModalType('');
    setInitialHasMore(true);
  };

  const updateBreadcrumbData = (newBreadcrumb) => {
    dispatch({
      type: 'update-breadcrumb-menu-name',
      payload: { breadcrumbMenuName: newBreadcrumb },
    });
  };

  const getAgentDetail = (app: any) => {
    // 存储当前的activeTab，用于返回时恢复
    sessionStorage.setItem(`agentTeam_${applicationData.id}_activeTab`, 'appSettings');

    // 更新面包屑数据，使用详情页路径和卡片名称
    const breadcrumbData = new Map([[agentDetailMenuName, app.name]]);
    updateBreadcrumbData(breadcrumbData);

    navigate('/agent/info', { state: { id: app.id, path: 'agentTeam', atId: applicationData.id } });
  }

  const handleSearch = async (value: any) => {
    if (modalType === 'agent') {
      setLoadingData(prev => ({ ...prev, agent: true }));
      try {
        const page = value.page || 1;
        const size = value.size || 16;
        const result = await fetchAgentList(value.name, value.label, page, size);
        
        if (result.data && Array.isArray(result.data)) {
          if (page === 1) {
            // 第一页，直接替换数据
            setTreeData(result.data);
          } else {
            // 后续页，合并数据
            setTreeData(prevData => [...prevData, ...result.data]);
          }
          
          // 返回数据和hasMore信息
          return {
            data: result.data,
            hasMore: result.hasMore
          };
        }
        return { data: [], hasMore: false };
      } catch (error) {
        console.error('搜索出错:', error);
        Message.error({
          content: locale['menu.application.agent.fetch.error']
        });
        return { data: [], hasMore: false };
      } finally {
        setLoadingData(prev => ({ ...prev, agent: false }));
      }
    }
    return { data: [], hasMore: false };
  };

  // 处理发布
  const handlePublish = async () => {
    if (!validateTeamId(teamId) || !validateTeamSecret(teamSecret)) {
      Message.error('请确保Team ID和Team Secret格式正确');
      return;
    }

    // 情况1: 初始数据没有Team ID，当前数据不为空，自动保存后发布
    if (!initialTeamId && teamId) {

      try {
        // 保存成功后继续发布流程
        await performPublish();
        return;
      } catch (error) {
        Message.error('保存配置失败，无法继续发布');
        return;
      }
    }

    // 情况2: published为false时，直接调用发布接口
    if (!published) {
      await performPublish();
      return;
    }

    // 情况3: published为true且Team Name或Team Secret有改变时，弹窗确认
    const teamNameChanged = teamName !== initialTeamName;
    const teamSecretChanged = teamSecret !== initialTeamSecret;

    if (published && (teamNameChanged || teamSecretChanged)) {
      Modal.confirm({
        style: {
          borderRadius: 16,
          padding: 24,
          width: 480,
          height: 200,
          background: '#FFFFFF',
        },
        title: (
          <div style={{
            width: '120px',
            height: '32px',
            fontWeight: 500,
            fontSize: '20px',
            lineHeight: '32px',
          }}>
            重新发布确认
          </div>
        ),
        content: (
          <div style={{
            color: '#86909C',
            width: '322px',
            fontWeight: 400,
            fontSize: '14px',
            lineHeight: '24px',
          }}>
            原Team配置将无法继续使用，请及时同步更新！
          </div>
        ),
        cancelText: '取消',
        okText: '确认发布',
        okButtonProps: {
          style: {
            backgroundColor: '#4455F2',
            borderColor: '#4455F2',
          },
        },
        icon: null,
        footer: (
          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'flex-end',
            gap: '8px',
          }}>
            <Button
              style={{
                width: '76px',
                height: '40px',
                borderRadius: '8px',
                backgroundColor: '#FFFFFF',
                border: '1px solid rgba(0, 0, 0, 0.08)',
                color: '#000000',
              }}
              onClick={() => Modal.destroyAll()}
            >
              取消
            </Button>
            <Button
              style={{
                width: '100px',
                height: '40px',
                background: '#4455F2',
                borderRadius: '8px',
                border: 'none',
                color: '#FFFFFF',
              }}
              onClick={async () => {
                Modal.destroyAll();
                await performPublish();
              }}
            >
              确认发布
            </Button>
          </div>
        ),
      });
    } else {
      // published为true但没有改变时，直接发布
      await performPublish();
    }
  };

  // 执行发布操作
  const performPublish = async () => {
    try {
      if (!applicationData?.id) {
        Message.error('应用ID不存在');
        return;
      }

      await onPublish();

      Message.success('发布成功！');
    } catch (error) {
      console.error('发布失败:', error);
      Message.error('发布失败，请重试');
    }
  };

  const leftContainer = () => {
    return (
      <div className={styles.leftContainer}>
        {/* 智能体 */}
        <RowComponent className={styles.titleRow}>
          <div className={styles.titleContent}>
            <Text className={styles.subtitle}>
              {locale['menu.application.info.setting.addAgent']}
            </Text>
            <Text className={styles.subtitlePlaceholder}>
              {locale['menu.application.info.setting.placeholder.addAgent']}
            </Text>
          </div>
          <Button
            className={styles.addApplication}
            onClick={() => openChooseModal('agent')}
            disabled={!isEditing}
            style={{
              opacity: !isEditing ? 0.5 : 1,
              cursor: !isEditing ? 'not-allowed' : 'pointer'
            }}
          >
            <AddIcon />
            <Text className={styles.operateText}>
              {locale['menu.application.template.setting.adds']}
            </Text>
          </Button>
        </RowComponent>

        <Col span={24} style={{ marginBottom: '8px' }} className={styles.selectedItemContainer}>
          {/* 渲染已选择的智能体 */}
          {agentSetting.length > 0 && (
            <div className={styles.selectedItemList} style={{ position: 'relative' }}>
              {agentSetting.slice(0, agentExpanded ? agentSetting.length : 3)
                .map((app) => (
                  <div key={app.id} style={{
                    display: 'flex',
                    alignItems: 'center',
                    width: '100%'
                  }}>
                    {app.type !== 'routing' && (
                      <IconLeftBar style={{ flexShrink: 0, marginRight: '8px' }} />
                    )}
                    <Row className={styles.selectedItemRow}>
                      <Col className={styles.selectedItemCol}>
                        <Image
                          src={app.icon_url || AgentIcon}
                          width={24}
                          height={24}
                          className={styles.agentIcon}
                        />
                        <Text className={styles.selectedItemText}
                          style={{
                            cursor: 'pointer'
                          }}
                          onClick={() => getAgentDetail(app)}>{app.name}</Text>
                        <Text className={styles.selectedItemTextContent}>{app.description}</Text>
                        {app.type !== 'routing' && (
                          <IconCloseTag
                            className={styles.deleteIcon}
                            style={{
                              cursor: !isEditing ? 'not-allowed' : 'pointer'
                            }}
                            onClick={() => {
                              if (!isEditing) return;
                              setAgentSetting(sortAgents(agentSetting.filter(item => item.id !== app.id)));
                            }}
                          />
                        )}
                      </Col>
                    </Row>
                  </div>
                ))}

              {/* 折叠/展开按钮 */}
              {agentSetting.length > 3 && (
                <div className={styles.toggleButton} onClick={() => setAgentExpanded(!agentExpanded)}>
                  <div className={styles.toggleArrow}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path
                        d={agentExpanded ? "M12 10L8 6L4 10" : "M4 6L8 10L12 6"}
                        stroke="#86909C"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <Text className={styles.toggleText}>
                    {agentExpanded ? '收起' : `展开剩余的 ${agentSetting.length - 3} 个智能体`}
                  </Text>
                </div>
              )}
            </div>
          )}
        </Col>

        {/* 渠道 */}
        <RowComponent className={styles.titleRow} style={{ marginTop: 16 }}>
          <div className={styles.titleContent}>
            <Text className={styles.subtitle}>
              {locale['menu.application.info.setting.inputChannel']}
            </Text>
            <Text className={styles.subtitlePlaceholder}>
              {locale['menu.application.info.setting.inputChannel.platform']}
            </Text>
          </div>
        </RowComponent>
        <RowComponent style={{ marginTop: 8 }}>
          <Select
            className={styles.channelSelect}
            value={channel}
            disabled={!isEditing}
            onChange={(value) => {
              setChannelSetting(value);
              form.setFieldValue('channel', value);
            }}
            placeholder={locale['menu.application.info.setting.placeholder.chooseChannel']}
          >
            <Option value="openapi">API</Option>
            {/* <Option value="miniProgram">小程序</Option>
            <Option value="web">Web</Option> */}
          </Select>
        </RowComponent>

        <RowComponent className={styles.titleRow} style={{ marginTop: 16 }}>
          <div className={styles.titleContent}>
            <Text className={styles.subtitlePlaceholder}>
              API ID
            </Text>
          </div>
        </RowComponent>
        <RowComponent style={{ marginTop: 8 }}>
          <TextArea
            disabled={true}
            className={styles.apiEndpoint}
            placeholder="该地址将在发布时由系统自动生成"
            value={apiId}
            // onChange={(value) => setApiEndpoint(value)}
          />
        </RowComponent>

        <RowComponent className={styles.titleRow} style={{ marginTop: 16 }}>
          <div className={styles.titleContent}>
            <Text className={styles.subtitlePlaceholder}>
              Team Name
            </Text>
          </div>
        </RowComponent>
        <RowComponent style={{ marginTop: 8 }}>
          <TextArea
            disabled={!isEditing || published}
            className={`${styles.teamName} ${published ? styles.teamIdWithButton : ''}`}
            placeholder={published ? "已发布，不可修改" : "请输入"}
            value={teamName}
            onChange={(value) => setTeamName(value)}
          />
        </RowComponent>

        <RowComponent className={styles.titleRow} style={{ marginTop: 16 }}>
          <div className={styles.titleContent}>
            <Text className={styles.subtitlePlaceholder}>
              Team ID
            </Text>
            {teamId && !validateTeamId(teamId) && (
              <Text style={{ fontSize: '12px', color: '#F53F3F', marginTop: '4px' }}>
                请输入有效的UUID格式
              </Text>
            )}
          </div>
        </RowComponent>
        <RowComponent style={{ marginTop: 8 }}>
          <div className={styles.teamIdContainer}>
            <TextArea
              disabled={!isEditing || teamIdExists}
              className={`${styles.teamId} ${!teamIdExists ? styles.teamIdWithButton : ''} ${teamId && !validateTeamId(teamId) ? styles.error : ''}`}
              placeholder={teamIdExists ? "Team ID已存在，不可修改" : "请输入UUID格式或点击生成"}
              value={teamId}
              onChange={handleTeamIdChange}
            />
            {!teamIdExists && (
              <div className={styles.teamIdButtons}>
                <Button
                  type="text"
                  size="small"
                  disabled={!isEditing}
                  onClick={handleGenerateId}
                  className={`${styles.teamIdGenerateButton} ${!isEditing ? styles.disabled : ''}`}
                >
                  生成
                </Button>
              </div>
            )}
          </div>
        </RowComponent>

        <RowComponent className={styles.titleRow} style={{ marginTop: 16 }}>
          <div className={styles.titleContent}>
            <Text className={styles.subtitlePlaceholder}>
              Team Secret
            </Text>
            {teamSecret && !validateTeamSecret(teamSecret) && (
              <Text style={{ fontSize: '12px', color: '#F53F3F', marginTop: '4px' }}>
                请输入32位密钥
              </Text>
            )}
          </div>
        </RowComponent>
        <RowComponent style={{ marginTop: 8 }}>
          <div className={styles.teamSecretContainer}>
            <TextArea
              disabled={!isEditing || published}
              className={`${styles.teamSecret} ${published ? styles.teamIdWithButton : ''} ${styles.teamSecretWithPassword} ${!showTeamSecret ? styles.hidden : ''} ${teamSecret && !validateTeamSecret(teamSecret) ? styles.error : ''}`}
              placeholder={published ? "已发布，不可修改" : "请输入32位密钥或点击生成"}
              value={teamSecret}
              onChange={handleTeamSecretChange}
            />
            <div className={styles.teamSecretButtons}>
              <Button
                type="text"
                size="small"
                onClick={() => setShowTeamSecret(!showTeamSecret)}
                className={styles.teamSecretButton}
              >
                {showTeamSecret ? <OpenEye style={{ width: '24px', height: '24px' }} /> : <CloseEye style={{ width: '24px', height: '24px' }} />}
              </Button>
              {!published && (
                <>
                  <div className={styles.teamSecretDivider} />
                  <Button
                    type="text"
                    size="small"
                    disabled={!isEditing}
                    onClick={handleGeneratePassword}
                    className={`${styles.teamSecretGenerateButton} ${!isEditing ? styles.disabled : ''}`}
                  >
                    生成
                  </Button>
                </>
              )}
            </div>
          </div>
        </RowComponent>

        {/* 发布按钮 */}
        <RowComponent style={{ marginTop: 16, marginBottom: 16 }}>
          {(() => {
            // 检查Team Name或Team Secret是否有改变
            const teamNameChanged = teamName !== initialTeamName;
            const teamSecretChanged = teamSecret !== initialTeamSecret;
            const hasChanges = teamNameChanged || teamSecretChanged;

            // 判断按钮状态
            const isFormValid = validateTeamId(teamId) && validateTeamSecret(teamSecret) && teamName.trim();
            const isButtonDisabled = !isEditing || !isFormValid;

            if (published && !hasChanges) {
              return (
                <Button
                  className={styles.publishButtonPublished}
                  disabled={true}
                >
                  已发布
                </Button>
              );
            } else if (published && hasChanges) {
              return (
                <Button
                  className={styles.publishButton}
                  type="primary"
                  disabled={isButtonDisabled}
                  onClick={handlePublish}
                >
                  重新发布
                </Button>
              );
            } else {
              return (
                <Button
                  className={styles.publishButton}
                  type="primary"
                  disabled={isButtonDisabled}
                  onClick={handlePublish}
                >
                  发布
                </Button>
              );
            }
          })()}
        </RowComponent>

      </div>
    );
  };

  const rightContainer = () => {
    return (
      <div className={styles.rightContainer}>
        <div className={styles.titleContent}>
          <RowComponent>
            <Text className={styles.subtitle}>
              {locale['menu.application.info.setting.openingSpeech']}
            </Text>
          </RowComponent>
          <RowComponent style={{ marginTop: 6 }}>
            <Text className={styles.subtitlePlaceholder}>
              {locale['menu.application.info.setting.placeholder.openingSpeech']}
            </Text>
          </RowComponent>
        </div>
        <RowComponent style={{ marginTop: 8 }}>
          <FormItem
            field='welcome'
            rules={[{ required: true }]}
            validateTrigger={['onBlur', 'onChange']}
            style={{ marginBottom: 0 }}
          >
            <TextArea
              disabled={!isEditing}
              placeholder="使用自然语言填写"
              maxLength={500}
              value={welcome}
              onChange={(value) => {
                setWelcome(value);
                form.setFieldValue('welcome', value);
              }}
              className={styles.textarea}
            />
            <div style={{
              position: 'absolute',
              bottom: '8px',
              right: '8px',
              fontSize: '12px',
              color: 'rgba(0, 0, 0, 0.45)',
              pointerEvents: 'none'
            }}>
              {welcome.length}/500
            </div>
          </FormItem>
        </RowComponent>

      </div>
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.customContainer}>
        {leftContainer()}
        {rightContainer()}
      </div>
      <TreeModal
        type={modalType}
        title={modalTitle}
        visible={visibleTreeModal}
        onClose={handleModalClose}
        treeData={treeData}
        checkedIds={checkedIds}
        onCheck={handleCheck}
        onConfirm={handleTreeConfirm}
        onSearch={handleSearch}
        loading={loadingData.agent}
        onHasMoreChange={() => {}}
        initialHasMore={initialHasMore}
      />
    </div>
  );
}

export default ApplicationSettings;
