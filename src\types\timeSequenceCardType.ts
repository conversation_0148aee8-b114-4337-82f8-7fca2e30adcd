// src/types/timeSequenceCardType.ts

/**
 * 时序卡片相关类型定义
 */

/**
 * 时序卡片元数据接口
 */
export interface TimeSequenceCardMetadata {
    id?: string;              // 时序卡片ID（创建时不需要，更新时需要）
    name: string;             // 名称（必填）
    display_name?: string;    // 显示名称
    description?: string;     // 描述
    license?: string;         // 许可证
    tags?: string[];          // 标签
    content_schema: {         // 内容结构
        rootElement: string;  // 根元素
    };
    version?: string;         // 版本号
    is_default?: boolean;     // 是否为默认卡片
    is_enabled?: boolean;     // 是否启用
    is_deleted?: boolean;     // 是否已删除
    code?: string;            // 代码标识
    created_time?: string;    // 创建时间
    create_user_time?: string; // 创建用户时间
    create_user_id?: string;  // 创建用户ID
    updated_time?: string;    // 更新时间
    update_user_id?: string;  // 更新用户ID
}

/**
 * 时序卡片元数据列表查询接口
 */
export interface TimeSequenceCardMetadataListParams {
    pager?: {
        page?: number;
        size?: number;
        sort?: string | null;
        order?: string;
    };
    name?: string | null;
    is_default?: boolean | null;
    is_enabled?: boolean | null;
    code?: string | null;
}

/**
 * 时序卡片元数据列表响应接口
 */
export interface TimeSequenceCardMetadataListResponse {
    items?: TimeSequenceCardMetadata[];
    count?: number;
} 