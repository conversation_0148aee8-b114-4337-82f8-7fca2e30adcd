import React, { useState, useEffect } from 'react';
import styles from './style/index.module.less';
import useLocale from '@/utils/useLocale';
import { Card, Button, Input, Typography, Space, Select, Spin, Modal, Message, Popover, Grid } from '@arco-design/web-react';
import IconSearch from '@/assets/application/IconSearch.svg';
import IconClose from '@/assets/application/IconClose.svg';
import IconTimeSequenceCard from '@/assets/application/IconTimeSequenceCard.svg';
import IconAction from '@/assets/application/IconAction.svg';
import IconSortType from '@/assets/application/IconSortType.svg';
import IconEmptyTimeSequenceCard from '@/assets/application/IconEmptyTimeSequenceCard.svg';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { GlobalState } from '@/store/index';
import TimeSequenceCardTemplateModal from './CardTemplateModal/TimeSequenceCardTemplateModal';
import { getTimeSequenceCardMetadataList, deleteTimeSequenceCardMetadata, toggleTimeSequenceCardEnabled, createTimeSequenceCardCopy } from '@/lib/services/timeSequenceCard-service';

const { Text } = Typography;
const Option = Select.Option;
const { Row } = Grid;

function TimeSequenceCardPage() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const createTimeSequenceCradMenuName = useSelector((state: GlobalState) => state.createTimeSequenceCradMenuName);
    const timeSequenceCardDetailMenuName = useSelector((state: GlobalState) => state.timeSequenceCardDetailMenuName);
    const locale = useLocale();
    const [listData, setListData] = useState([]);
    const [filteredListData, setFilteredListData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [searchText, setSearchText] = useState('');
    const [sortType, setSortType] = useState('createTime');
    const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
    const [itemToDelete, setItemToDelete] = useState(null);
    const [templateModalVisible, setTemplateModalVisible] = useState(false);// 模板选择模态框状态

    useEffect(() => {
        fetchTimeSequenceCards();
    }, []);

    useEffect(() => {
        filterAndSortListData();
    }, [listData, searchText, sortType]);

    const fetchTimeSequenceCards = async () => {
        try {
            setLoading(true);
            // 调用API获取时序卡片元数据列表
            const response = await getTimeSequenceCardMetadataList();

            // 检查响应中是否包含items数组
            if (response && response.items) {
                // 格式化数据以适应UI组件需要的结构
                const formattedData = response.items.map((item) => ({
                    id: item.id,
                    author: item.create_user_id || 'AI4C',
                    templateName: item.name,
                    display_name: item.display_name,
                    tags: item.tags || [],
                    createdTime: item.created_time
                        ? new Date(item.created_time).toLocaleDateString()
                        : '-',
                    updatedTime: item.updated_time
                        ? new Date(item.updated_time).toLocaleDateString()
                        : '-',
                    description: item.description || '',
                    isEnabled: item.is_enabled,
                    code: item.code,
                    version: item.version,
                }));
                setListData(formattedData);
            } else if (Array.isArray(response)) {
                // 兼容数组格式的响应
                const formattedData = response.map((item) => ({
                    id: item.id,
                    author: item.create_user_id || 'AI4C',
                    templateName: item.name,
                    display_name: item.display_name,
                    tags: item.tags || [],
                    createdTime: item.created_time
                        ? new Date(item.created_time).toLocaleDateString()
                        : '-',
                    updatedTime: item.updated_time
                        ? new Date(item.updated_time).toLocaleDateString()
                        : '-',
                    description: item.description || '',
                    isEnabled: item.is_enabled,
                    code: item.code,
                    version: item.version,
                }));
                setListData(formattedData);
            } else {
                // 如果返回数据不符合预期，使用空数组
                setListData([]);
                console.warn('获取到的时序卡片元数据格式不符合预期', response);
            }

            setError(null);
        } catch (err) {
            console.error('获取时序卡片列表失败:', err);
            setError('获取时序卡片数据失败，请稍后重试');
            // 出错时显示空列表
            setListData([]);
        } finally {
            setLoading(false);
        }
    };

    const filterAndSortListData = () => {
        let result = [...listData];

        if (searchText) {
            const lowerCaseSearch = searchText.toLowerCase();
            result = result.filter(
                (item) =>
                    item.templateName.toLowerCase().includes(lowerCaseSearch) ||
                    (item.author &&
                        item.author.toLowerCase().includes(lowerCaseSearch)) ||
                    (item.description &&
                        item.description.toLowerCase().includes(lowerCaseSearch)) ||
                    (item.tags &&
                        item.tags.some((tag) =>
                            tag.toLowerCase().includes(lowerCaseSearch)
                        ))
            );
        }

        switch (sortType) {
            case 'createTime':
                result.sort(
                    (a, b) =>
                        new Date(b.createdTime || 0).getTime() -
                        new Date(a.createdTime || 0).getTime()
                );
                break;
            case 'updateTime':
                result.sort((a, b) => {
                    const timeA = a.updatedTime ? new Date(a.updatedTime).getTime() : 0;
                    const timeB = b.updatedTime ? new Date(b.updatedTime).getTime() : 0;
                    return timeB - timeA;
                });
                break;
            case 'name':
                result.sort((a, b) => a.templateName.localeCompare(b.templateName));
                break;
            default:
                break;
        }

        setFilteredListData(result);
    };

    const handleSort = (value) => {
        setSortType(value);
    };

    const handleSearch = (value) => {
        setSearchText(value);
    };

    const updateBreadcrumbData = (newBreadcrumb) => {
        dispatch({
            type: 'update-breadcrumb-menu-name',
            payload: { breadcrumbMenuName: newBreadcrumb },
        });
    };

    const gotoCreateTemplate = (item) => {
        // 根据是否有item决定是创建新卡片还是查看详情
        if (item) {
            // 构造完整的路径 - 使用RESTful风格（3层结构）
            const fullPath = `/timeSequenceCard/cards/${item.id}`;

            // 更新面包屑数据，需要设置完整路径的映射
            // 这样getBreadcrumbItem在处理最后一级时能找到自定义名称
            const breadcrumbData = new Map([
                [fullPath, item.templateName],
            ]);
            updateBreadcrumbData(breadcrumbData);

            // 导航到详情页面，传递ID参数
            navigate(fullPath);
        } else {
            // 创建新卡片
            const breadcrumbData = new Map([
                [createTimeSequenceCradMenuName, locale['menu.application.header.template']],
            ]);
            updateBreadcrumbData(breadcrumbData);

            navigate('/timeSequenceCard/create', { state: { id: '', mode: 'create' } });
        }
    };

    // 添加处理模板选择函数
    const handleTemplateSelect = (items) => {
        // 关闭模态框
        setTemplateModalVisible(false);

        // 将选中的模板项存储到全局状态
        dispatch({
            type: 'update-selected-template-items',
            payload: { selectedTemplateItems: items },
        });

        // 更新面包屑并导航到创建页面
        const breadcrumbData = new Map([
            [createTimeSequenceCradMenuName, locale['menu.application.header.template']],
        ]);
        updateBreadcrumbData(breadcrumbData);
        navigate('/timeSequenceCard/create');
    };

    // 添加打开模板选择模态框的函数
    const openTemplateModal = () => {
        setTemplateModalVisible(true);
    };

    const handleDeleteConfirm = (item) => {
        setItemToDelete(item);
        setConfirmDeleteVisible(true);
    };

    const handleCancelDelete = () => {
        setConfirmDeleteVisible(false);
        setItemToDelete(null);
    };

    const handleDisableAndDelete = async () => {
        if (itemToDelete) {
            try {
                setLoading(true);

                // 如果卡片还是启用状态，先禁用它
                if (itemToDelete.isEnabled) {
                    // 调用禁用API
                    await toggleTimeSequenceCardEnabled(itemToDelete.code, false);

                    // 更新列表中对应项的状态
                    setListData(prev => prev.map(card =>
                        card.id === itemToDelete.id ? { ...card, isEnabled: false } : card
                    ));

                }
                // 然后执行删除操作
                await deleteTimeSequenceCardMetadata(itemToDelete.code, true);

                // 从列表中移除被删除的项
                setListData((prev) =>
                    prev.filter((item) => item.id !== itemToDelete.id)
                );
                Message.success(
                    locale['menu.application.opreate.okMsg'] || '删除成功！'
                );
            } catch (error) {
                console.error('操作时序卡片失败:', error);
                Message.error('操作失败，请重试！');
            } finally {
                setConfirmDeleteVisible(false);
                setItemToDelete(null);
                setLoading(false);
            }
        }
    };

    const handleToggleEnabled = async (item) => {
        try {
            setLoading(true);
            // 调用API启用或禁用时序卡片
            await toggleTimeSequenceCardEnabled(item.code, !item.isEnabled);

            // 更新列表中对应项的状态
            setListData((prev) =>
                prev.map((card) =>
                    card.id === item.id ? { ...card, isEnabled: !item.isEnabled } : card
                )
            );
            Message.success(`${!item.isEnabled ? '启用' : '禁用'}成功！`);
        } catch (error) {
            console.error(`${!item.isEnabled ? '启用' : '禁用'}时序卡片失败:`, error);
            Message.error(`${!item.isEnabled ? '启用' : '禁用'}失败，请重试！`);
        } finally {
            setLoading(false);
        }
    };

    const handleCreateCopy = async (item) => {
        try {
            setLoading(true);
            // 调用创建副本API
            await createTimeSequenceCardCopy(item.id);

            // 重新获取列表数据，以显示新创建的副本
            await fetchTimeSequenceCards();

            Message.success('创建副本成功！');
        } catch (error) {
            console.error('创建时序卡片副本失败:', error);
            Message.error('创建副本失败，请重试！');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className={styles.container}>
            <div className={styles.customContainer}>
                <Text className={styles.headerText}>
                    {locale['menu.application.header.templateList']}
                </Text>
                <Row
                    style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        paddingBottom: 16,
                        borderBottom: '1px solid #f5f5f5',
                        marginBottom: 16,
                    }}
                >
                    <Button
                        onClick={openTemplateModal} // 修改为打开模板选择模态框
                        className={styles.createTimeSequenceCard}
                        htmlType="submit"
                    >
                        {locale['menu.application.createTimeSequenceCardTemplate']}
                    </Button>
                    <Space className={styles.rowEndCenter}>
                        <Text className={styles.countAppText}>
                            {locale['menu.application.timeSequenceCardCount']?.replace(
                                '{count}',
                                filteredListData.length
                            )}
                        </Text>
                        <Input
                            className={styles.searchBox}
                            prefix={<IconSearch />}
                            placeholder={
                                locale['menu.application.timeSequenceCard.header.search.placeholder']
                            }
                            onChange={handleSearch}
                            allowClear
                        />
                        <Select
                            prefix={<IconSortType />}
                            placeholder="按创建时间排序"
                            style={{ width: 160 }}
                            onChange={handleSort}
                            value={sortType}
                        >
                            <Option value="createTime">按创建时间排序</Option>
                            <Option value="updateTime">按更新时间排序</Option>
                            <Option value="name">按名称排序</Option>
                        </Select>
                    </Space>
                </Row>

                <div className={styles.content}>
                    {loading ? (
                        <div className={styles.loadingContainer}>
                            <Spin tip="加载中..." />
                        </div>
                    ) : error ? (
                        <div className={styles.errorContainer}>
                            <Text type="error">{error}</Text>
                        </div>
                    ) : filteredListData.length > 0 ? (
                        filteredListData.map((item, index) => (
                            <Card
                                key={item.id || index}
                                className={styles.timeSequenceCard}
                                hoverable
                                onClick={() => gotoCreateTemplate(item)}
                            >
                                <div className={styles.cardContent}>
                                    <Space
                                        className={styles.timeSequenceCardInfo}
                                        size={8}
                                        direction="vertical"
                                    >
                                        <Space className={styles.nameWrapper} size={12}>
                                            <IconTimeSequenceCard className={styles.icon} />
                                            <Text
                                                className={styles.timeSequenceCardName}
                                            >
                                                {item.templateName}
                                            </Text>
                                        </Space>
                                        <div className={styles.cardDescription}>
                                            <Text type="secondary">{item.description}</Text>
                                        </div>
                                        <Space className={styles.tags} size={8}>
                                            {item.tags &&
                                                item.tags.map((tag, tIndex) => (
                                                    <Button key={tIndex} type="secondary" size="mini">
                                                        {tag}
                                                    </Button>
                                                ))}
                                        </Space>
                                    </Space>
                                    <Row
                                        className={styles.cardFooter}
                                        align="center"
                                        justify="space-between"
                                    >
                                        <Space className={styles.creatorInfo}>
                                            <Text>{`@${item.author}`}</Text>
                                            <Text>|</Text>
                                            <Text>
                                                {item.updatedTime && item.updatedTime !== '-'
                                                    ? `更新时间：${item.updatedTime}`
                                                    : `创建时间：${item.createdTime}`}
                                            </Text>
                                        </Space>
                                        <div className={styles.actionWrapper}>
                                            <div
                                                className={`${styles.statusIndicator} ${item.isEnabled ? styles.enabled : styles.disabled
                                                    }`}
                                            >
                                                <span>{item.isEnabled ? '启用' : '禁用'}</span>
                                            </div>
                                            <Popover
                                                trigger="click"
                                                position="right"
                                                className={styles.customPopover}
                                                content={
                                                    <Space
                                                        direction="vertical"
                                                        size="mini"
                                                        className={styles.popoverContent}
                                                    >
                                                        <Button
                                                            className={`${styles.actionBtn} ${item.isEnabled
                                                                ? styles.disableBtn
                                                                : styles.enableBtn
                                                                }`}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleToggleEnabled(item);
                                                            }}
                                                        >
                                                            {item.isEnabled ? '禁用' : '启用'}
                                                        </Button>
                                                        <Button
                                                            className={`${styles.actionBtn} ${styles.copyBtn}`}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleCreateCopy(item);
                                                            }}
                                                        >
                                                            创建副本
                                                        </Button>
                                                        <Button
                                                            className={`${styles.actionBtn} ${styles.deleteBtn}`}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleDeleteConfirm(item);
                                                            }}
                                                        >
                                                            删除
                                                        </Button>
                                                    </Space>
                                                }
                                            >
                                                <Button
                                                    className={styles.triggerBtn}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                    }}
                                                >
                                                    <IconAction />
                                                </Button>
                                            </Popover>
                                        </div>
                                    </Row>
                                </div>
                            </Card>
                        ))
                    ) : (
                        <div className={styles.emptyContainer}>
                            <Space
                                direction="vertical"
                                size={16}
                                style={{ display: 'flex', alignItems: 'center' }}
                            >
                                <IconEmptyTimeSequenceCard style={{ width: 80, height: 80 }} />
                                <Text type="secondary">
                                    {searchText ? '未找到匹配的时序卡片' : '未找到时序卡片'}
                                </Text>
                            </Space>
                        </div>
                    )}
                </div>

                {/* 删除确认弹窗 */}
                <Modal
                    visible={confirmDeleteVisible}
                    title="删除时序卡片"
                    onCancel={handleCancelDelete}
                    closeIcon={<IconClose />}
                    className={styles.confirmDeleteModal}
                    maskClosable={false}
                >
                    <div className={styles.modalContent}>
                        <Text className={styles.modalContentText}>
                            {itemToDelete?.isEnabled
                                ? `时序卡片正在使用，直接删除将导致关联Agent Team无法继续调用该时序卡片，请确认是否禁用并删除？`
                                : `时序卡片删除后无法恢复，请确认是否删除？`}
                        </Text>
                    </div>
                    <div className={styles.modalFooter}>
                        <Space>
                            <Button onClick={handleCancelDelete} className={styles.cancelDeleteBtn}>取消</Button>
                            <Button type="primary" status="danger" onClick={handleDisableAndDelete} className={styles.confirmDeleteBtn}>
                                {itemToDelete?.isEnabled
                                    ? `禁用并删除`
                                    : `删除`}

                            </Button>
                        </Space>
                    </div>
                </Modal>

                {/* 使用TimeSequenceCardTemplateModal组件 */}
                <TimeSequenceCardTemplateModal
                    visible={templateModalVisible}
                    onClose={() => setTemplateModalVisible(false)}
                    onUseTemplate={handleTemplateSelect}
                />
            </div>
        </div>
    );
}

export default TimeSequenceCardPage; 