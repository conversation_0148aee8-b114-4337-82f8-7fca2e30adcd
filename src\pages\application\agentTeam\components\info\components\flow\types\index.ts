export interface customNodeInterface {
  id: string;
  data: { label: string };
  type: string;
  positionAbsoluteX: number;
  positionAbsoluteY: number;
  selected: boolean;
  selectable: boolean;
  draggable: boolean;
  deletable: boolean;
  isConnectable: boolean;
  dragging: boolean;
  zIndex: number;
  width: number;
  height: number;
}

export interface BasicInfoData {
  agent_name?: string;
  agent_description?: string;
  agent_tags?: any[];
  agent_instruction?: string;
  utilities?: any[];
  acp_tools?: any[];
}

export interface InputcontrolProps {
  onCreateCard?: () => void;
  onCreateFrame?: () => void;
  onUploadFile?: () => void;
  onCreateNewTopic?: (content: string) => void;
  onSaveBasicInfo?: (data: BasicInfoData) => void;
  handleOpen?: () => void;
  selectedNodes?: any[];
  ref?: React.MutableRefObject<HTMLDivElement>;
}

export interface TimeSeriesCard {
  id: string;
  created_time: string;
  create_user_time: string;
  updated_time: any;
  update_user_id: string;
  name: string;
  display_name: string;
  description: string;
  parent_id: string;
  artifact_metadataitem_id: string;
  artifact_metadataitem_version: string;
  input_arg_type: string;
  input_args: string;
  is_public: boolean;
  isActivate: boolean;
  artifact_type: string;
  content: any;
  artifact_source_type: string;
  artifact_source: string;
}

export interface ApplicationDataCard {
  id: string;
  name: string;
  description: string;
  type: string;
  provider: string;
  channel: string;
  iconUrl: string | null;
  installed: boolean;
  isPublished: boolean;
  disabled: boolean;
  createdDateTime: string;
  updatedDateTime: string;
  labels: string[];
  welcome: string;
}

