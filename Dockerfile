FROM node:18-alpine AS builder

WORKDIR /app

# 安装依赖
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

# 复制项目代码
COPY . .

# 构建项目
RUN yarn build

# 使用nginx作为生产环境服务器
FROM nginx:alpine

# 复制构建产物到nginx服务目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 创建nginx日志目录
RUN mkdir -p /usr/local/etc/nginx/logs

# 创建自定义样式目录
RUN mkdir -p /usr/local/etc/nginx/custom_styles

# 复制自定义CSS文件
COPY elsa/mudblazor.min.css /usr/local/etc/nginx/custom_styles/
COPY elsa/shell.css /usr/local/etc/nginx/custom_styles/
COPY elsa/material-base.css /usr/local/etc/nginx/custom_styles/

# 复制nginx配置
COPY elsa/nginx.conf /etc/nginx/nginx.conf.template

# 暴露3000端口
EXPOSE 3000

# 创建启动脚本文件
RUN echo '#!/bin/sh' > /docker-entrypoint.sh \
    && echo 'sed "s|http://localhost:13000|http://elsa-service:8080|g" /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf' >> /docker-entrypoint.sh \
    && echo 'sed -i "s|proxy_pass http://localhost:13001;|root /usr/share/nginx/html; try_files \$uri \$uri/ /index.html;|g" /etc/nginx/nginx.conf' >> /docker-entrypoint.sh \
    && echo 'nginx -g "daemon off;"' >> /docker-entrypoint.sh \
    && chmod +x /docker-entrypoint.sh

# 设置启动命令
CMD ["/docker-entrypoint.sh"] 