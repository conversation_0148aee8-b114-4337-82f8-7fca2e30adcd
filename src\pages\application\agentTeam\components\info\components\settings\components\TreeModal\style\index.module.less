.customModal {
  width: 800px;
  height: 720px;
  border-radius: 16px;

  :global(.arco-modal-header) {
    border-bottom: 0;
    padding: 24px 24px 0 24px;
    height: auto;
    border-radius: 8px;

    :global(.arco-modal-title) {
      text-align: left;
      font-size: 20px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      display: block;
    }
  }

  :global(.arco-modal-close-icon) {
    width: 24px;
    height: 24px;
    right: 24px;
    top: 24px;
  }

  :global(.arco-modal-content) {
    padding: 24px 32px;
  }

  :global(.arco-modal-footer) {
    padding: 0 32px 32px 32px;
    border-top: 0;
  }

  // Tree容器样式
  .treeContainer {
    height: 520px;
    overflow-y: scroll;
    border-radius: 4px;
    background: #ffffff;
    position: relative;

    // Webkit浏览器自定义滚动条样式
    &::-webkit-scrollbar {
      width: 8px;
      background: rgba(0, 0, 0, 0.02);
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      border: 1px solid rgba(255, 255, 255, 0.3);

      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }

      &:active {
        background: rgba(0, 0, 0, 0.4);
      }
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.02);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-corner {
      background: rgba(0, 0, 0, 0.02);
    }

    // Firefox滚动条样式
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) rgba(0, 0, 0, 0.02);
  }

  :global(.arco-tree) {
    border-radius: 4px;
    background: transparent;
    height: auto !important;
    overflow: visible !important;
  }

  :global(.arco-tree-node) {
    margin-bottom: 8px;
    padding: 0;

    &:hover {
      border-radius: 4px;
      background-color: rgba(252, 252, 252, 1);
    }

    :global(.arco-tree-node-title) {
      width: 100%;
      padding: 0;
      margin: 0;

      &:hover {
        background: transparent;
      }
    }

    :global(.arco-tree-node-switcher) {
      display: none;
    }

    :global(.arco-tree-node-switcher-icon) {
      display: none;
    }
  }

  .searchRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 40px;
    flex-direction: row;
    flex-wrap: nowrap;

    .searchBox {
      width: 240px;
      height: 32px;
      outline: none;
      font-size: 14px;
      font-weight: 400;
      background-color: #ffffff;

      :global(.arco-input-inner-wrapper) {
        background-color: #ffffff;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        border-radius: 8px;
      }

      input::placeholder {
        color: RGBA(0, 0, 0, 0.65);
      }
    }

    .selectBox {
      height: 32px;
      width: 160px;
      border-radius: 8px;

      :global(.arco-select-view) {
        background-color: #ffffff;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        border-radius: 8px;
      }

      :global(.arco-select-view-value) {
        color: RGBA(0, 0, 0, 0.8);
      }

      :global(.arco-select-view-value[title='']) {
        color: RGBA(0, 0, 0, 0.25);
      }
    }
  }

  .customTreeRow {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    height: 124px;
    position: relative;

    &:hover {
      background: transparent;

      .rightArea {
        .actionArea {
          opacity: 1;
        }
        .addedTextArea {
          opacity: 0;
        }
      }
    }

    .customIcon {
      flex-shrink: 0;
      padding: 8px 12px 0 8px;

      :global(.arco-image-img) {
        width: 48px;
        height: 48px;
      }
    }

    .contentArea {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
      margin-right: 12px;

      .nameArea {
        margin-top: 8px;

        .name {
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.9);
          margin-bottom: 4px;
          line-height: 24px;
        }

        .description {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.6);
          margin-bottom: 8px;
          line-height: 20px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .labels {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 8px;

          .label {
            padding: 2px 8px;
            background: rgba(68, 85, 242, 0.06);
            border-radius: 4px;
            font-size: 12px;
            color: rgba(68, 85, 242, 0.95);
          }
        }
      }

      .meta {
        display: flex;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        margin-bottom: 8px;

        .metaItem {
          display: flex;
          align-items: center;

          :global(.arco-icon) {
            font-size: 12px;
          }
        }
      }
    }

    .rightArea {
      flex-shrink: 0;
      display: flex;
      align-items: flex-end;
      height: 100%;
      margin: -8px 8px 8px 0;
      position: relative;

      .addedTextArea {
        opacity: 1;
        transition: opacity 0.2s ease-in-out;
        position: absolute;
        width: 76px;
        height: 32px;
        right: -1px;
        bottom: -1px;
        border-radius: 8px;
        border: 1px solid rgba(68, 85, 242, 0.08);
        font-size: 14px;
        z-index: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        .addedText {
          font-size: 14px;
          color: rgba(68, 85, 242, 0.32);
        }
      }

      .actionArea {
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
        position: relative;
        z-index: 2;

        .actionButton {
          box-sizing: border-box;
          display: flex;
          flex-direction: row;
          justify-content: center;
          align-items: center;
          padding: 4px 24px;
          gap: 8px;
          width: 76px;
          height: 32px;
          background: #FFFFFF;
          border-radius: 8px;
          flex: none;
          order: 0;
          align-self: flex-end;
          flex-grow: 0;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;

          &.add {
            border: 1px solid rgba(68, 85, 242, 0.15);
            color: rgba(68, 85, 242, 0.95);

            &:hover {
              background: rgba(68, 85, 242, 0.04);
            }
          }

          &.remove {
            border: 1px solid rgba(245, 63, 63, 0.15);
            color: rgba(245, 63, 63, 0.95);

            &:hover {
              background: rgba(245, 63, 63, 0.04);
            }
          }
        }
      }
    }


  }

  .customCheckbox {
    :global(.arco-checkbox-mask) {
      border-radius: 12px;
      height: 24px;
      width: 24px;
      border: 1px solid rgba(0, 0, 0, 0.08);
    }
  }

  .customFooterRow {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .cancelBut {
      border-radius: 4px;
      background-color: RGBA(0, 0, 0, 0.02);
      margin-right: 8px;
      height: 40px;
      width: 76px;

      span {
        font-weight: 600;
        color: RGBA(0, 0, 0, 0.65);
      }
    }

    .selectedCount {
      font-size: 14px;
      font-weight: 600;
      color: RGBA(0, 0, 0, 0.65);
    }

    .addBut {
      border-radius: 4px;
      background-color: RGBA(68, 85, 242, 0.04);
      height: 40px;
      width: 76px;

      span {
        font-weight: 600;
        color: RGBA(68, 85, 242, 0.95);
      }
    }
  }
}