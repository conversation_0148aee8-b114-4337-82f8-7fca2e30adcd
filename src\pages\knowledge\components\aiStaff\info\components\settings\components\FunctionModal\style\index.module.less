:global(.acp-tool-modal-wrap) {
    :global(.arco-modal) {
        display: flex;
        flex-direction: column;
        max-height: 80vh;
        position: relative;
    }

    :global(.arco-modal-header) {
        flex-shrink: 0;
    }

    :global(.arco-modal-content) {
        flex: 1;
        overflow-y: auto;
        max-height: calc(80vh - 120px);
        min-height: 500px;
    }

    :global(.arco-modal-footer) {
        flex-shrink: 0;
        border-top: 1px solid rgba(0, 0, 0, 0.06);
        padding: 16px 24px;
        background-color: #fff;
        position: sticky;
        bottom: 0;
        width: 100%;
        z-index: 100;
    }
}

:global(.ACPtoolModel) {
    height: 80%;
    width: 70%;
    border-radius: 10px;

    :global(.arco-modal-content) {
        padding: 24px 24px;

    }

    :global(.ACPtool-modal-title) {
        font-size: 20px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        display: block;
    }

    :global(.ACPtool-modal-content) {
        margin-top: 16px;
        margin-bottom: 16px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);

        .selectRowBox {
            height: 40px;
            width: 30%;
            border-radius: 4px;
            border: 1px solid RGBA(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;

            :global(.arco-select-view) {
                background: transparent;
                height: 40px;
                width: 100%;
                padding-right: 6px;

                :global(.arco-select-suffix) {
                    width: 20px;
                }
            }
        }
    }

    .titleRow {
        margin-top: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        width: 100%;

        .titleContent {
            display: flex;
            flex-direction: column;
            gap: 6px;

            .subtitle {
                font-size: 14px;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.85);
                display: block;
            }

            .subtitlePlaceholder {
                font-size: 12px;
                font-weight: 400;
                color: RGBA(0, 0, 0, .35);
            }

        }

        .switchContainer {
            display: flex;
            align-items: center;
            gap: 8px;
            height: 40px;
            width: 76px;
            justify-content: center;
            border-radius: 8px;
            background-color: transparent;
            padding: 0 2px;

            :global(.arco-switch) {
                min-width: 40px;
                height: 24px;
            }

            :global(.arco-switch-checked) {
                background-color: RGBA(13, 41, 254, 0.95) !important;
            }
        }

        .addParamsBut {
            width: 76px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid RGBA(0, 0, 0, 0.08);
            border-radius: 8px;
            cursor: pointer;
            background-color: transparent;
            padding: 0 2px;

            :global(.arco-icon) {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                flex-shrink: 0;
            }

            .operateText {
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                display: flex;
                align-items: center;
                height: 100%;
            }

            &:hover {
                background-color: RGBA(0, 0, 0, 0.02);
            }
        }
    }

    .paramsContainer {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        width: 100%;
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.01)), #FFFFFF;
        border-radius: 8px;
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
        margin-top: 8px;

        .headerRow {
            width: 100%;
            height: 32px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);

            .headerCheckbox {
                padding-left: 8px;
                width: 60px;
                white-space: nowrap;
            }

            .headerName {
                width: 200px;
            }

            .headerType {
                width: 160px;
            }

            .headerDescription {
                flex: 1;
            }
        }

        .selectedItemList {
            box-sizing: border-box;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 0 8px 8px 8px;
            border-radius: 4px;
            min-height: 48px;
            transition: all 0.3s ease;
            border-radius: 8px;

            &:hover {
                border-color: RGBA(0, 0, 0, 0.15);
            }

            .selectedItemRow {
                width: 100%;
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px;
                border-radius: 8px;

                :global(.arco-checkbox) {
                    margin-right: 8px;
                }

                .typeSelect {
                    width: 160px;
                    height: 40px;
                    background-color: #fff;
                    border-radius: 8px;
                }

                .descriptionInput {
                    flex: 1;
                    height: 40px;
                    background-color: #fff;
                    border: 1px solid RGBA(0, 0, 0, .08);
                    border-radius: 8px;
                }

                :global(.arco-input-inner-wrapper) {
                    height: 40px;
                    border-radius: 8px;
                    background-color: #fff;
                    border: 1px solid RGBA(0, 0, 0, .08);
                }

                :global(.arco-select-view) {
                    height: 40px;
                    border-radius: 8px;
                    background-color: #fff;
                    border: 1px solid RGBA(0, 0, 0, .08);
                    display: flex;
                    align-items: center;
                    padding: 0 12px;
                }

                .deleteIcon {
                    width: 24px;
                    height: 24px;
                    cursor: pointer;
                    opacity: 0.65;
                    transition: opacity 0.2s ease;

                    &:hover {
                        opacity: 1;
                    }
                }
            }
        }
    }

    .subtitle {
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        display: block;
    }

    .operateButGroup {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 0;
        margin: 0;

        .text {
            font-size: 14px;
            font-weight: 600;
        }

        .but {
            border-radius: 4px;
            width: 76px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .cancelBut {
            background-color: RGBA(250, 250, 250, 1);

            .text {
                color: RGBA(0, 0, 0, 0.65);
            }
        }

        .createBut {
            background-color: #4455F2;

            .text {
                color: white;
            }

            &.disabled {
                opacity: 0.32;
            }
        }
    }

}