import React, { useState } from 'react';
import { Modal, Typography, Input } from '@arco-design/web-react';
import IconClose from '@/assets/acp/IconClose.svg';
import styles from './style/index.module.less';

const { Text } = Typography;
const { TextArea } = Input;

// 定义资源数据类型
interface ResourceData {
    id: string;
    name: string;
    type: string;
    description: string;
    params: string;
    paramDetails?: {  // 新增详细参数信息
        [key: string]: {
            title: string;
            type: string;
            description: string;
        };
    };
}

interface ResourceDetailModalProps {
    visible: boolean;
    onClose: () => void;
    resourceData?: ResourceData | null;
}

// 模拟参数数据结构
interface ParameterItem {
    type: string;
    name: string;
    description: string;
}

function ResourceDetailModal({ visible, onClose, resourceData }: ResourceDetailModalProps) {    
    // 解析参数字符串为参数数组，使用详细参数信息
    const parseParams = (resourceData: ResourceData): ParameterItem[] => {
        if (!resourceData?.paramDetails) return [];

        // 使用详细参数信息构建参数数组
        return Object.entries(resourceData.paramDetails).map(([key, value]) => ({
            type: value.type || 'string',  // 使用实际的type字段
            name: value.title || key,      // 使用title字段作为显示名称，如果没有则使用key
            description: value.description || ''  // 描述可以根据需要添加
        }));
    };

    const [parameters, setParameters] = useState<ParameterItem[]>([]);

    // 当模态框打开时，解析参数
    React.useEffect(() => {
        if (visible && resourceData) {
            setParameters(parseParams(resourceData));
        }
    }, [visible, resourceData]);

    if (!resourceData) return null;

    return (
        <Modal
            title="详情"
            visible={visible}
            onCancel={onClose}
            footer={null}
            className={styles.resourceDetailModal}
            closable={true}
            closeIcon={<IconClose />}
        >
            <div className={styles.modalContent}>
                {/* 名称部分 */}
                <div className={styles.section}>
                    <Text className={styles.sectionLabel}>名称</Text>
                    <div className={styles.nameSection}>
                        <Input
                            value={resourceData.name}
                            placeholder="数据处理"
                            className={styles.nameInput}
                            readOnly
                        />
                    </div>
                </div>

                {/* 描述部分 */}
                <div className={styles.section}>
                    <Text className={styles.sectionLabel}>描述</Text>
                    <TextArea
                        value={resourceData.description}
                        placeholder="描述"
                        className={styles.descriptionTextarea}
                        readOnly
                        autoSize={{ minRows: 3, maxRows: 6 }}
                    />
                </div>

                {/* 参数部分 */}
                <div className={styles.section} style={{ marginBottom: 0}}>
                    <div className={styles.parameterHeader}>
                        <Text className={styles.sectionLabel} style={{ marginBottom: 0}}>参数</Text>
                        <Text className={styles.parameterDescription}>资源参数详情</Text>
                    </div>
                    
                    <div className={styles.parameterList}>
                        {parameters.map((param, index) => (
                            <div key={index} className={styles.parameterItem}>
                                <div className={styles.parameterRow}>
                                    <div className={styles.parameterField}>
                                        <Text className={styles.fieldLabel}>参数类型</Text>
                                        <Input
                                            value={param.type}
                                            placeholder="请选择"
                                            className={styles.parameterInput}
                                            readOnly
                                        />
                                    </div>

                                    <div className={styles.parameterField}>
                                        <Text className={styles.fieldLabel}>参数名称</Text>
                                        <Input
                                            value={param.name}
                                            placeholder="名称"
                                            className={styles.parameterInput}
                                            readOnly
                                        />
                                    </div>
                                </div>

                                <div className={styles.parameterDescRow}>
                                    <div className={styles.parameterField}>
                                        <Text className={styles.fieldLabel}>参数描述</Text>
                                        <Input
                                            value={param.description}
                                            placeholder="描述"
                                            className={styles.parameterInput}
                                            readOnly
                                        />
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </Modal>
    );
}

export default ResourceDetailModal; 