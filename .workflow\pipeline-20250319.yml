version: '1.0'
name: pipeline-20250319
displayName: pipeline-20250319
triggers:
  trigger: manual
  push:
    branches:
      precise:
        - test_v1.0
stages:
  - name: stage-70328ff3
    displayName: 编译
    strategy: naturally
    trigger: auto
    executor:
      - qiu-tianfu
    steps:
      - step: build@nodejs
        name: build_nodejs
        displayName: Nodejs 构建
        nodeVersion: 14.16.0
        commands:
          - '# 设置NPM源，提升安装速度'
          - npm config set registry https://registry.npmmirror.com
          - ''
          - '# 执行编译命令'
          - npm install && npm run build
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./dist
        caches:
          - ~/.npm
          - ~/.yarn
        notify: []
        strategy:
          retry: '0'
