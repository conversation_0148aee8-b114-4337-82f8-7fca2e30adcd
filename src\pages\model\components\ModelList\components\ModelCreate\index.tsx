import { useState, useEffect } from 'react';
import {
  Card,
  Grid,
  Typography,
  Upload,
  Input,
  Select,
  InputNumber,
  Button,
  Space,
  Tag,
  Switch,
  Form,
  Message,
} from '@arco-design/web-react';
import IconUpload from '@/assets/model/IconPlus.svg';
import IconModel from '@/assets/model/ModelIocn.svg';
import IconTagClose from '@/assets/model/IconTagClose.svg';
import styles from './style/index.module.less';
import {
  getLlmTypeList,
  getLlmProviderList,
  getLlmProviderModelList,
  createLlmModel,
} from '@/lib/services/llm-model-service';
import { useNavigate } from 'react-router-dom';
import { LlmProviderResponse } from '@/types/llmModelType';

const { Row, Col } = Grid;
const { Title, Text } = Typography;
const FormItem = Form.Item;

// 创建一个不显示必填图标的 Form.Item
const FormItemWithoutRequiredMark = (props) => {
  const { children, ...rest } = props;
  return (
    <Form.Item
      {...rest}
      requiredSymbol={false} // 禁用 Arco Design 默认的必填标记
    >
      {children}
    </Form.Item>
  );
};

// 自定义的必填图标组件
const RequiredIcon = () => <span className={styles.requiredIcon}>*</span>;

// 自定义表单项标签组件
const CustomLabel = ({ label, required }) => {
  return (
    <Space>
      <span>{label}</span>
      {required && <RequiredIcon />}
    </Space>
  );
};

const AddModelPage = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [tags, setTags] = useState([]);
  const [modelTypes, setModelTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [providers, setProviders] = useState<LlmProviderResponse[]>([]);
  const [providersLoading, setProvidersLoading] = useState(false);
  const [formValid, setFormValid] = useState(false);
  const [selectedModelType, setSelectedModelType] = useState(null);

  // 验证必填字段
  const validateForm = () => {
    try {
      // 获取当前表单值
      const values = form.getFieldsValue();
      // 必填字段列表
      const requiredFields = [
        'deployName',
        'provider',
        'modelType',
        'promptCost',
        'completionCost',
        'temperature',
      ];

      // 检查所有必填字段是否都已填写
      let allFieldsFilled = requiredFields.every((field) => {
        const value = values[field];
        return value !== undefined && value !== null && value !== '';
      });

      // 如果模型类型是Chat(2)或Text(1)，则maxTokens也是必填的
      if (values.modelType === 1 || values.modelType === 2) {
        const maxTokensValid = values.maxTokens !== undefined && 
                              values.maxTokens !== null && 
                              values.maxTokens !== '' && 
                              values.maxTokens > 0;
        allFieldsFilled = allFieldsFilled && maxTokensValid;
      }

      // 如果模型类型是Embedding(4)，则embeddingDimension也是必填的
      if (values.modelType === 4) {
        const embeddingValid = values.embeddingDimension !== undefined && 
                              values.embeddingDimension !== null && 
                              values.embeddingDimension !== '' && 
                              parseInt(values.embeddingDimension, 10) > 0;
        allFieldsFilled = allFieldsFilled && embeddingValid;
      }

      // 设置表单验证状态
      setFormValid(allFieldsFilled);
    } catch (error) {
      // 发生错误时设置为未验证通过
      setFormValid(false);
    }
  };

  // 表单值变化时触发验证
  const handleValuesChange = (changedValues, allValues) => {
    if ('modelType' in changedValues) {
      setSelectedModelType(changedValues.modelType);
    }
    validateForm();
  };

  // 初始检查表单状态
  useEffect(() => {
    // 首次加载时验证表单
    validateForm();
  }, []);

  //添加模型标签
  const handleAddTag = () => {
    if (tags.length >= 3) {
      Message.warning('标签最多添加3个');
      return;
    }
    // 直接添加一个空标签，然后自动聚焦到它
    setTags([...tags, '']);
  };

  // 处理标签变化
  const handleTagChange = (newTags) => {
    setTags(newTags);
  };

  // 删除标签
  const handleTagClose = (index) => {
    const newTags = [...tags];
    newTags.splice(index, 1);
    setTags(newTags);
  };

  // 调用 getLlmProviderList 获取供应商列表
  useEffect(() => {
    const fetchProviders = async () => {
      setProvidersLoading(true);
      try {
        const response = await getLlmProviderList();
        if (response) {
          setProviders(response);
        } else {
          Message.error('获取供应商列表失败');
        }
      } catch (error) {
        console.error('Failed to fetch providers:', error);
        Message.error('获取供应商列表失败，请稍后重试');
      } finally {
        setProvidersLoading(false);
      }
    };

    fetchProviders();
  }, []);

  //创建模型
  const handleSubmit = async () => {
    try {
      // 验证表单
      const values = await form.validate();

      // 如果对外展示名称为空，则使用模型部署名称作为默认值
      const name =
        values.name && values.name.trim() ? values.name : values.deployName;

      // 过滤掉空标签
      const nonEmptyTags = tags.filter((tag) => tag.trim() !== '');

      // 构造请求体，只包含 LlmModelCreateRequest 所需的字段
      const requestData = {
        id: values.id || '',
        llmProviderId: values.provider || '',
        modelId: values.modelId || '',
        icon: values.icon || '',
        name: name || '',
        deployName: values.deployName || '',
        version: values.modelVersion || '',
        apiVersion: values.apiVersion || '',
        type: values.modelType || 0,
        tags: nonEmptyTags.length > 0 ? nonEmptyTags : null,
        multiModal: values.multiModal || false,
        imageGeneration: values.allowGenerateImage || false,
        promptCost: values.promptCost || 0,
        completionCost: values.completionCost || 0,
        dimension: values.embeddingDimension ? parseInt(values.embeddingDimension, 10) : null,
        maxTokens: values.maxTokens !== undefined && values.maxTokens !== '' ? values.maxTokens : null,
        temperature: parseFloat(values.temperature) || 0,
        isEnabled: values.isEnabled || false,
      };

      // 调用创建接口
      const response = await createLlmModel(requestData);

      if (response) {
        Message.success('模型创建成功');
        // 清空表单
        form.resetFields();
        setTags([]);
        navigate('/model', { state: { fromModel: true } });
      } else {
        Message.error('模型创建失败');
      }
    } catch (error) {
      console.log('创建模型失败:', error);
      Message.error('模型创建失败，请检查输入或稍后重试');
    }
  };

  // 调用 getLlmTypeList 获取模型类型
  useEffect(() => {
    const fetchModelTypes = async () => {
      setLoading(true);
      try {
        const response = await getLlmTypeList();
        if (response) {
          setModelTypes(response);
        } else {
          Message.error('获取模型类型失败');
        }
      } catch (error) {
        console.error('Failed to fetch model types:', error);
        Message.error('获取模型类型失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    fetchModelTypes();
  }, []); // 空依赖数组，确保只在组件挂载时调用

  //取消创建模型
  const handleCancel = () => {
    navigate('/model', { state: { fromModel: true } });
  };

  return (
    <div className={styles.addModelContainer}>
      <Title
        heading={4}
        className={styles.addModelTitle}
        style={{ marginBottom: '24px' }}
      >
        新增模型
      </Title>
      <Form
        form={form}
        autoComplete="off"
        layout="vertical"
        onValuesChange={handleValuesChange}
        requiredSymbol={false}
      >
        <Row gutter={[40, 0]}>
          <Col span={12}>
            <Card bordered={false} style={{ padding: '0px' }}>
              <Row className={styles.addModelHeader}>
                <div className={styles.iconAndName}>
                  <div className={styles.addModelIconWrapper}>
                    <IconModel className={styles.addModelIcon} />
                  </div>
                  <div className={styles.divider}></div>
                  <div className={styles.nameFormContainer}>
                    <FormItemWithoutRequiredMark
                      label="对外展示名称"
                      field="name"
                      style={{ marginBottom: '0px' }}
                    >
                      <Input
                        placeholder="请输入"
                        className={styles.nameInput}
                      />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="模型部署名称" required={true} />}
                    field="deployName"
                    rules={[{ required: true, message: '请输入模型部署名称' }]}
                  >
                    <Input
                      placeholder="请输入"
                      className={styles.deployNameInput}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark label="ID" field="modelId">
                    <Input placeholder="请输入" className={styles.idInput} />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <FormItemWithoutRequiredMark className={styles.tagFormItem}>
                <Row
                  justify="space-between"
                  align="center"
                  style={{ marginBottom: '8px' }}
                >
                  <Space direction="vertical" size={'mini'}>
                    <Text
                      style={{
                        color: '#5c5c5c',
                        fontWeight: '600',
                        fontSize: '14px',
                      }}
                    >
                      标签
                    </Text>
                    <Text className={styles.labelExact}>标签最多添加3个</Text>
                  </Space>
                  <Button
                    className={styles.addTagBtn}
                    onClick={handleAddTag}
                  >
                    添加
                  </Button>
                </Row>
                {tags.length > 0 && (
                  <div className={styles.selectedItemList}>
                    {tags.map((tag, index) => (
                      <Row
                        key={`tag-${index}`}
                        className={styles.selectedItemRow}
                      >
                        <Input
                          autoFocus={tag === ''}
                          value={tag}
                          onChange={(value) => {
                            if (value && value.length > 20) {
                              return;
                            }
                            const newTags = [...tags];
                            newTags[index] = value;
                            handleTagChange(newTags);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              e.stopPropagation();
                            }
                          }}
                          placeholder={`标签 ${index + 1}`}
                          suffix={
                            <IconTagClose
                              className={styles.deleteIcon}
                              onClick={() => handleTagClose(index)}
                            />
                          }
                        />
                      </Row>
                    ))}
                  </div>
                )}
              </FormItemWithoutRequiredMark>
              <div>
                <div className={styles.switchContainer}>
                  <Space
                    className={styles.switchLeftContent}
                    direction="vertical"
                    size={4}
                  >
                    <Text className={styles.Switchlabel}>是否启用</Text>
                    <Text className={styles.SwitchTitle}>模型控制</Text>
                  </Space>
                  <div className={styles.switchRightContent}>
                    <FormItemWithoutRequiredMark
                      field="isEnabled"
                      triggerPropName="checked"
                      style={{ margin: 0 }}
                    >
                      <Switch />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
                <div className={styles.switchContainer}>
                  <Space
                    className={styles.switchLeftContent}
                    direction="vertical"
                    size={4}
                  >
                    <Text className={styles.Switchlabel}>
                      是否允许发送图片/视频
                    </Text>
                    <Text className={styles.SwitchTitle}>模型控制</Text>
                  </Space>
                  <div className={styles.switchRightContent}>
                    <FormItemWithoutRequiredMark
                      field="multiModal"
                      triggerPropName="checked"
                      style={{ margin: 0 }}
                    >
                      <Switch />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
                <div className={styles.switchContainer}>
                  <Space
                    className={styles.switchLeftContent}
                    direction="vertical"
                    size={4}
                  >
                    <Text className={styles.Switchlabel}>是否允许生成图片</Text>
                    <Text className={styles.SwitchTitle}>模型控制</Text>
                  </Space>
                  <div className={styles.switchRightContent}>
                    <FormItemWithoutRequiredMark
                      field="allowGenerateImage"
                      triggerPropName="checked"
                      style={{ margin: 0 }}
                    >
                      <Switch />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
              </div>
            </Card>
          </Col>

          <Col span={12} className={styles.Card}>
            <Card bordered={false}>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="供应商" required={true} />}
                    field="provider"
                    rules={[{ required: true, message: '请选择供应商' }]}
                  >
                    <Select placeholder="请选择" loading={providersLoading}>
                      {providers.map((provider) => (
                        <Select.Option key={provider.id} value={provider.id}>
                          {provider.provider}
                        </Select.Option>
                      ))}
                    </Select>
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="模型类型" required={true} />}
                    field="modelType"
                    rules={[{ required: true, message: '请选择模型类型' }]}
                  >
                    <Select placeholder="请选择" loading={loading}>
                      {modelTypes &&
                        Object.entries(modelTypes).map(([key, value]) => (
                          <Select.Option key={value} value={value}>
                            {key}
                          </Select.Option>
                        ))}
                    </Select>
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label="模型版本"
                    field="modelVersion"
                  >
                    <Input
                      placeholder="请输入"
                      className={styles.modelVersionInput}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label="API版本"
                    field="apiVersion"
                  >
                    <Input
                      placeholder="请输入"
                      className={styles.apiVersionInput}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={
                      <CustomLabel label="输入成本（每千Token/元）" required={true} />
                    }
                    field="promptCost"
                    rules={[{ required: true, message: '请输入输入成本（每千Token/元）' }]}
                  >
                    <InputNumber min={0} placeholder="0" />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={
                      <CustomLabel label="输出成本（每千Token/元）" required={true} />
                    }
                    field="completionCost"
                    rules={[{ required: true, message: '请输入输出成本（每千Token/元）' }]}
                  >
                    <InputNumber min={0} placeholder="0" />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={
                      <CustomLabel 
                        label="最大输出（Token数）" 
                        required={selectedModelType === 1 || selectedModelType === 2} 
                      />
                    }
                    field="maxTokens"
                    rules={[
                      {
                        validator: (value, callback) => {
                          const modelType = selectedModelType;
                          // 如果是Chat(2)或Text(1)类型
                          if (modelType === 1 || modelType === 2) {
                            if (value === undefined || value === null || value === '') {
                              callback('当模型类型为Chat或Text时，最大输出Token数必填');
                            } else if (value === 0) {
                              callback('最大输出Token数不能为0');
                            } else {
                              callback();
                            }
                          } else {
                            // 其他类型，如果有值则不能为0
                            if (value !== undefined && value !== null && value !== '' && value === 0) {
                              callback('最大输出Token数不能为0');
                            } else {
                              callback();
                            }
                          }
                        }
                      }
                    ]}
                  >
                    <InputNumber 
                      min={0} 
                      placeholder={
                        selectedModelType === 1 || selectedModelType === 2 
                          ? "请输入（必填）" 
                          : "请输入（可选）"
                      } 
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={
                      <CustomLabel 
                        label="嵌入维度" 
                        required={selectedModelType === 4} 
                      />
                    }
                    field="embeddingDimension"
                    rules={[
                      {
                        validator: (value, callback) => {
                          const modelType = selectedModelType;
                          // 如果是Embedding(4)类型
                          if (modelType === 4) {
                            if (value === undefined || value === null || value === '') {
                              callback('当模型类型为Embedding时，嵌入维度必填');
                            } else {
                              const numValue = parseInt(value, 10);
                              if (isNaN(numValue) || numValue <= 0) {
                                callback('嵌入维度不能为0');
                              } else {
                                callback();
                              }
                            }
                          } else {
                            // 其他类型，如果有值则不能为0
                            if (value !== undefined && value !== null && value !== '') {
                              const numValue = parseInt(value, 10);
                              if (isNaN(numValue) || numValue <= 0) {
                                callback('嵌入维度不能为0');
                              } else {
                                callback();
                              }
                            } else {
                              callback();
                            }
                          }
                        }
                      }
                    ]}
                  >
                    <Input
                      placeholder={
                        selectedModelType === 4 
                          ? "请输入（必填）" 
                          : "请输入（可选）"
                      }
                      className={styles.embeddingDimensionInput}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark 
                    label={<CustomLabel label="Temperature" required={true} />}
                    field="temperature"
                    rules={[
                      { required: true, message: '请输入Temperature' },
                      { 
                        validator: (value, callback) => {
                          if (value !== undefined && value !== null && value !== '') {
                            const numValue = parseFloat(value);
                            if (isNaN(numValue) || numValue <= 0 || numValue > 1) {
                              callback('Temperature必须是0-1之间的数字，且不能为0');
                            } else {
                              callback();
                            }
                          } else {
                            callback();
                          }
                        }
                      }
                    ]}
                  >
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.1}
                      placeholder="请输入（0.1-1）"
                      // className={styles.temperatureInput}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
        <Space
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            paddingTop: '16px',
            borderTop: '1px solid #f5f5f5',
            marginTop:'16px'
          }}
          size={8}
        >
          <Button onClick={handleCancel} className={styles.cancelButton}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={handleSubmit}
            className={`${styles.submitButton} ${
              !formValid ? styles.submitButtonDisabled : ''
            }`}
            disabled={!formValid}
          >
            创建
          </Button>
        </Space>
      </Form>
    </div>
  );
};

export default AddModelPage;
