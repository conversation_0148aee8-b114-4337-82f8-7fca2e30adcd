import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Typography, Space, Select, Modal, Message, Popover, Tag } from '@arco-design/web-react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import IconSearch from '@/assets/knowledge/IconSearch.svg';
import IconSortType from '@/assets/application/IconSortType.svg';
import IconEmptyFile from '@/assets/knowledge/NotFile.svg';
import IconAction from '@/assets/knowledge/iconMore.svg';
import IconFile from '@/assets/knowledge/IconKnowledge.png';
import styles from './style/index.module.less';

const { Text } = Typography;
const Option = Select.Option;

// 文件类型接口
interface FileItem {
    id: string;
    name: string;
    description: string;
    type: string;
    size: number;
    fileCount: number;
    creator: string;
    created_time: string;
    updated_time?: string;
    status: 'available' | 'unavailable';
}

interface FilesMainProps {
    onEnterFileManager?: (fileId: string) => void;
}

function FilesMain({ onEnterFileManager }: FilesMainProps) {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [files, setFiles] = useState<FileItem[]>([]);
    const [filteredFiles, setFilteredFiles] = useState<FileItem[]>([]);
    const [loading, setLoading] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [sortType, setSortType] = useState('default');
    const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
    const [fileToDelete, setFileToDelete] = useState<FileItem | null>(null);

    // 测试数据
    const mockFiles: FileItem[] = [
        {
            id: '1',
            name: '产品',
            description: '存储产品需求文档、技术规范、UI/UX 设计稿及迭代计划等文档',
            type: 'document',
            size: 25.6,
            fileCount: 8,
            creator: 'AI4C',
            created_time: '2024-10-25T10:00:00Z',
            updated_time: '2024-10-25T15:30:00Z',
            status: 'available'
        },
        {
            id: '2',
            name: '技术',
            description: '竞品分析、营销策略、销售工具及客户案例收集',
            type: 'technical',
            size: 18.9,
            fileCount: 12,
            creator: 'AI4C',
            created_time: '2024-10-24T14:20:00Z',
            status: 'available'
        },
        {
            id: '3',
            name: '人力',
            description: '员工手册、绩效考核标准、培训课程及招聘材料',
            type: 'hr',
            size: 32.4,
            fileCount: 15,
            creator: 'AI4C',
            created_time: '2024-10-23T09:15:00Z',
            status: 'unavailable'
        },
        {
            id: '4',
            name: '市场',
            description: '财务报表、供应商合同、法务协议及审计记录',
            type: 'marketing',
            size: 45.2,
            fileCount: 20,
            creator: 'AI4C',
            created_time: '2024-10-22T16:45:00Z',
            updated_time: '2024-10-23T11:20:00Z',
            status: 'available'
        }
    ];

    useEffect(() => {
        // 模拟加载数据
        setLoading(true);
        setTimeout(() => {
            setFiles(mockFiles);
            setLoading(false);
        }, 500);
    }, []);

    // 当files或搜索/排序条件改变时，更新过滤后的文件列表
    useEffect(() => {
        filterAndSortFiles();
    }, [files, searchText, sortType]);

    // 获取文件图标
    const getFileIcon = (type: string) => {
        return <img src={IconFile} alt="file" style={{ width: '48px', height: '48px' }} />;
    };

    // 获取文件类型显示文本和样式
    const getFileTypeInfo = (type: string) => {
        const typeMap = {
            document: { text: '系统', className: 'system' },
            technical: { text: '用户', className: 'user' },
            hr: { text: '系统', className: 'system' },
            marketing: { text: '用户', className: 'user' }
        };
        return typeMap[type] || { text: '系统', className: 'system' };
    };

    // 格式化文件大小
    const formatFileSize = (size: number) => {
        return `${size.toFixed(1)}MB`;
    };

    // 筛选和排序文件列表
    const filterAndSortFiles = () => {
        let result = [...files];

        // 搜索过滤
        if (searchText) {
            const lowerCaseSearch = searchText.toLowerCase();
            result = result.filter(file =>
                file.name.toLowerCase().includes(lowerCaseSearch) ||
                file.description.toLowerCase().includes(lowerCaseSearch) ||
                file.creator.toLowerCase().includes(lowerCaseSearch)
            );
        }

        // 排序
        switch (sortType) {
            case 'createTime':
                result.sort((a, b) => {
                    const timeA = new Date(a.created_time).getTime();
                    const timeB = new Date(b.created_time).getTime();
                    return timeB - timeA;
                });
                break;
            case 'updateTime':
                result.sort((a, b) => {
                    const timeA = a.updated_time ? new Date(a.updated_time).getTime() : 0;
                    const timeB = b.updated_time ? new Date(b.updated_time).getTime() : 0;
                    return timeB - timeA;
                });
                break;
            case 'name':
                result.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'size':
                result.sort((a, b) => b.size - a.size);
                break;
            case 'default':
            default:
                break;
        }

        setFilteredFiles(result);
    };

    // 处理排序变化
    const handleSort = (value: string) => {
        setSortType(value);
    };

    // 处理搜索
    const handleSearch = (value: string) => {
        setSearchText(value);
    };

    // 处理"查看"文件详情 - 跳转到file-manager
    const handleViewFile = (file: FileItem) => {
        if (onEnterFileManager) {
            onEnterFileManager(file.id);
        } else {
            Message.info(`正在进入 ${file.name} 的文件管理器...`);
        }
    };

    // 处理创建文件夹
    const handleCreateFolder = () => {
        Message.info('创建文件夹功能开发中...');
    };

    // 处理删除确认
    const handleDeleteConfirm = (file: FileItem) => {
        setFileToDelete(file);
        setConfirmDeleteVisible(true);
    };

    // 确认删除
    const handleConfirmDelete = () => {
        if (fileToDelete) {
            setFiles(files.filter(f => f.id !== fileToDelete.id));
            Message.success(`文件夹 ${fileToDelete.name} 已删除`);
            setConfirmDeleteVisible(false);
            setFileToDelete(null);
        }
    };

    // 取消删除
    const handleCancelDelete = () => {
        setConfirmDeleteVisible(false);
        setFileToDelete(null);
    };

    // 渲染内容
    const renderContent = () => {
        if (loading) {
            return (
                <div className={styles.emptyContainer}>
                    <Text>加载中...</Text>
                </div>
            );
        }

        if (filteredFiles.length === 0) {
            return (
                <div className={styles.emptyContainer}>
                    <Space direction='vertical' size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <IconEmptyFile style={{ width: 80, height: 80 }} />
                        <Text type="secondary">{searchText ? '未找到匹配的文件' : '暂无文件'}</Text>
                    </Space>
                </div>
            );
        }

        return (
            <>
                {filteredFiles.map((file) => (
                    <Card key={file.id} className={styles.fileCard} onClick={() => handleViewFile(file)}>
                        <div className={styles.fileInfo}>
                            <Space direction='vertical' size={8}>
                                <Space className={styles.nameWrapper} size={12}>
                                    <span className={styles.icon}>
                                        {getFileIcon(file.type)}
                                    </span>
                                    <Text className={styles.name}>{file.name}</Text>
                                </Space>
                                <Space className={styles.fileDetails} direction='vertical' size={8}>
                                    <Text className={styles.description}>{file.description}</Text>
                                    <div className={styles.tags}>
                                        <Tag className={`${styles.fileType} ${styles[getFileTypeInfo(file.type).className]}`}>
                                            {getFileTypeInfo(file.type).text}
                                        </Tag>
                                        <Tag className={styles.tagLabel}>标签</Tag>
                                        <Tag className={styles.tagLabel}>标签</Tag>
                                    </div>
                                </Space>
                            </Space>
                            <div className={styles.fileFooter}>
                                {/* 默认显示的文件信息 */}
                                <div className={styles.defaultInfo}>
                                    <Space className={styles.fileStats}>
                                        <Text className={styles.fileCount}>{file.fileCount} 个文件</Text>
                                    </Space>
                                    <div className={styles.statusInfo}>
                                        <Tag
                                            className={`${styles.statusTag} ${file.status === 'available' ? styles.statusAvailable : styles.statusUnavailable}`}
                                            color={file.status === 'available' ? "green" : "red"}
                                        >
                                            {file.status === 'available' ? "启用" : "禁用"}
                                        </Tag>
                                    </div>
                                </div>

                                {/* 悬浮时显示的元信息和操作按钮 */}
                                <div className={styles.hoverInfo}>
                                    <Space className={styles.metaText}>
                                        <Text className={styles.creator}>@{file.creator}</Text>
                                        <Text>|</Text>
                                        <Text className={styles.timeInfo}>
                                            {file.updated_time
                                                ? `更新时间：${new Date(file.updated_time).toLocaleDateString('zh-CN')}`
                                                : `创建时间：${new Date(file.created_time).toLocaleDateString('zh-CN')}`
                                            }
                                        </Text>
                                    </Space>
                                    <Popover
                                        trigger="click"
                                        position="right"
                                        content={
                                            <Space className={styles.popoverContent} direction='vertical' size={'mini'}>
                                                <Button
                                                    className={styles.actionBtn}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleViewFile(file);
                                                    }}
                                                >
                                                    查看详情
                                                </Button>
                                                <Button
                                                    className={`${styles.actionBtn} ${styles.deleteBtn}`}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleDeleteConfirm(file);
                                                    }}
                                                >
                                                    删除
                                                </Button>
                                            </Space>
                                        }
                                    >
                                        <Button
                                            className={styles.triggerBtn}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                            }}
                                        >
                                            <IconAction />
                                        </Button>
                                    </Popover>
                                </div>
                            </div>
                        </div>
                    </Card>
                ))}
            </>
        );
    };

    return (
        <div className={styles.filesMain}>
            <div className={styles.header}>
                <Button type="primary" className={styles.createBtn} onClick={handleCreateFolder}>
                    创建文件组
                </Button>
                <Space size={'small'}>
                    <Text className={styles.fileNumber}>
                        共 {filteredFiles.length} 个知识库
                    </Text>
                    <Input
                        prefix={<IconSearch />}
                        placeholder="AI搜索..."
                        style={{ width: 240 }}
                        onChange={(value) => handleSearch(value)}
                        allowClear
                    />
                    <Select
                        placeholder="全部"
                        style={{ width: 160 }}
                        onChange={handleSort}
                        value={sortType}
                    >
                        <Option value="default">全部</Option>
                        <Option value="createTime">按创建时间排序</Option>
                        <Option value="updateTime">按更新时间排序</Option>
                        <Option value="name">按名称排序</Option>
                        <Option value="size">按大小排序</Option>
                    </Select>
                </Space>
            </div>

            <div className={styles.content}>
                {renderContent()}
            </div>

            {/* 删除确认弹窗 */}
            <Modal
                visible={confirmDeleteVisible}
                title="删除文件夹"
                onCancel={handleCancelDelete}
                className={styles.confirmDeleteModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <Text className={styles.modalContentText}>
                        {`文件夹 ${fileToDelete?.name || ''} 将被删除，请确认您是否要删除？`}
                    </Text>
                </div>
                <div className={styles.modalFooter}>
                    <Space>
                        <Button onClick={handleCancelDelete} className={styles.cancelDeleteBtn}>
                            取消
                        </Button>
                        <Button onClick={handleConfirmDelete} className={styles.confirmDeleteBtn}>
                            删除
                        </Button>
                    </Space>
                </div>
            </Modal>
        </div>
    );
}

export default FilesMain; 