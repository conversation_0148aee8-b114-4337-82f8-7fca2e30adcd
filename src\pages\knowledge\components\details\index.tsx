import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import styles from './style/index.module.less';
import IconFile from '@/assets/knowledge/IconFile.svg';
import { Button, Message, Input, Modal } from '@arco-design/web-react';
import { deleteVectorKnowledgeFile } from '@/lib/services/knowledge-service';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import Text from '@arco-design/web-react/es/Typography/text';
import ButtonComponent from '@arco-design/web-react/es/Button';
import useLocale from '@/utils/useLocale';
import {
  updateKnowledgeFile,
  getKnowledgeFiles,
} from '@/lib/services/knowledge-service';
import FoldIcon from '@/assets/knowledge/fold.svg';
import UnfoldIcon from '@/assets/knowledge/unfold.svg';
import DeleteIcon from '@/assets/knowledge/delete.svg';
import EditIcon from '@/assets/knowledge/edit.svg';
import TrueIcon from '@/assets/knowledge/true.svg';
import CloseIcon from '@/assets/knowledge/close.svg';



interface FileDetailState {
  name: string;
  type: string;
  id: string;
  characters: string;
  time: string;
  dataSource: string;
  fileSource: string;
  text: string;
  knowledgeName: string;
  user_name: string;
  createTime: string;
}

function FileDetail() {
  const location = useLocation();
  const fileData = location.state as FileDetailState;
  const {
    id,
    name,
    characters,
    time,
    dataSource,
    fileSource,
    text: initialText,
    type,
    knowledgeName,
    user_name,
    createTime,
  } = fileData;
  const locale = useLocale();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [QAText, setQAText] = useState(name);
  const [text, setText] = useState(initialText);
  const [textSegments, setTextSegments] = useState<Array<{ id: string, text: string, originalText: string, isExpanded: boolean, isEditing: boolean }>>([]);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [deleteSegmentId, setDeleteSegmentId] = useState<string | null>(null);



  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleCancel = () => {
    setIsEditing(false);
    setText(initialText);
    setQAText(name);
  };

  const handleSaveQA = async () => {
    await updateKnowledgeFile(knowledgeName, {
      id: id,
      text: QAText,
      data_source: 'user',
      payload: { answer: text },
    });
    setIsEditing(false);
    Message.success('更新成功');
  };

  const handleBack = () => {
    navigate('/knowledge');
  };

  // 切换展开/折叠状态
  const toggleExpand = (segmentId: string) => {
    setTextSegments(prevSegments =>
      prevSegments.map(segment =>
        segment.id === segmentId
          ? { ...segment, isExpanded: !segment.isExpanded }
          : segment
      )
    );
  };

  // 开始编辑特定segment
  const startEditSegment = (segmentId: string) => {
    setTextSegments(prevSegments =>
      prevSegments.map(segment =>
        segment.id === segmentId
          ? { ...segment, isEditing: true, isExpanded: true, originalText: segment.text }
          : segment
      )
    );
  };

  // 保存特定segment的编辑
  const saveSegment = async (segmentId: string) => {
    const segment = textSegments.find(seg => seg.id === segmentId);
    if (!segment) return;

    try {
      // 这里可以添加保存到后端的逻辑
      // await updateSegmentText(knowledgeName, segmentId, segment.text);
      await updateKnowledgeFile(knowledgeName, {
        id: segment.id,
        text: segment.text,
        data_source: 'user',
        payload: {
          "fileName": name,
          "fileSource": fileSource,
          "dataSource": dataSource,
          "fileId": id
        },
      });
      setIsEditing(false);

      setTextSegments(prevSegments =>
        prevSegments.map(seg =>
          seg.id === segmentId
            ? { ...seg, isEditing: false }
            : seg
        )
      );
      Message.success('分段更新成功');
    } catch (error) {
      Message.error('更新失败');
    }
  };

  // 取消编辑特定segment
  const cancelEditSegment = (segmentId: string) => {
    setTextSegments(prevSegments =>
      prevSegments.map(segment =>
        segment.id === segmentId
          ? { ...segment, isEditing: false, text: segment.originalText }
          : segment
      )
    );
  };

  // 判断文本是否需要折叠（超过3行）
  const shouldShowToggle = (text: string) => {
    const lines = text.split('\n').length;
    return lines > 3 || text.length > 150; // 超过3行或150个字符
  };

  // 删除段落
  const deleteSegment = (segmentId: string) => {
    setDeleteSegmentId(segmentId);
    setIsDeleteModalVisible(true);
  };

  // 确认删除段落
  const handleDeleteConfirm = async () => {
    try {
      if (!deleteSegmentId) return;

      await deleteVectorKnowledgeFile(knowledgeName, deleteSegmentId);
      setTextSegments(prevSegments =>
        prevSegments.filter(segment => segment.id !== deleteSegmentId)
      );
      Message.success('分段删除成功');
      setIsDeleteModalVisible(false);
      setDeleteSegmentId(null);
    } catch (error) {
      console.error('删除分段失败:', error);
      Message.error('删除失败，请重试');
    }
  };

  // 取消删除段落
  const handleDeleteCancel = () => {
    setIsDeleteModalVisible(false);
    setDeleteSegmentId(null);
  };

  useEffect(() => {
    if (type === 'document') {
      const fetchKnowledgeFiles = async () => {
        const response = await getKnowledgeFiles(knowledgeName, {
          page: 1,
          size: 73,
          with_vector: true,
          included_payloads: [
            'text',
            'fileId',
            'fileName',
            'dataSource',
            'fileSource',
            'fileUrl',
          ],
          search_pairs: [
            {
              key: 'fileId',
              value: id,
            },
          ],
        });
        const segments = response.items
          .filter(item => item.data && item.data.text)
          .map((item, index) => ({
            id: item.id || `segment-${index}`,
            text: item.data.text,
            originalText: item.data.text,
            isExpanded: false,
            isEditing: false
          }));
        setTextSegments(segments);
      };
      fetchKnowledgeFiles();
    }
  }, []);

  const leftContainer = () => {
    return (
      <div className={styles.leftContainer}>
        {type === 'document' ? (
          <div className={styles.content}>
            <div className={styles.title}>
              {locale['menu.application.knowledge.file.content']}
            </div>
            {textSegments.length > 0 ? (
              <div className={styles.segmentsContainer}>
                {textSegments.map((segment, index) => {
                  const needsToggle = shouldShowToggle(segment.text);
                  const displayText = !segment.isEditing && needsToggle && !segment.isExpanded
                    ? segment.text.split('\n').slice(0, 3).join('\n') + (segment.text.split('\n').length > 3 ? '...' : '')
                    : segment.text;

                  return (
                    <div key={segment.id} className={styles.segmentCard}>
                      <div className={styles.segmentHeader}>
                        <span className={styles.segmentNumber}>分段 {index + 1}</span>
                        <div className={styles.buttonGroup}>
                          {needsToggle && !segment.isEditing && (
                            <button
                              className={styles.toggleButton}
                              onClick={() => toggleExpand(segment.id)}
                            >
                              {segment.isExpanded ? (
                                <FoldIcon className={styles.toggleIcon} />
                              ) : (
                                <UnfoldIcon className={styles.toggleIcon} />
                              )}
                            </button>
                          )}
                          {segment.isEditing ? (
                            <>
                              <button
                                className={styles.saveButton}
                                onClick={() => saveSegment(segment.id)}
                              >
                                <TrueIcon className={styles.saveIcon} />
                              </button>
                              <button
                                className={styles.cancelButton}
                                onClick={() => cancelEditSegment(segment.id)}
                              >
                                <CloseIcon className={styles.cancelIcon} />
                              </button>
                            </>
                          ) : (
                            <>
                              <button
                                className={styles.editButton}
                                onClick={() => startEditSegment(segment.id)}
                              >
                                <EditIcon className={styles.editIcon} />
                              </button>
                              <button
                                className={styles.deleteButton}
                                onClick={() => deleteSegment(segment.id)}
                              >
                                <DeleteIcon className={styles.deleteIcon} />
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                      <div className={styles.segmentContent}>
                        {segment.isEditing ? (
                          <div
                            className={`${styles.textDisplay} ${styles.editable} ${needsToggle && !segment.isExpanded ? styles.collapsed : ''}`}
                            contentEditable
                            suppressContentEditableWarning
                            onBlur={(e) => {
                              const newValue = e.target.textContent || '';
                              setTextSegments(prevSegments =>
                                prevSegments.map(seg =>
                                  seg.id === segment.id
                                    ? { ...seg, text: newValue }
                                    : seg
                                )
                              );
                            }}
                            dangerouslySetInnerHTML={{
                              __html: needsToggle && !segment.isExpanded ? displayText : segment.text
                            }}
                          />
                        ) : (
                          <div
                            className={`${styles.textDisplay} ${needsToggle && !segment.isExpanded ? styles.collapsed : ''
                              }`}
                          >
                            {displayText}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div
                className={`${styles.text} ${isEditing ? styles.editing : ''}`}
              >
                <textarea
                  className={styles.textContent}
                  value={text}
                  disabled={!isEditing}
                  onChange={(e) => {
                    const newValue = e.target.value;
                    setText(newValue);
                  }}
                />
              </div>
            )}
          </div>
        ) : (
          <div className={styles.content}>
            <div className={styles.title}>
              {locale['menu.application.knowledge.file.question']}
            </div>
            <div
              className={`${styles.question} ${isEditing ? styles.editing : ''
                }`}
            >
              <textarea
                className={styles.textContent}
                value={QAText}
                disabled={!isEditing}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setQAText(newValue);
                }}
              />
            </div>
            <div
              className={styles.title}
              style={{
                marginBottom: '8px',
              }}
            >
              {locale['menu.application.knowledge.file.answer']}
            </div>
            <div
              className={`${styles.text} ${isEditing ? styles.editing : ''}`}
            >
              <textarea
                className={styles.textContent}
                value={text}
                disabled={!isEditing}
                onChange={(e) => {
                  const newValue = e.target.value;
                  setText(newValue);
                }}
              />
            </div>
          </div>
        )}
      </div>
    );
  };

  const rightContainer = () => {
    return (
      <div className={styles.rightContainer}>
        <div className={styles.fileInfo}>
          {type === 'document' && (
            <>
              <div className={styles.title}>
                {locale['menu.application.knowledge.file.name']}
              </div>
              <div className={styles.text}>
                <textarea
                  className={styles.name}
                  value={name}
                  disabled={true}
                />
              </div>
            </>
          )}

          <div className={styles.title}>
            基础{locale['menu.application.knowledge.file.info']}
          </div>
          <div className={styles.info}>
            <div className={styles.infoBar}>
              <span className={styles.label}>
                {locale['menu.application.knowledge.file.size']}
              </span>
              <span className={styles.value}>{characters || '-'}</span>
            </div>
            <div className={styles.infoBar}>
              <span className={styles.label}>
                {locale['menu.application.knowledge.file.createTime']}
              </span>
              <span className={styles.value}>{createTime || '-'}</span>
            </div>
            <div className={styles.infoBar}>
              <span className={styles.label}>
                {locale['menu.application.knowledge.file.updateTime']}
              </span>
              <span className={styles.value}>{createTime || '-'}</span>
            </div>
            <div className={styles.infoBar}>
              <span className={styles.label}>
                {locale['menu.application.knowledge.file.createUser']}
              </span>
              <span className={styles.value}>{user_name || 'admin'}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <RowComponent className={styles.titleContainer}>
        <Text className={styles.title}>
          {type === 'document'
            ? locale['menu.application.knowledge.file.documentDetail']
            : locale['menu.application.knowledge.file.qandaDetail']}
        </Text>
      </RowComponent>
      <div className={styles.customContainer}>
        {leftContainer()}
        {rightContainer()}
      </div>
      {type !== "document" && (
        <div className={styles.footer}>
          <RowComponent className={styles.operateButGroup}>
            {isEditing ? (
              <>
                <ButtonComponent
                  type="secondary"
                  className={[styles.cancelBut, styles.but]}
                  onClick={handleCancel}
                >
                  <Text className={styles.text}>{locale['cancelBut']}</Text>
                </ButtonComponent>

                <ButtonComponent
                  type="primary"
                  className={[styles.saveBut, styles.but]}
                  onClick={handleSaveQA}
                >
                  <Text className={styles.text}>{locale['saveBut']}</Text>
                </ButtonComponent>
              </>
            ) : (
              <>
                <ButtonComponent
                  type="secondary"
                  className={[styles.cancelBut, styles.but]}
                  onClick={handleBack}
                >
                  <Text className={styles.text}>{locale['backBut']}</Text>
                </ButtonComponent>
                <ButtonComponent
                  type="primary"
                  className={[styles.editBut, styles.but]}
                  onClick={handleEdit}
                >
                  <Text className={styles.text}>{locale['editBut']}</Text>
                </ButtonComponent>
              </>
            )}
          </RowComponent>
        </div>
      )}

      {/* 删除确认弹窗 */}
      <Modal
        title="确认删除"
        visible={isDeleteModalVisible}
        onOk={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        autoFocus={false}
        maskClosable={false}
        okText="确认"
        cancelText="取消"
        className={styles.deleteModal}
        okButtonProps={{
          className: styles.submitBtn,
        }}
        cancelButtonProps={{
          className: styles.cancelBtn,
        }}
      >
        <p>确定要删除这个分段吗？删除后将无法恢复。</p>
      </Modal>
    </div>
  );
}

export default FileDetail;
