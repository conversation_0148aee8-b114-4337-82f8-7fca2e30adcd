import React, { useState, useEffect } from 'react';
import { Tabs, Message } from '@arco-design/web-react';
import { useParams } from 'react-router-dom';
import styles from './style/index.module.less';
import { getAcpServerList, getAcpServerTools } from '@/lib/services/acp-server-service';
import ResourcesList from './ResourcesList';
import Settings from './Settings';
import { AcpServerResponse, AcpTools } from '@/types/acpServerType';

const { TabPane } = Tabs;

function ACPServerDetail() {
    const { id } = useParams<{ id: string }>();
    const [activeTab, setActiveTab] = useState('resources');
    const [acpServer, setAcpServer] = useState<AcpServerResponse | null>(null);
    const [acpTools, setAcpTools] = useState<AcpTools[]>([]);
    const [allAcpServers, setAllAcpServers] = useState<AcpServerResponse[]>([]);
    const [loading, setLoading] = useState(true);

    // 刷新ACP工具数据的函数
    const refreshAcpTools = async () => {
        if (!id) return;
        
        try {
            const tools = await getAcpServerTools(id);
            setAcpTools(tools);
        } catch (error) {
            console.error('刷新ACP工具数据失败:', error);
            Message.error('刷新ACP工具数据失败');
        }
    };

    // 获取ACP服务器详情和工具列表
    useEffect(() => {
        const fetchAcpServerDetail = async () => {
            try {
                setLoading(true);
                // 获取所有ACP服务器列表
                const acpList = await getAcpServerList();
                setAllAcpServers(acpList);
                
                // 找到当前ACP服务器
                const server = acpList.find(acp => acp.id === id);
                if (server) {
                    setAcpServer(server);
                }

                // 获取ACP服务器工具详情
                if (id) {
                    const tools = await getAcpServerTools(id);
                    setAcpTools(tools);
                }
            } catch (error) {
                console.error('获取ACP服务器详情失败:', error);
                Message.error('获取ACP服务器详情失败');
            } finally {
                setLoading(false);
            }
        };

        if (id) {
            fetchAcpServerDetail();
        }
    }, [id]);

    // 处理Tab切换
    const handleTabChange = (key) => {
        setActiveTab(key);
    };

    // 处理保存设置
    const handleSaveSettings = (updatedServer: AcpServerResponse) => {
        // 更新本地状态
        setAcpServer(updatedServer);
        
        // 同时更新allAcpServers中对应的服务器信息
        setAllAcpServers(prevList => 
            prevList.map(server => 
                server.id === updatedServer.id ? updatedServer : server
            )
        );
        
        Message.success('设置已保存');
    };

    return (
        <div className={styles.acpServerDetail}>
            <Tabs activeTab={activeTab} onChange={handleTabChange} className={styles.tabs}>
                <TabPane key="resources" title="ACP资源列表">
                    <ResourcesList 
                        acpServerId={id} 
                        acpTools={acpTools}
                        loading={loading}
                    />
                </TabPane>
                <TabPane key="settings" title="设置">
                    <Settings 
                        acpServer={acpServer} 
                        onSave={handleSaveSettings}
                        existingAcps={allAcpServers}
                        onTestConnectionComplete={refreshAcpTools}
                    />
                </TabPane>
            </Tabs>
        </div>
    );
}

export default ACPServerDetail; 