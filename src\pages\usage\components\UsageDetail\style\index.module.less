.container {

    .Title {
        font-weight: 600;
        font-size: 20px;
        line-height: 24px;
        color: #333333;
        padding-bottom: 16px;
        border-bottom: 1px solid #ebebeb;
        margin-bottom: 24px;
    }
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 24px;
    border-radius: 4px;
    border: 1px solid #ebebeb;

    .appIcon {
        width: 48px;
        height: 48px;
        border-radius: 4px;
    }

    .appInfo {
        .titleRow {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 4px;

            :global(.arco-typography) {
                font-weight: 600;
                font-size: 20px;
                line-height: 28px;
                color: #333333;
            }

            :global(.arco-tag) {
                padding: 2px 6px;
                background-color: #ffffff;
                border: 1px solid #ebebeb;
                border-radius: 4px;
                font-weight: 400;
                font-size: 12px;
                color: #595959;
            }
        }

        .creatorInfo {
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #a6a6a6;
        }
    }

    .configButton,
    .testButton {
        display: flex;
        justify-content: center;
        align-items: center;
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        padding: 8px 24px;
    }

    .configButton {
        flex: 1;
        background-color: #4d5ef3;
        color: #ffffff;
        border: 1px solid #4d5ef3;
        border-radius: 4px;

        &:hover {
            background-color: #374aed !important;
        }
    }

    .testButton {
        flex: 1;
        color: #595959;
        background-color: #fafafa;
        border-radius: 4px;
    }

}

.mainContent {
    .overviewCol {
        padding-right: 24px !important;
        border-right: 1px solid #ebebeb;
    }

    .qualityCol {
        padding-left: 24px !important;
    }

    .sectionTitle {
        font-weight: 600;
        font-size: 14px;
        color: #595959;
        margin-bottom: 8px;
    }

    :global(.arco-card-body) {
        padding: 24px;
        border-radius: 4px;
        border: 1px solid #ebebeb;
    }

    :global(.h5.arco-typography) {
        color: #374aed;
    }
}

.sectionTitle {
    margin: 24px 0 16px;
}

.overviewCard,
.qualityCard {
    margin-bottom: 24px;

    .statItem {
        text-align: center;

        h3 {
            margin-bottom: 4px;
            font-weight: 600;
            font-size: 20px;
            color: #595959;
        }

        :global(.arco-typography-secondary) {
            font-weight: 400;
            font-size: 12px;
            line-height: 20px;
            color: #a6a6a6;
        }
    }
}

.chartCard {
    margin-bottom: 24px;
}

.qualityCard {
    margin-bottom: 24px;

    .qualityItem {
        padding: 16px;
        background: var(--color-fill-2);
        border-radius: 4px;
        text-align: center;

        h3 {
            margin-bottom: 4px;
            color: var(--color-text-1);
        }
    }
}

.evaluationCard {

    :global(.arco-card-body) {
        border: none;
    }
}

.scoreContent {
    :global(.arco-card-body) {
        border-color: #f0f2fe;
    }
}

.scoreSection {
    background: linear-gradient(90deg, rgba(68, 85, 242, 0) 0%, rgba(68, 85, 242, 0.1) 100%);
    position: relative;

    .ScoreImg {
        position: absolute;
        bottom: 0;
        right: 40px;
    }

    :global(.arco-typography) {
        color: #4d5ef3;
        font-weight: 600;
        font-size: 20px;
        line-height: 28px;
        margin-bottom: 0;
    }

    :global(.arco-typography-secondary) {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #a6a6a6;
    }
}

// 响应式布局
@media screen and (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 16px;

        .appInfo {
            .titleRow {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    }
}

.searchHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 20px;
}

.rowEndCenter {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
}

.searchBox {
  width: 240px;
  height: 32px;
  border-radius: 4px;
  background-color: var(--color-fill-2);
  border: none;

  &:hover,
  &:focus {
    background-color: var(--color-fill-3);
  }
}

.selectBox {
  width: 200px;
  height: 32px;
  border-radius: 4px;
  background-color: var(--color-fill-2);
  border: none;

  &:hover {
    background-color: var(--color-fill-3);
  }
}

.customCardBox {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 20px;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
}

.customCard {
  border-radius: 8px;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

.rowStartCenter {
  display: flex;
  align-items: center;
  gap: 8px;
}

.folderIcon {
  width: 32px;
  height: 32px;
}

.name {
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.description {
  font-size: 14px;
  color: var(--color-text-3);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.profileTag {
  padding: 2px 8px;
  background-color: var(--color-fill-2);
  border-radius: 4px;
  font-size: 12px;
  color: var(--color-text-2);
  margin-right: 8px;
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--color-border);
}

.footerText {
  font-size: 14px;
  color: var(--color-text-2);
}

.metaText {
  font-size: 12px;
  color: var(--color-text-3);
}

.hoverContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loadingContainer,
.emptyContainer {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loadingText,
.emptyText {
  margin-top: 16px;
  color: var(--color-text-3);
}

.red {
  color: rgb(var(--red-6));
  cursor: pointer;
  padding: 5px 12px;
  margin: 0;

  &:hover {
    background-color: var(--color-fill-2);
  }
}

.countAppText {
  font-size: 14px;
  color: var(--color-text-2);
}