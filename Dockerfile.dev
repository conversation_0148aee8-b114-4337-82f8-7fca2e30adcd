FROM nginx:alpine

# 创建nginx日志目录
RUN mkdir -p /usr/local/etc/nginx/logs

# 创建自定义样式目录
RUN mkdir -p /usr/local/etc/nginx/custom_styles

# 复制自定义CSS文件
COPY elsa/mudblazor.min.css /usr/local/etc/nginx/custom_styles/
COPY elsa/shell.css /usr/local/etc/nginx/custom_styles/
COPY elsa/material-base.css /usr/local/etc/nginx/custom_styles/

# 复制nginx配置
COPY elsa/nginx.conf /etc/nginx/nginx.conf.template

# 暴露3000端口
EXPOSE 3000

# 创建启动脚本文件
RUN echo '#!/bin/sh' > /docker-entrypoint.sh \
    && echo 'sed "s|http://localhost:13001|http://frontend-vite:13001|g" /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf' >> /docker-entrypoint.sh \
    && echo 'sed -i "s|http://localhost:13000|http://elsa-service:8080|g" /etc/nginx/nginx.conf' >> /docker-entrypoint.sh \
    && echo 'nginx -g "daemon off;"' >> /docker-entrypoint.sh \
    && chmod +x /docker-entrypoint.sh

# 设置启动命令
CMD ["/docker-entrypoint.sh"] 