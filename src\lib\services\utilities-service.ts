import axiosInstance from './interceptors';
import { endpoints } from './api-endpoints';

export interface UtilityListResponse {
  items: Array<{
    id: string;
    name: string;
    description: string;
    disabled: boolean;
    functions: Array<{
      name: string;
    }>;
    templates: Array<{
      name: string;
    }>;
  }>;
}

/**
 * 获取工具列表
 * @param params 查询参数
 * @returns Promise<UtilityListResponse>
 */
export async function getUtilityList(
  searchValue?: string,
  searchLabel?: string
): Promise<UtilityListResponse> {
  try {
    const response = await axiosInstance.get(endpoints.agentUtilityOptionsUrl, {
      params: {
        'Name': searchValue,
        'Labels': [searchLabel]
      },
    });
    return response.data;
  } catch (error) {
    console.error('获取工具列表失败:', error);
    throw error;
  }
}

/**
 * 获取系统工具标签
 */
export async function getSystemToolLabelOptions() {
  return axiosInstance.get(endpoints.agentUtilityLabelOptionsUrl);
}
