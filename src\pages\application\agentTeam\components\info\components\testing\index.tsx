import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Input, Button } from 'antd';
import IconClean from '@/assets/clean.png';
import IconSend from '@/assets/chat/sendBtn.svg';
import IconPeopleAvator from '@/assets/chat/people-avator.svg';
import IconBotAvator from '@/assets/chat/ai-avator.svg';
import styles from './style/index.module.less';
import useLocale from '@/utils/useLocale';
import Text from '@arco-design/web-react/es/Typography/text';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import { Collapse, Divider, Modal } from '@arco-design/web-react';
import { IconDown } from '@arco-design/web-react/icon';
import ReactMarkdown from 'react-markdown';
import { createConversation, sendMessage, ConversationParams } from '@/lib/services/conversation-service';
import { signalr } from '@/lib/services/signalr-service';
import type { Message, ConversationLog, ConversationSenderAction } from '@/lib/services/signalr-service';
import { ApplicationResponse } from '@/lib/services/application-service';
const CollapseItem = Collapse.Item;
const { TextArea } = Input;

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: string;
  conversation_id?: string;
  sender?: {
    id: string;
    user_name: string;
    type: string;
    role: string;
    avatar: string;
  };
  instruction?: {
    question: string;
    function: string;
    response: string;
    next_action_agent: string;
  };
  states?: {
    [key: string]: string;
  };
}
interface ApplicationTestingProps {
  applicationData: ApplicationResponse | null;
  loading: boolean;
}

// 分析项接口
interface AnalyzeItem {
  message_id: string;
  name: string;
  content: string;
  created_at: string;
}

// 思考等待动画
const ThinkingAnimation: React.FC = () => {
  const [dots, setDots] = useState('');

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '') return '.';
        if (prev === '.') return '..';
        if (prev === '..') return '...';
        return '';
      });
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={styles.thinkingAnimation}>
      <span className={styles.dots}>{dots}</span>
    </div>
  );
};

// 简单的表格渲染组件
const renderMessageContent = (content: string) => {
  // 检测是否包含表格（简单的检测方式）
  const hasTable = content.includes('|') && content.includes('---');
  
  if (hasTable) {
    const lines = content.split('\n');
    const tableStart = lines.findIndex(line => line.includes('|') && line.includes('---'));
    
    if (tableStart > 0) {
      const beforeTable = lines.slice(0, tableStart - 1).join('\n');
      const tableLines = lines.slice(tableStart - 1);
      const afterTableIndex = tableLines.findIndex((line, idx) => idx > 2 && !line.includes('|'));
      
      let tableContent: string[];
      let afterTable = '';
      
      if (afterTableIndex > 0) {
        tableContent = tableLines.slice(0, afterTableIndex);
        afterTable = tableLines.slice(afterTableIndex).join('\n');
      } else {
        tableContent = tableLines;
      }
      
      // 解析表格
      const headers = tableContent[0]?.split('|').map(h => h.trim()).slice(1, -1);
      const rows = tableContent.slice(2).map(row => 
        row.split('|').map(cell => cell.trim()).slice(1, -1)
      ).filter(row => row.length > 0);
      
      return (
        <div>
          {beforeTable && <ReactMarkdown components={{
            a: ({ href, children, ...props }) => (
              <a href={href} target="_blank" rel="noopener noreferrer" {...props}>
                {children}
              </a>
            )
          }}>{beforeTable}</ReactMarkdown>}
          <table style={{ width: '100%', borderCollapse: 'collapse', margin: '8px 0', fontSize: '12px' }}>
            <thead>
              <tr>
                {headers?.map((header, idx) => (
                  <th key={idx} style={{ border: '1px solid #ddd', padding: '4px 8px', backgroundColor: '#f5f5f5', textAlign: 'left' }}>
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {rows.map((row, idx) => (
                <tr key={idx}>
                  {row.map((cell, cellIdx) => (
                    <td key={cellIdx} style={{ border: '1px solid #ddd', padding: '4px 8px' }}>
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
          {afterTable && <ReactMarkdown components={{
            a: ({ href, children, ...props }) => (
              <a href={href} target="_blank" rel="noopener noreferrer" {...props}>
                {children}
              </a>
            )
          }}>{afterTable}</ReactMarkdown>}
        </div>
      );
    }
  }
  
  return <ReactMarkdown components={{
    a: ({ href, children, ...props }) => (
      <a href={href} target="_blank" rel="noopener noreferrer" {...props}>
        {children}
      </a>
    )
  }}>{content}</ReactMarkdown>;
};

const AgentCreate: React.FC<ApplicationTestingProps> = ({ applicationData, loading: parentLoading }) => {
  const locale = useLocale();
  const [tabIndex, setTabIndex] = useState(1);
  const [stateList, setStateList] = useState<Array<{ id: string; content: string; created_at: string }>>([]);
  const [analyzeList, setAnalyzeList] = useState<AnalyzeItem[]>([]);
  const analyzeContentRef = useRef<HTMLDivElement>(null);
  const messageListRef = useRef<HTMLDivElement>(null);

  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isExpanded, setIsExpanded] = useState<boolean[]>([]);
  const [loading, setLoading] = useState(false);
  const [conversationId, setConversationId] = useState<string>('');

  // 添加流式输出控制的状态和引用
  const streamingQueues = useRef<Map<string, string[]>>(new Map());
  const streamingTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const streamingMessages = useRef<Map<string, string>>(new Map());
  
  // 流式输出配置
  const STREAM_OUTPUT_INTERVAL = 50; // 每50ms输出一次
  const STREAM_OUTPUT_CHARS_PER_INTERVAL = 3; // 每次输出3个字符

  const handleExpand = (index: number) => {
    setIsExpanded(prevState => {
      const newState = [...prevState];
      newState[index] = !newState[index];
      return newState;
    });
  }

  const initConversation = async () => {
    if (!applicationData?.id) return;

    console.log("initConversation");
    console.log(applicationData.agent.id);
    setLoading(true);
    try {
      const response = await createConversation(applicationData.agent.id);
      setConversationId(response.id);

      // 添加系统欢迎消息
      const welcomeMessage: ChatMessage = {
        id: Date.now().toString(),
        content: `欢迎测试 ${applicationData.name}`,
        isUser: false,
        timestamp: new Date().toLocaleTimeString(),
        conversation_id: response.id,
        sender: {
          id: response.agent_id,
          user_name: response.agent_name,
          type: 'agent',
          role: 'agent',
          avatar: applicationData.iconUrl || '',
        }
      };
      setMessages([welcomeMessage]);
    } catch (error) {
      console.error('初始化会话失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !conversationId || !applicationData?.agent?.id) return;

    try {
      const params: ConversationParams = {
        agent_id: applicationData.agent.id,
        conversation_id: conversationId,
        text: inputMessage,
        states: []
      };

      setInputMessage('');
      sendMessage(params);

    } catch (error) {
      console.error('发送消息失败:', error);
      // 添加错误提示消息
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        content: '消息发送失败，请重试',
        isUser: false,
        timestamp: new Date().toLocaleTimeString(),
        conversation_id: conversationId,
      };
      setMessages((prev) => [...prev, errorMessage]);
    }
  };

  // 处理从客户端收到的消息
  const onMessageReceivedFromClient = (message: Message) => {
    const userMessage: ChatMessage = {
      id: 'user-' + message.message_id,
      content: message.text,
      isUser: true,
      timestamp: message.created_at.toString(),
      conversation_id: message.conversation_id
    };

    const thinkingMessage: ChatMessage = {
      id: 'thinking-' + message.message_id,
      content: 'THINKING_ANIMATION',
      isUser: false,
      timestamp: new Date().toLocaleTimeString(),
      conversation_id: message.conversation_id,
      sender: {
        id: applicationData.agent.id || '',
        user_name: applicationData.name || '',
        type: 'assistant',
        role: 'assistant',
        avatar: applicationData.iconUrl || '',
      }
    };

    setMessages(prev => [...prev, userMessage, thinkingMessage]);
  };

  // 处理从CSR收到的消息
  const onMessageReceivedFromCsr = (message: Message) => {
    setMessages(prev => [...prev, {
      id: message.message_id,
      content: message.text,
      isUser: false,
      timestamp: message.created_at.toString(),
      conversation_id: message.conversation_id,
      sender: {
        id: message.sender?.id || '',
        user_name: message.sender?.user_name || '',
        type: 'csr',
        role: message.sender?.role || '',
        avatar: message.sender?.avatar || ''
      }
    } as ChatMessage]);
  };

  // 处理从助手收到的消息
  const onMessageReceivedFromAssistant = (message: Message) => {
    setMessages(prev => {
      // 移除thinking动画
      const filteredMessages = prev.filter(msg => !msg.id.startsWith('thinking-'));
      
      // 查找是否已存在相同message_id的消息
      const existingMessageIndex = filteredMessages.findIndex(msg => msg.id === message.message_id);
      
      const assistantMessage: ChatMessage = {
        id: message.message_id,
        content: message.text,
        isUser: false,
        timestamp: message.created_at.toString(),
        conversation_id: message.conversation_id,
        sender: {
          id: message.sender?.id || '',
          user_name: message.sender?.user_name || '',
          type: 'assistant',
          role: message.sender?.role || '',
          avatar: message.sender?.avatar || ''
        }
      };

      if (existingMessageIndex >= 0) {
        // 如果消息已存在，直接覆盖整个消息内容
        const updatedMessages = [...filteredMessages];
        updatedMessages[existingMessageIndex] = assistantMessage;
        return updatedMessages;
      } else {
        // 如果消息不存在，添加完整消息
        return [...filteredMessages, assistantMessage];
      }
    });
  };

  // 处理从助手收到的流式消息
  const onStreamMessageReceivedFromAssistant = (message: Message) => {
    setMessages(prev => {
      // 移除thinking动画
      const filteredMessages = prev.filter(msg => !msg.id.startsWith('thinking-'));
      
      // 查找是否已存在相同message_id的消息
      const existingMessageIndex = filteredMessages.findIndex(msg => msg.id === message.message_id);
      
      if (existingMessageIndex >= 0) {
        // 如果消息已存在，使用流式输出控制
        startStreamingOutput(message.message_id, message.text);
        return filteredMessages;
      } else {
        const assistantMessage: ChatMessage = {
          id: message.message_id,
          content: '',
          isUser: false,
          timestamp: message.created_at.toString(),
          conversation_id: message.conversation_id,
          sender: {
            id: message.sender?.id || '',
            user_name: message.sender?.user_name || '',
            type: 'assistant',
            role: message.sender?.role || '',
            avatar: message.sender?.avatar || ''
          }
        };
        
        // 启动流式输出
        startStreamingOutput(message.message_id, message.text);
        
        return [...filteredMessages, assistantMessage];
      }
    });
  };

  // 处理会话内容日志生成
  const onConversationContentLogGenerated = (log: any) => {
    if (!log) return;
    setAnalyzeList(prev => [...prev, { ...log }]);
  };

  // 处理会话状态日志生成
  const onConversationStateLogGenerated = (log: any) => {
    if (!log) return;
    setStateList(prev => [{
      id: log.message_id,
      created_at: log.created_at,
      content: Object.entries(log.states)
        .map(([key, value]) => {
          switch (key) {
            case 'llm_total_cost':
              return `${key}: $${Number(value).toFixed(6)}`;
            case 'temperature':
            case 'sampling_factor':
              return `${key}: ${Number(value).toFixed(2)}`;
            case 'prompt_total':
            case 'completion_total':
              return `${key}: ${Number(value).toLocaleString()}`;
            case 'max_tokens':
              return value ? `${key}: ${value}` : '';
            default:
              return `${key}: ${value}`;
          }
        })
        .filter(Boolean)
        .join('\n')
    }, ...prev]);
  };

  // 处理消息状态变化
  const onStateChangeGenerated = (log: any) => {
    if (!log) return;
    // 处理消息状态日志
  };

  // 处理代理队列变化
  const onAgentQueueChanged = (log: any) => {
    if (!log) return;
    // 处理代理队列日志
  };

  // 处理发送者动作
  const onSenderActionGenerated = (data: ConversationSenderAction) => {
    if (data?.sender_action === 1) {
    } else if (data?.sender_action === 0) {
    }
  };

  // 处理会话消息删除
  const onConversationMessageDeleted = (data: ConversationLog) => {
    if (!data?.message_id) return;
    setMessages(prev => prev.filter(msg => msg.id !== data.message_id));
  };

  // 添加自动滚动到底部的 effect
  useEffect(() => {
    if (analyzeContentRef.current && analyzeList.length > 0) {
      analyzeContentRef.current.scrollTop = analyzeContentRef.current.scrollHeight;
    }
  }, [analyzeList]);

  // 消息列表自动滚动到底部
  useEffect(() => {
    if (messageListRef.current) {
      messageListRef.current.scrollTop = messageListRef.current.scrollHeight;
    }
  }, [messages]);

  // 组件卸载时清理所有定时器
  useEffect(() => {
    return () => {
      // 清理所有流式输出定时器
      streamingTimers.current.forEach((timer) => {
        clearInterval(timer);
      });
      streamingTimers.current.clear();
      streamingQueues.current.clear();
      streamingMessages.current.clear();
    };
  }, []);

  useEffect(() => {
    if (applicationData?.agent?.id) {
      initConversation();
    }
  }, [applicationData?.agent?.id]);

  useEffect(() => {
    if (!conversationId) return;

    signalr.onMessageReceivedFromClient = onMessageReceivedFromClient;
    signalr.onMessageReceivedFromCsr = onMessageReceivedFromCsr;
    signalr.onMessageReceivedFromAssistant = onMessageReceivedFromAssistant;
    signalr.onStreamMessageReceivedFromAssistant = onStreamMessageReceivedFromAssistant;
    signalr.onConversationContentLogGenerated = onConversationContentLogGenerated;
    signalr.onConversationStateLogGenerated = onConversationStateLogGenerated;
    signalr.onStateChangeGenerated = onStateChangeGenerated;
    signalr.onAgentQueueChanged = onAgentQueueChanged;
    signalr.onSenderActionGenerated = onSenderActionGenerated;
    signalr.onConversationMessageDeleted = onConversationMessageDeleted;

    signalr.start(conversationId);

  }, [conversationId]);

  const handleClean = () => {
    Modal.confirm({
      icon: null,
      title: <div style={{ textAlign: 'left', fontSize: 20, fontWeight: 500, color: '#000000' }}>{locale['menu.application.info.testing.clean.title']}</div>,
      content: locale['menu.application.info.testing.clean.content'],
      okText: locale['confirmBut'],
      cancelText: locale['cancelBut'],
      style: {
        borderRadius: 16,
        padding: 24,
      },
      okButtonProps: {
        status: 'danger',
      },
      footer: (
        <div style={{ textAlign: 'right' }}>
          <Button
            style={{
              height: 40,
              width: 76,
              borderRadius: 8,
              backgroundColor: '#FFFFFF',
              border: '1px solid rgba(0, 0, 0, 0.08)'
            }}
            onClick={() => Modal.destroyAll()}
          >
            {locale['cancelBut']}
          </Button>
          <Button
            style={{
              height: 40,
              width: 76,
              marginLeft: 8,
              borderRadius: 8,
              color: '#FFFFFF',
              backgroundColor: '#D54941'
            }}
            onClick={() => {
              setMessages([]);
              Modal.destroyAll();
            }}
          >
            {locale['cleanBut']}
          </Button>
        </div>
      ),
    });
  };

  // 启动流式输出
  const startStreamingOutput = (messageId: string, newText: string) => {
    // 获取或初始化该消息的队列
    const queue = streamingQueues.current.get(messageId) || [];
    
    // 将新文本添加到队列中
    const chars = newText.split('');
    queue.push(...chars);
    streamingQueues.current.set(messageId, queue);
    
    // 如果已有定时器在运行，直接返回
    if (streamingTimers.current.has(messageId)) {
      return;
    }
    
    // 启动定时器
    const timer = setInterval(() => {
      const currentQueue = streamingQueues.current.get(messageId) || [];
      
      if (currentQueue.length === 0) {
        // 队列为空，清除定时器
        clearInterval(timer);
        streamingTimers.current.delete(messageId);
        return;
      }
      
      // 从队列中取出字符并显示
      const charsToOutput = currentQueue.splice(0, STREAM_OUTPUT_CHARS_PER_INTERVAL);
      const currentContent = streamingMessages.current.get(messageId) || '';
      const newContent = currentContent + charsToOutput.join('');
      streamingMessages.current.set(messageId, newContent);
      
      // 更新消息显示
      setMessages(prev => {
        const filteredMessages = prev.filter(msg => !msg.id.startsWith('thinking-'));
        const existingMessageIndex = filteredMessages.findIndex(msg => msg.id === messageId);
        
        if (existingMessageIndex >= 0) {
          const updatedMessages = [...filteredMessages];
          updatedMessages[existingMessageIndex] = {
            ...updatedMessages[existingMessageIndex],
            content: newContent,
            isUser: false
          };
          return updatedMessages;
        }
        return prev;
      });
      
      // 更新队列
      streamingQueues.current.set(messageId, currentQueue);
    }, STREAM_OUTPUT_INTERVAL);
    
    streamingTimers.current.set(messageId, timer);
  };

  const leftContainer = () => {
    return (
      <div className={styles.leftContainer}>
        <div>
          <span className={styles.title}>{locale['menu.application.info.testing.title']}</span>
        </div>
        <div className={styles.chatContainer}>

          <div className={styles.createTitle}>{locale['menu.application.info.testing.testApplication']}</div>
          <div className={styles.messageList} ref={messageListRef}>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`${styles.messageItem} ${message.isUser ? styles.userMessage : styles.aiMessage
                  }`}
              >
                <div className={styles.messageMain}>
                  {!message.isUser && (
                    <div className={styles.messageHeader}>
                      <div
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'flex-start',
                        }}
                      >
                        <div className={styles.avatar}>
                          <IconBotAvator />
                        </div>
                        <span className={styles.messageName}>
                          {applicationData.name}
                        </span>
                      </div>
                    </div>
                  )}
                  <div className={styles.messageContent}>
                    <div className={styles.messageText}>
                      {message.content === 'THINKING_ANIMATION' ? (
                        <ThinkingAnimation />
                      ) : (
                        renderMessageContent(message.content)
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className={styles.inputContainer}>
            <div className={styles.inputWrapper}>
              <Button className={styles.leftButton} onClick={handleClean}>
                <img src={IconClean} style={{ width: 24, height: 24 }} />
              </Button>
              <Divider type="vertical" style={{ height: 24, margin: '0' }} />
              <TextArea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                placeholder={locale['menu.application.create.saysomething']}
                autoSize={false}
                onPressEnter={(e) => {
                  if (!e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
              />
              <Button className={styles.sendButton} onClick={handleSendMessage}>
                <IconSend />
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const rightContainer = () => {
    return (
      <div className={styles.rightContainer}>
        <RowComponent className={styles.tabs}>
          <span className={tabIndex === 1 ? styles.selected : ''} onClick={() => { setTabIndex(1) }}>{locale['menu.application.info.testing.buttom.statisticians']}</span>
          <span className={tabIndex === 2 ? styles.selected : ''} onClick={() => { setTabIndex(2) }}>{locale['menu.application.info.testing.buttom.log']}</span>
        </RowComponent>
        {
          tabIndex === 1 ?
            (<div className={styles.contentBox}>
              <RowComponent className={styles.subtitle}>{locale['menu.application.info.testing.content.state']}</RowComponent>
              <RowComponent className={styles.content} style={{ height: 200, paddingTop: 8, paddingBottom: 8 }}>
                {stateList.length > 0 && (
                  <RowComponent className={styles.row}>
                    <div className={styles.collapseItem} style={{ width: '100%' }}>
                      <table className={styles.paramsTable} style={{ width: '100%' }}>
                        <tbody>
                          {stateList[0]?.content.split('\n').map((line, i) => {
                            const [key, value] = line.split(': ');
                            const valueClassName = key === 'llm_total_cost' ? styles.cost :
                              key === 'temperature' ? styles.temperature :
                                key === 'model' ? styles.model : styles.normal;
                            return (
                              <tr key={i}>
                                <td className={styles.paramName}>{key}</td>
                                <td className={`${styles.paramValue} ${valueClassName}`}>{value}</td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </RowComponent>
                )}
              </RowComponent>
              <RowComponent className={styles.subtitle}>{locale['menu.application.info.testing.content.historyState']}</RowComponent>
              <RowComponent className={styles.content} style={{ height: `calc(100vh - 562px)` }}>
                {stateList.map((item, index) => (
                  <RowComponent key={index} className={styles.collapse}>
                    <Collapse>
                      <CollapseItem className={styles.collapseItem} header={item.content} name={item.id}>
                        {item.content.split('\n').map((line, index) => {
                          if (line.includes(': ')) {
                            const [key, value] = line.split(': ');
                            const valueClassName = key === 'llm_total_cost' ? styles.cost :
                              key === 'temperature' ? styles.temperature :
                                key === 'model' ? styles.model : styles.normal;
                            return (
                              <div key={index} style={{ padding: '4px 0' }}>
                                <span className={styles.paramName}>{key}: </span>
                                <span className={valueClassName}>{value}</span>
                              </div>
                            );
                          }
                          return <div key={index}>{line}</div>;
                        })}
                      </CollapseItem>
                    </Collapse>
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <RowComponent className={styles.messageId}>MessageId: {item.id}</RowComponent>
                      <RowComponent className={styles.createdAt}>Time: {new Date(item.created_at).toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                      })}</RowComponent>
                    </div>
                  </RowComponent>
                ))}
              </RowComponent>
            </div>)
            :
            (<div className={styles.logBox + ' ' + styles.contentBox + ' customLogBox'}>
              <RowComponent className={styles.subtitle}>{locale['menu.application.info.testing.content.analyze']}</RowComponent>
              <RowComponent className={styles.content} ref={analyzeContentRef} style={{ height: `calc(100vh - 307px)` }}>
                {analyzeList.map((item, index) => (
                  <RowComponent className={styles.analyzeItem} key={index}>
                    <RowComponent className={styles.analyzeItemOneline}>
                      <Text className={styles.analyzeItemName}>{item.name}</Text>
                      <Text className={styles.analyzeItemTime}>{new Date(item.created_at).toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                      })}</Text>
                    </RowComponent>
                    <RowComponent className={styles.analyzeItemContent}>
                      <div className={styles.markdownWrapper} style={{
                        maxHeight: isExpanded[index] ? 'none' : '60px',
                        overflow: 'hidden'
                      }}>
                        <div ref={(el) => {
                          if (el) {
                            const height = el.getBoundingClientRect().height;
                            const button = el.parentElement?.nextElementSibling as HTMLElement;
                            if (button) {
                              button.style.display = height > 60 ? 'flex' : 'none';
                            }
                          }
                        }}>
                          {renderMessageContent(item.content)}
                        </div>
                      </div>
                      <RowComponent className={styles.buttomRow}>
                        <Button
                          className={styles.showMore}
                          type="text"
                          onClick={() => handleExpand(index)}
                          icon={<IconDown
                            style={{
                              transform: isExpanded[index] ? 'rotate(180deg)' : 'none',
                              transition: 'transform 0.3s'
                            }}
                          />}
                        >
                          {isExpanded[index] ? '收起' : '详情'}
                        </Button>
                      </RowComponent>
                    </RowComponent>
                    <RowComponent className={styles.analyzeItemId}>MessageId: {item.message_id}</RowComponent>
                  </RowComponent>
                ))}
              </RowComponent>
            </div>)
        }
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.customContainer}>
        {leftContainer()}
        <div className={styles.divider} />
        {rightContainer()}
      </div>
    </div>
  );
};

export default AgentCreate;
