/* eslint-disable react/react-in-jsx-scope */
import ButtonComponent from '@arco-design/web-react/es/Button';
import useLocale from '@/utils/useLocale';
import styles from './style/index.module.less';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import { exportApplication } from '@/lib/services/application-service';
import {
  Card,
  Image,
  Input,
  Message,
  Modal,
  Popover,
  Select,
  Form,
  Button,
  Grid,
  InputTag,
  Spin,
  Upload,
  Space,
} from '@arco-design/web-react';
import folderIcon from '@/assets/application/appIcon1.png';
import folderIcon2 from '@/assets/application/appIcon2.png';
import folderIcon3 from '@/assets/application/appIcon3.png';
import folderIcon4 from '@/assets/application/appIcon4.png';
import uploadSvg from '@/assets/application/upload.png';
import TopIcon from '@/assets/top.svg';
import zipIcon from '@/assets/application/zip.png';
import { useEffect, useState, useRef, useCallback } from 'react';
import Text from '@arco-design/web-react/es/Typography/text';
import {
  IconClose,
  IconMore,
  IconPlus,
  IconExclamationCircle,
  IconArrowUp,
} from '@arco-design/web-react/icon';
import IconSearch from '@/assets/application/search.svg';
import IconScreen from '@/assets/application/screen.svg';
import { useNavigate } from 'react-router-dom';
import {
  getApplicationList,
  deleteApplication,
  updateApplication,
  getApplicationLabels,
  importApplication,
  getApplicationDetail,
} from '@/lib/services/application-service';
import NotATIcon from '@/assets/application/NotAT.svg';
import JSZip from 'jszip';
import { useDispatch, useSelector } from 'react-redux';
import { GlobalState } from '@/store/index';
import AgentTeamIcon from '@/assets/application/IconAgentTeam.svg';

interface ApplicationClass {
  id: string;
  name: string;
  description: string;
  type: string;
  provider: string;
  channel: string;
  iconUrl: string | null;
  installed: boolean;
  isPublished: boolean;
  disabled: boolean;
  createdDateTime: string;
  updatedDateTime: string;
  popoverVisible?: boolean;
  labels: string[];
  agent: any;
  openapi_auth_options: any;
}

const FormItem = Form.Item;

function ApplicationList() {
  const dispatch = useDispatch();
  const applicationDetailMenuName = useSelector((state: GlobalState) => state.applicationDetailMenuName);
  const navigate = useNavigate();
  const locale = useLocale();
  const [listData, setListData] = useState<ApplicationClass[]>([]);
  const Option = Select.Option;
  const [uploadVisible, setUploadVisible] = useState(false);
  const [chooseVisible, setChooseVisible] = useState(false);
  const [selectedOption, setSelectedOption] = useState<
    'Replace' | 'Reserve' | null
  >(null);
  const [uploadFile, setUploadFile] = useState<{
    file: File;
    name: string;
    size: number;
  } | null>(null);
  const [existingApp, setExistingApp] = useState<ApplicationClass | null>(null);

  // 添加分页相关状态
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchLabel, setSearchLabel] = useState<string>('');
  const [labelOptions, setLabelOptions] = useState<
    { value: string; label: string }[]
  >([]);
  const pageSize = 16;
  const cardBoxRef = useRef<HTMLDivElement>(null);

  const folderIconList = [folderIcon, folderIcon2, folderIcon3, folderIcon4];

  const [count, setCount] = useState<number>(0);
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [hoveredCardId, setHoveredCardId] = useState<string | null>(null);

  // 处理搜索输入变化
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
    setHasMore(true);
    setListData([]);
  };

  // 处理标签变化
  const handleSearchLabelChange = (value: string) => {
    setSearchLabel(value);
    setCurrentPage(1);
    setHasMore(true);
    setListData([]);
  };

  // 加载更多数据
  const loadMoreData = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    try {
      const nextPage = currentPage + 1;
      const params: any = {
        Pager: {
          Page: nextPage,
          Size: pageSize,
        },
      };

      if (searchValue) {
        params.ApplicationName = searchValue;
      }
      if (searchLabel) {
        params.label = searchLabel;
      }

      const response = await getApplicationList(params);
      console.log(response);

      if (response.items && response.items.length > 0) {
        const newData: ApplicationClass[] = response.items.map((item) => ({
          ...item,
        }));

        // 合并新旧数据
        setListData((prevData) => [...prevData, ...newData]);
        setCurrentPage(nextPage);

        if (response.items.length < pageSize) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      Message.error({
        content: locale['menu.application.opreate.errMsg'],
      });
      console.error('加载更多应用失败:', error);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    loading,
    hasMore,
    pageSize,
    locale,
    searchValue,
    searchLabel,
  ]);

  useEffect(() => {
    getApplicationListData();
    getApplicationLabelsData();
  }, [searchValue, searchLabel, pageSize, locale]);

  const getApplicationListData = useCallback(async () => {
    setLoading(true);
    try {
      const params: any = {
        Pager: {
          Page: 1,
          Size: pageSize,
        },
      };

      if (searchValue) {
        params.ApplicationName = searchValue;
      }
      if (searchLabel) {
        params.label = searchLabel;
      }
      const response = await getApplicationList(params);

      setCount(response.count);

      const formattedData: ApplicationClass[] = response.items.map((item) => ({
        ...item,
      }));

      setListData(formattedData);
      setCurrentPage(1);

      if (response.items.length < pageSize) {
        setHasMore(false);
      }
    } catch (error) {
      Message.error({
        content: locale['menu.application.opreate.errMsg'],
      });
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [pageSize, searchValue, locale, searchLabel]);

  const getApplicationLabelsData = useCallback(async () => {
    try {
      const response = await getApplicationLabels();
      if (response && Array.isArray(response.data)) {
        const formattedLabels = response.data.map((label) => ({
          value: label,
          label: label,
        }));
        setLabelOptions(formattedLabels);
      }
    } catch (error) {
      console.error('获取标签列表失败:', error);
      Message.error({
        content: '获取标签列表失败',
      });
    }
  }, []);

  // 滚动监听函数
  const handleScroll = useCallback(() => {
    if (!cardBoxRef.current) return;

    const container = cardBoxRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;

    // 处理回到顶部按钮显示
    setShowBackToTop(scrollTop > 300);

    // 处理加载更多数据
    if (!loading && hasMore && scrollHeight - scrollTop - clientHeight < 150) {
      loadMoreData();
    }
  }, [loading, hasMore, loadMoreData]);

  // 独立监测容器和卡片位置关系
  useEffect(() => {
    if (!cardBoxRef.current || loading || !hasMore) return;

    const checkCardPosition = () => {
      const container = cardBoxRef.current;
      if (!container) return;

      const lastCard = container.querySelector(
        `.${styles.customCard}:last-child`
      );
      if (lastCard) {
        const containerBottom = container.getBoundingClientRect().bottom;
        const lastCardBottom = lastCard.getBoundingClientRect().bottom;
        const distanceToBottom = containerBottom - lastCardBottom;

        // 如果最后一个卡片到容器底部的距离大于100px
        if (distanceToBottom > 100) {
          loadMoreData();
        }
      }
    };

    // 创建 ResizeObserver 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      checkCardPosition();
    });

    // 监听容器大小变化
    resizeObserver.observe(cardBoxRef.current);

    // 初始检查
    checkCardPosition();

    return () => {
      resizeObserver.disconnect();
    };
  }, [loading, hasMore, loadMoreData, listData.length]);

  // 回到顶部
  const scrollToTop = useCallback(() => {
    if (!cardBoxRef.current) return;

    setShowBackToTop(false);

    cardBoxRef.current.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  // 添加滚动监听
  useEffect(() => {
    const container = cardBoxRef.current;
    if (!container) return;

    // 使用防抖处理滚动事件，避免频繁触发
    let scrollTimeout: NodeJS.Timeout;

    const scrollListener = () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      scrollTimeout = setTimeout(() => {
        handleScroll();
      }, 50);
    };

    container.addEventListener('scroll', scrollListener);

    return () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      container.removeEventListener('scroll', scrollListener);
    };
  }, [handleScroll]);

  const comfirmDel = (event, application) => {
    event.stopPropagation();
    console.log('event', event);
    const title = locale['menu.application.opreate.del'] + application.name;
    const content = locale['menu.application.delete.content'];
    Modal.confirm({
      style: {
        borderRadius: 16,
        padding: 24,
      },
      title: <div style={{ textAlign: 'left' }}>{title}</div>,
      content: content,
      cancelText: locale['cancelBut'],
      okText: locale['deleteBut'],
      okButtonProps: {
        status: 'danger',
      },
      icon: null,
      footer: (
        <div style={{ textAlign: 'right' }}>
          <Button
            style={{
              height: 40,
              width: 76,
              borderRadius: 8,
              backgroundColor: '#FFFFFF',
              border: '1px solid rgba(0, 0, 0, 0.08)',
            }}
            onClick={() => Modal.destroyAll()}
          >
            {locale['cancelBut']}
          </Button>
          <Button
            status="danger"
            style={{
              height: 40,
              width: 76,
              marginLeft: 8,
              borderRadius: 8,
              color: '#FFFFFF',
              backgroundColor: '#D54941',
            }}
            onClick={() => {
              return new Promise<void>((resolve, reject) => {
                deleteApplication(application.id)
                  .then(() => {
                    resolve();
                    Message.success({
                      content: locale['menu.application.opreate.okMsg'],
                    });
                    // 删除成功后刷新列表
                    getApplicationListData();
                    Modal.destroyAll();
                  })
                  .catch((e) => {
                    reject(e);
                    Message.error({
                      content: locale['menu.application.opreate.errMsg'],
                    });
                    console.error(e);
                  });
              });
            }}
          >
            {locale['deleteBut']}
          </Button>
        </div>
      ),
    });
  };

  const toggleDisabled = async (event, data: ApplicationClass) => {
    event.stopPropagation();
    const updateData = {
      ...data,
      disabled: !data.disabled,
    };
    try {
      await updateApplication(data.id, updateData);
      Message.success({
        content: locale['menu.application.opreate.okMsg'],
      });
      // 更新成功后刷新列表
      getApplicationListData();
    } catch (error) {
      Message.error({
        content: locale['menu.application.opreate.errMsg'],
      });
      console.error(error);
    }
  };

  const updateBreadcrumbData = (newBreadcrumb) => {
    dispatch({
      type: 'update-breadcrumb-menu-name',
      payload: { breadcrumbMenuName: newBreadcrumb },
    });
  };

  const gotoCreate = () => {

    // 更新面包屑数据，使用详情页路径和卡片名称
    const breadcrumbData = new Map([[applicationDetailMenuName, 'New AgentTeam']]);
    updateBreadcrumbData(breadcrumbData);

    navigate('/application/info', { state: { id: null } });
  };

  const gotoInfoPage = (item: ApplicationClass) => {
    // 更新面包屑数据，使用详情页路径和卡片名称
    const breadcrumbData = new Map([[applicationDetailMenuName, item.name]]);
    updateBreadcrumbData(breadcrumbData);

    navigate('/application/info', { state: { id: item.id } });
  };

  const handleImport = () => {
    setUploadVisible(true);
  };

  const handleUploadCancel = () => {
    setUploadVisible(false);
  };

  const handleBeforeUpload = async (file: File) => {
    // 检查文件类型
    const isZip =
      file.type === 'application/zip' ||
      file.type === 'application/x-zip-compressed';

    if (!isZip) {
      Message.error({
        content:
          locale['menu.application.upload.type.error'],
      });
      return false;
    }

    // 检查文件大小
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      Message.error({
        content:
          locale['menu.application.upload.size.error'],
      });
      return false;
    }

    // 读取zip文件内容
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const zip = new JSZip();
        const zipContent = await zip.loadAsync(e.target?.result as ArrayBuffer);

        // 查找json文件
        const jsonFile = Object.values(zipContent.files).find((file) =>
          file.name.endsWith('.json')
        );

        if (!jsonFile) {
          Message.error({
            content: '未找到JSON配置文件',
          });
          return;
        }

        // 读取json文件内容
        const jsonContent = await jsonFile.async('text');
        const appConfig = JSON.parse(jsonContent);

        if (!appConfig.appInfo || !appConfig.appInfo.id) {
          Message.error({
            content: '配置文件格式错误',
          });
          return;
        }

        // 检查应用是否存在
        try {
          const response = await getApplicationDetail(appConfig.appInfo.id);
          if (response) {
            // 应用已存在，显示选择弹窗
            setExistingApp(response);
            setUploadFile({
              file,
              name: file.name,
              size: file.size,
            });
            setUploadVisible(false);
            setChooseVisible(true);
          } else {
            // 应用不存在，直接导入
            try {
              const response = await importApplication({
                file,
                import_type: 'Default',
              });
              const res = response.data;
              if (res.Success) {
                Message.success({
                  content: res.Message || '导入成功',
                });
                getApplicationListData();
                setUploadVisible(false);
              } else {
                Message.error({
                  content: res.Message || '导入失败',
                });
              }
            } catch (err) {
              console.error('导入失败:', err);
              Message.error({
                content: '导入失败',
              });
            }
          }
        } catch (error) {
          console.error('检查应用是否存在失败:', error);
          Message.error({
            content: '检查应用是否存在失败',
          });
        }
      } catch (error) {
        console.error('处理文件失败:', error);
        Message.error({
          content: '处理文件失败',
        });
      }
    };

    reader.readAsArrayBuffer(file);
    return false;
  };

  const handleOptionSelect = (option: 'Replace' | 'Reserve') => {
    setSelectedOption(option);
  };

  const handleChooseCancel = () => {
    setChooseVisible(false);
    setSelectedOption(null);
    setUploadFile(null);
    setExistingApp(null);
  };

  const handleChooseConfirm = () => {
    if (!selectedOption || !uploadFile) {
      Message.error('请选择处理方式');
      return;
    }

    const formData = {
      file: uploadFile.file,
      import_type: selectedOption,
    };

    importApplication(formData)
      .then((response) => {
        const res = response.data;
        if (res.Success) {
          Message.success({
            content: res.Message || '导入成功',
          });
          // 更新应用列表
          getApplicationListData();
          // 关闭弹窗
          handleChooseCancel();
        } else {
          Message.error({
            content: res.Message || '导入失败',
          });
        }
      })
      .catch((err) => {
        console.error('导入失败:', err);
        Message.error({
          content: '导入失败',
        });
      });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const handleExport = async (item: ApplicationClass) => {
    try {
      setLoading(true);

      const res = await exportApplication(item.id);
      
      // 处理文件下载
      if (res && res.data) {
        // 从响应头中获取文件名
        const contentDisposition = res.headers['content-disposition'];
        let filename = 'agent_team_export.zip';
        
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename\*?=['"]?([^'";\r\n]+)['"]?/);
          if (filenameMatch && filenameMatch[1]) {
            // 处理UTF-8编码的文件名
            filename = decodeURIComponent(filenameMatch[1]);
          }
        }

        // 创建Blob对象 (res.data 现在是 ArrayBuffer)
        const blob = new Blob([res.data], { type: 'application/zip' });
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        
        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }

      Message.success(locale['menu.application.opreate.export.success']);
    }
    catch (error) {
      console.error('Export error:', error);
      Message.error(locale['menu.application.opreate.export.errMsg']);
    } finally {
      setLoading(false);
    }
  };

  const cardDom = () => {
    return (
      <div className={styles.customCardBox} ref={cardBoxRef}>
        {listData.map((item, i) => {
          return (
            <Card
              key={item.id}
              className={[styles.customCard]}
              hoverable
              onClick={() => {
                gotoInfoPage(item);
              }}
              onMouseEnter={() => setHoveredCardId(item.id)}
              onMouseLeave={() => setHoveredCardId(null)}
            >
              <div className={styles.agentTeamInfo}>
                <div className={styles.cardContent}>
                  <Space direction="vertical" size={8}>
                    <Space className={styles.nameWrapper} size={12}>
                      <span className={styles.icon}>
                        {item.iconUrl ? (
                          <Image
                            src={item.iconUrl}
                            className={styles.folderIcon}
                          />
                        ) : (
                          <AgentTeamIcon className={styles.folderIcon} />
                        )}
                      </span>
                      <Text className={styles.name}>{item.name}</Text>
                    </Space>

                    <div className={styles.description}>
                      {item.description}
                    </div>

                    {item.labels && item.labels.length > 0 && (
                      <div className={styles.labelWrapper}>
                        {item.labels.slice(0, 3).map((label, index) => (
                          <Text key={index} className={styles.profileTag}>
                            {label}
                          </Text>
                        ))}
                      </div>
                    )}
                  </Space>
                </div>

                <div className={styles.cardFooter}>
                  <div className={styles.metaInfo}>
                    <div className={styles.statusInfo}>
                      <Text className={styles.metaText}>
                        @{item.provider || 'AI4C'}
                      </Text>
                      <Text className={styles.metaText}>
                        |
                      </Text>
                      <div className={styles.createTime}>
                        <Text className={styles.metaText}>
                          {item.updatedDateTime && item.updatedDateTime !== item.createdDateTime
                            ? `更新时间：${
                                new Date(item.updatedDateTime)
                                  .toLocaleDateString('zh-CN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                  })
                                  .replace(/\//g, '/')
                              }`
                            : `创建时间：${
                                new Date(item.createdDateTime)
                                  .toLocaleDateString('zh-CN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                  })
                                  .replace(/\//g, '/')
                              }`
                          }
                        </Text>
                      </div>
                    </div>
                    <div
                      className={styles.statusWrapper}
                      style={
                        item.disabled
                          ? { 
                              backgroundColor: 'rgba(245, 63, 63, 0.04)',
                              opacity: hoveredCardId === item.id || item.popoverVisible ? 0 : 1,
                              visibility: hoveredCardId === item.id || item.popoverVisible ? 'hidden' : 'visible',
                              transition: 'all 0.3s ease',
                            }
                          : { 
                              backgroundColor: 'rgba(0, 180, 42, 0.04)',
                              opacity: hoveredCardId === item.id || item.popoverVisible ? 0 : 1,
                              visibility: hoveredCardId === item.id || item.popoverVisible ? 'hidden' : 'visible',
                              transition: 'all 0.3s ease',
                            }
                      }
                    >
                      <Text
                        className={styles.statusTag}
                        style={
                          item.disabled
                            ? { color: '#D54941' }
                            : { color: '#00B42A' }
                        }
                      >
                        {item.disabled
                          ? locale['menu.application.status.disabled']
                          : locale['menu.application.status.enabled']}
                      </Text>
                    </div>
                  </div>
                  <Popover
                    trigger="click"
                    position="right"
                    className={styles.customPopover}
                    onVisibleChange={(visible) => {
                      setListData((prevData) =>
                        prevData.map((application) =>
                          application.id === item.id
                            ? { ...application, popoverVisible: visible }
                            : application
                        )
                      );
                    }}
                    content={
                      <Space
                        direction="vertical"
                        size="mini"
                        className={styles.popoverContent}
                      >
                        <Button
                          className={`${styles.actionBtn} ${item.disabled
                            ? styles.enableBtn
                            : styles.disableBtn
                            }`}
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleDisabled(e, item);
                          }}
                        >
                          {item.disabled
                            ? locale['menu.application.opreate.enable'] ||
                            '启用'
                            : locale['menu.application.opreate.disable'] ||
                            '禁用'}
                        </Button>
                        <Button
                          className={`${styles.actionBtn} ${styles.deleteBtn}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleExport(item);
                          }}
                        >
                          {locale['menu.application.opreate.export']}
                        </Button>
                        <Button
                          className={`${styles.actionBtn} ${styles.deleteBtn}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            comfirmDel(e, item);
                          }}
                        >
                          {locale['menu.application.opreate.del']}
                        </Button>
                      </Space>
                    }
                  >
                    <Button
                      className={styles.triggerBtn}
                      onClick={(e) => e.stopPropagation()}
                      style={{ 
                        opacity: hoveredCardId === item.id || item.popoverVisible ? 1 : 0,
                        visibility: hoveredCardId === item.id || item.popoverVisible ? 'visible' : 'hidden',
                        transition: 'all 0.3s ease',
                        position: 'absolute',
                        right: 0,
                        bottom: 0,
                        zIndex: 99,
                      }}
                    >
                      <IconMore style={{ fontSize: 16, color: '#666' }} />
                    </Button>
                  </Popover>
                </div>
              </div>
            </Card>
          );
        })}
        {loading && (
          <div className={styles.loadingContainer}>
            <Text className={styles.loadingText}>加载中...</Text>
          </div>
        )}
        {!loading && listData.length === 0 && (
          <div className={styles.emptyContainer}>
            <NotATIcon />
            <Text className={styles.emptyText}>未找到Agent Team</Text>
          </div>
        )}

        {/* 底部空白区域 */}
        <div style={{ width: '100%', gridColumn: '1 / -1' }}>
          {listData.length < count ? (
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(4, 1fr)',
                gap: '16px',
                width: '100%',
                padding: '0 0 24px 0',
              }}
            >
              {[1, 2, 3, 4].map((item) => (
                <Card
                  key={item}
                  className={[styles.customCard]}
                  hoverable
                  style={{ opacity: 0.5 }}
                >
                  <div className={styles.agentTeamInfo}>
                    <div className={styles.cardContent}>
                      <Space direction="vertical" size={8}>
                        <Space className={styles.nameWrapper} size={12}>
                          <span className={styles.icon}>
                            <div
                              className={styles.folderIcon}
                              style={{
                                background: 'rgba(0, 0, 0, 0.04)',
                                borderRadius: '4px',
                              }}
                            ></div>
                          </span>
                          <div
                            className={styles.name}
                            style={{
                              background: 'rgba(0, 0, 0, 0.04)',
                              height: '24px',
                              width: '70%',
                            }}
                          ></div>
                        </Space>

                        <div
                          className={styles.description}
                          style={{ background: 'rgba(0, 0, 0, 0.04)', height: '24px' }}
                        ></div>

                        <div className={styles.labelWrapper}>
                          <div
                            className={styles.profileTag}
                            style={{
                              background: 'rgba(0, 0, 0, 0.04)',
                              width: '40px',
                            }}
                          ></div>
                          <div
                            className={styles.profileTag}
                            style={{
                              background: 'rgba(0, 0, 0, 0.04)',
                              width: '40px',
                            }}
                          ></div>
                        </div>
                      </Space>
                    </div>

                    <div className={styles.cardFooter}>
                      <div className={styles.metaInfo}>
                        <div
                          className={styles.metaText}
                          style={{
                            background: 'rgba(0, 0, 0, 0.04)',
                            height: '16px',
                            width: '60%',
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <div
              style={{ height: '100px', width: '100%', gridColumn: '1 / -1' }}
            >
              {listData.length < count ? '加载中...' : ''}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <div className={styles.customContainer}>
        <RowComponent
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingBottom: 16,
            borderBottom: '1px solid #f5f5f5',
          }}
        >
          <RowComponent>
            <ButtonComponent
              onClick={() => {
                gotoCreate();
              }}
              className={[styles.blueBut, styles.butFont]}
              style={{ marginRight: 8, width: 150 }}
              htmlType="submit"
            >
              {locale['menu.application.createApp']}
            </ButtonComponent>
            <ButtonComponent
              onClick={handleImport}
              className={styles.importApp}
            >
              {locale['menu.application.import']}
            </ButtonComponent>
          </RowComponent>
          <RowComponent className={styles.rowEndCenter}>
            <Text className={styles.countAppText}>
              {locale['menu.application.appListCount']?.replace(
                '{count}',
                count
              )}
            </Text>
            <Input
              className={styles.searchBox}
              prefix={<IconSearch />}
              placeholder={
                locale['menu.application.search.placeholder'] || '搜索Agent Team名称'
              }
              value={searchValue}
              onChange={handleSearchChange}
              allowClear
            />
            <Select
              placeholder={locale['menu.application.agent.search.tags']}
              className={styles.selectBox}
              prefix={<IconScreen />}
              value={searchLabel || undefined}
              onChange={handleSearchLabelChange}
              allowClear
              triggerProps={{
                autoAlignPopupWidth: false,
                position: 'bl',
                className: 'agent-team-select-popup',
              }}
            >
              {labelOptions.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </RowComponent>
        </RowComponent>
        {cardDom()}
      </div>
      <div
        className={`${styles.backToTop} ${!showBackToTop ? styles.hidden : ''}`}
        onClick={scrollToTop}
      >
        <TopIcon />
        <span>返回顶部</span>
      </div>
      <Modal
        visible={uploadVisible}
        onCancel={handleUploadCancel}
        footer={null}
        maskClosable={false}
        className={styles.uploadModal}
        style={{ width: '640px', height: '272px', borderRadius: '8px' }}
      >
        <div className={styles.title}>{locale['menu.application.import']}</div>

        <div className={styles.uploadContainer}>
          <Upload
            drag
            accept=".zip"
            showUploadList={false}
            beforeUpload={handleBeforeUpload}
          >
            <div className={styles.uploadContent}>
              <div className={styles.uploadTitle}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '8px',
                  }}
                >
                  <img
                    src={uploadSvg}
                    className={styles.uploadIcon}
                    style={{ display: 'block' }}
                  />
                  <span>点击/拖拽压缩包</span>
                </div>
              </div>
              <div className={styles.uploadSubtitle}>
                {locale['menu.application.upload.type.support']}
              </div>
            </div>
          </Upload>
        </div>
      </Modal>

      <Modal
        visible={chooseVisible}
        onCancel={handleChooseCancel}
        footer={null}
        maskClosable={false}
        className={styles.chooseModal}
        style={{ width: '640px', height: '508px', borderRadius: '8px' }}
      >
        <div className={styles.title}>{locale['menu.application.import']}</div>

        <div className={styles.warningBox}>
          <div className={styles.warningIcon}>
            <IconExclamationCircle style={{ color: '#4455F2', fontSize: 16 }} />
          </div>
          <div className={styles.warningText}>存在同名应用</div>
        </div>

        <div className={styles.tagetTitle}>目标</div>

        <div className={styles.fileInfo}>
          <img src={zipIcon} className={styles.zipIcon} />
          <div className={styles.fileDetails}>
            <div className={styles.fileName}>
              {uploadFile?.name || '压缩包.zip'}
            </div>
            <div className={styles.fileSize}>
              {uploadFile ? formatFileSize(uploadFile.size) : ''}
            </div>
          </div>
        </div>

        <div className={styles.appInfo}>
          <div className={styles.appContent}>
            <img src={folderIcon} className={styles.appIcon} />
            <div className={styles.appDetails}>
              <div className={styles.appTitle}>
                <span className={styles.appName}>{existingApp?.name}</span>
                <div className={styles.tags}>
                  <span className={styles.tag}>{existingApp?.type}</span>
                  <span className={styles.tag}>{existingApp?.channel}</span>
                </div>
              </div>
              <div className={styles.appMeta}>
                @创建人｜创建时间：{existingApp?.createdDateTime}
              </div>
            </div>
          </div>
        </div>

        <div className={styles.optionsTitle}>导入方式</div>

        <div className={styles.optionsContainer}>
          <div
            className={`${styles.optionBox} ${selectedOption === 'Replace' ? styles.selected : ''
              }`}
            onClick={() => handleOptionSelect('Replace')}
          >
            <div className={styles.optionRadio}>
              <div
                className={
                  selectedOption === 'Replace'
                    ? styles.radioSelected
                    : styles.radioNormal
                }
              />
            </div>
            <div className={styles.optionContent}>
              <div className={styles.optionTitle}>替换</div>
              <div className={styles.optionDesc}>覆盖现有的同名应用</div>
            </div>
          </div>

          <div
            className={`${styles.optionBox} ${selectedOption === 'Reserve' ? styles.selected : ''
              }`}
            onClick={() => handleOptionSelect('Reserve')}
          >
            <div className={styles.optionRadio}>
              <div
                className={
                  selectedOption === 'Reserve'
                    ? styles.radioSelected
                    : styles.radioNormal
                }
              />
            </div>
            <div className={styles.optionContent}>
              <div className={styles.optionTitle}>保留两者</div>
              <div className={styles.optionDesc}>创建新的应用副本</div>
            </div>
          </div>
        </div>

        <div className={styles.footer}>
          <RowComponent className={styles.operateButGroup}>
            <ButtonComponent
              type="secondary"
              style={{ marginRight: 8 }}
              className={[styles.cancelBut, styles.but]}
              onClick={handleChooseCancel}
            >
              <Text className={styles.text}>
                {locale['menu.application.template.setting.operate.cancel']}
              </Text>
            </ButtonComponent>
            <ButtonComponent
              type="primary"
              className={[styles.createBut, styles.but]}
              onClick={handleChooseConfirm}
              loading={loading}
            >
              <Text className={styles.text}>
                {locale['menu.application.opreate.save']}
              </Text>
            </ButtonComponent>
          </RowComponent>
        </div>
      </Modal>
    </>
  );
}

export default ApplicationList;

