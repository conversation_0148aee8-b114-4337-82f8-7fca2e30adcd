import React, { useState, useEffect } from 'react';
import { Input, Select, Space, Typography, Table, Button, Spin } from '@arco-design/web-react';
import { IconRight } from '@arco-design/web-react/icon';
import IconSort from '@/assets/usage/IconSort.svg';
import IconSearch from '@/assets/usage/IconSearch.svg';
import AppIcon from '@/assets/usage/appImg.svg';
import styles from './style/index.module.less';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { GlobalState } from '@/store/index';
import useLocale from '@/utils/useLocale';
import IconEmptyUsage from '@/assets/usage/IconEmptyUsage.svg';
import IconBuildingUsage from '@/assets/IconBuilding.svg';
const { Text, Title } = Typography;

// 定义周期类型
type PeriodType = 'week' | 'month' | 'quarter';

// Mock data
// const mockApps = Array.from({ length: 12 }, (_, index) => {
//   const usagePercentage =
//     index < 1
//       ? 80
//       : index < 3
//       ? 60
//       : index < 5
//       ? 40
//       : index < 7
//       ? 20
//       : index < 9
//       ? 10
//       : 5;

//   return {
//     id: index + 1,
//     name: 'Agent Team',
//     creator: '@创建人',
//     createtime: '2024/10/25',
//     percentage: usagePercentage,
//     tokens: 1630,
//     usage: '1',
//   };
// });
const mockApps = [];

// Main Component
const UsageList = () => {
  const [sortType, setSortType] = useState('升序');
  const [searchValue, setSearchValue] = useState('');
  const [filteredData, setFilteredData] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedApp, setSelectedApp] = useState(null);
  const [period, setPeriod] = useState<PeriodType>('quarter');
  const [loading, setLoading] = useState(false);
  const [building, setBuilding] = useState(true); // 新增建设中状态

  const usageDetailMenuName = useSelector(
    (state: GlobalState) => state.usageDetailMenuName
  );
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const locale = useLocale();

  // 表格列定义
  const columns = [
    {
      title: 'Agent Team',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      render: (name) => (
        <div className={styles.nameColumn}>
          <div className={styles.iconWrapper}>
            <AppIcon style={{ width: 48, height: 48 }} />
          </div>
          <Text className={styles.nameText}>{name}</Text>
        </div>
      )
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      key: 'creator',
      width: 200,
      render: (creator) => (
        <Text className={styles.creatorText}>{creator}</Text>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createtime',
      key: 'createtime',
      width: 200,
      render: (createtime) => (
        <Text className={styles.timeText}>{createtime}</Text>
      )
    },
    {
      title: '使用量',
      dataIndex: 'usage',
      key: 'usage',
      width: 150,
      render: (usage) => (
        <Text className={styles.usageText}>{usage}</Text>
      )
    },
    {
      title: '费用（¥）',
      dataIndex: 'tokens',
      key: 'tokens',
      width: 150,
      render: (tokens) => (
        <Text className={styles.tokenText}>{tokens}</Text>
      )
    },
    {
      title: '',
      key: 'arrow',
      width: 50,
      render: () => (
        <div className={styles.arrowColumn}>
          <IconRight style={{ color: '#adadad', fontSize: 16 }} />
        </div>
      )
    }
  ];

  useEffect(() => {
    let result = [...mockApps];

    // Filter by search
    if (searchValue) {
      result = result.filter(
        (app) =>
          app.name.includes(searchValue) || app.creator.includes(searchValue)
      );
    }

    // Sort
    result.sort((a, b) => {
      return sortType === '升序'
        ? a.percentage - b.percentage
        : b.percentage - a.percentage;
    });

    setFilteredData(result);
  }, [searchValue, sortType]);

  //   const updateBreadcrumbData = (newBreadcrumb: Map<string, string>) => {
  //     dispatch({
  //       type: 'update-breadcrumb-menu-name',
  //       payload: { breadcrumbMenuName: Object.fromEntries(newBreadcrumb) },
  //     });
  //   };

  const gotoList = (item) => {
    // const breadcrumbData = new Map<string, string>([
    //   [usageDetailMenuName, item.name],
    // ]);
    // updateBreadcrumbData(breadcrumbData);
    navigate('/usage/detail');
  };

  // 处理表格行点击事件
  const handleRowClick = (record) => {
    gotoList(record);
  };

  // 处理搜索输入变化
  const handleSearchChange = (value) => {
    setSearchValue(value);
  };

  // 处理周期选择变化
  const handlePeriodChange = (value: PeriodType) => {
    setPeriod(value);
    // 这里可以添加根据周期筛选数据的逻辑
  };

  const renderContent = () => {
    if (loading && filteredData.length === 0) {
      return (
        <div className={styles.loadingContainer}>
          <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{ padding: 24 }}>
              <Spin tip={locale['menu.loading'] || '加载中...'} />
            </div>
          </Space>
        </div>
      );
    }

    // 建设中状态
    if (building && filteredData.length === 0 && !searchValue) {
      return (
        <div className={styles.buildingContainer}>
          <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
            <IconBuildingUsage style={{ width: 80, height: 80 }} />
            <Text className={styles.buildingText}>
              努力建设中，马上就来~
            </Text>
          </Space>
        </div>
      );
    }

    // 搜索无结果状态
    if (filteredData.length === 0) {
      return (
        <div className={styles.emptyContainer}>
          <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
            <IconEmptyUsage style={{ width: 80, height: 80 }} />
            <Text className={styles.emptyText}>
              {searchValue ? '未找到匹配的数据' : '暂无数据'}
            </Text>
          </Space>
        </div>
      );
    }

    return (
      <Table
        columns={columns}
        data={filteredData}
        className={styles.usageTable}
        rowKey="id"
        pagination={false}
        onRow={(record) => ({
          onClick: () => handleRowClick(record),
          style: { cursor: 'pointer' }
        })}
      />
    );
  };

  return (
    <div className={styles.usageStatistics}>
      <Title className={styles.title} style={{ fontSize: 20, marginBottom: 8 }}>
        用量列表
      </Title>
      <div className={styles.usageHeader}>
        <Space size={8}>
          <Input
            prefix={<IconSearch />}
            placeholder="AI搜索..."
            className={styles.searchInput}
            value={searchValue}
            onChange={handleSearchChange}
            allowClear
          />
          <Select
            placeholder="按创建时间排序"
            className={styles.sortSelect}
            value={sortType}
            onChange={setSortType}
            prefix={<IconSort />}
          >
            <Select.Option value="升序">升序</Select.Option>
            <Select.Option value="降序">降序</Select.Option>
          </Select>
          <Text className={styles.appCount}>
            共 {filteredData.length} 个数据
          </Text>
        </Space>
        <div className={styles.periodControls}>
          <Button.Group>
            <Button
              type={period === 'week' ? 'primary' : 'default'}
              onClick={() => handlePeriodChange('week')}
            >
              周
            </Button>
            <Button
              type={period === 'month' ? 'primary' : 'default'}
              onClick={() => handlePeriodChange('month')}
            >
              月
            </Button>
            <Button
              type={period === 'quarter' ? 'primary' : 'default'}
              onClick={() => handlePeriodChange('quarter')}
            >
              季度
            </Button>
          </Button.Group>
        </div>
      </div>
      <div className={styles.usageContent}>
        {renderContent()}
      </div>
    </div>
  );
};

export default UsageList;
