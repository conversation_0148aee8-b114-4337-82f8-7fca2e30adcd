import { useState, useEffect, useRef } from 'react';
import {
  Card,
  Grid,
  Typography,
  Input,
  Select,
  InputNumber,
  Button,
  Space,
  Tag,
  Switch,
  Form,
  Message,
} from '@arco-design/web-react';
import IconModel from '@/assets/model/ModelIocn.svg';
import IconTagClose from '@/assets/model/IconTagClose.svg';
import styles from './style/index.module.less';
import {
  getLlmTypeList,
  getLlmProviderList,
  createLlmModelConfig,
  getLlmModelKeys,
  getLlmProviderModelListById,
  getLlmModelKeysByProvider,
} from '@/lib/services/llm-model-service';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { GlobalState } from '@/store/index';
import _ from 'lodash';

const { Row, Col } = Grid;
const { Title, Text } = Typography;

// 创建一个不显示必填图标的 Form.Item
const FormItemWithoutRequiredMark = (props) => {
  const { children, ...rest } = props;
  return (
    <Form.Item
      {...rest}
      requiredSymbol={false} // 禁用 Arco Design 默认的必填标记
    >
      {children}
    </Form.Item>
  );
};

// 自定义的必填图标组件
const RequiredIcon = () => <span className={styles.requiredIcon}>*</span>;

// 自定义表单项标签组件
const CustomLabel = ({ label, required }) => {
  return (
    <Space>
      <span>{label}</span>
      {required && <RequiredIcon />}
    </Space>
  );
};

const ConfigDetaillPage = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [tags, setTags] = useState([]);
  const [modelTypes, setModelTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [providers, setProviders] = useState([]);
  const [providersLoading, setProvidersLoading] = useState(false);
  const [modelKeys, setModelKeys] = useState([]);
  const [modelKeysLoading, setModelKeysLoading] = useState(false);
  const [providerModels, setProviderModels] = useState([]);
  const [providerModelsLoading, setProviderModelsLoading] = useState(false);
  const selectedModelConfig = useSelector(
    (state: GlobalState) => state.selectedModelConfig
  );
  const [isEditing, setIsEditing] = useState(false);
  const [formChanged, setFormChanged] = useState(false);// 添加状态跟踪表单是否有变化
  const [originalTags, setOriginalTags] = useState([]);// 保存原始标签数据以便比较
  const originalFormValuesRef = useRef({});  // 保存原始表单值的引用
  const [userEditing, setUserEditing] = useState(false);// 添加一个状态变量，用于跟踪用户是否正在手动编辑表单
  const [selectedModelType, setSelectedModelType] = useState(null); // 添加状态跟踪选中的模型类型

  // 处理表单值变化
  const handleFormValuesChange = (changedValues, allValues) => {
    if (isEditing) {
      // 当用户手动修改表单时，标记为正在编辑状态
      setUserEditing(true);

      // 当供应商变化时，获取该供应商的模型列表
      if ('provider' in changedValues && changedValues.provider) {
        // 查找选中的供应商对象以获取ID
        const selectedProvider = providers.find(
          (p) => p.provider === changedValues.provider
        );
        if (selectedProvider) {
          // 使用供应商ID获取模型列表
          // fetchProviderModels(selectedProvider.id);
        }
      }

      // 当选择模型时，自动填充相关字段
      if ('model' in changedValues && changedValues.model) {
        fillFormFieldsByModel(changedValues.model);
      }

      // 如果模型类型发生变化，更新状态以触发重新渲染
      if ('modelType' in changedValues) {
        setSelectedModelType(changedValues.modelType);
      }

      // 任何值发生变化时，设置表单已修改状态
      const formHasChanged =
        JSON.stringify(allValues) !==
        JSON.stringify(originalFormValuesRef.current);
      const tagsHasChanged =
        JSON.stringify(tags) !== JSON.stringify(originalTags);
      setFormChanged(formHasChanged || tagsHasChanged);
    }
  };

  // 重置编辑状态
  const resetEditState = () => {
    setIsEditing(true);
    setUserEditing(false);
    // 更新原始表单值引用
    originalFormValuesRef.current = JSON.parse(
      JSON.stringify(form.getFieldsValue())
    );
    // 重置表单变化状态
    setFormChanged(false);
  };

  // 添加模型标签
  const handleAddTag = () => {
    if (tags.length >= 3) {
      Message.warning('标签最多添加3个');
      return;
    }
    // 直接添加一个空标签，然后自动聚焦到它
    const newTags = [...tags, ''];
    setTags(newTags);
    // 检查标签是否发生变化
    setFormChanged(JSON.stringify(newTags) !== JSON.stringify(originalTags));
  };

  // 处理标签变化
  const handleTagChange = (newTags) => {
    setTags(newTags);
    // 检查标签是否发生变化
    setFormChanged(JSON.stringify(newTags) !== JSON.stringify(originalTags));
  };

  // 修改标签删除逻辑，添加对表单变化的检测
  const handleTagClose = (index) => {
    if (!isEditing) return;
    const newTags = [...tags];
    newTags.splice(index, 1);
    setTags(newTags);
    // 检查标签是否发生变化
    setFormChanged(JSON.stringify(newTags) !== JSON.stringify(originalTags));
  };

  // 取消或返回
  const handleCancel = () => {
    // 重置用户编辑状态
    setUserEditing(false);
    dispatch({
      type: 'update-selected-modelconfig',
      payload: { selectedModelConfig: null },
    });
    navigate('/model');
  };

  // 更新模型配置
  const handleSubmit = async () => {
    if (!isEditing) {
      resetEditState();
      return;
    }

    // 如果表单没有变化，不执行保存
    if (!formChanged) {
      return;
    }

    try {
      const values = await form.validate();

      // 过滤掉空标签
      const nonEmptyTags = tags.filter((tag) => tag.trim() !== '');

      // 根据选择的 provider 名称查找对应的 id
      const selectedProvider = providers.find(
        (p) => p.provider === values.provider
      );
      const llmProviderId = selectedProvider
        ? selectedProvider.id
        : selectedModelConfig.llmProviderId || '';

      // 根据选择的 llmModelKeyId 查找对应的 id
      const selectedModelKey = modelKeys.find(
        (k) => k.id === values.llmModelKeyId
      );
      const llmModelKeyId = selectedModelKey
        ? selectedModelKey.id
        : values.llmModelKeyId || '';

      // 根据选择的模型查找对应的id
      const selectedModel = providerModels.find(
        (m) => m.id === values.model
      );
      const llmModelId = selectedModel
        ? selectedModel.id
        : values.model || selectedModelConfig.llmModelId || '';

      // 以表单数据为基础，补充 selectedModelConfig 中的数据
      const requestData = {
        ...values,
        id: values.id !== undefined ? values.id : selectedModelConfig.id || '',
        llmProviderId: llmProviderId, // 提交时使用 id
        llmModelId: llmModelId, // 添加llmModelId字段
        modelId:
          values.modelId !== undefined
            ? values.modelId
            : selectedModelConfig.modelId || '',
        icon:
          values.icon !== undefined
            ? values.icon
            : selectedModelConfig.icon || '',
        name:
          values.name !== undefined
            ? values.name
            : selectedModelConfig.name || values.deployName || '',
        deployName:
          values.deployName !== undefined
            ? values.deployName
            : selectedModelConfig.deployName || '',
        version:
          values.modelVersion !== undefined
            ? values.modelVersion
            : selectedModelConfig.version || '',
        apiVersion:
          values.apiVersion !== undefined
            ? values.apiVersion
            : selectedModelConfig.apiVersion || '',
        type:
          values.modelType !== undefined
            ? values.modelType
            : selectedModelConfig.type || 0,
        group:
          values.group !== undefined
            ? values.group
            : selectedModelConfig.group || null,
        llmModelKeyId: llmModelKeyId, // 提交时使用 id
        tags: nonEmptyTags, // 使用过滤后的标签数组
        multiModal:
          values.multiModal !== undefined
            ? values.multiModal
            : selectedModelConfig.multiModal || false,
        imageGeneration:
          values.allowGenerateImage !== undefined
            ? values.allowGenerateImage
            : selectedModelConfig.imageGeneration || false,
        promptCost:
          values.promptCost !== undefined
            ? values.promptCost
            : selectedModelConfig.promptCost || 0,
        completionCost:
          values.completionCost !== undefined
            ? values.completionCost
            : selectedModelConfig.completionCost || 0,
        dimension:
          values.embeddingDimension !== undefined && values.embeddingDimension !== ''
            ? parseInt(values.embeddingDimension, 10)
            : null,
        maxTokens:
          values.maxTokens !== undefined && values.maxTokens !== ''
            ? values.maxTokens
            : null,
        temperature:
          values.temperature !== undefined
            ? parseFloat(values.temperature)
            : selectedModelConfig.temperature || 1,
        isEnabled:
          values.isEnabled !== undefined
            ? values.isEnabled
            : selectedModelConfig.isEnabled || false,
      };

      console.log('提交的 requestData:', requestData);
      const response = await createLlmModelConfig(requestData);

      if (response) {
        Message.success('模型配置更新成功');
        setIsEditing(false);
        // 重置用户编辑状态
        setUserEditing(false);
        // 更新原始表单值和标签数据
        originalFormValuesRef.current = JSON.parse(
          JSON.stringify(form.getFieldsValue())
        );
        setOriginalTags([...nonEmptyTags]);
        setFormChanged(false);
        dispatch({
          type: 'update-selected-modelconfig',
          payload: {
            selectedModelConfig: { ...selectedModelConfig, ...requestData },
          },
        });
        navigate('/model');
      } else {
        Message.error('模型更新失败');
      }
    } catch (error) {
      console.log('更新模型失败:', error);
      Message.error('模型更新失败，请检查输入或稍后重试');
    }
  };

  // 获取模型类型
  useEffect(() => {
    const fetchModelTypes = async () => {
      setLoading(true);
      try {
        const response = await getLlmTypeList();
        if (response) {
          setModelTypes(response);
        } else {
          Message.error('获取模型类型失败');
        }
      } catch (error) {
        console.error('Failed to fetch model types:', error);
        Message.error('获取模型类型失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };
    fetchModelTypes();
  }, []);

  // 获取供应商ID列表
  useEffect(() => {
    const fetchProviders = async () => {
      setProvidersLoading(true);
      try {
        const response = await getLlmProviderList();
        if (response && Array.isArray(response)) {
          setProviders(response);
        } else {
          Message.error('获取供应商ID列表失败');
        }
      } catch (error) {
        console.error('Failed to fetch providers:', error);
        Message.error('获取供应商ID列表失败，请稍后重试');
      } finally {
        setProvidersLoading(false);
      }
    };
    fetchProviders();
  }, []);

  // 获取模型密钥ID列表
  useEffect(() => {
    const fetchModelKeys = async () => {
      setModelKeysLoading(true);
      try {
        const response = await getLlmModelKeys();
        if (response && Array.isArray(response)) {
          setModelKeys(response);
        } else {
          Message.error('获取模型密钥列表失败');
        }
      } catch (error) {
        console.error('Failed to fetch model keys:', error);
        Message.error('获取模型密钥列表失败，请稍后重试');
      } finally {
        setModelKeysLoading(false);
      }
    };
    fetchModelKeys();
  }, []);

  // 清空表单自动填充的字段
  const clearAutoFilledFields = () => {
    form.setFieldsValue({
      model: undefined,
      modelId: '',
      modelVersion: '',
      apiVersion: '',
      modelType: undefined,
      promptCost: undefined,
      completionCost: undefined,
      maxTokens: undefined,
      embeddingDimension: undefined,
      temperature: undefined
    });
  };

  // 根据所选模型填充表单字段
  const fillFormFieldsByModel = (modelId) => {
    // 查找选中的模型
    const selectedModel = providerModels.find(model => model.id === modelId);

    if (selectedModel) {
      // 自动填充表单字段
      form.setFieldsValue({
        modelId: selectedModel.modelId, 
        modelVersion: selectedModel.version,
        apiVersion: selectedModel.apiVersion,
        modelType: selectedModel.type,
        promptCost: selectedModel.promptCost,
        completionCost: selectedModel.completionCost,
        maxTokens: selectedModel.maxTokens,
        embeddingDimension: selectedModel.dimension,
        temperature: selectedModel.temperature,
      });
    }
  };

  // 获取供应商模型列表
  const fetchProviderModels = async (providerId) => {
    if (!isEditing) return;

    // 清空之前自动填充的数据
    clearAutoFilledFields();

    setProviderModelsLoading(true);
    try {
      const response = await getLlmProviderModelListById(providerId);
      if (response && Array.isArray(response)) {

        const enabledModels = response.filter(model => model.isEnabled === true);
        setProviderModels(enabledModels);
      } else {
        Message.error('获取供应商模型列表失败');
      }
    } catch (error) {
      console.error('Failed to fetch provider models:', error);
      Message.error('获取供应商模型列表失败，请稍后重试');
    } finally {
      setProviderModelsLoading(false);
    }
  };

  // 添加防抖包装的获取模型密钥列表函数
  const debouncedFetchModelKeys = _.debounce((provider, isUserAction = false) => {
    if (!provider) return;

    setModelKeysLoading(true);
    getLlmModelKeysByProvider(provider)
      .then(response => {
        if (response && Array.isArray(response)) {
          // 如果是用户手动选择的供应商，标记为用户编辑状态，防止自动重置
          if (isUserAction) {
            setUserEditing(true);
          }
          setModelKeys(response);
          console.log('已获取供应商模型密钥列表:', response);
        } else {
          Message.error('获取供应商模型密钥列表失败');
        }
      })
      .catch(error => {
        console.error('Failed to fetch provider model keys:', error);
        Message.error('获取供应商模型密钥列表失败，请稍后重试');
      })
      .finally(() => {
        setModelKeysLoading(false);
      });
  }, 300); // 300ms防抖

  // fetchModelKeysByProvider函数，仅调用防抖函数
  const fetchModelKeysByProvider = (provider, isUserAction = false) => {
    if (!isEditing) return;
    debouncedFetchModelKeys(provider, isUserAction);
  };

  // 初始化表单数据 - 只在selectedModelConfig变化时执行
  useEffect(() => {
    const initializeForm = async () => {
      if (selectedModelConfig) {
        // 如果providers和modelKeys已加载，则初始化表单
        if (providers.length > 0 && modelKeys.length > 0) {
          setFormValuesFromModelConfig();
        }
      } else {
        form.resetFields();
        originalFormValuesRef.current = {};
        setTags([]);
        setOriginalTags([]);
        setSelectedModelType(null);
      }
    };

    initializeForm();

  }, [selectedModelConfig]);

  // 当providers或modelKeys加载完成后，再次尝试初始化表单
  useEffect(() => {
    // 只有在用户没有手动编辑的情况下，且是首次加载或刷新页面时，才重新初始化表单
    if (selectedModelConfig && providers.length > 0 && modelKeys.length > 0 && !userEditing) {
      setFormValuesFromModelConfig();
    }
  }, [providers, modelKeys]);

  // 抽取设置表单值的函数，避免代码重复
  const setFormValuesFromModelConfig = () => {
    // 根据 llmProviderId 查找对应的 provider 名称
    const selectedProvider = providers.find(
      (p) => p.id === selectedModelConfig.llmProviderId
    );
    const providerName = selectedProvider
      ? selectedProvider.provider
      : selectedModelConfig.llmProviderId || '';

    // 根据 llmModelKeyId 查找对应的 llmProviderId 名称
    const selectedModelKey = modelKeys.find(
      (k) => k.id === selectedModelConfig.llmModelKeyId
    );
    const modelKeyName = selectedModelKey
      ? selectedModelKey.id
      : selectedModelConfig.llmModelKeyId || '';

    // 设置表单初始值
    const initialValues = {
      id: selectedModelConfig.id || '',
      modelId: selectedModelConfig.modelId || '',
      provider: providerName,
      model: selectedModelConfig.llmModelId || '', // 添加model字段初始值
      name: selectedModelConfig.name || '',
      deployName: selectedModelConfig.deployName || '',
      modelVersion: selectedModelConfig.version || '',
      apiVersion: selectedModelConfig.apiVersion || '',
      modelType: selectedModelConfig.type || 0,
      group: selectedModelConfig.group || null,
      llmModelKeyId: modelKeyName,
      multiModal: selectedModelConfig.multiModal || false,
      allowGenerateImage: selectedModelConfig.imageGeneration || false,
      promptCost: selectedModelConfig.promptCost || 0,
      completionCost: selectedModelConfig.completionCost || 0,
      embeddingDimension: selectedModelConfig.dimension || '',
      maxTokens: selectedModelConfig.maxTokens || '',
      temperature: selectedModelConfig.temperature || 1,
      isEnabled: selectedModelConfig.isEnabled || false,
      createdTime:
        new Date(selectedModelConfig.createdTime).toLocaleString() || '',
      updatedTime: selectedModelConfig.updatedTime
        ? new Date(selectedModelConfig.updatedTime).toLocaleString()
        : '',
    };

    form.setFieldsValue(initialValues);
    // 保存原始表单值以便比较
    originalFormValuesRef.current = JSON.parse(JSON.stringify(initialValues));
    // 设置初始模型类型
    setSelectedModelType(selectedModelConfig.type || 0);

    // 如果有供应商信息，获取该供应商的模型列表
    if (selectedProvider) {
      // 使用供应商ID获取模型列表
      fetchProviderModels(selectedProvider.id);
    }

    setTags(selectedModelConfig.tags || []);
    setOriginalTags(selectedModelConfig.tags || []);
  };

  //独立加载供应商模型数据
  useEffect(() => {
    const loadProviderModels = async () => {
      if (selectedModelConfig && providers.length > 0) {
        // 获取供应商ID
        const providerId = selectedModelConfig.llmProviderId;

        if (providerId) {
          setProviderModelsLoading(true);
          try {
            const response = await getLlmProviderModelListById(providerId);
            if (response && Array.isArray(response)) {
              const enabledModels = response.filter(model => model.isEnabled === true);
              setProviderModels(enabledModels);
            } else {
              console.error('加载供应商模型列表失败');
            }
          } catch (error) {
            console.error('Failed to load provider models:', error);
          } finally {
            setProviderModelsLoading(false);
          }
        }
      }
    };

    loadProviderModels();
  }, [selectedModelConfig, providers]);

  useEffect(() => {
    const loadModelKeys = async () => {
      if (selectedModelConfig && providers.length > 0) {
        // 根据llmProviderId查找对应的provider对象
        const selectedProvider = providers.find(
          (p) => p.id === selectedModelConfig.llmProviderId
        );

        if (selectedProvider && selectedProvider.provider) {
          // 避免重复请求，使用防抖函数
          debouncedFetchModelKeys(selectedProvider.provider, false);
        }
      }
    };
    loadModelKeys();
  }, [selectedModelConfig, providers]);

  return (
    <div className={styles.addModelContainer}>
      <Title
        heading={4}
        className={styles.addModelTitle}
        style={{ marginBottom: '24px' }}
      >
        详情
      </Title>
      <Form
        form={form}
        autoComplete="off"
        layout="vertical"
        initialValues={{
          multiModal: false,
          allowGenerateImage: false,
          isEnabled: false,
        }}
        requiredSymbol={false}
        onValuesChange={handleFormValuesChange}
      >
        <Row gutter={[40, 0]}>
          <Col span={12}>
            <Card bordered={false} style={{ padding: '0px' }}>
              <Row className={styles.addModelHeader}>
                <div className={styles.iconAndName}>
                  <div className={styles.addModelIconWrapper}>
                    <IconModel className={styles.addModelIcon} />
                  </div>
                  <div className={styles.divider}></div>
                  <div className={styles.nameFormContainer}>
                    <FormItemWithoutRequiredMark
                      label="对外展示名称"
                      field="name"
                      style={{ marginBottom: '0px' }}
                    >
                      <Input
                        placeholder="请输入"
                        className={styles.nameInput}
                        disabled={!isEditing}
                      />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="模型部署名称" required={true} />}
                    field="deployName"
                    rules={[{ required: true, message: '请输入模型部署名称' }]}
                  >
                    <Input
                      placeholder="请输入"
                      className={styles.deployNameInput}
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark label="ID" field="modelId">
                    <Input
                      placeholder="请输入"
                      className={styles.idInput}
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark label="组" field="group">
                    <Input
                      placeholder="请输入"
                      className={styles.groupInput}
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <FormItemWithoutRequiredMark className={styles.tagFormItem}>
                <Row
                  justify="space-between"
                  align="center"
                  style={{ marginBottom: '8px' }}
                >
                  <Space direction="vertical" size={'mini'}>
                    <Text
                      style={{
                        color: '#5c5c5c',
                        fontWeight: '600',
                        fontSize: '14px',
                      }}
                    >
                      标签
                    </Text>
                    <Text className={styles.labelExact}>标签最多添加3个</Text>
                  </Space>
                  {isEditing && (
                    <Button
                      className={styles.addTagBtn}
                      onClick={handleAddTag}
                    >
                      添加
                    </Button>
                  )}
                </Row>
                {tags.length > 0 && (
                  <div className={styles.selectedItemList}>
                    {tags.map((tag, index) => (
                      <Row
                        key={`tag-${index}`}
                        className={styles.selectedItemRow}
                      >
                        <Input
                          autoFocus={tag === ''}
                          value={tag}
                          onChange={(value) => {
                            if (value && value.length > 20) {
                              return;
                            }
                            const newTags = [...tags];
                            newTags[index] = value;
                            handleTagChange(newTags);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              e.stopPropagation();
                            }
                          }}
                          placeholder={`标签 ${index + 1}`}
                          disabled={!isEditing}
                          className={!isEditing ? styles.disabledTagInput : ''}
                          suffix={
                            <IconTagClose
                              className={`${styles.deleteIcon} ${!isEditing ? styles.disabledDeleteIcon : ''}`}
                              onClick={
                                !isEditing
                                  ? undefined
                                  : () => handleTagClose(index)
                              }
                            />
                          }
                        />
                      </Row>
                    ))}
                  </div>
                )}
              </FormItemWithoutRequiredMark>
              <div>
                <div className={styles.switchContainer}>
                  <Space
                    className={styles.switchLeftContent}
                    direction="vertical"
                    size={4}
                  >
                    <Text className={styles.Switchlabel}>是否启用</Text>
                    <Text className={styles.SwitchTitle}>模型控制</Text>
                  </Space>
                  <div className={styles.switchRightContent}>
                    <FormItemWithoutRequiredMark
                      field="isEnabled"
                      triggerPropName="checked"
                      style={{ margin: 0 }}
                    >
                      <Switch disabled={!isEditing} />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
                <div className={styles.switchContainer}>
                  <Space
                    className={styles.switchLeftContent}
                    direction="vertical"
                    size={4}
                  >
                    <Text className={styles.Switchlabel}>
                      是否允许发送图片/视频
                    </Text>
                    <Text className={styles.SwitchTitle}>模型控制</Text>
                  </Space>
                  <div className={styles.switchRightContent}>
                    <FormItemWithoutRequiredMark
                      field="multiModal"
                      triggerPropName="checked"
                      style={{ margin: 0 }}
                    >
                      <Switch disabled={!isEditing} />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
                <div className={styles.switchContainer}>
                  <Space
                    className={styles.switchLeftContent}
                    direction="vertical"
                    size={4}
                  >
                    <Text className={styles.Switchlabel}>是否允许生成图片</Text>
                    <Text className={styles.SwitchTitle}>模型控制</Text>
                  </Space>
                  <div className={styles.switchRightContent}>
                    <FormItemWithoutRequiredMark
                      field="allowGenerateImage"
                      triggerPropName="checked"
                      style={{ margin: 0 }}
                    >
                      <Switch disabled={!isEditing} />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
              </div>
            </Card>
          </Col>

          <Col span={12} className={styles.Card}>
            <Card bordered={false}>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="供应商" required={true} />}
                    field="provider"
                    rules={[{ required: true, message: '请选择供应商' }]}
                  >
                    <Select
                      placeholder="请选择"
                      loading={providersLoading}
                      disabled={!isEditing}
                      onChange={(value) => {
                        // 清空模型和模型密钥选择
                        if (isEditing) {
                          // 仅清空与模型相关的字段，而不是整个表单
                          form.setFieldsValue({
                            model: undefined,
                            llmModelKeyId: undefined,
                            // 清除自动填充的字段
                            modelId: '',
                            modelVersion: '',
                            apiVersion: '',
                            modelType: undefined,
                            promptCost: undefined,
                            completionCost: undefined,
                            maxTokens: undefined,
                            embeddingDimension: undefined,
                            temperature: undefined
                          });

                          // 找到对应的供应商对象
                          const selectedProvider = providers.find(p => p.provider === value);
                          if (selectedProvider) {
                            // 重新加载模型列表
                            fetchProviderModels(selectedProvider.id);
                            // 使用防抖函数获取模型密钥列表，标记为用户操作
                            fetchModelKeysByProvider(value, true);
                          }
                        }
                      }}
                    >
                      {providers.map((item) => (
                        <Select.Option key={item.id} value={item.provider}>
                          {item.provider}
                        </Select.Option>
                      ))}
                    </Select>
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="模型" required={true} />}
                    field="model"
                    rules={[{ required: true, message: '请选择模型' }]}
                  >
                    <Select placeholder="请选择" loading={providerModelsLoading} disabled={!isEditing || !form.getFieldValue('provider')}>
                      {providerModels.map((item) => (
                        <Select.Option key={item.id} value={item.id}>
                          {item.name}
                        </Select.Option>
                      ))}
                    </Select>
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="密钥" required={true} />}
                    field="llmModelKeyId"
                    rules={[{ required: true, message: '请选择模型密钥' }]}
                  >
                    <Select placeholder="请选择" loading={modelKeysLoading} disabled={!isEditing}>
                      {modelKeys.map((item) => (
                        <Select.Option key={item.id} value={item.id}>
                          {item.llmProvider ? (item.name ? `${item.llmProvider}(${item.name})` : item.llmProvider) : '未知供应商'}
                        </Select.Option>
                      ))}
                    </Select>
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="模型类型" required={true} />}
                    field="modelType"
                    rules={[{ required: true, message: '请选择模型类型' }]}
                  >
                    <Select placeholder="请选择" loading={loading} disabled={!isEditing}>
                      {modelTypes && Object.entries(modelTypes).map(([key, value]) => (
                        <Select.Option key={value} value={value}>{key}</Select.Option>
                      ))}
                    </Select>
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark label="模型版本" field="modelVersion">
                    <Input placeholder="请输入" className={styles.modelVersionInput} disabled={!isEditing} />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark label="API版本" field="apiVersion">
                    <Input placeholder="请输入" className={styles.apiVersionInput} disabled={!isEditing} />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="输入成本（每千Token/元）" required={true} />}
                    field="promptCost"
                    rules={[{ required: true, message: '请输入输入成本（每千Token/元）' }]}
                  >
                    <InputNumber min={0} placeholder="0" disabled={!isEditing} />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="输出成本（每千Token/元）" required={true} />}
                    field="completionCost"
                    rules={[{ required: true, message: '请输入输出成本（每千Token/元）' }]}
                  >
                    <InputNumber min={0} placeholder="0" disabled={!isEditing} />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark 
                    label={
                      <CustomLabel 
                        label="最大输出（Token数）" 
                        required={selectedModelType === 1 || selectedModelType === 2} 
                      />
                    }
                    field="maxTokens" 
                    className={styles.maxTokens}
                    rules={[
                      {
                        validator: (value, callback) => {
                          const modelType = selectedModelType;
                          // 如果是Chat(2)或Text(1)类型
                          if (modelType === 1 || modelType === 2) {
                            if (value === undefined || value === null || value === '') {
                              callback('当模型类型为Chat或Text时，最大输出Token数必填');
                            } else if (value === 0) {
                              callback('最大输出Token数不能为0');
                            } else {
                              callback();
                            }
                          } else {
                            // 其他类型，如果有值则不能为0
                            if (value !== undefined && value !== null && value !== '' && value === 0) {
                              callback('最大输出Token数不能为0');
                            } else {
                              callback();
                            }
                          }
                        }
                      }
                    ]}
                  >
                    <InputNumber 
                      min={0} 
                      placeholder={
                        selectedModelType === 1 || selectedModelType === 2 
                          ? "请输入（必填）" 
                          : "请输入（可选）"
                      } 
                      disabled={!isEditing} 
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={
                      <CustomLabel 
                        label="嵌入维度" 
                        required={selectedModelType === 4} 
                      />
                    }
                    field="embeddingDimension"
                    rules={[
                      {
                        validator: (value, callback) => {
                          const modelType = selectedModelType;
                          // 如果是Embedding(4)类型
                          if (modelType === 4) {
                            if (value === undefined || value === null || value === '') {
                              callback('当模型类型为Embedding时，嵌入维度必填');
                            } else {
                              const numValue = parseInt(value, 10);
                              if (isNaN(numValue) || numValue <= 0) {
                                callback('嵌入维度不能为0');
                              } else {
                                callback();
                              }
                            }
                          } else {
                            // 其他类型，如果有值则不能为0
                            if (value !== undefined && value !== null && value !== '') {
                              const numValue = parseInt(value, 10);
                              if (isNaN(numValue) || numValue <= 0) {
                                callback('嵌入维度不能为0');
                              } else {
                                callback();
                              }
                            } else {
                              callback();
                            }
                          }
                        }
                      }
                    ]}
                  >
                    <Input
                      placeholder={
                        selectedModelType === 4 
                          ? "请输入（必填）" 
                          : "请输入（可选）"
                      }
                      className={styles.embeddingDimensionInput}
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark 
                    label={<CustomLabel label="Temperature" required={true} />}
                    field="temperature"
                    rules={[
                      { required: true, message: '请输入Temperature' },
                      { 
                        validator: (value, callback) => {
                          if (value !== undefined && value !== null && value !== '') {
                            const numValue = parseFloat(value);
                            if (isNaN(numValue) || numValue <= 0 || numValue > 1) {
                              callback('Temperature必须是0-1之间的数字，且不能为0');
                            } else {
                              callback();
                            }
                          } else {
                            callback();
                          }
                        }
                      }
                    ]}
                  >
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.1}
                      placeholder="请输入（0.1-1）"
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark label="创建时间" field="createdTime">
                    <Input className={styles.deployNameInput} disabled />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark label="更新时间" field="updatedTime">
                    <Input className={styles.deployNameInput} disabled />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
            </Card>

          </Col>
        </Row>

      </Form>
      <Space style={{ display: 'flex', justifyContent: 'flex-end', paddingTop: '16px', marginTop: '16px', borderTop: '1px solid #f5f5f5' }} size={8}>
        <Button type="secondary" onClick={handleCancel} className={styles.cancelButton}>返回</Button>
        <Button
          type="primary"
          onClick={handleSubmit}
          className={`${styles.submitButton} ${isEditing && !formChanged ? styles.submitButtonDisabled : ''}`}
          disabled={isEditing && !formChanged}
        >
          {isEditing ? '保存' : '编辑'}
        </Button>
      </Space>
    </div>
  );
};

export default ConfigDetaillPage;
