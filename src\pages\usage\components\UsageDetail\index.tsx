import React, { useEffect, useRef } from 'react';
import {
  Card,
  Space,
  Button,
  Typography,
  Grid,
  Tag,
} from '@arco-design/web-react';
import AppIcon from '@/assets/usage/appImg.svg';
import ScoreImg from '@/assets/usage/scoreImg.svg';
import { Column, Radar } from '@antv/g2plot';
import styles from './style/index.module.less';

const { Title, Text } = Typography;
const { Row, Col } = Grid;

// 模拟数据
const mockData = {
  icon: <AppIcon />,
  name: '教育培训助手',
  tags: ['agent', '教育培训', '办公人事'],
  creator: 'Admin',
  createTime: '2024/06/24',

  monthlyTokens: Array.from({ length: 30 }, (_, i) => ({
    date: String(i + 1),
    value: i < 8 ? Math.floor(Math.random() * 100 + 50) : 20,
  })),
  monthlyUsers: Array.from({ length: 30 }, (_, i) => ({
    date: String(i + 1),
    value: i < 8 ? Math.floor(Math.random() * 100 + 50) : 20,
  })),
  overview: {
    tokens: '163',
    usage: '16M',
    activeUsers: '163',
  },
  userSessions: {
    normalSessions: '163',
    failedSessions: '163',
    sessionRetries: '163',
  },
  evaluation: {
    score: 72.26,
    metrics: [
      { name: '语言处理', value: 80 },
      { name: '内容评估', value: 70 },
      { name: '数学逻辑', value: 65 },
      { name: '代码开发', value: 75 },
      { name: '工具调用', value: 60 },
      { name: '知识应用', value: 85 },
    ],
  },
};

const UsageDetail = () => {
  const tokenChartRef = useRef(null);
  const userChartRef = useRef(null);
  const radarChartRef = useRef(null);

  useEffect(() => {
    // Token消耗柱状图
    if (tokenChartRef.current) {
      const tokenChart = new Column(tokenChartRef.current, {
        data: mockData.monthlyTokens,
        xField: 'date',
        yField: 'value',
        // 设置柱子之间的间隔
        columnWidthRatio: 0.8, // 控制柱子宽度比例
        marginRatio: 0.1, // 设置柱子之间的间距
        xAxis: {
          label: {
            autoHide: false,
            autoRotate: false,
            // 设置X轴标签间隔显示
            formatter: (text, item, index) => {
              // 每2个标签显示一个
              return index % 1 === 0 ? text : '';
            },
          },
          // 确保所有tick都显示
          tickCount: mockData.monthlyTokens.length / 2,
        },
        yAxis: {
          // 保持默认设置
        },
        columnStyle: {
          fill: '#4d5ef3',
          fillOpacity: 0.1,
        },
        meta: {
          date: {
            alias: '日期',
          },
          value: {
            alias: 'Token数',
          },
        },
      });
      tokenChart.render();
      return () => tokenChart.destroy();
    }
  }, []);

  useEffect(() => {
    // 活跃用户柱状图
    if (userChartRef.current) {
      const userChart = new Column(userChartRef.current, {
        data: mockData.monthlyUsers,
        xField: 'date',
        yField: 'value',
        xAxis: {
          label: {
            autoHide: false,
            autoRotate: false,
            // 设置X轴标签间隔显示
            formatter: (text, item, index) => {
              // 每2个标签显示一个
              return index % 1 === 0 ? text : '';
            },
          },
          // 确保所有tick都显示
          tickCount: mockData.monthlyTokens.length / 2,
        },
        columnStyle: {
          fill: '#4d5ef3',
          fillOpacity: 0.1,
        },
        // 设置柱子之间的间隔
        columnWidthRatio: 0.8, // 控制柱子宽度比例
        marginRatio: 0.1, // 设置柱子之间的间距
        yAxis: {},
        meta: {
          date: {
            alias: '日期',
          },
          value: {
            alias: '用户数',
          },
        },
      });

      userChart.render();
      return () => userChart.destroy();
    }
  }, []);

  useEffect(() => {
    // 六边形雷达图
    if (radarChartRef.current) {
      const radarChart = new Radar(radarChartRef.current, {
        data: mockData.evaluation.metrics,
        xField: 'name',
        yField: 'value',
        radius: 0.8,
        startAngle: Math.PI / 2,
        endAngle: Math.PI * 2.5,
        // 设置坐标轴
        xAxis: {
          line: null,
          tickLine: null,
          grid: {
            line: {
              style: {
                lineDash: null,
              },
            },
          },
        },
        yAxis: {
          label: false,
          grid: {
            line: {
              type: 'line',
              style: {
                lineDash: null,
              },
            },
          },
        },
        // 设置面积样式
        area: {
          style: {
            fill: '#4d5ef3',
            fillOpacity: 0.2,
          },
        },
        // 设置线条样式
        lineStyle: {
          stroke: '#4d5ef3',
          lineWidth: 2,
        },
        // 设置点的样式
        point: {
          size: 4,
          style: {
            fill: '#4d5ef3',
            stroke: '#fff',
            lineWidth: 2,
          },
        },
        legend: false,
        tooltip: {
          shared: true,
          showCrosshairs: true,
          crosshairs: {
            type: 'xy',
          },
        },
      });
      radarChart.render();
      return () => radarChart.destroy();
    }
  }, []);

  return (
    <div className={styles.container}>
      {/* 头部信息 */}
      <div className={styles.header}>
        <Space size="large" align="start">
          <AppIcon className={styles.appIcon} />
          <div className={styles.appInfo}>
            <div className={styles.titleRow}>
              <Title style={{ margin: 0 }}>{mockData.name}</Title>
              <Space size={4}>
                {mockData.tags.map((tag) => (
                  <Tag key={tag} bordered>
                    {tag}
                  </Tag>
                ))}
              </Space>
            </div>
            <Text type="secondary" className={styles.creatorInfo}>
              创建人：{mockData.creator} | 创建时间：{mockData.createTime}
            </Text>
          </div>
        </Space>
        <Space size="medium">
          <Button className={styles.testButton} type="secondary">
            测试
          </Button>
          <Button className={styles.configButton} type="primary">
            配置
          </Button>
        </Space>
      </div>
      <Title heading={5} className={styles.Title}>
        用量分析
      </Title>
      {/* 主要内容区域 */}
      <Row gutter={[24, 24]} className={styles.mainContent}>
        <Col span={12} className={styles.overviewCol}>
          {/* 概览卡片 */}
          <Title heading={5} className={styles.sectionTitle}>
            概览
          </Title>
          <Card className={styles.overviewCard}>
            <Row>
              <Col span={8}>
                <div
                  className={styles.statItem}
                  style={{ borderRight: '1px solid #ebebeb' }}
                >
                  <Title heading={3}>{mockData.overview.tokens}</Title>
                  <Text type="secondary">Token消耗</Text>
                </div>
              </Col>
              <Col span={8}>
                <div
                  className={styles.statItem}
                  style={{ borderRight: '1px solid #ebebeb' }}
                >
                  <Title heading={3}>{mockData.overview.usage}</Title>
                  <Text type="secondary">使用量</Text>
                </div>
              </Col>
              <Col span={8}>
                <div className={styles.statItem}>
                  <Title heading={3}>{mockData.overview.activeUsers}</Title>
                  <Text type="secondary">使用人数</Text>
                </div>
              </Col>
            </Row>
          </Card>
          {/* Token消耗图表 */}
          <Title heading={5} className={styles.sectionTitle}>
            当月Token消耗
          </Title>
          <Card className={styles.chartCard}>
            <div ref={tokenChartRef} style={{ height: 300 }} />
          </Card>
          {/* 活跃用户图表 */}
          <Title heading={5} className={styles.sectionTitle}>
            当月活跃人数
          </Title>
          <Card className={styles.chartCard}>
            <div ref={userChartRef} style={{ height: 300 }} />
          </Card>
        </Col>
        <Col span={12} className={styles.qualityCol}>
          {/* 会话质量卡片 */}
          <Title heading={5} className={styles.sectionTitle}>
            用户会话质量
          </Title>
          <Card className={styles.qualityCard}>
            <Row>
              <Col span={8}>
                <div
                  className={styles.statItem}
                  style={{ borderRight: '1px solid #ebebeb' }}
                >
                  <Title heading={3}>
                    {mockData.userSessions.normalSessions}
                  </Title>
                  <Text type="secondary">正常会话次数</Text>
                </div>
              </Col>
              <Col span={8}>
                <div
                  className={styles.statItem}
                  style={{ borderRight: '1px solid #ebebeb' }}
                >
                  <Title heading={3}>
                    {mockData.userSessions.failedSessions}
                  </Title>
                  <Text type="secondary">会话失败次数</Text>
                </div>
              </Col>
              <Col span={8}>
                <div className={styles.statItem}>
                  <Title heading={3}>
                    {mockData.userSessions.sessionRetries}
                  </Title>
                  <Text type="secondary">会话重试</Text>
                </div>
              </Col>
            </Row>
          </Card>
          {/* 综合评价卡片 */}
          <Title heading={5} className={styles.sectionTitle}>
            综合评价
          </Title>
          <div className={styles.scoreContent}>
            <Card className={styles.scoreSection}>
              <Title heading={2}>{mockData.evaluation.score}%</Title>
              <Text type="secondary">评估分数计算方式</Text>
              <ScoreImg className={styles.ScoreImg} />
            </Card>
          </div>
          <div className={styles.evaluationCard}>
            <Card>
              <div ref={radarChartRef} style={{ height: 500 }} />
            </Card>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default UsageDetail;
