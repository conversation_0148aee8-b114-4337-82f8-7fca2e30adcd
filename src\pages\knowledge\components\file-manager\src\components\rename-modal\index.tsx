import { useTranslate } from '@/pages/knowledge/components/file-manager/src/hooks/common-hooks';
import { useGetFolderId } from '@/pages/knowledge/components/file-manager/src/hooks/file-manager-hooks';
import { Form, Input, Modal, Button } from 'antd';
import { useEffect, useState } from 'react';
import { IModalManagerChildrenProps } from '@/pages/knowledge/components/file-manager/src/components/modal-manager';
import styles from './index.module.less';
import FileIcon from '@/assets/knowledge/FileIcon.svg';
import IconCloseTag from '@/assets/close.svg';
import { Space, Typography } from 'antd';
const { Text } = Typography;

interface IProps extends Omit<IModalManagerChildrenProps, 'showModal'> {
  loading: boolean;
  initialName?: string;
  initialDescription?: string;
  initialTags?: string[];
  onOk: (params: { new_name: string; description?: string; tags?: string[] }) => void;
  showModal?(): void;
}

const RenameModal = ({
  visible,
  hideModal,
  loading,
  initialName = '',
  initialDescription = '',
  initialTags = [],
  onOk,
}: IProps) => {
  const [form] = Form.useForm();
  const { t } = useTranslate('common');
  const [formValues, setFormValues] = useState<{ new_name?: string; description?: string; tags?: string[] }>({});
  const [tags, setTags] = useState<string[]>(initialTags);
  const currentFolderId = useGetFolderId();
  const isRootDirectory = !currentFolderId;

  type FieldType = {
    new_name?: string;
    description?: string;
    tags?: string[];
  };

  const handleOk = async () => {
    const ret = await form.validateFields();
    return onOk({
      new_name: ret.new_name,
      description: isRootDirectory ? ret.description : '',
      tags: isRootDirectory ? tags.filter(tag => tag.trim() !== '') : [],
    });
  };

  // 监听表单值变化
  const handleFormValuesChange = (changedValues: any, allValues: any) => {
    setFormValues(allValues);
  };

  // 处理Modal关闭
  const handleCancel = () => {
    form.resetFields();
    setFormValues({});
    hideModal();
  };

  // 判断确认按钮是否应该禁用
  const isConfirmDisabled = !formValues.new_name?.trim() || loading;

  useEffect(() => {
    if (visible) {
      form.setFieldsValue({
        new_name: initialName,
        description: initialDescription,
      });
      setFormValues({
        new_name: initialName,
        description: initialDescription,
      });
      setTags(initialTags);
    }
  }, [initialName, initialDescription, initialTags, form, visible]);

  const showTagInput = () => {
    if (tags.length >= 3) {
      return;
    }
    setTags([...tags, '']);
  };
  const handleTagChange = (newTags: string[]) => {
    setTags(newTags);
  };

  // 自定义footer
  const customFooter = (
    <div className={styles.customFooter}>
      <Button
        className={styles.cancelButton}
        onClick={handleCancel}
      >
        取消
      </Button>
      <Button
        type="primary"
        className={styles.confirmButton}
        loading={loading}
        disabled={isConfirmDisabled}
        onClick={handleOk}
      >
        保存
      </Button>
    </div>
  );

  return (
    <Modal
      title={'编辑'}
      open={visible}
      onCancel={handleCancel}
      footer={customFooter}
      className={styles.renameModal}
    >
      <div className={styles.formContainer}>
        <Form
          name="fileEditForm"
          layout="vertical"
          autoComplete="off"
          form={form}
          onValuesChange={handleFormValuesChange}
        >
          <div className={styles.topSection}>
            <div className={styles.iconSection}>
              <FileIcon className={styles.fileIcon} />
            </div>
            <div className={styles.divider}></div>
            <div className={styles.nameFormSection}>
              <Form.Item<FieldType>
                label={
                  <span className={styles.labelWithRequired}>
                    名称
                    <span className={styles.requiredStar}>*</span>
                  </span>
                }
                name="new_name"
                required={false}
                rules={[{ required: true, message: t('namePlaceholder') }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
            </div>
          </div>
          
          {isRootDirectory && (
            <div className={styles.bottomSection}>
              <Form.Item<FieldType>
                label="描述"
                name="description"
                required={false}
              >
                <Input.TextArea
                  placeholder="请输入"
                  rows={4}
                  maxLength={100}
                />
              </Form.Item>
              
              <Form.Item<FieldType>
                className={styles.tagFormItem}
              >
                <div className={styles.tagHeader}>
                  <Space direction="vertical" size={4}>
                    <Text className={styles.tagLabel}>标签</Text>
                    <Text className={styles.tagHint}>标签至多添加3个</Text>
                  </Space>
                  <Button
                    className={styles.addTagBtn}
                    onClick={showTagInput}
                    disabled={tags.length >= 3}
                  >
                    添加
                  </Button>
                </div>
                {tags.length > 0 && (
                  <div className={styles.selectedItemList}>
                    {tags.map((tag, index) => (
                      <div key={`tag-${index}`} className={styles.selectedItemRow}>
                        <Input
                          autoFocus={tag === ''}
                          value={tag}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value && value.length > 20) {
                              return;
                            }
                            const newTags = [...tags];
                            newTags[index] = value;
                            handleTagChange(newTags);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              e.stopPropagation();
                            }
                          }}
                          placeholder={`标签 ${index + 1}`}
                          suffix={
                            <IconCloseTag
                              className={styles.deleteIcon}
                              onClick={() => {
                                const newTags = [...tags];
                                newTags.splice(index, 1);
                                handleTagChange(newTags);
                              }}
                            />
                          }
                        />
                      </div>
                    ))}
                  </div>
                )}
              </Form.Item>
            </div>
          )}
        </Form>
      </div>
    </Modal>
  );
};

export default RenameModal;

