import React, { memo } from 'react';
import type { customNodeInterface } from '../types';
import { <PERSON>le, Position } from '@xyflow/react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { GlobalState } from '@/store/index';

import icon1 from '@/assets/flow/icon_1.png';
import icon3 from '@/assets/flow/icon_3.png';

import styles from '../style/leftNode.module.less';

// 预先检测是否为客户端环境
const isClientSide = typeof window !== 'undefined';

const LeftNode = (props: Partial<customNodeInterface>) => {
  const { data, isConnectable } = props as any;
  const agentDetailMenuName = useSelector((state: GlobalState) => state.agentDetailMenuName);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const updateBreadcrumbData = (newBreadcrumb) => {
    dispatch({
      type: 'update-breadcrumb-menu-name',
      payload: { breadcrumbMenuName: newBreadcrumb },
    });
  };

  const handleClickAgentDetail = (data: any) => {
    // 存储当前的activeTab，用于返回时恢复
    sessionStorage.setItem(`agentTeam_${data.applicationId}_activeTab`, 'routing');

    // 更新面包屑数据，使用详情页路径和卡片名称
    const breadcrumbData = new Map([[agentDetailMenuName, data.name]]);
    updateBreadcrumbData(breadcrumbData);

    navigate('/agent/info', { state: { id: data.id, path: 'routing', atId: data.applicationId } });
  };

  return (
    <div onClick={() => handleClickAgentDetail(data)}
      style={{
        cursor: 'pointer'
      }}
    >
      <div className={styles.leftNodeContainer}>
        <div className={styles.RightNodeTopContent}>

          <div className={styles.leftNodeContent}>
            <img className={styles.leftNodeLogo} src={icon1} />
            <div className={styles.leftNodeTitleBox}>
              <span className={styles.leftNodeTitleText}>
                {data?.name}
              </span>
            </div>
          </div>
          <div className={styles.LeftNodeDescBox}>
            <span className={styles.LeftNodeDescText}>{data?.description}</span>
          </div>
          <div className={styles.LeftNodeBottomBox}>
            {data?.labels && data.labels.length !== 0 &&
              data.labels.map((item: any, i: number) => (
                <div className={styles.LeftNodeBottomBtn} key={i}>
                  <span className={styles.LeftNodeBottomBtnText}>{item}</span>
                </div>
              ))}
          </div>
        </div>
        {/* <img className={styles.more} src={icon5} /> */}
      </div>

      {isClientSide && (
        <Handle
          type="source"
          isConnectable={isConnectable}
          style={{
            backgroundImage: `url(${icon3})`,
            backgroundSize: '100% 100%',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            width: '24px',
            height: '24px',
            backgroundColor: 'transparent',
            border: 'none',
          }}
          position={Position.Right}
        />
      )}
    </div>
  );
};

LeftNode.displayName = 'LeftNode';

export default memo(LeftNode);
