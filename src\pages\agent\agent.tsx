import React from 'react';
import { Tabs } from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import styles from './style/index.module.less';
import LIST from './components/list';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';

function Agent() {
    const locale = useLocale();

    return (
        <div className={styles.container}>
            <Tabs className={styles.tabs}>
                {/* 应用页面内容 */}
                <TabPane
                    key="agentList"
                    title={locale['menu.application.header.agentList']}
                >
                    <LIST />
                </TabPane>
            </Tabs>
        </div>
    );
}

export default Agent; 