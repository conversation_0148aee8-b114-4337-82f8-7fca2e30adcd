import { useState, useImperativeHandle, forwardRef } from 'react';
import { Modal, Form, Collapse, Checkbox } from '@arco-design/web-react';
import IconUser2 from '@/assets/IconUser2.svg';
import IconKnowledgeMember from '@/assets/IconKnowledgeMember.svg';
import { IconDown } from '@arco-design/web-react/icon';

const CollapseItem = Collapse.Item;
const CheckboxGroup = Checkbox.Group;

// 选择成员
const KnowledgeMemberModal = forwardRef((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => {
    return {
      open: () => setVisible(true),
    };
  });

  const memberList = [
    {
      title: '产研',
      list: [
        { name: '张明', post: '产品经理', id: '1' },
        { name: '张明', post: '产品经理', id: '2' },
      ],
    },
    { title: '测试', list: [{ name: '王晓宇', post: '测试经理', id: '3' }] },
  ];

  const [memberValue, setMemberValue] = useState<string[][]>([]);

  const onPostChange = (value: boolean, postIndex: number) => {
    console.log(value);
    const post = memberList[postIndex];
    const ids = post.list.map((item) => item.id);
    setMemberValue((prev) => {
      const newValue = [...prev];
      newValue[postIndex] = value ? ids : [];
      return newValue;
    });
  };

  const onMemberChange = (value, postIndex: number) => {
    setMemberValue((prev) => {
      const newValue = [...prev];
      newValue[postIndex] = value;
      return newValue;
    });
  };

  const getCheckAllStatus = (postIndex: number) => {
    const post = memberList[postIndex];
    const ids = post.list.map((item) => item.id);
    const checkedIds = memberValue[postIndex];
    const checked = checkedIds?.length === ids.length;
    const indeterminate =
      !checked && checkedIds?.some((id) => ids.includes(id));

    return {
      indeterminate: indeterminate,
      checked: checked,
    };
  };

  return (
    <Modal
      title="选择成员"
      visible={visible}
      cancelText="取消"
      okText="添加"
      alignCenter={false}
      className="min-w-[640px] [&_.arco-modal-content]:p-[24px] top-[5%]"
      onCancel={() => setVisible(false)}
    >
      <Collapse
        style={{ maxWidth: 1180 }}
        expandIconPosition="right"
        expandIcon={<IconDown />}
        bordered={false}
        defaultActiveKey={['0']}
      >
        {memberList.map((post, postIndex) => {
          const checkAllStatus = getCheckAllStatus(postIndex);
          return (
            <CollapseItem
              key={postIndex}
              header={
                <div className="flex">
                  <Checkbox
                    className="mr-[34px]"
                    onChange={(value) => onPostChange(value, postIndex)}
                    onClick={(e) => e.stopPropagation()}
                    indeterminate={checkAllStatus.indeterminate}
                    checked={checkAllStatus.checked}
                  />
                  <div className="flex items-center gap-x-[8px]">
                    <IconUser2 />
                    {post.title}
                  </div>
                </div>
              }
              name={`${postIndex}`}
              className="border-0 mb-[4px] [&_.arco-collapse-item-header]:bg-[#f7f7f7] [&_.arco-collapse-item-header]:rounded-[8px] [&_.arco-collapse-item-content]:bg-white [&_.arco-collapse-item-icon-hover-right>.arco-collapse-item-header-icon-down]:rotate-[-90deg]"
            >
              <CheckboxGroup
                value={memberValue[postIndex] || []}
                className="[&_.arco-checkbox]:flex [&_.arco-checkbox]:items-center [&_.arco-checkbox-text]:ml-[32px]"
                direction="vertical"
                options={post.list.map((member, memberIndex) => ({
                  label: (
                    <div className="flex items-center gap-x-[8px]">
                      <IconKnowledgeMember />
                      <span>{member.name}</span>
                      <span className="text-[13px] text-[#999999]">
                        {member.post}
                      </span>
                    </div>
                  ),
                  value: member.id,
                }))}
                onChange={(value) => onMemberChange(value, postIndex)}
              />
            </CollapseItem>
          );
        })}
      </Collapse>
    </Modal>
  );
});

export default KnowledgeMemberModal;
