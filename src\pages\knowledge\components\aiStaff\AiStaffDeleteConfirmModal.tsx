import { Modal, <PERSON>ton, Typography, Space } from '@arco-design/web-react';
import IconClose from '@/assets/acp/IconClose.svg';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';

const { Text } = Typography;

const AiStaffDeleteConfirmModal = forwardRef(
  ({ confirmDelete }: { confirmDelete: (data: any) => void }, ref) => {
    const [visible, setVisible] = useState(false);
    const currentModalData = useRef<any>();
    const open = (row: any) => {
      currentModalData.current = row;
      setVisible(true);
    };
    const toggleModal = () => {
      setVisible(!visible);
      currentModalData.current = null;
    };
    useImperativeHandle(ref, () => ({ open }));
    const _confirmDelete = () => {
      confirmDelete(currentModalData.current);
      toggleModal();
    };
    return (
      <Modal
        visible={visible}
        onCancel={toggleModal}
        closeIcon={<IconClose />}
        maskClosable={false}
        footer={null}
        simple={true}
        closable={true}
        className="p-[24px] w-[480px] rounded-[16px] [&_.arco-modal-title]"
        title={
          <div className="text-left font-semibold text-[18px] leading-[24px] text-[#333] flex justify-between">
            <span>删除AI员工</span>
          </div>
        }
      >
        <div className="flex flex-col">
          <Text className="text-[14px] text-[#5c5c5c] font-[400]">
            删除后该AI员工将被永久移除，无法恢复。
          </Text>
        </div>
        <div className="mt-[24px] flex justify-end gap-[8px]">
          <Space>
            <Button
              className="flex justify-center items-center p-[18px_24px] rounded-[8px] font-[600] text-[14px] bg-white text-[#5c5c5c] border border-[#ebebeb] hover:bg-[#fafafa]"
              onClick={toggleModal}
            >
              取消
            </Button>
            <Button
              className="flex justify-center items-center p-[18px_24px] rounded-[8px] font-[600] text-[14px] bg-[#d54941] text-white hover:bg-[#cd463e]"
              type="primary"
              status="danger"
              onClick={_confirmDelete}
            >
              删除
            </Button>
          </Space>
        </div>
      </Modal>
    );
  }
);

export default AiStaffDeleteConfirmModal;
