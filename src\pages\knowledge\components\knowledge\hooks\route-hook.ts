import { useLocation, useSearchParams } from 'react-router-dom';
import { useCallback, useState } from 'react';
import { KnowledgeSearchParams } from '../constants/knowledge';
export const useSetPaginationParams = () => {
  const location = useLocation();
  const { page, pageSize } = location.state || {};

  const [paginationParams, setPaginationParams] = useState({
    page,
    pageSize,
  });

  const onPaginationParams = useCallback(
    (page = 1, pageSize?: number) => {
      setPaginationParams((prev) => {
        const pag = { ...prev };
        pag.page = page;
        if (pageSize) {
          pag.pageSize = pageSize;
        }
        return pag;
      });
    },
    [paginationParams]
  );

  return {
    setPaginationParams: onPaginationParams,
    page: Number(paginationParams.page) || 1,
    size: Number(paginationParams.pageSize) || 10,
  };
};

export const useGetKnowledgeSearchParams = () => {
  const [currentQueryParameters] = useSearchParams();

  return {
    documentId:
      currentQueryParameters.get(KnowledgeSearchParams.DocumentId) || '',
    knowledgeId:
      currentQueryParameters.get(KnowledgeSearchParams.KnowledgeId) || '',
  };
};
