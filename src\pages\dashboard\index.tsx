import React, { useState } from 'react';
import { Grid, Typography, Tabs } from '@arco-design/web-react';
import styles from './style/index.module.less';
import CostDataOverview from './components/CostDataOverview';
import UsageDataOverview from './components/UsageDataOverview';
import TokenCostQuarterly from './components/TokenCostQuarterly';
import TokenCostMonthly from './components/TokenCostMonthly';
import TokenCostWeekly from './components/TokenCostWeekly';
import ModelTokenUsage from './components/ModelTokenUsage';
import AgentTeamTokenUsage from './components/AgentTeamTokenUsage';
import ACPTokenUsage from './components/ACPTokenUsage';

const { Row, Col } = Grid;
const { Text } = Typography;
const TabPane = Tabs.TabPane;

function Dashboard() {
    const [activeTab, setActiveTab] = useState('usage');

    // 费用统计模块内容
    const renderCostStatistics = () => {
        return (
            <>
                <CostDataOverview />
                <Row style={{marginBottom:'8px',fontSize:'16px',color:'#333333'}}>分析</Row>
                <Row gutter={[8, 16]} className={styles.row}>
                    <Col span={12} xs={24} sm={24} md={12} lg={12} xl={12}>
                        <TokenCostQuarterly />
                    </Col>
                    <Col span={12} xs={24} sm={24} md={12} lg={12} xl={12}>
                        <TokenCostMonthly />
                    </Col>
                </Row>
                <Row gutter={[16, 16]} className={styles.row}>
                    <Col span={24}>
                        <TokenCostWeekly />
                    </Col>
                </Row>
            </>
        );
    };

    // 使用统计模块内容
    const renderUsageStatistics = () => {
        return (
            <>
                <UsageDataOverview />
                <Row style={{marginBottom:'8px',fontSize:'16px',color:'#333333'}}>分析</Row>
                <Row gutter={[8, 16]} className={styles.row}>
                    <Col span={12} xs={24} sm={24} md={12} lg={12} xl={12}>
                        <ModelTokenUsage />
                    </Col>
                    <Col span={12} xs={24} sm={24} md={12} lg={12} xl={12}>
                        <AgentTeamTokenUsage />
                    </Col>
                </Row>
                <Row gutter={[16, 16]} className={styles.row}>
                    <Col span={24}>
                        <ACPTokenUsage />
                    </Col>
                </Row>
            </>
        );
    };

    return (
        <div className={styles.layout}>
            <Tabs 
                activeTab={activeTab}
                onChange={setActiveTab}
                className={styles.tabs}
            >
                {/* <TabPane key="cost" title="费用统计">
                    <div className={styles['layout-content']}>
                        {renderCostStatistics()}
                    </div>
                </TabPane> */}
                <TabPane key="usage" title="使用统计">
                    <div className={styles['layout-content']}>
                        {renderUsageStatistics()}
                    </div>
                </TabPane>
            </Tabs>
        </div>
    );
}

export default Dashboard;