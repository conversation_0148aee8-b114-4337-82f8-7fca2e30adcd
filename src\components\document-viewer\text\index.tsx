import { Spin } from 'antd';
import FileError from '../file-error';

import { useFetchText } from '../hooks';
import styles from './index.less';

const Text = ({ filePath }: { filePath: string }) => {
  const { succeed, containerRef, error } = useFetchText(filePath);

  return (
    <>
      {succeed ? (
        <section className={styles.textContainer}>
          <div id="text" ref={containerRef}>
            <Spin />
          </div>
        </section>
      ) : (
        <FileError>{error}</FileError>
      )}
    </>
  );
};

export default Text;
