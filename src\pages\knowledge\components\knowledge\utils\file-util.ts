import fileManagerService from '../services/file-manager-service';
import { FileMimeType } from '../constants/common';
async function fetchDocumentBlob(id: string, mimeType?: FileMimeType) {
  const response = await fileManagerService.getDocumentFile({}, id);
  const blob = new Blob([response.data], {
    type: mimeType || response.data.type,
  });

  return blob;
}

export const downloadDocument = async ({
  id,
  filename,
}: {
  id: string;
  filename?: string;
}) => {
  const blob = await fetchDocumentBlob(id);
  downloadFileFromBlob(blob, filename);
};

export const downloadFileFromBlob = (blob: Blob, name?: string) => {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  if (name) {
    a.download = name;
  }
  a.click();
  window.URL.revokeObjectURL(url);
};
