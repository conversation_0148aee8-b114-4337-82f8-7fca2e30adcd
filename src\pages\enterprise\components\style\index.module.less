.container {
  display: flex;
  flex-direction: column;
  // gap: 16px; 


  .memberListCard {
    position: relative;
    min-height: 400px;
    height: calc(100vh - 128px);

    .MembersTable {
      // padding: 0 24px 24px;  

      .indexCell {
        font-weight: 400;
        font-size: 14px;
        color: #a6a6a6;
      }

      .indexName {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }

      .indexEmail {
        font-weight: 400;
        font-size: 14px;
        color: #a6a6a6;
      }

      .joinTime {
        font-weight: 400;
        font-size: 14px;
        color: #a6a6a6;
      }

      .indexRole{
        font-weight: 400;
        font-size: 14px;
        color: #a6a6a6;
      }
    }

    // 加载状态容器
    .loadingContainer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #ffffff;
      min-height: 400px;

      :global(.arco-spin) {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
    }

    // 建设中状态容器
    .buildingContainer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #ffffff;
      min-height: 400px;

      .buildingText {
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;
        text-align: center;
      }
    }

    // 空状态容器
    .emptyContainer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #ffffff;
      min-height: 400px;

      .emptyText {
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;
        text-align: center;
      }
    }

    :global(.arco-table-container) {
      border: none;
    }

    :global(.arco-table-container::before) {
        display: none;
    }

    :global(.arco-table-th) {
        background-color: #ffffff !important;
        border-bottom: none;
        font-weight: 600;
        font-size: 16px;
        color: #5c5c5c;
        border-left: none;
    }

    :global(.arco-table-th-item) {
        padding: 4px 24px;
        border-radius: 8px;
        background-color: #fcfcfc;
        margin-right: 8px;

        &:not(:last-child) {
            margin-right: 1px;
        }

        :global(.arco-table-th-item-title) {
            font-weight: 600;
            font-size: 14px;
            line-height: 24px;
            color: #adadad;
        }
    }

    :global(.arco-table-header) {
        position: sticky;
        top: 0;
        z-index: 1;
        margin-bottom: 8px;
    }

    :global(.arco-table-td) {
        padding: 16px;
        border-top: 1px solid #f5f5f5;
        border-bottom: none;
        color: #5c5c5c;
        font-size: 14px;
        font-weight: 400;
        border-left: none;
    }

    // :global(.arco-table-th) {
    //   background-color: #f8f8f8;
    //   font-weight: 600;
    //   font-size: 14px;
    //   color: #595959;
    //   // padding: 12px 16px !important;
    // }

    // :global(.arco-table-td) {
    //   padding: 16px !important;
    //   border-bottom: 1px solid #f2f2f2;
    // }
  }
}

.inviteModal {
  position: relative;
  padding: 32px;
  width: 640px;
  border-radius: 8px;

  .inviteTag {
    font-weight: 400;
    font-size: 14px;
    color: #a6a6a6;
  }

  :global(.arco-modal-header) {
    padding: 0;
    height: auto;
    border-bottom: none;

    :global(.arco-modal-title) {
      font-weight: 600;
      font-size: 18px;
      line-height: 24px;
      color: #333333;
      margin-bottom: 8px;
      text-align: left;
    }
  }

  :global(.arco-form-item) {
    margin-bottom: 24px;

    :global(.arco-form-item > .arco-form-label-item) {
      font-weight: 600;
      font-size: 14px;
      line-height: 24px;
      color: #595959;
    }
  }

  :global(.arco-input) {
    padding: 8px 12px;
    border: 1px solid #ebebeb;
    border-radius: 4px;
    background-color: transparent;

    &::placeholder {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #d6d6d6;
    }
  }

  :global(.arco-select-size-default.arco-select-single .arco-select-view) {
    padding: 8px;
    height: auto;
    line-height: 24px;
    font-weight: 400;
    font-size: 14px;
    color: #595959;
    border-radius: 4px;
    border: 1px solid #ebebeb;
    background-color: transparent;
  }

  :global(.arco-modal-content) {
    min-height: 160px;
    padding: 0;
    width: 100%;
    height: 100%;
  }

  :global(.arco-modal-footer) {
    display: none;
  }

  :global(.arco-modal-close-icon) {
    position: absolute;
    right: 32px;
    top: 32px;
    font-size: 12px;
    cursor: pointer;
    color: var(--color-text-1);
  }

  .cancelButton,
  .confirmButton {
    padding: 8px 24px;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .cancelButton {
    background-color: #fafafa;
    color: #595959;
  }

  .confirmButton {
    background-color: #f8f8ff;
    color: #4d5ef3;
  }

  :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
    background-color: #e9e9ff;
    color: #4d5ef3;
  }
}

:global(.arco-table-hover
    .arco-table-tr:not(.arco-table-empty-row):hover
    .arco-table-td:not(.arco-table-col-fixed-left):not(.arco-table-col-fixed-right)) {
  background-color: #fcfcfc;
}

