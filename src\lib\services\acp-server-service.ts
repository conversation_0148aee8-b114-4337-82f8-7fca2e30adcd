import axiosInstance from './interceptors';
import { endpoints } from './api-endpoints';
import { AcpServer, AcpServerResponse, TransportType, AcpTools, AcpServerTestResponse, AcpServerAvailableResponse } from '@/types/acpServerType';


/**
 * 将后端数据转换为前端AcpServer格式
 * @param data 后端返回的数据
 * @returns 格式化后的AcpServer对象
 */
export const formatAcpServer = (data: any): AcpServer => {
    return {
        id: data.id || undefined,
        // server_id: data.serverId || '',
        name: data.name || '',
        transport_type: typeof data.transport_type === 'number' ? data.transport_type : 0,
        location: data.location || undefined,
        description: data.description || undefined,
        is_available: data.is_available || false,
        query_params: data.query_params || {},
        http_transport_model: data.http_transport_model || {
            name: '',
            connection_timeout_seconds: 0,
            additional_headers: {}
        },
    };
};


/**
 * 获取AcpServer列表
 * @returns AcpServerResponse列表
 */
export const getAcpServerList = async (): Promise<AcpServerResponse[]> => {
    try {
        const response = await axiosInstance.get(endpoints.acpServerListUrl);
        return Array.isArray(response.data) ? response.data : [];
    } catch (error) {
        console.error('获取AcpServer列表失败:', error);
        throw error;
    }
};


/**
 * 保存AcpServer（创建或更新）
 * @param data AcpServer数据
 * @returns 保存结果
 */
export const saveAcpServer = async (data: AcpServer) => {
    try {
        const response = await axiosInstance.post(endpoints.acpServerSaveUrl, data);
        return response.data;
    } catch (error) {
        console.error('保存AcpServer失败:', error);
        throw error;
    }
};


/**
 * 删除AcpServer
 * @param Id ACP ID
 * @returns 删除结果，成功返回true，失败返回false
 */
export const deleteAcpServer = async (Id: string): Promise<boolean> => {
    try {
        const url = endpoints.acpServerDeleteUrl.replace('{serverId}', Id);
        const response = await axiosInstance.delete(url);
        return response.data === true;
    } catch (error) {
        console.error('删除AcpServer失败:', error);
        throw error;
    }
};


/**
 * 获取AcpServer传输类型列表
 * @returns 传输类型映射对象，如 {"auto_detect_http": 1, "streamable_http": 2, "sse": 3}
 */
export const getTransportTypes = async (): Promise<TransportType> => {
    try {
        const response = await axiosInstance.get(endpoints.acpServerTransportTypesUrl);
        return response.data || {};
    } catch (error) {
        console.error('获取AcpServer传输类型列表失败:', error);
        throw error;
    }
};


/**
 * 获取AcpServer详情
 * @param serverId 服务ID
 * @returns AcpServer详情
 */
export const getAcpServerDetail = async (serverId: string): Promise<AcpServer | null> => {
    try {
        const url = endpoints.acpServerDetailUrl.replace('{serverId}', serverId);
        const response = await axiosInstance.get(url);
        return response.data ? formatAcpServer(response.data) : null;
    } catch (error) {
        console.error('获取AcpServer详情失败:', error);
        throw error;
    }
};


/**
 * 通过名称检查AcpServer是否存在
 * @param name 服务名称
 * @returns 检查结果，如果存在返回true，不存在返回false
 */
export const checkAcpServerName = async (name: string): Promise<boolean> => {
    try {
        const url = endpoints.acpServerNameCheckUrl.replace('{name}', name);
        const response = await axiosInstance.get(url);
        return response.data === true;
    } catch (error) {
        console.error('检查AcpServer名称失败:', error);
        throw error;
    }
};


/** 根据serverId获取acpTools */
export const getAcpToolsById = async (id: string): Promise<AcpTools[]> => {
    try {
        const response = await axiosInstance.get(endpoints.acpServerToolsListUrl.replace('{id}', id));
        return response.data.data?.mcpClientTools || [];
    } catch (error) {
        console.error('获取AcpTools失败:', error);
        throw error;
    }
};


/**
 * 获取ACP服务器工具详情
 * @param id ACP服务器ID
 * @returns ACP工具详情数据
 */
export const getAcpServerTools = async (id: string): Promise<AcpTools[]> => {
    try {
        const url = endpoints.acpServerToolsUrl.replace('{id}', id);
        const response = await axiosInstance.get(url);
        // response.data.data.mcpClientTools
        return response.data?.data?.mcpClientTools || [];
    } catch (error) {
        console.error('获取ACP服务器工具详情失败:', error);
        throw error;
    }
};


/**
 * 测试ACP服务器连接
 * @param data 包含name、description、transport_type、location的测试数据
 * @returns 测试响应对象
 */
export const testAcpServerConnection = async (data: {
    name: string;
    description: string;
    transport_type: number;
    location: string;
}): Promise<AcpServerTestResponse> => {
    try {
        const response = await axiosInstance.post(endpoints.acpServerPingUrl, data);
        return response.data;
    } catch (error) {
        console.error('测试ACP服务器连接失败:', error);
        // 返回失败的响应格式
        return {
            success: false,
            code: 500,
            message: '网络请求失败',
            data: {
                server_capabilities: null,
                server_info: null,
                server_instructions: null
            }
        };
    }
};


/**
 * 探测ACP服务器是否可用
 * @param id ACP服务器ID
 * @returns 探测响应对象
 */
export const checkAcpServerAvailable = async (id: string): Promise<AcpServerAvailableResponse> => {
    try {
        const url = endpoints.acpServerAvailableUrl.replace('{id}', id);
        const response = await axiosInstance.patch(url);
        
        // 解析API响应格式: {success: boolean, code: number, message: string, data: object}
        const apiResponse = response.data;
        
        // 直接使用success字段判断连接状态
        const isConnected = apiResponse.success === true;
        
        return {
            success: isConnected,
            message: isConnected ? '测试连接成功' : '测试连接失败，请检查配置'
        };
    } catch (error) {
        console.error('探测ACP服务器可用性失败:', error);
        return {
            success: false,
            message: '测试连接失败，请检查配置'
        };
    }
}; 
