import { Flex, Form, InputNumber, Slider } from 'antd';

export const AutoKeywordsItem = () => {
  return (
    <Form.Item
      label={'自动关键词提取'}
      tooltip={
        '自动为每个文本块中提取 N 个关键词，用以提升查询精度。请注意：该功能采用“系统模型设置”中设置的默认聊天模型提取关键词，因此也会产生更多 Token 消耗。另外，你也可以手动更新生成的关键词。详情请见 https://ragflow.io/docs/dev/autokeyword_autoquestion。'
      }
    >
      <Flex gap={20} align="center">
        <Flex flex={1}>
          <Form.Item
            name={['parser_config', 'auto_keywords']}
            noStyle
            initialValue={0}
          >
            <Slider max={30} style={{ width: '100%' }} />
          </Form.Item>
        </Flex>
        <Form.Item name={['parser_config', 'auto_keywords']} noStyle>
          <InputNumber max={30} min={0} />
        </Form.Item>
      </Flex>
    </Form.Item>
  );
};

export const AutoQuestionsItem = () => {
  return (
    <Form.Item
      label={'自动问题提取'}
      tooltip={
        '利用“系统模型设置”中设置的 chat model 对知识库的每个文本块提取 N 个问题以提高其排名得分。请注意，开启后将消耗额外的 token。您可以在块列表中查看、编辑结果。如果自动问题提取发生错误，不会妨碍整个分块过程，只会将空结果添加到原始文本块。详情请见 https://ragflow.io/docs/dev/autokeyword_autoquestion。'
      }
    >
      <Flex gap={20} align="center">
        <Flex flex={1}>
          <Form.Item
            name={['parser_config', 'auto_questions']}
            noStyle
            initialValue={0}
          >
            <Slider max={10} style={{ width: '100%' }} />
          </Form.Item>
        </Flex>
        <Form.Item name={['parser_config', 'auto_questions']} noStyle>
          <InputNumber max={10} min={0} />
        </Form.Item>
      </Flex>
    </Form.Item>
  );
};
