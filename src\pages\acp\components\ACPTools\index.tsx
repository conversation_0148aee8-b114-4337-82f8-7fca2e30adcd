import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Typography, Space, Select, Spin, Modal, Form, Message, Popover, Radio } from '@arco-design/web-react';
import IconSearch from '@/assets/acp/IconSearch.svg';
import IconTool from '@/assets/acp/IconACPTools.svg';
import IconClose from '@/assets/acp/IconClose.svg';
import IconAction from '@/assets/acp/IconAction.svg';
import IconSortType from '@/assets/application/IconSortType.svg';
import styles from './style/index.module.less';
// import { getToolsList, createTool, updateTool, deleteTool } from '@/lib/services/acp-tools-service';

const { Text } = Typography;
const Option = Select.Option;
const FormItem = Form.Item;

const CustomLabel = ({ label, required }) => {
    return (
        <Space>
            <span>{label}</span>
            {required && <Typography.Text type="secondary" className={styles.required}>(必填)</Typography.Text>}
        </Space>
    );
};

function ACPTools() {
    const [tools, setTools] = useState([]);
    const [filteredTools, setFilteredTools] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [modalMode, setModalMode] = useState('view'); // 'view' for "Tool详情", 'create' for "新增Tool"
    const [selectedTool, setSelectedTool] = useState(null);
    const [form] = Form.useForm(); // 创建Form实例
    const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
    const [toolToDelete, setToolToDelete] = useState(null);
    const [searchText, setSearchText] = useState('');
    const [sortType, setSortType] = useState('createTime');
    // const toolIcons = [IconTool, IconTool2, IconTool3, IconTool4];
    
    // 获取Tool图标的函数
    const getToolIcon = (id) => {
        const tool = tools.find(t => t.id === id);
        if (tool?.logo) {
            return <img src={tool.logo} alt="logo" style={{ width: 24, height: 24 }} />;
        }
        // 使用简单的哈希函数生成索引
        const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        // const index = hash % toolIcons.length;
        // const IconComponent = toolIcons[index];
        return <IconTool />;
    };

    useEffect(() => {
        fetchTools();
    }, []);

    // 当tools或搜索/排序条件改变时，更新过滤后的Tool列表
    useEffect(() => {
        filterAndSortTools();
    }, [tools, searchText, sortType]);

    const mockTools = [
        {
            id: '1',
            name: 'ACP Tool',
            description: '这是一段关于这个ACP Tool的描述',
            type: 'utility',
            createdTime: '2024/10/25 14:30:00',
            updatedTime: '2024/10/25 14:30:00',
            creator: '创建人'
        },
        {
            id: '2',
            name: 'ACP Tool',
            description: '这是一段关于这个ACP Tool的描述',
            type: 'analysis',
            createdTime: '2024/10/25 13:45:00',
            updatedTime: '2024/10/25 13:45:00',
            creator: '创建人'
        },
        {
            id: '3',
            name: 'ACP Tool',
            description: '这是一段关于这个ACP Tool的描述',
            type: 'enhancement',
            createdTime: '2024/10/25 12:15:00',
            updatedTime: '2024/10/25 12:15:00',
            creator: '创建人'
        },
        {
            id: '4',
            name: 'ACP Tool',
            description: '这是一段关于这个ACP Tool的描述',
            type: 'integration',
            createdTime: '2024/10/25 10:30:00',
            updatedTime: '2024/10/25 10:30:00',
            creator: '创建人'
        }
    ];

    // 使用模拟数据
    const fetchTools = async () => {
        try {
            setLoading(true);
            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 800));

            setTools(mockTools);
            setError(null);
        } catch (err) {
            console.error('获取ACP Tools列表失败:', err);
            setError('获取ACP Tools数据失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    };

    // 修改handleSubmit函数，使用模拟数据处理
    const handleSubmit = () => {
        form.validate().then(async (values) => {
            try {
                if (modalMode === 'create') {
                    // 构造新Tool对象
                    const newTool = {
                        id: Date.now().toString(), // 使用时间戳作为临时ID
                        name: values.displayName || 'ACP Tool',
                        description: values.description || '',
                        type: values.type || 'utility',
                        createdTime: new Date().toLocaleString(),
                        updatedTime: new Date().toLocaleString(),
                        creator: '创建人'
                    };

                    // 添加到模拟数据中
                    setTools(prevTools => [newTool, ...prevTools]);
                    Message.success('ACP Tool创建成功！');
                } else {
                    // 更新已有Tool
                    setTools(prevTools => prevTools.map(tool => {
                        if (tool.id === selectedTool.id) {
                            return {
                                ...tool,
                                name: values.displayName || 'ACP Tool',
                                description: values.description || '',
                                type: values.type || 'utility',
                                updatedTime: new Date().toLocaleString()
                            };
                        }
                        return tool;
                    }));
                    Message.success('ACP Tool更新成功！');
                }
            } catch (error) {
                console.error(modalMode === 'create' ? '创建ACP Tool失败:' : '更新ACP Tool失败:', error);
                Message.error(`${modalMode === 'create' ? '创建' : '更新'}ACP Tool失败，请检查网络或联系管理员！`);
            } finally {
                setModalVisible(false);
                setSelectedTool(null);
                form.resetFields();
            }
        }).catch(errors => {
            console.log('Validation errors:', errors);
            Message.error('请检查表单字段！');
        });
    };

    // 处理删除确认
    const handleConfirmDelete = async () => {
        if (toolToDelete) {
            try {
                // 模拟API调用延迟
                await new Promise(resolve => setTimeout(resolve, 500));

                // 从模拟数据中删除
                setTools(prevTools => prevTools.filter(tool => tool.id !== toolToDelete.id));
                Message.success('ACP Tool删除成功！');
            } catch (error) {
                console.error('删除ACP Tool失败:', error);
                Message.error('删除ACP Tool失败，请检查网络或联系管理员！');
            } finally {
                setConfirmDeleteVisible(false);
                setToolToDelete(null);
            }
        }
    };

    // 筛选和排序Tool列表
    const filterAndSortTools = () => {
        let result = [...tools];

        // 搜索过滤
        if (searchText) {
            const lowerCaseSearch = searchText.toLowerCase();
            result = result.filter(tool =>
                tool.name.toLowerCase().includes(lowerCaseSearch) ||
                tool.description?.toLowerCase().includes(lowerCaseSearch) ||
                tool.type?.toLowerCase().includes(lowerCaseSearch)
            );
        }

        // 排序
        switch (sortType) {
            case 'createTime':
                result.sort((a, b) => new Date(b.createdTime || 0).getTime() - new Date(a.createdTime || 0).getTime());
                break;
            case 'updateTime':
                result.sort((a, b) => {
                    const timeA = a.updatedTime ? new Date(a.updatedTime).getTime() : 0;
                    const timeB = b.updatedTime ? new Date(b.updatedTime).getTime() : 0;
                    return timeB - timeA;
                });
                break;
            case 'name':
                result.sort((a, b) => a.name.localeCompare(b.name));
                break;
            default:
                break;
        }

        setFilteredTools(result);
    };

    // 处理排序变化
    const handleSort = (value) => {
        setSortType(value);
    };

    // 处理搜索
    const handleSearch = (value) => {
        setSearchText(value);
    };

    // 处理"添加ACP Tool"按钮
    const handleAddTool = () => {
        form.resetFields(); // 重置表单字段
        form.setFieldsValue({ type: 'utility' }); // 默认选择工具类型
        setModalMode('create');
        setModalVisible(true);
    };

    // 处理"查看"Tool详情
    const handleViewTool = (tool) => {
        form.setFieldsValue({
            displayName: tool.name || '',
            description: tool.description || '',
            type: tool.type || 'utility',
        });
        setSelectedTool(tool);
        setModalMode('view');
        setModalVisible(true);
    };

    // 处理删除确认
    const handleDeleteConfirm = (tool) => {
        setToolToDelete(tool);
        setConfirmDeleteVisible(true);
    };

    // 取消删除
    const handleCancelDelete = () => {
        setConfirmDeleteVisible(false);
        setToolToDelete(null);
    };

    // 处理取消按钮
    const handleCancel = () => {
        setModalVisible(false);
        setSelectedTool(null);
        form.resetFields();
    };

    return (
        <div className={styles.acpTools}>
            <div className={styles.header}>
                <Button type="primary" className={styles.addToolBtn} onClick={handleAddTool}>新增ACP Tools</Button>
                <Space size={'small'}>
                    <Text className={styles.toolNumber}>
                        共 {filteredTools.length} 个ACP tool
                    </Text>
                    <Input
                        prefix={<IconSearch />}
                        placeholder="AI搜索..."
                        style={{ width: 240 }}
                        onChange={(value) => handleSearch(value)}
                        allowClear
                    />
                    <Select
                        prefix={<IconSortType />}
                        placeholder="按创建时间排序"
                        style={{ width: 160 }}
                        onChange={handleSort}
                        value={sortType}
                    >
                        <Option value="createTime">按创建时间排序</Option>
                        <Option value="updateTime">按更新时间排序</Option>
                        <Option value="name">按名称排序</Option>
                    </Select>
                </Space>
            </div>

            {loading ? (
                <div className={styles.loadingContainer}>
                    <Spin tip="加载中..." />
                </div>
            ) : error ? (
                <div className={styles.errorContainer}>
                    <Text type="error">{error}</Text>
                </div>
            ) : (
                <div className={styles.content}>
                    {filteredTools.length > 0 ? (
                        filteredTools.map((tool, index) => (
                            <Card key={tool.id || index} className={styles.toolCard}>
                                <div className={styles.toolInfo}>
                                    <Space direction='vertical' size={8}>
                                        <Space className={styles.nameWrapper} size={12}>
                                            <span className={styles.icon}>
                                                {getToolIcon(tool.id)}
                                            </span>
                                            <Text className={styles.name} onClick={() => handleViewTool(tool)}>{tool.name}</Text>
                                        </Space>
                                        <div className={styles.toolDetails}>
                                            <Text className={styles.toolDescription}>{tool.description}</Text>
                                        </div>
                                    </Space>
                                    <div className={styles.toolFooter}>
                                        <Space className={styles.metaText}>
                                            <Text className={styles.updateTime}>最近更新： {new Date(tool.updatedTime).toLocaleDateString('zh-CN')}</Text>
                                            <Text>|</Text>
                                            <Text className={styles.creator}>@{tool.creator}</Text>
                                        </Space>    
                                        <Space size={8}>
                                            <Button 
                                                className={styles.serverButton} 
                                                size="small"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    // Handle ACP Server action
                                                    console.log('ACP Server clicked for', tool.id);
                                                }}
                                            >
                                                ACP Server
                                            </Button>
                                            <Button 
                                                className={styles.variableButton} 
                                                size="small"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    // Handle 变量 action
                                                    console.log('变量 clicked for', tool.id);
                                                }}
                                            >
                                                变量
                                            </Button>
                                            <Popover
                                                trigger="click"
                                                position="right"
                                                className={styles.popoverContent}
                                                content={
                                                    <Space className={styles.popoverContent} direction='vertical' size={'mini'}>
                                                        <Button
                                                            className={`${styles.actionBtn} ${styles.deleteBtn}`}
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                handleDeleteConfirm(tool)
                                                            }}
                                                        >
                                                            删除
                                                        </Button>
                                                    </Space>
                                                }
                                            >
                                                <Button className={styles.triggerBtn}>
                                                    <IconAction />
                                                </Button>
                                            </Popover>
                                        </Space>
                                    </div>
                                </div>
                            </Card>
                        ))
                    ) : (
                        <div className={styles.emptyContainer}>
                            <Text type="secondary">{searchText ? '未找到匹配的ACP Tool' : '暂无ACP Tool数据'}</Text>
                        </div>
                    )}
                </div>
            )}

            {/* Tool详情/新增Tool模态框 */}
            <Modal
                visible={modalVisible}
                title={modalMode === 'create' ? '新增ACP Tool' : 'ACP Tool详情'}
                onCancel={handleCancel}
                closeIcon={<IconClose />}
                className={styles.toolDetailModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <div className={styles.iconContainer}>
                        <div className={styles.toolIconWrapper}>
                            {/* <IconTool className={styles.toolIcon} /> */}
                        </div>
                    </div>

                    <Form form={form} autoComplete="off" layout="vertical">
                        <FormItem
                            label="对外展示名称"
                            field="displayName"
                        >
                            <Input placeholder="请输入" />
                        </FormItem>

                        <FormItem
                            label="描述"
                            field="description"
                        >
                            <Input.TextArea placeholder="请输入描述" />
                        </FormItem>

                        <FormItem
                            label="工具类型"
                            field="type"
                            initialValue="utility"
                        >
                            <Radio.Group>
                                <div className={styles.toolTypeCards}>
                                    <Radio value="utility">
                                        {({ checked }) => (
                                            <div className={`${styles.toolTypeCard} ${checked ? styles.toolTypeCardChecked : ''}`}>
                                                <div className={styles.radioMark}>
                                                    <div className={styles.radioMarkDot}></div>
                                                </div>
                                                <div className={styles.toolTypeContent}>
                                                    <Text>实用工具</Text>
                                                </div>
                                            </div>
                                        )}
                                    </Radio>
                                    <Radio value="analysis">
                                        {({ checked }) => (
                                            <div className={`${styles.toolTypeCard} ${checked ? styles.toolTypeCardChecked : ''}`}>
                                                <div className={styles.radioMark}>
                                                    <div className={styles.radioMarkDot}></div>
                                                </div>
                                                <div className={styles.toolTypeContent}>
                                                    <Text>分析工具</Text>
                                                </div>
                                            </div>
                                        )}
                                    </Radio>
                                    <Radio value="enhancement">
                                        {({ checked }) => (
                                            <div className={`${styles.toolTypeCard} ${checked ? styles.toolTypeCardChecked : ''}`}>
                                                <div className={styles.radioMark}>
                                                    <div className={styles.radioMarkDot}></div>
                                                </div>
                                                <div className={styles.toolTypeContent}>
                                                    <Text>增强工具</Text>
                                                </div>
                                            </div>
                                        )}
                                    </Radio>
                                </div>
                            </Radio.Group>
                        </FormItem>
                    </Form>
                </div>
                <div className={styles.modalFooter}>
                    <Button onClick={handleCancel} className={styles.cancelBtn}>取消</Button>
                    <Button type="primary" onClick={handleSubmit} className={styles.saveBtn}>
                        {modalMode === 'create' ? '创建' : '保存'}
                    </Button>
                </div>
            </Modal>

            {/* 删除确认弹窗 */}
            <Modal
                visible={confirmDeleteVisible}
                title="删除ACP Tool"
                onCancel={handleCancelDelete}
                closeIcon={<IconClose />}
                className={styles.confirmDeleteModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <Text className={styles.modalContentText}>
                        {`ACP Tool ${toolToDelete?.name || ''} 将被删除，请确认您是否要删除？`}
                    </Text>
                </div>
                <div className={styles.modalFooter}>
                    <Space>
                        <Button onClick={handleCancelDelete} className={styles.cancelDeleteBtn}>取消</Button>
                        <Button type="primary" status="danger" onClick={handleConfirmDelete} className={styles.confirmDeleteBtn}>
                            删除
                        </Button>
                    </Space>
                </div>
            </Modal>
        </div>
    );
}

export default ACPTools;