import ButtonComponent from '@arco-design/web-react/es/Button';
import useLocale from '@/utils/useLocale';
import styles from './style/index.module.less';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import {
  Card,
  Image,
  Input,
  Message,
  Modal,
  Popover,
  Select,
  Form,
  Button,
  Grid,
  InputTag,
  Spin,
} from '@arco-design/web-react';
import folderIcon from '@/assets/application/agentIcon1.png';
import folderIcon2 from '@/assets/application/agentIcon2.png';
import folderIcon3 from '@/assets/application/agentIcon3.png';
import folderIcon4 from '@/assets/application/agentIcon4.png';
import TopIcon from '@/assets/top.svg';
import { useEffect, useState, useCallback, useRef } from 'react';
import { Typography } from '@arco-design/web-react';
import {
  IconClose,
  IconMore,
  IconPlus,
  IconArrowUp,
} from '@arco-design/web-react/icon';
import {
  getAgentList,
  deleteAgent,
  createAgent,
  getAgentLabelOptions,
} from '@/lib/services/agent-service';
import { useNavigate } from 'react-router-dom';
import IconSearch from '@/assets/application/search.svg';
import IconScreen from '@/assets/application/screen.svg';
import NotAgentIcon from '@/assets/application/NotAgent.svg';
import { useDispatch, useSelector } from 'react-redux';
import { GlobalState } from '@/store/index';

const { Text } = Typography;

interface AgentListClass {
  id: string;
  name: string;
  provider: string;
  description: string;
  type: string;
  instruction: string;
  isPublic: boolean;
  isHost: boolean;
  iconUrl: string;
  disabled: boolean;
  profiles: string[];
  labels: string[];
  workflowId: string;
  popoverVisible?: boolean;
  createdDateTime: string;
  is_editable: boolean;
}

const FormItem = Form.Item;

function List() {
  const navigate = useNavigate();
  const locale = useLocale();
  const dispatch = useDispatch();
  const agentDetailMenuName = useSelector((state: GlobalState) => state.agentDetailMenuName);
  const [listData, setListData] = useState<AgentListClass[]>([]);
  const Option = Select.Option;
  const [visibleCreateGroup, setVisibleCreateGroup] = useState(false);
  const [createGroupConfirmLoading, setCreateGroupConfirmLoading] =
    useState(false);
  const [form] = Form.useForm();
  const labelsState = Form.useFormState('labels', form) || {};

  // 添加分页相关状态
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchLabel, setSearchLabel] = useState<string>('');
  const [labelOptions, setLabelOptions] = useState<
    { value: string; label: string }[]
  >([]);
  const pageSize = 16;
  const cardBoxRef = useRef<HTMLDivElement>(null);

  const folderIconList = [folderIcon, folderIcon2, folderIcon3, folderIcon4];

  const [count, setCount] = useState<number>(0);
  const [showBackToTop, setShowBackToTop] = useState(false);

  // 处理搜索输入变化
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    setCurrentPage(1);
    setHasMore(true);
    setListData([]);
  };

  // 处理查询标签变化
  const handleSearchLabelChange = (value: string) => {
    setSearchLabel(value);
    setCurrentPage(1);
    setHasMore(true);
    setListData([]);
  };

  // 加载更多数据
  const loadMoreData = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    try {
      const nextPage = currentPage + 1;
      const params: any = {
        Pager: {
          Page: nextPage,
          Size: pageSize,
        },
      };

      if (searchValue) {
        params.agent_name = searchValue;
      }
      if (searchLabel) {
        params.label = searchLabel;
      }

      const response = await getAgentList(params);

      if (response.items && response.items.length > 0) {
        const newData: AgentListClass[] = response.items.map((item) => ({
          id: item.id,
          name: item.name,
          provider: item.plugin.name,
          type: item.type,
          description: item.description,
          instruction: item.instruction,
          isPublic: item.is_public,
          isHost: item.is_host,
          iconUrl: item.icon_url,
          disabled: item.disabled,
          workflowId: item.workflowId,
          createdDateTime: item.created_time,
          profiles: Array.isArray(item.profiles)
            ? item.profiles
              .map((p) => {
                try {
                  return typeof p === 'string'
                    ? p.replace(/[\[\]"\\]/g, '').trim()
                    : p;
                } catch (e) {
                  console.error('处理profile时出错:', e);
                  return '';
                }
              })
              .filter((p) => p && p.length > 0)
            : [],
          labels: item.labels,
          is_editable: item.is_editable,
        }));

        // 合并新旧数据
        setListData((prevData) => [...prevData, ...newData]);
        setCurrentPage(nextPage);

        if (response.items.length < pageSize) {
          setHasMore(false);
        }
      } else {
        setHasMore(false);
      }
    } catch (error) {
      Message.error({
        content: locale['menu.application.opreate.errMsg'],
      });
      console.error('加载更多智能体失败:', error);
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [
    currentPage,
    loading,
    hasMore,
    pageSize,
    locale,
    searchValue,
    searchLabel,
  ]);

  // 滚动监听函数
  const handleScroll = useCallback(() => {
    if (!cardBoxRef.current) return;

    const container = cardBoxRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;

    // 处理回到顶部按钮显示
    setShowBackToTop(scrollTop > 300);

    // 处理加载更多数据
    if (!loading && hasMore && scrollHeight - scrollTop - clientHeight < 150) {
      loadMoreData();
    }
  }, [loading, hasMore, loadMoreData]);

  // 独立监测容器和卡片位置关系
  useEffect(() => {
    if (!cardBoxRef.current || loading || !hasMore) return;

    const checkCardPosition = () => {
      const container = cardBoxRef.current;
      if (!container) return;

      const lastCard = container.querySelector(
        `.${styles.customCard}:last-child`
      );
      if (lastCard) {
        const containerBottom = container.getBoundingClientRect().bottom;
        const lastCardBottom = lastCard.getBoundingClientRect().bottom;
        const distanceToBottom = containerBottom - lastCardBottom;

        // 如果最后一个卡片到容器底部的距离大于100px
        if (distanceToBottom > 100) {
          loadMoreData();
        }
      }
    };

    // 创建 ResizeObserver 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      checkCardPosition();
    });

    // 监听容器大小变化
    resizeObserver.observe(cardBoxRef.current);

    // 初始检查
    checkCardPosition();

    return () => {
      resizeObserver.disconnect();
    };
  }, [loading, hasMore, loadMoreData, listData.length]);

  // 回到顶部
  const scrollToTop = useCallback(() => {
    if (!cardBoxRef.current) return;

    setShowBackToTop(false);

    cardBoxRef.current.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  }, []);

  useEffect(() => {
    getAgentListData();
    getAgentLabelsData();
  }, [searchValue, searchLabel, pageSize, locale]);

  const getAgentListData = useCallback(async () => {
    setLoading(true);
    try {
      const params: any = {
        Pager: {
          Page: 1,
          Size: pageSize,
        },
      };

      if (searchValue) {
        params.agent_name = searchValue;
      }
      if (searchLabel) {
        params.label = searchLabel;
      }

      const response = await getAgentList(params);

      setCount(response.count);

      const formattedData: AgentListClass[] = response.items.map((item) => ({
        id: item.id,
        name: item.name,
        provider: item.plugin.name,
        type: item.type,
        description: item.description,
        instruction: item.instruction,
        isPublic: item.is_public,
        isHost: item.is_host,
        iconUrl: item.icon_url,
        disabled: item.disabled,
        workflowId: item.workflowId,
        createdDateTime: item.created_time,
        profiles: Array.isArray(item.profiles)
          ? item.profiles
            .map((p) => {
              try {
                return typeof p === 'string'
                  ? p.replace(/[\[\]"\\]/g, '').trim()
                  : p;
              } catch (e) {
                console.error('处理profile时出错:', e);
                return '';
              }
            })
            .filter((p) => p && p.length > 0)
          : [],
        labels: item.labels,
        is_editable: item.is_editable,
      }));

      setListData(formattedData);
      setCurrentPage(1);

      if (response.items.length < pageSize) {
        setHasMore(false);
      }
    } catch (error) {
      Message.error({
        content: locale['menu.application.opreate.errMsg'],
      });
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  }, [pageSize, searchValue, locale, searchLabel]);

  const getAgentLabelsData = useCallback(async () => {
    try {
      const response = await getAgentLabelOptions();
      if (response && Array.isArray(response.data)) {
        const formattedLabels = response.data.map((label) => ({
          value: label,
          label: label,
        }));
        setLabelOptions(formattedLabels);
      }
    } catch (error) {
      console.error('获取标签列表失败:', error);
      Message.error({
        content: '获取标签列表失败',
      });
    }
  }, []);

  // 单独处理滚动事件监听
  useEffect(() => {
    const container = cardBoxRef.current;
    if (!container) return;

    // 使用防抖处理滚动事件，避免频繁触发
    let scrollTimeout: NodeJS.Timeout;

    const scrollListener = () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      scrollTimeout = setTimeout(() => {
        handleScroll();
      }, 50);
    };

    container.addEventListener('scroll', scrollListener);

    return () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      container.removeEventListener('scroll', scrollListener);
    };
  }, [handleScroll]);

  const comfirmDel = (event, agent) => {
    event.stopPropagation();
    const title = locale['menu.application.opreate.del'] + agent.name;
    const content = locale['menu.application.agent.delete.content'];
    Modal.confirm({
      style: {
        borderRadius: 16,
        padding: 24,
      },
      title: <div style={{ textAlign: 'left' }}>{title}</div>,
      content: <div style={{ color: '#86909C' }}>{content}</div>,
      cancelText: locale['cancelBut'],
      okText: locale['deleteBut'],
      okButtonProps: {
        status: 'danger',
      },
      icon: null,
      footer: (
        <div style={{ textAlign: 'right' }}>
          <Button
            style={{
              height: 40,
              width: 76,
              borderRadius: 8,
              backgroundColor: '#FFFFFF',
              border: '1px solid rgba(0, 0, 0, 0.08)',
            }}
            onClick={() => Modal.destroyAll()}
          >
            {locale['cancelBut']}
          </Button>
          <Button
            status="danger"
            style={{
              height: 40,
              width: 76,
              marginLeft: 8,
              borderRadius: 8,
              color: '#FFFFFF',
              backgroundColor: '#D54941',
            }}
            onClick={() => {
              return new Promise<void>((resolve, reject) => {
                deleteAgent(agent.id)
                  .then((r) => {
                    resolve();
                    if (r.data) {
                      Message.success({
                        content: locale['menu.application.opreate.okMsg'],
                      });
                      // 删除成功后刷新列表
                      getAgentListData();
                      Modal.destroyAll();
                    } else {
                      Message.error({
                        content: locale['menu.application.opreate.errMsg'],
                      });
                      Modal.destroyAll();

                    }
                  })
                  .catch((e) => {
                    reject(e);
                    Message.error({
                      content: locale['menu.application.opreate.errMsg'],
                    });
                    console.error(e);
                  });
              });
            }}
          >
            {locale['deleteBut']}
          </Button>
        </div>
      ),
    });
  };

  const createGroup = () => { };

  const formItemLayout = {
    labelCol: {
      span: 4,
    },
    wrapperCol: {
      span: 20,
    },
  };

  const updateBreadcrumbData = (newBreadcrumb) => {
    dispatch({
      type: 'update-breadcrumb-menu-name',
      payload: { breadcrumbMenuName: newBreadcrumb },
    });
  };

  const gotoCreate = () => {
    // 更新面包屑数据，使用详情页路径和卡片名称
    const breadcrumbData = new Map([[agentDetailMenuName, 'New Agent']]);
    updateBreadcrumbData(breadcrumbData);

    navigate('/agent/info', { state: { id: null } });
  };

  const gotoInfoPage = (item: AgentListClass) => {
    // 更新面包屑数据，使用详情页路径和卡片名称
    const breadcrumbData = new Map([[agentDetailMenuName, item.name]]);
    updateBreadcrumbData(breadcrumbData);

    navigate('/agent/info', { state: { id: item.id } });
  };

  const createGroupModal = () => {
    return (
      <Modal
        className={styles.customModal}
        title={locale['menu.application.create.group.title']}
        cancelText={locale['cancelBut']}
        okText={locale['saveBut']}
        visible={visibleCreateGroup}
        onOk={createGroup}
        confirmLoading={createGroupConfirmLoading}
        onCancel={() => setVisibleCreateGroup(false)}
      >
        <Form
          {...formItemLayout}
          form={form}
          labelCol={{
            style: { flexBasis: 90 },
          }}
          wrapperCol={{
            style: { flexBasis: 'calc(100% - 90px)' },
          }}
        >
          <FormItem field="name" rules={[{ required: true }]}>
            <RowComponent>
              <Text className={styles.label}>
                {locale['menu.application.create.group.form.name']}
              </Text>
              <Text className={styles.subLabel}>
                {locale['menu.application.create.group.form.subName']}
              </Text>
            </RowComponent>
            <Input
              placeholder={
                locale['menu.application.create.group.form.name.placeholder']
              }
            />
          </FormItem>
          <FormItem field="description">
            <Text className={styles.label}>
              {locale['menu.application.create.group.form.description']}
            </Text>
            <Input
              placeholder={
                locale[
                'menu.application.create.group.form.description.placeholder'
                ]
              }
            />
          </FormItem>
          <FormItem field="description">
            <RowComponent>
              <Text className={styles.label}>
                {locale['menu.application.create.group.form.label']}
              </Text>
            </RowComponent>
            <RowComponent>
              <Text className={styles.subLabel2}>
                {locale['menu.application.create.group.form.label.desc']}
              </Text>
            </RowComponent>
            <RowComponent>
              <Form.List field="labels">
                {(fields, { add }) => {
                  return (
                    <div className={styles.customBlock}>
                      {fields.map((item, index) => {
                        return (
                          <Grid.Row key={item.key}>
                            <Form.Item field={item.field}>
                              <InputTag
                                suffix={
                                  <IconClose
                                    onClick={() => {
                                      form.setFieldValue(`labels.${index}`, '');
                                    }}
                                  />
                                }
                                placeholder={locale[
                                  'menu.application.create.group.form.label.placeholder'
                                ].replace('{index}', index + 1)}
                              />
                            </Form.Item>
                          </Grid.Row>
                        );
                      })}
                      <RowComponent>
                        <Button
                          disabled={fields.length >= 3}
                          onClick={() => {
                            add();
                          }}
                          type="outline"
                          icon={<IconPlus />}
                        >
                          {
                            locale[
                            'menu.application.create.group.form.label.addbutton'
                            ]
                          }
                        </Button>
                      </RowComponent>
                    </div>
                  );
                }}
              </Form.List>
            </RowComponent>
          </FormItem>
        </Form>
      </Modal>
    );
  };

  const cardDom = () => {
    return (
      <div className={styles.customCardBox} ref={cardBoxRef}>
        {listData.map((item, i) => {
          return (
            <Card
              key={item.id}
              className={[styles.customCard]}
              hoverable

            >
              <div
                onClick={() => {
                  gotoInfoPage(item);
                }}
              >
                <RowComponent className={styles.rowStartCenter}>
                  <Image
                    src={item.iconUrl || folderIconList[i % 4]}
                    className={styles.folderIcon}
                  />
                  <div className={styles.name}>{item.name}</div>
                </RowComponent>

                <RowComponent
                  className={styles.rowStartCenter}
                  style={{ marginTop: 16 }}
                >
                  <Text className={styles.description}>{item.description}</Text>

                  {item.labels && item.labels.length > 0 && (
                    <RowComponent
                      className={styles.rowStartCenter}
                      style={{ marginTop: 8 }}
                    >
                      {item.labels.slice(0, 3).map((label, index) => (
                        <Text key={index} className={styles.profileTag}>
                          {label}
                        </Text>
                      ))}
                    </RowComponent>
                  )}
                </RowComponent>
              </div>


              <RowComponent className={styles.footer}>
                <div className={styles.statusContainer}>
                  <Text className={styles.metaText}>@{item.provider}</Text>
                </div>
                <div className={styles.hoverContainer}>
                  <Text className={styles.metaText}>
                    创建时间：{' '}
                    {new Date(item.createdDateTime)
                      .toLocaleDateString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                      })
                      .replace(/\//g, '/')}
                  </Text>
                  <Popover
                    trigger="click"
                    position="rt"
                    onVisibleChange={(visible) => {
                      setListData((prevData) =>
                        prevData.map((application) =>
                          application.id === item.id
                            ? { ...application, popoverVisible: visible }
                            : application
                        )
                      );
                    }}
                    content={
                      <span>
                        <p
                          className={styles.black}
                          onClick={(event) => {
                            event.stopPropagation();
                            if (item.is_editable) {
                              comfirmDel(event, item);
                            }
                          }}
                          style={{
                            cursor: item.is_editable ? 'pointer' : 'not-allowed',
                          }}
                        >
                          {locale['menu.application.opreate.del']}
                        </p>
                      </span>
                    }
                  >
                    <div
                      onClick={(e) => e.stopPropagation()}
                      className={`${styles.iconMoreContainer} ${item.popoverVisible ? styles.active : ''
                        }`}
                    >
                      <IconMore style={{ fontSize: 20, cursor: 'pointer' }} />
                    </div>
                  </Popover>
                </div>
              </RowComponent>
            </Card>
          );
        })}
        {loading && (
          <div className={styles.loadingContainer}>
            <Text className={styles.loadingText}>加载中...</Text>
          </div>
        )}
        {!loading && listData.length === 0 && (
          <div className={styles.emptyContainer}>
            <NotAgentIcon />
            <Text className={styles.emptyText}>未找到智能体</Text>
          </div>
        )}

        {/* 底部空白区域 */}
        <div style={{ width: '100%', gridColumn: '1 / -1' }}>
          {listData.length < count ? (
            <div
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(4, 1fr)',
                gap: '16px',
                width: '100%',
                padding: '0 0 24px 0',
              }}
            >
              {[1, 2, 3, 4].map((item) => (
                <Card
                  key={item}
                  className={[styles.customCard]}
                  hoverable
                  style={{ opacity: 0.5 }}
                >
                  <RowComponent className={styles.rowStartCenter}>
                    <div
                      className={styles.folderIcon}
                      style={{
                        background: 'rgba(0, 0, 0, 0.04)',
                        borderRadius: '4px',
                      }}
                    ></div>
                    <div
                      className={styles.name}
                      style={{
                        background: 'rgba(0, 0, 0, 0.04)',
                        height: '24px',
                        width: '70%',
                      }}
                    ></div>
                  </RowComponent>
                  <RowComponent
                    className={styles.rowStartCenter}
                    style={{ marginTop: 16 }}
                  >
                    <div
                      className={styles.description}
                      style={{ background: 'rgba(0, 0, 0, 0.04)' }}
                    ></div>
                  </RowComponent>

                  <RowComponent
                    className={styles.rowStartCenter}
                    style={{ marginTop: 8 }}
                  >
                    <div
                      className={styles.profileTag}
                      style={{
                        background: 'rgba(0, 0, 0, 0.04)',
                        width: '40px',
                      }}
                    ></div>
                    <div
                      className={styles.profileTag}
                      style={{
                        background: 'rgba(0, 0, 0, 0.04)',
                        width: '40px',
                      }}
                    ></div>
                  </RowComponent>

                  <RowComponent className={styles.footer}>
                    <div className={styles.statusContainer}>
                      <div
                        className={styles.metaText}
                        style={{
                          background: 'rgba(0, 0, 0, 0.04)',
                          height: '16px',
                          width: '60%',
                        }}
                      ></div>
                      <div
                        className={styles.statusWrapper}
                        style={{
                          backgroundColor: 'rgba(0, 0, 0, 0.04)',
                          width: '60px',
                          height: '24px',
                        }}
                      ></div>
                    </div>
                  </RowComponent>
                </Card>
              ))}
            </div>
          ) : (
            <div
              style={{ height: '100px', width: '100%', gridColumn: '1 / -1' }}
            >
              {listData.length < count ? '加载中...' : ''}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <div className={styles.customContainer}>
        <RowComponent
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingBottom: 16,
            borderBottom: '1px solid RGBA(0, 0, 0, 0.08)',
          }}
        >
          <RowComponent>
            <ButtonComponent
              onClick={() => {
                gotoCreate();
              }}
              className={[styles.blueBut, styles.butFont]}
              style={{ marginRight: 8 }}
              htmlType="submit"
            >
              {locale['menu.application.createAgent']}
            </ButtonComponent>
            {createGroupModal()}
          </RowComponent>
          <RowComponent className={styles.rowEndCenter}>
            <Text className={styles.countAppText}>
              {locale['menu.application.agent.count']?.replace(
                '{count}',
                count
              )}
            </Text>
            <Input
              className={styles.searchBox}
              prefix={<IconSearch />}
              placeholder={locale['menu.application.agent.search.placeholder']}
              value={searchValue}
              onChange={handleSearchChange}
              allowClear
            />
            <Select
              placeholder={locale['menu.application.agent.search.tags']}
              allowClear
              className={styles.selectBox}
              prefix={<IconScreen />}
              value={searchLabel || undefined}
              onChange={handleSearchLabelChange}
              triggerProps={{
                autoAlignPopupWidth: false,
                position: 'bl',
                className: 'agent-select-popup',
              }}
            >
              {labelOptions.map((option) => (
                <Option
                  key={option.value}
                  value={option.value}
                  style={{
                    color: '#00000052',
                  }}
                >
                  {option.label}
                </Option>
              ))}
            </Select>
          </RowComponent>
        </RowComponent>
        {cardDom()}
      </div>
      <div
        className={`${styles.backToTop} ${!showBackToTop ? styles.hidden : ''}`}
        onClick={scrollToTop}
      >
        <TopIcon />
        <span>返回顶部</span>
      </div>
    </>
  );
}

export default List;
