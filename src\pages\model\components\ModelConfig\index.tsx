import { useState, useEffect } from 'react';
import {
    Card,
    Button,
    Input,
    Typography,
    Space,
    Select,
    Popover,
    Modal,
    Message,
    Tag,
    Grid,
    Spin
} from '@arco-design/web-react';
import IconSearch from '@/assets/model/IconSearch.svg';
import IconModelTag from '@/assets/model/IconModelTag.svg';
import IconModelTag2 from '@/assets/model/IconModelTag2.svg';
import IconModelTag3 from '@/assets/model/IconModelTag3.svg';
import IconModelTag4 from '@/assets/model/IconModelTag4.svg';
import IconAction from '@/assets/model/IconAction.svg';
import IconEnable from '@/assets/model/iconEnable.svg';
import IconDisable from '@/assets/model/iconDisable.svg';
import IconClose from '@/assets/model/IconClose.svg';
import IconSortType from '@/assets/model/IconSortType.svg';
import IconEmptyModelConfig from '@/assets/model/IconEmptyModelConfig.svg';
import styles from './style/index.module.less';
import {
    getLlmModelConfigList,
    createLlmModelConfig,
    deleteLlmModelConfig,
} from '@/lib/services/llm-model-service';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { GlobalState } from '@/store/index';
import {
    LlmModelConfigResponse,
    LlmModelConfigCreateRequest,
} from '@/types/llmModelType';

const { Text, Paragraph } = Typography;
const { Row } = Grid;
const Option = Select.Option;

function ModelConfig() {
    const [sortType, setSortType] = useState('createTime'); // 默认按创建时间排序
    const [searchTerm, setSearchTerm] = useState('');
    const [modelConfigs, setModelConfigs] = useState<LlmModelConfigResponse[]>(
        []
    );
    const [filteredModelConfigs, setFilteredModelConfigs] = useState<
        LlmModelConfigResponse[]
    >([]);
    const [loading, setLoading] = useState(true); // 添加loading状态
    const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
    const [selectedModelId, setSelectedModelId] = useState<string | null>(null);
    const createModelConfigMenuName = useSelector(
        (state: GlobalState) => state.createModelConfigMenuName
    );
    const modelConfigDetailMenuName = useSelector(
        (state: GlobalState) => state.modelConfigDetailMenuName
    );
    const ModelIcons = [
        IconModelTag,
        IconModelTag2,
        IconModelTag3,
        IconModelTag4,
    ];
    const navigate = useNavigate();
    const dispatch = useDispatch();

    // 获取模型配置列表
    useEffect(() => {
        const fetchLlmModelConfigs = async () => {
            try {
                setLoading(true);
                const response = await getLlmModelConfigList();
                setModelConfigs(response);
            } catch (error) {
                console.error('获取模型配置列表失败:', error);
                Message.error('获取模型配置列表失败，请稍后重试');
            } finally {
                setLoading(false);
            }
        };
        fetchLlmModelConfigs();
    }, []);

    // 当模型配置列表、搜索词或排序方式改变时，更新过滤后的模型列表
    useEffect(() => {
        filterAndSortModelConfigs();
    }, [modelConfigs, searchTerm, sortType]);

    // 处理搜索输入变化
    const handleSearchChange = (value) => {
        setSearchTerm(value);
    };

    // 处理排序类型变化
    const handleSortChange = (value) => {
        setSortType(value);
    };

    // 过滤和排序模型配置数据
    const filterAndSortModelConfigs = () => {
        let result = [...modelConfigs];

        // 搜索过滤
        if (searchTerm) {
            const lowerCaseSearch = searchTerm.toLowerCase();
            result = result.filter((model) =>
                model.name.toLowerCase().includes(lowerCaseSearch)
            );
        }

        // 排序
        switch (sortType) {
            case 'createTime':
                result.sort(
                    (a, b) =>
                        new Date(b.createdTime || 0).getTime() -
                        new Date(a.createdTime || 0).getTime()
                );
                break;
            case 'updateTime':
                result.sort((a, b) => {
                    // 处理 updatedTime 可能为 null 的情况
                    const timeA = a.updatedTime ? new Date(a.updatedTime).getTime() : 0;
                    const timeB = b.updatedTime ? new Date(b.updatedTime).getTime() : 0;
                    return timeB - timeA;
                });
                break;
            case 'name':
                result.sort((a, b) => a.name.localeCompare(b.name));
                break;
            default:
                break;
        }

        setFilteredModelConfigs(result);
    };

    // 启用模型配置
    const handleEnable = async (modelId) => {
        try {
            const modelToEnable = modelConfigs.find((model) => model.id === modelId);
            if (!modelToEnable) {
                Message.error('找不到指定的模型配置');
                return;
            }

            const updateRequest: LlmModelConfigCreateRequest = {
                ...modelToEnable,
                isEnabled: true,
            };
            const success = await createLlmModelConfig(updateRequest);

            if (success) {
                setModelConfigs(
                    modelConfigs.map((model) =>
                        model.id === modelId ? { ...model, isEnabled: true } : model
                    )
                );
                Message.success('模型配置已启用');
            } else {
                Message.error('启用模型配置失败，请稍后重试');
            }
        } catch (error) {
            console.error('启用模型配置失败:', error);
            Message.error('启用模型配置失败，请稍后重试');
        }
    };

    // 禁用模型配置
    const handleDisable = async (modelId) => {
        try {
            const modelToDisable = modelConfigs.find((model) => model.id === modelId);
            if (!modelToDisable) {
                Message.error('找不到指定的模型配置');
                return;
            }

            const updateRequest: LlmModelConfigCreateRequest = {
                ...modelToDisable,
                isEnabled: false,
            };

            const success = await createLlmModelConfig(updateRequest);

            if (success) {
                setModelConfigs(
                    modelConfigs.map((model) =>
                        model.id === modelId ? { ...model, isEnabled: false } : model
                    )
                );
                Message.success('模型配置已取消启用');
            } else {
                Message.error('取消启用模型配置失败，请稍后重试');
            }
        } catch (error) {
            console.error('取消启用模型配置失败:', error);
            Message.error('取消启用模型配置失败，请稍后重试');
        }
    };

    // 删除模型配置确认
    const handleConfirmDelete = async () => {
        if (selectedModelId) {
            try {
                const success = await deleteLlmModelConfig(selectedModelId);
                if (success) {
                    Message.success('模型配置删除成功');
                    setConfirmDeleteVisible(false);
                    setSelectedModelId(null);
                    // 重新获取最新列表数据
                    try {
                        setLoading(true);
                        const response = await getLlmModelConfigList();
                        setModelConfigs(response);
                    } catch (error) {
                        console.error('获取模型配置列表失败:', error);
                        Message.error('获取模型配置列表失败，请稍后重试');
                    } finally {
                        setLoading(false);
                    }
                } else {
                    Message.error('删除模型配置失败，请稍后重试');
                }
            } catch (error) {
                console.error('删除模型配置失败:', error);
                Message.error('删除模型配置失败，请稍后重试');
            }
        }
    };

    //取消删除模型配置
    const handleCancelDelete = () => {
        setConfirmDeleteVisible(false);
        setSelectedModelId(null);
    };

    const updateBreadcrumbData = (newBreadcrumb) => {
        dispatch({
            type: 'update-breadcrumb-menu-name',
            payload: { breadcrumbMenuName: newBreadcrumb },
        });
    };

    const gotoCreate = (item?: { name: string }) => {
        const modelName = item ? item.name : '新增模型配置';
        const breadcrumbData = new Map([[createModelConfigMenuName, modelName]]);
        updateBreadcrumbData(breadcrumbData);
        navigate('/model/configcreate');
    };

    const gotoDetail = (model) => {
        dispatch({
            type: 'update-selected-modelconfig',
            payload: { selectedModelConfig: model },
        });
        const breadcrumbData = new Map([[modelConfigDetailMenuName, model.name]]);
        updateBreadcrumbData(breadcrumbData);
        navigate('/model/configdetail');
    };

    // 获取图标组件的函数
    const getModelIcon = (id) => {
        // 如果有自定义图标则使用自定义图标，否则根据id循环选择默认图标
        const model = modelConfigs.find((m) => m.id === id);
        if (model?.icon) {
            return (
                <img src={model.icon} alt="icon" style={{ width: 24, height: 24 }} />
            );
        }
        // 使用 id 的数字部分取模，循环使用图标数组
        const numericId = parseInt(id.match(/\d+/)?.[0] || '0');
        const index = numericId % ModelIcons.length;
        const IconComponent = ModelIcons[index];
        return <IconComponent />;
    };

    // 格式化日期函数
    const formatDate = (dateString) => {
        if (!dateString) return ''; // 处理空值
        const date = new Date(dateString);
        const year = date.getUTCFullYear();
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const day = String(date.getUTCDate()).padStart(2, '0');
        return `${year}/${month}/${day}`;
    };

    // 渲染内容
    const renderContent = () => {
        if (loading) {
            return (
                <div className={styles.loadingContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ padding: 24 }}>
                            <Spin tip="模型配置加载中..." />
                        </div>
                    </Space>
                </div>
            );
        }

        if (filteredModelConfigs.length === 0) {
            return (
                <div className={styles.emptyContainer}>
                    <Space direction='vertical' size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <IconEmptyModelConfig style={{ width: 80, height: 80 }} />
                        <Text type="secondary">{searchTerm ? '未找到匹配的模型配置' : '未找到模型配置'}</Text>
                    </Space>
                </div>
            );
        }

        return (
            <>
                {filteredModelConfigs.map((model) => (
                    <Card
                        key={model.id}
                        className={styles.modelCard}
                        onClick={(e) => {
                            e.stopPropagation();
                            gotoDetail(model);
                        }}
                    >
                        <Space className={styles.modelInfo} direction="vertical">
                            <Space className={styles.infoTag} size={12}>
                                <div className={styles.icon}>
                                    {model.icon ? (
                                        <img
                                            src={model.icon}
                                            alt="icon"
                                            style={{ width: 48, height: 48 }}
                                        />
                                    ) : (
                                        <span
                                            className={styles.icon}
                                            style={{ width: 48, height: 48 }}
                                        >
                                            {getModelIcon(model.id)}
                                        </span>
                                    )}
                                </div>
                                <div className={styles.name} style={{ cursor: 'pointer' }}>
                                    {model.name}
                                </div>
                            </Space>
                            {/* 多模态和图片生成 暂时隐藏 */}
                            {/* <Space className={styles.details} direction='vertical' size={0}>
                                <Space className={styles.multiModal} size={4} align='center'>
                                    {model.multiModal ? (
                                        <IconEnable style={{ width: 24, height: 24 }} />
                                    ) : (
                                        <IconDisable style={{ width: 24, height: 24 }} />
                                    )}
                                    <Text
                                        type="secondary"
                                        className={styles.label}
                                        style={{ color: model.multiModal ? '#2ba471' : '#d54941' }}
                                    >
                                        {model.multiModal ? '允许发送图片/视频' : '不允许发送图片/视频'}
                                    </Text>
                                </Space>
                                <Space className={styles.imageGeneration} size={4}>
                                    {model.imageGeneration ? (
                                        <IconEnable style={{ width: 24, height: 24 }} />
                                    ) : (
                                        <IconDisable style={{ width: 24, height: 24 }} />
                                    )}
                                    <Text
                                        type="secondary"
                                        className={styles.label}
                                        style={{ color: model.imageGeneration ? '#2ba471' : '#d54941' }}
                                    >
                                        {model.imageGeneration ? '允许生成图片' : '不允许生成图片'}
                                    </Text>
                                </Space>
                            </Space> */}
                            
                            {/* 多模态和图片生成 新逻辑 */}
                            <Space className={styles.details} direction='vertical' size={0}>
                                {model.multiModal && (
                                    <Space className={styles.multiModal} size={4} align='center'>
                                        <IconEnable style={{ width: 24, height: 24 }} />
                                        <Text
                                            type="secondary"
                                            className={styles.label}
                                            style={{ color: '#2ba471' }}
                                        >
                                            允许发送图片/视频
                                        </Text>
                                    </Space>
                                )}
                                {model.imageGeneration && (
                                    <Space className={styles.imageGeneration} size={4}>
                                        <IconEnable style={{ width: 24, height: 24 }} />
                                        <Text
                                            type="secondary"
                                            className={styles.label}
                                            style={{ color: '#2ba471' }}
                                        >
                                            允许生成图片
                                        </Text>
                                    </Space>
                                )}
                            </Space>
                            <Row className={styles.tags}>
                                {model.tags && model.tags.length > 0 && (
                                    <Space size="small">
                                        {model.tags.map((tag, index) => (
                                            <Tag key={index} color="arcoblue">{tag}</Tag>
                                        ))}
                                    </Space>
                                )}
                            </Row>
                        </Space>
                        <div className={styles.status}>
                            <Space size={4} className={styles.metaInfo}>
                                {/* <Text>{`@${model.createUserId}`}</Text> */}
                                <Text>@AI4C</Text>
                                <Text>|</Text>
                                <Text>
                                    {model.updatedTime
                                        ? `更新时间：${formatDate(model.updatedTime)}`
                                        : `创建时间：${formatDate(model.createdTime)}`}
                                </Text>
                            </Space>
                            <div className={styles.actionWrapper} style={{ height: '32px' }}>
                                <div className={`${styles.statusIndicator} ${model.isEnabled ? styles.enabled : styles.disabled}`}>
                                    <span>{model.isEnabled ? '启用' : '禁用'}</span>
                                </div>
                                <Popover
                                    trigger="click"
                                    position="right"
                                    className={styles.popoverContent}
                                    content={
                                        <Space className={styles.popoverContent} direction="vertical" size="mini">
                                            <Button
                                                className={`${styles.actionBtn} ${model.isEnabled ? styles.disableBtn : styles.enableBtn}`}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    model.isEnabled ? handleDisable(model.id) : handleEnable(model.id);
                                                }}
                                            >
                                                {model.isEnabled ? '禁用' : '启用'}
                                            </Button>
                                            <Button
                                                className={`${styles.actionBtn} ${styles.deleteBtn}`}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setConfirmDeleteVisible(true);
                                                    setSelectedModelId(model.id);
                                                }}
                                            >
                                                删除
                                            </Button>
                                        </Space>
                                    }
                                >
                                    <Button
                                        className={styles.triggerBtn}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                        }}>
                                        <IconAction />
                                    </Button>
                                </Popover>
                            </div>
                        </div>
                    </Card>
                ))}
            </>
        );
    };

    return (
        <div className={styles.modelLibrary}>
            <div className={styles.header}>
                <Button
                    type="primary"
                    className={styles.addModelConfigBtn}
                    onClick={() => gotoCreate()}
                >
                    配置模型
                </Button>
                <Space size={'small'}>
                    <Text className={styles.modelNumber}>
                        共 {filteredModelConfigs.length} 个模型配置
                    </Text>
                    <Input
                        prefix={<IconSearch />}
                        placeholder="AI搜索..."
                        style={{ width: 240 }}
                        value={searchTerm}
                        onChange={handleSearchChange}
                        allowClear
                    />
                    <Select
                        prefix={<IconSortType />}
                        placeholder="按创建时间排序"
                        style={{ width: 160 }}
                        onChange={handleSortChange}
                        value={sortType}
                    >
                        <Option value="createTime">按创建时间排序</Option>
                        <Option value="updateTime">按更新时间排序</Option>
                        <Option value="name">按名称排序</Option>
                    </Select>
                </Space>
            </div>
            <div className={styles.content}>
                {renderContent()}
            </div>

            {/* 删除模型确认弹窗 */}
            <Modal
                visible={confirmDeleteVisible}
                title="删除模型配置"
                onCancel={handleCancelDelete}
                closeIcon={<IconClose />}
                className={styles.confirmDeleteModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <Text className={styles.modalContentText}>
                        模型配置{' '}
                        {modelConfigs.find((model) => model.id === selectedModelId)?.name ||
                            ''}{' '}
                        将被删除，请确认您是否要删除？
                    </Text>
                </div>
                <div className={styles.modalFooter}>
                    <Space>
                        <Button
                            onClick={handleCancelDelete}
                            className={styles.cancelDeleteBtn}
                        >
                            取消
                        </Button>
                        <Button
                            onClick={handleConfirmDelete}
                            className={styles.confirmDeleteBtn}
                        >
                            删除
                        </Button>
                    </Space>
                </div>
            </Modal>
        </div>
    );
}

export default ModelConfig;
