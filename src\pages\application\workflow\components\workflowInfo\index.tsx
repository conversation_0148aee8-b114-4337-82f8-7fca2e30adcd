import { useEffect, useState, useMemo, useRef } from 'react';
import { useLocation } from 'react-router-dom';

function WorkflowInfo() {
    const location = useLocation<{ id: string }>();
    const { definitionId } = location.state || {};

    const iframeRef = useRef(null);

    const iframeUrl = useMemo(() => {
        // return definitionId ? `/workflows/definitions` : null
        return definitionId ? `/workflows/definitions/${definitionId}/edit` : null
        // return definitionId ? `https://elasa-agent-team.ai4c.cn/workflows/definitions/${definitionId}/edit` : null
    }, [definitionId])

    useEffect(() => {
        const iframe = iframeRef.current;
        if (!iframe) return;
        // 用于隐藏侧边栏
        const handleLoad = () => {
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        const nodes = Array.from(mutation.addedNodes);
                        nodes.forEach((node) => {
                            if (node.nodeType === 1) {
                                const element = node as Element;
                                if (element.matches('div[data-index="1"][style*="flex-basis: 35%;"]')) {
                                    console.log("找到目标元素1:", element);
                                    (element as HTMLElement).style.display = 'none';
                                    observer.disconnect(); // 停止监听
                                }
                                if (element.matches('div[class*="rz-splitter-bar rz-splitter-bar-resizable"]')) {
                                    console.log("找到目标元素2:", element);
                                    (element as HTMLElement).style.display = 'none';
                                    observer.disconnect(); // 停止监听
                                }
                                if (element.matches('div[data-index="0"][style*="flex-basis: 65%;"]')) {
                                    console.log("找到目标元素3:", element);
                                    (element as HTMLElement).style.flexBasis = '95%';
                                    observer.disconnect(); // 停止监听
                                }
                            }
                        });
                    });
                });

                observer.observe(iframeDoc, { childList: true, subtree: true });
            } catch (e) {
                console.error('无法访问 iframe DOM:', e);
            }
        };

        iframe.addEventListener('load', handleLoad);
        return () => iframe.removeEventListener('load', handleLoad);
    }, []);

    return (
        <>
            <iframe ref={iframeRef} src={iframeUrl} id="myIframe" style={{ border: 'none', width: '100%', height: 'calc(100vh - 100px)' }}></iframe>
        </>
    );
}

export default WorkflowInfo; 