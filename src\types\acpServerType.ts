// src/types/acpServerType.ts

/**
 * ACP服务器相关类型定义
 */

/**
 * 创建AcpServer接口
 */
export interface AcpServer {
    id?: string;              // 服务器ID（可选）
    // server_id: string;        // 服务ID
    name: string;             // 名称
    transport_type: number;   // 传输类型（数字）
    location?: string;        // 位置（可选）
    description?: string;     // 描述（可选）
    is_available?: boolean;   // 是否可用（可选）
    query_params?: {          // 查询参数
        [key: string]: string;
    };
    http_transport_model?: {  // SSE客户端传输模型
        name: string;                      // 客户端名称
        connection_timeout_seconds: number; // 初始连接超时时间（秒）
        additional_headers: {              // 自定义HTTP请求头
            [key: string]: string;
        };
    };
}

/**
 * AcpServer列表响应数据接口
 */
export interface AcpServerResponse {
    id: string;               // 服务器ID
    created_time: string;      // 创建时间
    create_user_id: string;     // 创建用户ID
    updated_time: string;      // 更新时间
    update_user_id: string;     // 更新用户ID
    name: string;             // 名称
    transport_type: number;    // 传输类型
    location: string;         // 位置
    is_available: boolean;       // 是否可用
    description: string;       // 描述
    query_params?: {          // 查询参数
        [key: string]: string;
    };
    http_transport_model?: {  // SSE客户端传输模型
        name: string;                      // 客户端名称
        connection_timeout_seconds: number; // 初始连接超时时间（秒）
        additional_headers: {              // 自定义HTTP请求头
            [key: string]: string;
        };
    };
}

/**
 * AcpServer传输类型接口
 */
export interface TransportType {
    [key: string]: number;  // 传输类型名称: 传输类型ID 映射
}

/**
 * AcpTool接口
 */
export interface AcpTools {
    protocolTool: {
        name: string;
        description: string;
        inputSchema: {
            properties: {
                [key: string]: {
                    title: string;
                    type: string;
                    anyOf?: Array<{type: string}>;
                    default?: any;
                }
            };
            required?: string[];
            title: string;
            type: string;
        };
        annotations: any;
    };
    name: string;
    description: string;
    jsonSchema: {
        properties: {
            [key: string]: {
                title: string;
                type: string;
                anyOf?: Array<{type: string}>;
                default?: any;
            }
        };
        required?: string[];
        title: string;
        type: string;
    };
    jsonSerializerOptions: {
        converters: any[];
        typeInfoResolver: any;
        typeInfoResolverChain: any[];
        allowOutOfOrderMetadataProperties: boolean;
        allowTrailingCommas: boolean;
        defaultBufferSize: number;
        encoder: any;
        dictionaryKeyPolicy: any;
        ignoreNullValues: boolean;
        defaultIgnoreCondition: number;
        numberHandling: number;
        preferredObjectCreationHandling: number;
        ignoreReadOnlyProperties: boolean;
        ignoreReadOnlyFields: boolean;
        includeFields: boolean;
        maxDepth: number;
        propertyNamingPolicy: any;
        propertyNameCaseInsensitive: boolean;
        readCommentHandling: number;
        unknownTypeHandling: number;
        unmappedMemberHandling: number;
        writeIndented: boolean;
        indentCharacter: string;
        indentSize: number;
        referenceHandler: any;
        newLine: string;
        respectNullableAnnotations: boolean;
        respectRequiredConstructorParameters: boolean;
        isReadOnly: boolean;
    };
    additionalProperties: {
        Strict: boolean;
    };
    underlyingMethod: any;
}

/**
 * 测试ACP服务器连接的响应接口
 */
export interface AcpServerTestResponse {
    success: boolean;
    code: number;
    message: string;
    data: {
        server_capabilities: any;
        server_info: any;
        server_instructions: any;
    };
}

/**
 * 探测ACP服务器可用性响应接口
 */
export interface AcpServerAvailableResponse {
    success: boolean;
    message?: string;
} 