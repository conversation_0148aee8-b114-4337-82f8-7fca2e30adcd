import React, { useEffect, useRef, useState } from 'react';
import { Card, Spin } from '@arco-design/web-react';
import { Column } from '@antv/g2plot';
import styles from './style/TokenCostQuarterly.module.less';
import IconTokenCostQuarterly from '@/assets/dashboard/TokenCostQuarterly.svg'


type DataType = {
    date: string;
    count: number;
};

const TokenCostQuarterly = () => {
    const chartRef = useRef(null);
    const containerRef = useRef(null);
    const [loading, setLoading] = useState(false);
    const [chartHeight, setChartHeight] = useState(300); // 默认高度

    // 季度数据，根据设计稿提供
    const quarterData: DataType[] = [
        { date: '第1季度', count: 2200 },
        { date: '第2季度', count: 5400 },
        { date: '第3季度', count: 4600 },
        { date: '第4季度', count: 3180 },
    ];

    const updateChartSize = () => {
        if (!containerRef.current) return;

        // 根据容器宽度计算适当的高度
        const parent = containerRef.current.parentElement;
        const containerWidth = parent.clientWidth;

        // 使用比例来确定高度，并确保最小高度为200px
        const calculatedHeight = Math.max(containerWidth * 0.33, 200);

        setChartHeight(calculatedHeight);

        // 如果图表已经初始化，更新其尺寸
        if (chartRef.current) {
            chartRef.current.changeSize(containerWidth, calculatedHeight);
        }
    };

    const initChart = () => {
        if (!containerRef.current) return;

        // 获取容器尺寸
        const containerWidth = containerRef.current.clientWidth;

        // 找出最大值，用于设置Y轴刻度
        const maxValue = Math.max(...quarterData.map(item => item.count));
        // 计算合适的Y轴最大值，避免顶部空白过多
        const yAxisMax = Math.ceil(maxValue * 1.1);

        const column = new Column(containerRef.current, {
            data: quarterData,
            padding: [20, 10, 20, 20],
            xField: 'date',
            yField: 'count',
            color: '#4455f2',
            autoFit: true,
            xAxis: {
                grid: null,
                line: {
                    style: {
                        stroke: '#f5f5f5', // 设置X轴线条的颜色
                    },
                },
                label: {
                    style: {
                        fill: '#adadad', // 文字颜色
                        fontSize: 12, // 文字大小
                        fontWeight: 'normal', // 文字粗细
                    },
                    offset: 8, // 与轴线的距离
                    autoRotate: false, // 禁止自动旋转
                    autoHide: true, // 自动隐藏重叠的标签
                },
            },
            yAxis: {
                grid: {
                    line: {
                        style: {
                            stroke: '#f5f5f5',
                        },
                    },
                },
                // 设置固定刻度，避免重复
                min: 0,
                max: yAxisMax,
                // 指定固定的刻度间隔，保证刻度均匀且不重复
                tickCount: 5, // 减少刻度数量，避免拥挤
                label: {
                    formatter: (val) => {
                        const value = parseFloat(val);
                        // 确保0显示为0
                        if (value === 0) return '0';
                        // 1000及以上的值用k表示
                        if (value >= 1000) {
                            return `${(value / 1000).toFixed(0)}k`;
                        }
                        // 小于1000的值直接显示
                        return `${value}`;
                    },
                }
            },
            columnStyle: {
                radius: [4, 4, 4, 4], // 柱状图圆角
            },
            tooltip: {
                showMarkers: false,
                formatter: (datum) => {
                    return {
                        name: '费用',
                        value: datum.count,
                    };
                }
            },
            state: {
                active: {
                    style: {
                        fill: '#6b7afb', // 悬浮时的颜色
                    },
                },
            },
            // 添加柱状图的间距设置
            columnWidthRatio: 0.6, // 增加柱状图宽度占比
            minColumnWidth: 20, // 最小柱宽
            maxColumnWidth: 40, // 最大柱宽
            height: chartHeight,
        });

        chartRef.current = column;
        column.render();
    };  

    useEffect(() => {
        // 初始设置
        updateChartSize();
        initChart();

        // 添加窗口大小变化监听
        const handleResize = () => {
            updateChartSize();
        };

        window.addEventListener('resize', handleResize);

        // 清理函数
        return () => {
            window.removeEventListener('resize', handleResize);
            if (chartRef.current) {
                chartRef.current.destroy();
            }
        };
    }, []);

    return (
        <Card
            title={
                <div className={styles.cardHeader}>
                    <div className={styles.HeaderTag}>
                        <IconTokenCostQuarterly />
                        <div className={styles.title}>每季度Token费用统计（¥）</div>
                    </div>
                </div>
            }
            className={styles.card}
        >
            <Spin loading={loading} style={{ width: '100%' }}>
                <div
                    ref={containerRef}
                    className={styles.chart}
                    style={{ height: `${chartHeight}px` }}
                />
            </Spin>
        </Card>
    );
};

export default TokenCostQuarterly;
