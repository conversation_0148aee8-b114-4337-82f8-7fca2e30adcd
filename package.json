{"name": "arco-design-pro", "version": "1.0.0", "description": "Arco Design Pro", "type": "module", "engines": {"node": ">=22.12.0"}, "scripts": {"start": "vite", "dev": "vite --port 3000", "preview": "vite preview", "build": "vite build", "eslint": "eslint src/ --ext .ts,.tsx,.js,.jsx --fix --cache", "stylelint": "stylelint 'src/**/*.less' 'src/**/*.css' --fix --cache", "pre-commit": "pretty-quick --staged && npm run eslint && npm run stylelint", "check-circular": "madge --circular --extensions ts,tsx,js,jsx src/", "dependency-graph": "madge --image dependency-graph.svg --extensions ts,tsx,js,jsx src/", "analyze-deps": "madge --summary --extensions ts,tsx,js,jsx src/"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@antv/data-set": "^0.11.8", "@antv/g2plot": "^2.4.33", "@arco-design/color": "^0.4.0", "@arco-design/web-react": ">=2.0.0", "@arco-themes/react-arco-pro": "^0.0.7", "@js-preview/excel": "^1.7.14", "@microsoft/signalr": "^8.0.7", "@monaco-editor/react": "4.6", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^4.36.1", "@turf/turf": "^6.5.0", "@xyflow/react": "^12.5.5", "ahooks": "^3.7.8", "antd": "^5.25.0", "arco-design-pro": "^2.8.1", "axios": "^0.24.0", "bizcharts": "^4.1.11", "class-variance-authority": "^0.7.1", "classnames": "^2.3.1", "clsx": "^1.2.1", "cmdk": "^0.2.1", "copy-to-clipboard": "^3.3.1", "dayjs": "^1.11.13", "dompurify": "^2.5.8", "eventsource-parser": "^1.1.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^6.1.8", "input-otp": "^1.2.4", "intl-messageformat": "^10.7.15", "jszip": "^3.10.1", "keycloak-js": "^24.0.1", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "mammoth": "1.9.1", "mockjs": "^1.1.0", "monaco-editor": "^0.52.2", "nanoid": "^5.1.5", "next-themes": "^0.2.1", "nprogress": "^0.2.0", "pdfjs-dist": "2.16.105", "query-string": "^6.13.8", "rc-tween-one": "^3.0.6", "react": "^18.2.0", "react-color": "^2.18.1", "react-dom": "^18.2.0", "react-hook-form": "^7.45.4", "react-i18next": "^15.6.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^8.0.7", "react-pdf-highlighter": "6.1.0", "react-redux": "^8.1.3", "react-router": "^6.26.0", "react-router-dom": "^6.26.0", "react-sortablejs": "^6.1.4", "react-string-replace": "^1.1.1", "recharts": "^2.15.1", "redux": "^4.1.2", "sonner": "^0.7.4", "sortablejs": "^1.15.6", "tailwind-merge": "^1.14.0", "umi-request": "^1.4.0", "uuid": "^8.3.2"}, "devDependencies": {"@arco-design/webpack-plugin": "^1.6.0", "@arco-plugins/vite-react": "^1.3.3", "@svgr/webpack": "^5.5.0", "@tailwindcss/postcss": "^4.0.7", "@tailwindcss/vite": "^4.1.11", "@types/dompurify": "^3.0.5", "@types/jszip": "^3.4.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vitejs/plugin-react": "^4.3.0", "autoprefixer": "^10.4.20", "eslint": "^8.10.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.27.1", "eslint-plugin-react-hooks": "^4.3.0", "husky": "^7.0.2", "less": "^4.1.2", "less-loader": "7.3.0", "madge": "^8.0.0", "postcss": "^8.5.3", "postcss-less": "4", "prettier": "^2.4.1", "pretty-quick": "^3.1.2", "stylelint": "^14.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-standard": "^24.0.0", "tailwindcss": "^4.0.7", "typescript": "^5.8.3", "vite": "^7.0.3", "vite-plugin-svgr": "^4.3.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --cache"], "*.{css, less}": ["stylelint --fix"]}, "peerDependencies": {"@arco-design/web-react": ">=2.0.0"}}