import React, { useState } from 'react';
import { Tabs } from '@arco-design/web-react';
import { useNavigate, useLocation } from 'react-router-dom';
import UserInfo from '../UserInfo';
import UserSetting from '../UserSetting';
import styles from './style/index.module.less';

const { TabPane } = Tabs;

function UserTab() {
    const navigate = useNavigate();
    const location = useLocation();
    const [activeTab, setActiveTab] = useState('account');

    // 处理Tab切换
    const handleTabChange = (key) => {
        setActiveTab(key);
        // if (key === 'account') {
        //     history.push('/user');
        // } else if (key === 'setting') {
        //     history.push('/user/setting');
        // }
    };

    return (
        <div className={styles.userTabContainer}>
                <Tabs activeTab={activeTab} onChange={handleTabChange} className={styles.tabs}>
                    <TabPane key="account" title="账户">
                        {activeTab === 'account' && <UserInfo />}
                    </TabPane>
                    {/* <TabPane key="setting" title="设置">
                        {activeTab === 'setting' && <UserSetting />}
                    </TabPane> */}
                </Tabs>
        </div>
    );
}

export default UserTab; 