.addActionContainer {
    height: calc(100vh - 88px); // Ensure the container fills the viewport height
    display: flex;
    flex-direction: column;

    .addActionTitle {
        font-weight: 600;
        font-size: 20px;
        color: #333333;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f5;
    }

    .actionIcon {
        font-weight: 600;
        font-size: 14px;
        color: #5c5c5c;
    }

    .required {
        font-weight: 400;
        font-size: 12px;
        color: #4b5cf2;
    }

    .Card {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        border-left: 1px solid #ebebeb;
    }

    .descriptionInput,
    .actionNameInput,
    .appInput,
    .taskActionInput {
        padding: 8px 12px;
        background-color: transparent;
        border-radius: 4px;
        border: 1px solid #ebebeb;
    }

    .descriptionInput {
        height: 88px;
        resize: none;
        /* 禁用调整大小 */

        ::placeholder {
            font-weight: 400;
            font-size: 14px;
            color: #d6d6d6;
        }
    }


    .upload {
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #ebebeb;
        border-radius: 4px;
        padding: 8px 16px 8px 8px;
        font-weight: 400;
        font-size: 14px;
        color: #5c5c5c;
    }

    :global(.arco-form-item) {
        margin-bottom: 24px;
    }

    :global(.arco-form-label-item) {
        font-weight: 600;
        font-size: 14px;
        color: #5c5c5c;
    }

    :global(.arco-space-item) {
        margin-bottom: 0;
    }

    :global(.arco-tag-checked) {
        padding: 8px 16px 8px 8px;
        background-color: white;
        border-radius: 4px;
        border: 1px solid #ebebeb;
        height: auto;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    :global(.arco-tag-icon) {
        box-sizing: border-box;
        height: 24px;
        width: 24px;
    }

    :global(.arco-card-size-default .arco-card-body) {
        padding: 0;
    }

    :global(.arco-switch-checked) {
        background-color: #4b5cf2;
    }

    :global(.arco-input-inner-wrapper) {
        padding: 8px;
        background-color: transparent;
        border-radius: 4px;
        border: 1px solid #ebebeb;

        ::placeholder {
            font-weight: 400;
            font-size: 14px;
            color: #d6d6d6;
        }

        :global(.arco-input) {
            padding-top: 0;
            padding-bottom: 0;
            padding-left: 8px;
        }
    }

    :global(.arco-form) {
        height: 100%;
    }

    :global(.arco-row) {
        height: 100%;
    }

    :global(.arco-col-12) {
        height: 100%;
    }

    .cancelButton,
    .submitButton {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px 24px;
        border-radius: 4px;
        font-weight: 600;
        font-size: 14px;
    }

    .cancelButton {
        background-color: #fafafa;
        color: #5c5c5c;
    }

    .submitButton {
        background-color: #4b5cf2;
    }

    .chooseRowBox {
        cursor: pointer;
        width: calc(100% - 24px);
        border-radius: 4px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 12px;

        .chooseRowCol {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            &.left {
                .chooseRowColImg {
                    img {
                        width: 32px;
                        height: 32px;
                    }
                }

                .chooseName {
                    font-size: 14px;
                    font-weight: 600;
                    color: RGBA(0, 0, 0, 0.65);
                    margin-left: 8px;
                }

                .chooseCount {
                    font-size: 14px;
                    font-weight: 400;
                    color: RGBA(0, 0, 0, 0.35);
                    margin-left: 24px;

                    &.count {
                        color: RGBA(68, 85, 242, 0.95);
                    }
                }
            }

            &.right {
                .operateText {
                    font-size: 12px;
                    font-weight: 500;
                    color: #5c5c5c;
                }
            }
        }
    }

    .addApplication,
    .addLabelBut,
    .addTaskAction {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 8px 16px 8px 8px;
        background-color: transparent;
        border: 1px solid #ebebeb;
        border-radius: 4px;
        font-weight: 400;
        font-size: 14px;
        color: #5c5c5c;

        .operateText {
            margin-left: 4px;
        }
    }

    .addLabelBut,
    .addApplication {
        margin-top: 8px;
    }

    .triggerCard {
        height: calc(100vh - 200px);
        overflow: hidden;
        overflow-y: auto;

        .taskExecution,
        .triggerCondition {
            font-weight: 600;
            font-size: 14px;
            color: #5c5c5c;
            margin-bottom: 4px;
        }

        .triggerEvent,
        .controlTag,
        .bindApp {
            color: #adadad;
            font-size: 12px;
        }
    }

    .controlTagInput {
        margin-bottom: 0px;
        margin-top: 8px;
    }

    .selectedItemList {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 8px;
        border: 1px solid #ebebeb;
        border-radius: 4px;
        height: 100%;
        margin-top: 8px;
    }

    .selectedList {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-top: 8px;
        padding: 12px;
        border: 1px solid #ebebeb;
        border-radius: 4px;
    }

    .selectedItemRow {
        display: flex;
        align-items: center;
        padding: 4px;
        height: 32px;

        &:last-child {
            border-bottom: none;
        }
    }

    .selectedItemCol {
        display: flex;
        align-items: center;
        width: 100%;
    }
    
    .selectedItemText {
        font-weight: 500;
        font-size: 14px;
        color: #5c5c5c;
        flex: 1;
    }

    .dragHandle {
        cursor: move;
        margin-right: 8px;
        font-size: 16px;
        color: #999;
    }

    .deleteIcon {
        cursor: pointer;
        margin-left: 8px;
    }

}