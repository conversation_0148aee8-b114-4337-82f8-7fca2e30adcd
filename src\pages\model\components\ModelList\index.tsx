import { useState, useEffect } from 'react';
import { Card, Button, Input, Typography, Space, Select, Popover, Modal, Message, Tag, Spin } from '@arco-design/web-react';
// import IconAzure from '../assets/IconModelTag.svg';
import IconModelTag from '@/assets/model/IconModelTag.svg';
import IconModelTag2 from '@/assets/model/IconModelTag2.svg';
import IconModelTag3 from '@/assets/model/IconModelTag3.svg';
import IconModelTag4 from '@/assets/model/IconModelTag4.svg';
import IconSearch from '@/assets/model/IconSearch.svg';
// import IconEnabled from '../assets/enabledIcon.svg';
// import IconDisabled from '../assets/disabledIcon.svg';
import IconAction from '@/assets/model/IconAction.svg';
import IconEnable from '@/assets/model/iconEnable.svg';
import IconDisable from '@/assets/model/iconDisable.svg';
import IconClose from '@/assets/model/IconClose.svg';
import IconSortType from '@/assets/model/IconSortType.svg';
import IconEmptyModel from '@/assets/model/IconEmptyModel.svg';
import styles from './style/index.module.less';
import { getLlmModelList, createLlmModel, deleteLlmModel } from '@/lib/services/llm-model-service';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { GlobalState } from '@/store/index';
import { LlmModelCreateRequest } from '@/types/llmModelType';


const { Text } = Typography;
const Option = Select.Option;

const ModelList = () => {
    const [sortType, setSortType] = useState('default');
    const [searchTerm, setSearchTerm] = useState('');
    const [models, setModels] = useState([]); // 添加状态来存储模型数据
    const [loading, setLoading] = useState(true); // 添加loading状态
    const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false); // 控制删除确认弹窗的可见性
    const [selectedModelId, setSelectedModelId] = useState<string | null>(null); // 存储当前选中模型的 ID
    const createModelMenuName = useSelector((state: GlobalState) => state.createModelMenuName);
    const modelDetailMenuName = useSelector((state: GlobalState) => state.modelDetailMenuName);
    const ModelIcons = [IconModelTag, IconModelTag2, IconModelTag3, IconModelTag4];
    const navigate = useNavigate();
    const dispatch = useDispatch();

    // 获取模型列表数据的函数
    const fetchLlmModels = async () => {
        try {
            setLoading(true);
            const response = await getLlmModelList();
            setModels(response);
        } catch (error) {
            console.error('获取模型列表失败:', error);
            Message.error('获取模型列表失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchLlmModels();
    }, []);

    const handleSearchChange = (value) => {
        setSearchTerm(value);
    };

    // 过滤模型列表
    const filteredModels = models.filter(model =>
        model.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // 根据排序类型对数据进行排序
    const sortedModels = [...filteredModels].sort((a, b) => {
        switch (sortType) {
            case 'createTime':
                return new Date(b.createdTime).getTime() - new Date(a.createdTime).getTime();
            case 'tokenPrice':
                return a.promptCost - b.promptCost;
            case 'provider':
                return a.llmProviderId.localeCompare(b.llmProviderId);
            case 'name':
                return a.name.localeCompare(b.name);
            default:
                return 0;
        }
    });

    const handleEnable = async (modelId) => {
        try {
            const modelToEnable = models.find(model => model.id === modelId);
            if (!modelToEnable) {
                Message.error('找不到指定的模型');
                return;
            }

            const updateRequest: LlmModelCreateRequest = {
                ...modelToEnable,
                isEnabled: true
            };
            const success = await createLlmModel(updateRequest);

            if (success) {
                setModels(models.map(model =>
                    model.id === modelId ? { ...model, isEnabled: true } : model
                ));
                Message.success('模型已启用');
            } else {
                Message.error('启用模型失败，请稍后重试');
            }
        } catch (error) {
            console.error('启用模型失败:', error);
            Message.error('启用模型失败，请稍后重试');
        }
    };

    const handleDisable = async (modelId) => {
        try {
            const modelToDisable = models.find(model => model.id === modelId);
            if (!modelToDisable) {
                Message.error('找不到指定的模型');
                return;
            }

            const updateRequest: LlmModelCreateRequest = {
                ...modelToDisable,
                isEnabled: false
            };

            const success = await createLlmModel(updateRequest);

            if (success) {
                setModels(models.map(model =>
                    model.id === modelId ? { ...model, isEnabled: false } : model
                ));
                Message.success('模型已取消启用');
            } else {
                Message.error('取消启用模型失败，请稍后重试');
            }
        } catch (error) {
            console.error('取消启用模型失败:', error);
            Message.error('取消启用模型失败，请稍后重试');
        }
    };

    const handleConfirmDelete = async () => {
        if (selectedModelId) {
            try {
                const success = await deleteLlmModel(selectedModelId);
                if (success) {
                    Message.success('模型删除成功');
                    setConfirmDeleteVisible(false);
                    setSelectedModelId(null);
                    // 重新获取最新的列表数据
                    fetchLlmModels();
                } else {
                    Message.error('删除模型失败，请稍后重试');
                }
            } catch (error) {
                console.error('删除模型失败:', error);
                Message.error('删除模型失败，请稍后重试');
            }
        }
    };

    const handleCancelDelete = () => {
        setConfirmDeleteVisible(false);
        setSelectedModelId(null);
    };

    const updateBreadcrumbData = (newBreadcrumb) => {
        dispatch({
            type: 'update-breadcrumb-menu-name',
            payload: { breadcrumbMenuName: newBreadcrumb },
        });
    };

    const gotoCreate = (item?: { name: string }) => {
        const modelName = item ? item.name : '新增模型';
        const breadcrumbData = new Map([[createModelMenuName, modelName]]);
        updateBreadcrumbData(breadcrumbData);
        navigate('/model/create');
    };

    const gotoDetail = (model) => {
        dispatch({
            type: 'update-selected-model',
            payload: { selectedModel: model },
        });
        const breadcrumbData = new Map([[modelDetailMenuName, model.name]]);
        updateBreadcrumbData(breadcrumbData);
        navigate('/model/detail');
    };

    // 获取图标组件的函数
    const getModelIcon = (id) => {
        const model = models.find(m => m.id === id);
        if (model?.icon) {
            return <img src={model.icon} alt="icon" style={{ width: 24, height: 24 }} />;
        }
        // 使用 id 的数字部分取模，循环使用图标数组
        const numericId = parseInt(id.match(/\d+/)?.[0] || '0');
        const index = numericId % ModelIcons.length;
        const IconComponent = ModelIcons[index];
        return <IconComponent />;
    };

    // 格式化日期函数
    const formatDate = (dateString) => {
        if (!dateString) return ''; // 处理空值
        const date = new Date(dateString);
        const year = date.getUTCFullYear();
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const day = String(date.getUTCDate()).padStart(2, '0');
        return `${year}/${month}/${day}`;
    };

    // 渲染内容
    const renderContent = () => {
        if (loading) {
            return (
                <div className={styles.loadingContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ padding: 24 }}>
                            {/* 加载中内容 */}
                            <Spin tip="模型加载中..." />
                        </div>
                    </Space>
                </div>
            );
        }

        if (sortedModels.length === 0) {
            return (
                <div className={styles.emptyContainer}>
                    <Space direction='vertical' size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <IconEmptyModel style={{ width: 80, height: 80 }} />
                        <Text type="secondary">{searchTerm ? '未找到匹配的模型' : '未找到模型'}</Text>
                    </Space>
                </div>
            );
        }

        return (
            <>
                {sortedModels.map((model) => (
                    <Card key={model.id} className={styles.modelCard} onClick={(e) => {
                        e.stopPropagation();
                        gotoDetail(model);
                    }}>
                        <Space className={styles.modelInfo} direction='vertical' size={8}>
                            <Space className={styles.infoTag} size={12}>
                                <div className={styles.icon}>
                                    {model.icon ? (
                                        <img src={model.icon} alt="icon" style={{ width: 24, height: 24 }} />
                                    ) : (
                                        <span className={styles.icon} style={{ width: 48, height: 48 }}>
                                            {getModelIcon(model.id)}
                                        </span>
                                    )}
                                </div>
                                <div>
                                    {/* <div className={styles.provider}>{model.llmProviderId}</div> */}
                                    <div
                                        className={styles.name}
                                        style={{ cursor: 'pointer' }}
                                    >
                                        {model.name}
                                    </div>
                                </div>
                            </Space>

                            {/* 多模态和图片生成 暂时隐藏 */}
                            {/* <Space className={styles.details} direction='vertical' size={0}>
                                <Space className={styles.multiModal} size={4}>
                                    {model.multiModal ? (
                                        <IconEnable style={{ width: 24, height: 24 }} />
                                    ) : (
                                        <IconDisable style={{ width: 24, height: 24 }} />
                                    )}
                                    <Text
                                        type="secondary"
                                        className={styles.label}
                                        style={{ color: model.multiModal ? '#2ba471' : '#d54941' }}
                                    >
                                        {model.multiModal ? '允许发送图片/视频' : '不允许发送图片/视频'}
                                    </Text>
                                </Space>
                                <Space className={styles.imageGeneration}>
                                    {model.imageGeneration ? (
                                        <IconEnable style={{ width: 24, height: 24 }} />
                                    ) : (
                                        <IconDisable style={{ width: 24, height: 24 }} />
                                    )}
                                    <Text
                                        type="secondary"
                                        className={styles.label}
                                        style={{ color: model.imageGeneration ? '#2ba471' : '#d54941' }}
                                    >
                                        {model.imageGeneration ? '允许生成图片' : '不允许生成图片'}
                                    </Text>
                                </Space>
                            </Space> */}

                            {/* 多模态和图片生成 新逻辑 */}
                            <Space className={styles.details} direction='vertical' size={0}>
                                {model.multiModal && (
                                    <Space className={styles.multiModal} size={4}>
                                        <IconEnable style={{ width: 24, height: 24 }} />
                                        <Text
                                            type="secondary"
                                            className={styles.label}
                                            style={{ color: '#2ba471' }}
                                        >
                                            允许发送图片/视频
                                        </Text>
                                    </Space>
                                )}
                                {model.imageGeneration && (
                                    <Space className={styles.imageGeneration}>
                                        <IconEnable style={{ width: 24, height: 24 }} />
                                        <Text
                                            type="secondary"
                                            className={styles.label}
                                            style={{ color: '#2ba471' }}
                                        >
                                            允许生成图片
                                        </Text>
                                    </Space>
                                )}
                            </Space>

                            <div className={styles.tags}>
                                {model.tags && model.tags.length > 0 && (
                                    <div className={styles.tags}>
                                        <Space size="small">
                                            {model.tags.map((tag, index) => (
                                                <Tag key={index}>{tag}</Tag>
                                            ))}
                                        </Space>
                                    </div>
                                )}
                            </div>
                        </Space>
                        <div className={styles.status}>
                            <Space size={4} className={styles.metaInfo}>
                                {/* <Text>{`@${model.createUserId}`}</Text> */}
                                <Text>@AI4C</Text>
                                <Text>|</Text>
                                <Text>
                                    {model.updatedTime
                                        ? `更新时间：${formatDate(model.updatedTime)}`
                                        : `创建时间：${formatDate(model.createdTime)}`}
                                </Text>
                            </Space>
                            <div className={styles.actionWrapper} style={{ height: '32px' }}>
                                <div className={`${styles.statusIndicator} ${model.isEnabled ? styles.enabled : styles.disabled}`}>
                                    <span>{model.isEnabled ? '启用' : '禁用'}</span>
                                </div>
                                <Popover
                                    trigger="click"
                                    position="right"
                                    className={styles.popoverContent}
                                    content={
                                        <Space className={styles.popoverContent} direction="vertical" size="mini">
                                            <Button
                                                className={`${styles.actionBtn} ${model.isEnabled ? styles.disableBtn : styles.enableBtn}`}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    model.isEnabled ? handleDisable(model.id) : handleEnable(model.id);
                                                }}
                                            >
                                                {model.isEnabled ? '禁用' : '启用'}
                                            </Button>
                                            <Button
                                                className={`${styles.actionBtn} ${styles.deleteBtn}`}
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    setConfirmDeleteVisible(true);
                                                    setSelectedModelId(model.id);
                                                }}
                                            >
                                                删除
                                            </Button>
                                        </Space>
                                    }
                                >
                                    <Button
                                        className={styles.triggerBtn}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                        }}>
                                        <IconAction />
                                    </Button>
                                </Popover>
                            </div>
                        </div>
                    </Card>
                ))}
            </>
        );
    };

    return (
        <div className={styles.modelList}>
            <div className={styles.header}>
                <Button type="primary" className={styles.addModelBtn}
                    // onClick={() => gotoCreate()}
                    style={{ visibility: 'hidden' }}
                >
                    新增模型
                </Button>
                <Space size={'small'}>
                    <Text className={styles.modelNumber}>
                        共 {models.length} 个模型
                    </Text>
                    <Input
                        prefix={<IconSearch />}
                        placeholder="AI搜索..."
                        style={{ width: 240 }}
                        value={searchTerm}
                        onChange={handleSearchChange}
                        allowClear
                    />
                    <Select
                        prefix={<IconSortType />}
                        placeholder="按创建时间排序"
                        style={{ width: 160 }}
                        onChange={(value) => setSortType(value)}
                    >
                        <Option value="createTime">按创建时间排序</Option>
                        <Option value="tokenPrice">按价格排序</Option>
                        <Option value="provider">按提供商排序</Option>
                        <Option value="name">按名称排序</Option>
                    </Select>
                </Space>
            </div>
            <div className={styles.content}>
                {renderContent()}
            </div>

            {/* 删除模型确认弹窗 */}
            <Modal
                visible={confirmDeleteVisible}
                title="删除模型"
                onCancel={handleCancelDelete}
                closeIcon={<IconClose />}
                className={styles.confirmDeleteModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <Text className={styles.modalContentText}>
                        模型 {models.find((model) => model.id === selectedModelId)?.name || ''} 将被删除，请确认您是否要删除？
                    </Text>
                </div>
                <div className={styles.modalFooter}>
                    <Space>
                        <Button onClick={handleCancelDelete} className={styles.cancelDeleteBtn}>取消</Button>
                        <Button onClick={handleConfirmDelete} className={styles.confirmDeleteBtn}>
                            删除
                        </Button>
                    </Space>
                </div>
            </Modal>
        </div>
    );
};

export default ModelList;