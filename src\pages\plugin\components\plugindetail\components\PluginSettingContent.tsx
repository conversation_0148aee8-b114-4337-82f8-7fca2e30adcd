import React, { useState, useEffect } from 'react';
import { Input, Typography, Button, Switch, Grid, Message } from '@arco-design/web-react';
import PluginIconPlus from '@/assets/plugin/PluginIconPlus.svg';
import styles from '../style/index.module.less';

const { Text } = Typography;
const { Row, Col } = Grid;

interface PluginSettingContentProps {
    pluginData: any;
}

// 插件设置组件
function PluginSettingContent({ pluginData }: PluginSettingContentProps) {
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [isEnabled, setIsEnabled] = useState(true);
    const [isOpen, setIsOpen] = useState(false);

    // 当pluginData变化时更新表单数据
    useEffect(() => {
        if (pluginData) {
            setName(pluginData.name || '');
            setDescription(pluginData.description || '');
            setIsEnabled(pluginData.enabled || false);
            setIsOpen(!pluginData.is_core || false);
        }
    }, [pluginData]);

    const handleSave = () => {
        // 这里可以添加保存逻辑
        Message.success('设置已保存');
    };

    return (
        <div className={styles.settingContent}>
            <Row gutter={[24, 24]}>
                <Col span={12}>
                    <div className={styles.formItem}>
                        <div className={styles.iconAndName}>
                            <div className={styles.iconWrapper}>
                                {pluginData?.icon_url ? (
                                    <img 
                                        src={pluginData.icon_url} 
                                        alt={pluginData.name} 
                                        className={styles.pluginIcon}
                                    />
                                ) : (
                                    <PluginIconPlus className={styles.pluginIcon} />
                                )}
                            </div>
                            <div className={styles.divider}></div>
                            <div className={styles.nameInputContainer}>
                                <div className={styles.formLabel}>名称</div>
                                <Input
                                    placeholder="请输入"
                                    value={name}
                                    onChange={(value) => setName(value)}
                                    style={{ width: '100%', height: '40px' }}
                                />
                            </div>
                        </div>
                    </div>
                    <div className={styles.formItem}>
                        <div className={styles.formLabel}>描述</div>
                        <Input.TextArea
                            placeholder="请输入"
                            value={description}
                            onChange={(value) => setDescription(value)}
                            style={{ width: '100%' }}
                            rows={3}
                            maxLength={1000}
                            showWordLimit
                        />
                    </div>
                    <div className={styles.switchItem}>
                        <div className={styles.switchLabel}>
                            <Text style={{ fontWeight: 'bold' }}>是否启用</Text>
                            <Text type="secondary">控制插件</Text>
                        </div>
                        <div className={styles.switchControl}>
                            <Switch
                                checked={isEnabled}
                                onChange={setIsEnabled}
                                disabled
                            />
                        </div>
                    </div>
                    <div className={styles.switchItem}>
                        <div className={styles.switchLabel}>
                            <Text style={{ fontWeight: 'bold' }}>是否开放使用</Text>
                            <Text type="secondary">开启后，插件可被平台用户查看使用</Text>
                        </div>
                        <div className={styles.switchControl}>
                            <Switch
                                checked={isOpen}
                                onChange={setIsOpen}
                                disabled
                            />
                        </div>
                    </div>
                </Col>
            </Row>
            {/* <div className={styles.buttonWrapper}>
                <Button type="primary" onClick={handleSave}>保存</Button>
            </div> */}
        </div>
    );
}

export default PluginSettingContent; 