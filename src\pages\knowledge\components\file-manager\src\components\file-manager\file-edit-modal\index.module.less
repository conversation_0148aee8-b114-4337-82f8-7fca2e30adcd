.fileEditModal {
    :global(.ant-modal-content) {
        border-radius: 12px;
        overflow: hidden;
    }

    :global(.ant-modal-header) {
        border-bottom: none;
        padding: 24px 24px 0 24px;
    }

    :global(.ant-modal-body) {
        padding: 0 24px 24px 24px;
    }

    :global(.ant-modal-footer) {
        display: none;
    }
}

.formContainer {
    .topSection {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        margin-bottom: 24px;

        .iconSection {
            flex-shrink: 0;

            .fileIcon {
                width: 48px;
                height: 48px;
                color: #4455f2;
            }
        }

        .divider {
            width: 1px;
            height: 48px;
            background-color: #f0f0f0;
            margin: 0 8px;
        }

        .nameFormSection {
            flex: 1;
            min-width: 0;
        }
    }

    .bottomSection {
        .tagFormItem {
            margin-bottom: 0;

            .tagHeader {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 12px;

                .tagLabel {
                    font-size: 14px;
                    font-weight: 500;
                    color: #333;
                    margin: 0;
                }

                .tagHint {
                    font-size: 12px;
                    color: #adadad;
                    margin: 0;
                }

                .addTagBtn {
                    height: 28px;
                    font-size: 12px;
                    padding: 0 12px;
                    border-radius: 6px;
                    background-color: #ffffff;
                    border: 1px solid #ebebeb;
                    color: #333333;

                    &:hover {
                        background-color: #fafafa;
                        border-color: #d9d9d9;
                    }
                }
            }

            .selectedItemList {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .selectedItemRow {
                    :global(.ant-input) {
                        border-radius: 6px;
                        border: 1px solid #ebebeb;
                        font-size: 12px;
                        height: 32px;

                        &:hover {
                            border-color: #d9d9d9;
                        }

                        &:focus {
                            border-color: #4455f2;
                            box-shadow: 0 0 0 2px rgba(68, 85, 242, 0.1);
                        }
                    }

                    .deleteIcon {
                        width: 12px;
                        height: 12px;
                        color: #adadad;
                        cursor: pointer;
                        transition: color 0.2s;

                        &:hover {
                            color: #e74639;
                        }
                    }
                }
            }
        }
    }
}

.labelWithRequired {
    font-size: 14px;
    font-weight: 500;
    color: #333;

    .requiredStar {
        color: #e74639;
        margin-left: 2px;
    }
}

.customFooter {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px;
    border-top: 1px solid #f0f0f0;
    background-color: #fafafa;

    .cancelButton {
        height: 36px;
        font-size: 14px;
        padding: 0 16px;
        border-radius: 8px;
        background-color: #ffffff;
        border: 1px solid #ebebeb;
        color: #333333;

        &:hover {
            background-color: #fafafa;
            border-color: #d9d9d9;
        }
    }

    .confirmButton {
        height: 36px;
        font-size: 14px;
        padding: 0 16px;
        border-radius: 8px;
        background-color: #4455f2;
        border-color: #4455f2;
        color: #ffffff;

        &:hover {
            background-color: #3f4edf;
            border-color: #3f4edf;
        }

        &:disabled {
            background-color: #f5f5f5;
            border-color: #d9d9d9;
            color: #adadad;
        }
    }
}