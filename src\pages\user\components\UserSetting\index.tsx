import React, { useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { Card, Grid, Typography, Button, Space, List, Anchor, Avatar, Modal, Form, Input, Message } from '@arco-design/web-react';
import { IconPlus, IconStorage, IconLink, IconMore, IconClose } from '@arco-design/web-react/icon';
import useLocale from '@/utils/useLocale';
import locale from './locale';
import styles from './style/index.module.less';

const { Row, Col } = Grid;
const { Title, Text } = Typography;
const FormItem = Form.Item;

// 创建一个禁用默认必填标记的表单项组件
const FormItemWithoutRequiredMark = (props) => {
  return <FormItem {...props} requiredSymbol={false} />;
};

// 创建自定义标签组件
const CustomLabel = ({ label, required }) => {
  return (
    <Space>
      <span>{label}</span>
      {required && <Text className={styles.requiredIcon}>*</Text>}
    </Space>
  );
};

function UserSetting() {
  const t = useLocale(locale);
  const userInfo = useSelector((state: any) => state.userInfo);
  const loading = useSelector((state: any) => state.userLoading);
  const [form] = Form.useForm(); // 创建Form实例
  const [channelForm] = Form.useForm(); // 创建渠道表单实例
  
  // 状态管理
  const [dataSources, setDataSources] = useState([
    { id: 1, name: 'Notion', description: 'YTF\'s Notion' },
    { id: 2, name: 'API', description: '' }
  ]);
  
  const [channels, setChannels] = useState([
    { id: 1, name: '名称', description: '描述' }
  ]);

  // 数据源模态框相关状态
  const [dataSourceModalVisible, setDataSourceModalVisible] = useState(false);
  const [formValid, setFormValid] = useState(false);
  
  // 渠道模态框相关状态
  const [channelModalVisible, setChannelModalVisible] = useState(false);
  const [channelFormValid, setChannelFormValid] = useState(false);

  // 添加数据源
  const handleAddDataSource = () => {
    form.resetFields(); // 重置表单字段
    setFormValid(false); // 重置表单验证状态
    setDataSourceModalVisible(true);
  };

  // 处理取消按钮 - 数据源
  const handleCancel = () => {
    setDataSourceModalVisible(false);
    form.resetFields();
  };

  // 验证表单 - 数据源
  const validateForm = () => {
    const apiKey = form.getFieldValue('apiKey');
    const baseUrl = form.getFieldValue('baseUrl');
    if (
      apiKey && apiKey.trim() !== '' && 
      baseUrl && baseUrl.trim() !== ''
    ) {
      setFormValid(true);
    } else {
      setFormValid(false);
    }
  };

  // 处理表单字段变化 - 数据源
  const handleFormValuesChange = (changedValues, allValues) => {
    if ('apiKey' in changedValues || 'baseUrl' in changedValues) {
      validateForm();
    }
  };

  // 处理表单提交 - 数据源
  const handleSubmit = () => {
    form
      .validate()
      .then(async (values) => {
        try {
          // 这里应该会有创建数据源的API调用
          console.log('提交数据:', values);
          
          // 模拟添加新数据源到列表
          const newDataSource = {
            id: Date.now(), // 使用时间戳作为临时ID
            name: 'API', // 或者可以从表单获取更多信息
            description: `${values.baseUrl}`
          };
          
          setDataSources([...dataSources, newDataSource]);
          Message.success('数据源添加成功！');
          setDataSourceModalVisible(false);
          form.resetFields();
        } catch (error) {
          console.error('添加数据源失败:', error);
          Message.error('添加数据源失败，请重试！');
        }
      })
      .catch((errors) => {
        console.log('验证错误:', errors);
        Message.error('请检查表单字段！');
      });
  };

  // 添加渠道
  const handleAddChannel = () => {
    channelForm.resetFields(); // 重置表单字段
    setChannelFormValid(false); // 重置表单验证状态
    setChannelModalVisible(true);
  };
  
  // 处理取消按钮 - 渠道
  const handleCancelChannel = () => {
    setChannelModalVisible(false);
    channelForm.resetFields();
  };
  
  // 验证表单 - 渠道
  const validateChannelForm = () => {
    const name = channelForm.getFieldValue('name');
    const baseUrl = channelForm.getFieldValue('baseUrl');
    if (
      name && name.trim() !== '' && 
      baseUrl && baseUrl.trim() !== ''
    ) {
      setChannelFormValid(true);
    } else {
      setChannelFormValid(false);
    }
  };
  
  // 处理表单字段变化 - 渠道
  const handleChannelFormValuesChange = (changedValues, allValues) => {
    if ('name' in changedValues || 'baseUrl' in changedValues) {
      validateChannelForm();
    }
  };
  
  // 处理表单提交 - 渠道
  const handleSubmitChannel = () => {
    channelForm
      .validate()
      .then(async (values) => {
        try {
          console.log('提交渠道数据:', values);
          
          // 模拟添加新渠道到列表
          const newChannel = {
            id: Date.now(), // 使用时间戳作为临时ID
            name: values.name,
            description: values.description || ''
          };
          
          setChannels([...channels, newChannel]);
          Message.success('渠道添加成功！');
          setChannelModalVisible(false);
          channelForm.resetFields();
        } catch (error) {
          console.error('添加渠道失败:', error);
          Message.error('添加渠道失败，请重试！');
        }
      })
      .catch((errors) => {
        console.log('验证错误:', errors);
        Message.error('请检查表单字段！');
      });
  };

  const dataSourceSection = useRef(null);
  const channelSection = useRef(null);

  return (
    <div className={styles.settingContainer}>
      <Row gutter={24}>
        {/* 左侧内容区域 */}
        <Col span={12}>
          {/* 数据源部分 */}
          <div ref={dataSourceSection} id="dataSource">
            <Card className={styles.cardContainer} bordered={false}>
              <div className={styles.cardHeader}>
                <div className={styles.headerTitleWrapper}>
                  <Title heading={6}>{t['userSetting.dataSource.title'] || '数据源'}</Title>
                  <Text type="secondary">{t['userSetting.dataSource.subtitle'] || '添加您的数据源'}</Text>
                </div>
                <div className={styles.headerButtonWrapper}>
                  <Button type="primary" onClick={handleAddDataSource} className={styles.handleAddDataSource}>
                    {t['userSetting.dataSource.add'] || '添加'}
                  </Button>
                </div>
              </div>
              
              <List
                className={styles.listContainer}
                dataSource={dataSources}
                render={(item, index) => (
                  <List.Item key={item.id} className={styles.listItem}>
                    <div className={styles.listItemMain}>
                      <div className={styles.listItemContent}>
                        <div className={styles.listItemIcon}>
                          {index === 0 ? (
                            <Avatar className={styles.notionIcon}>N</Avatar>
                          ) : (
                            <Avatar className={styles.apiIcon}>A</Avatar>
                          )}
                        </div>
                        <div className={styles.listItemTextContent}>
                          <Space  size={8}>
                            <Text bold><b>{item.name}</b></Text>
                            {item.description && <Text type="secondary">{item.description}</Text>}
                          </Space>
                        </div>
                        <div className={styles.listItemAction}>
                          <Button type="text" className={styles.actionButton} icon={<IconMore />} />
                        </div>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            </Card>
          </div>

          {/* 渠道部分 */}
          <div ref={channelSection} id="channel" className={styles.sectionMargin}>
            <Card className={styles.cardContainer} bordered={false}>
              <div className={styles.cardHeader}>
                <div className={styles.headerTitleWrapper}>
                  <Title heading={6}>{t['userSetting.channel.title'] || '渠道'}</Title>
                  <Text type="secondary">{t['userSetting.channel.subtitle'] || '添加您的发布渠道'}</Text>
                </div>
                <div className={styles.headerButtonWrapper}>
                  <Button type="primary" onClick={handleAddChannel} className={styles.handleAddChannel}>
                    {t['userSetting.channel.add'] || '添加'}
                  </Button>
                </div>
              </div>
              
              <List
                className={styles.listContainer}
                dataSource={channels}
                render={(item, index) => (
                  <List.Item key={item.id} className={styles.listItem}>
                    <div className={styles.listItemMain}>
                      <div className={styles.listItemContent}>
                        <div className={styles.listItemIcon}>
                          <Avatar className={styles.channelIcon}>C</Avatar>
                        </div>
                        <div className={styles.listItemTextContent}>
                          <Space size={8}>
                            <Text bold><b>{item.name}</b></Text>
                            {item.description && <Text type="secondary">{item.description}</Text>}
                          </Space>
                        </div>
                        <div className={styles.listItemAction}>
                          <Button type="text" className={styles.actionButton} icon={<IconMore />} />
                        </div>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            </Card>
          </div>
        </Col>

        {/* 右侧导航区域 */}
        <Col span={12}>
          <Card className={styles.navigationCard} bordered={false}>
            <Anchor lineless={true} boundary={5}>
              <div className={styles.anchorContainer}>
                <Anchor.Link href="#dataSource" title={
                  <Space className={styles.navItem}>
                    <span className={styles.navIcon}>
                      <IconStorage />
                    </span>
                    {t['userSetting.navigation.dataSource'] || '数据源'}
                  </Space>
                } />
                <Anchor.Link href="#channel" title={
                  <Space className={styles.navItem}>
                    <span className={styles.navIcon}>
                      <IconLink />
                    </span>
                    {t['userSetting.navigation.channel'] || '渠道'}
                  </Space>
                } />
              </div>
            </Anchor>
          </Card>
        </Col>
      </Row>

      {/* 添加数据源模态框 */}
      <Modal
        visible={dataSourceModalVisible}
        title="添加数据源"
        onCancel={handleCancel}
        closeIcon={<IconClose />}
        className={styles.dataSourceModal}
        maskClosable={false}
      >
        <div className={styles.modalContent}>
          <Form form={form} autoComplete="off" layout="vertical" onValuesChange={handleFormValuesChange} requiredSymbol={false}>
            <FormItemWithoutRequiredMark
              label={<CustomLabel label="API Key" required={true} />}
              field="apiKey"
              rules={[{ required: true, message: '请输入API Key' }]}
            >
              <Input
                placeholder="请输入"
              />
            </FormItemWithoutRequiredMark>

            <FormItemWithoutRequiredMark
              label={<CustomLabel label="Base URL" required={true} />}
              field="baseUrl"
              rules={[{ required: true, message: '请输入Base URL' }]}
            >
              <Input
                placeholder="请输入"
              />
            </FormItemWithoutRequiredMark>
          </Form>
        </div>
        <div className={styles.modalFooter}>
          <Button onClick={handleCancel} className={styles.cancelBtn}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={handleSubmit}
            className={`${styles.saveBtn} ${!formValid ? styles.disabledBtn : ''}`}
            disabled={!formValid}
          >
            确定
          </Button>
        </div>
      </Modal>
      
      {/* 添加渠道模态框 */}
      <Modal
        visible={channelModalVisible}
        title="添加渠道"
        onCancel={handleCancelChannel}
        closeIcon={<IconClose />}
        className={styles.channelModal}
        maskClosable={false}
      >
        <div className={styles.modalContent}>
          <Form form={channelForm} autoComplete="off" layout="vertical" onValuesChange={handleChannelFormValuesChange} requiredSymbol={false}>
            <Row className={styles.channelHeader}>
              <div className={styles.iconAndName}>
                <div className={styles.channelIconWrapper}>
                  <IconLink className={styles.channelModalIcon} />
                </div>
                <div className={styles.divider}></div>
                <div className={styles.nameFormContainer}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="名称" required={true} />}
                    field="name"
                    rules={[{ required: true, message: '请输入名称' }]}
                    className={styles.nameFormItem}
                  >
                    <Input
                      placeholder="请输入"
                    />
                  </FormItemWithoutRequiredMark>
                </div>
              </div>
            </Row>

            <FormItemWithoutRequiredMark
              label={<CustomLabel label="描述" required={false} />}
              field="description"
            >
              <Input.TextArea
                placeholder="用简单明了的话描述~"
                rows={4}
                maxLength={100}
                className={styles.descriptionInput}
                showWordLimit
              />
            </FormItemWithoutRequiredMark>
            
            <FormItemWithoutRequiredMark
              label={<CustomLabel label="Base URL" required={true} />}
              field="baseUrl"
              rules={[{ required: true, message: '请输入Base URL' }]}
            >
              <Input
                placeholder="请输入"
              />
            </FormItemWithoutRequiredMark>
          </Form>
        </div>
        <div className={styles.modalFooter}>
          <Button onClick={handleCancelChannel} className={styles.cancelBtn}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={handleSubmitChannel}
            className={`${styles.saveBtn} ${!channelFormValid ? styles.disabledBtn : ''}`}
            disabled={!channelFormValid}
          >
            确定
          </Button>
        </div>
      </Modal>
    </div>
  );
}

export default UserSetting;
