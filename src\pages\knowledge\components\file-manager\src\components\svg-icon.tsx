import { IconMap } from '@/pages/knowledge/components/file-manager/src/constants/llm';
import { cn } from '@/pages/knowledge/components/file-manager/src/lib/utils';
import Icon, { UserOutlined } from '@ant-design/icons';
import { IconComponentProps } from '@ant-design/icons/lib/components/Icon';
import { Avatar } from 'antd';
import { AvatarSize } from 'antd/es/avatar/AvatarContext';

// 使用 Vite 的 import.meta.glob 动态导入 SVG 文件
const svgModules = import.meta.glob('../assets/svg/**/*.svg', { 
  eager: true,
  as: 'url'
});

// 处理路径并创建图标映射
const routeList: { name: string; value: string }[] = Object.entries(svgModules).map(([path, url]) => {
  // 提取文件名路径，例如：../assets/svg/file-icon/pdf.svg -> file-icon/pdf
  const name = path.replace('../assets/svg/', '').replace('.svg', '');
  return { 
    name, 
    value: url as string 
  };
});

// 开发环境下打印调试信息
if (import.meta.env.DEV) {
  // console.log('Loaded SVG icons:', routeList.map(item => item.name));
  // console.log('Available file-icon types:', routeList.filter(item => item.name.startsWith('file-icon/')).map(item => item.name));
}

interface IProps extends IconComponentProps {
  name: string;
  width: string | number;
  height?: string | number;
  imgClass?: string;
}

const SvgIcon = ({ name, width, height, imgClass, ...restProps }: IProps) => {
  const ListItem = routeList.find((item) => item.name === name);
  
  // 如果找不到图标，显示一个默认的占位符
  if (!ListItem) {
    console.warn(`SVG icon not found: ${name}`);
    return (
      <Icon
        component={() => (
          <div
            style={{ 
              width, 
              height: height || width, 
              backgroundColor: '#f0f0f0',
              border: '1px dashed #ccc',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '10px',
              color: '#999'
            }}
          >
            {name.split('/').pop()}
          </div>
        )}
        {...(restProps as any)}
      />
    );
  }

  return (
    <Icon
      component={() => (
        <img
          src={ListItem.value}
          alt=""
          width={width}
          height={height}
          className={cn(imgClass, 'max-w-full')}
        />
      )}
      {...(restProps as any)}
    />
  );
};

export const LlmIcon = ({
  name,
  height = 48,
  width = 48,
  size = 'large',
  imgClass,
}: {
  name: string;
  height?: number;
  width?: number;
  size?: AvatarSize;
  imgClass?: string;
}) => {
  const icon = IconMap[name as keyof typeof IconMap];

  return icon ? (
    <SvgIcon
      name={`llm/${icon}`}
      width={width}
      height={height}
      imgClass={imgClass}
    ></SvgIcon>
  ) : (
    <Avatar shape="square" size={size} icon={<UserOutlined />} />
  );
};

export default SvgIcon;
