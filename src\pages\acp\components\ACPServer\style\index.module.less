.acp {
    display: flex;
    flex-direction: column;

    .headerText {
        font-weight: 600;
        font-size: 20px;
        line-height: 32px;
        color: #333333;
        margin-bottom: 8px;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f5;

        .addAcpBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            background-color: #4455f2;
            font-weight: 500;
            font-size: 14px;
            line-height: 24px;
            color: #ffffff;
            border-radius: 8px;
            transition: all 0.3s;
            height: 40px;

            &:hover {
                background-color: #3144f1;
            }
        }

        //搜索输入框
        :global(.arco-input-inner-wrapper) {
            padding: 8px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            ::placeholder {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                color: #d6d6d6;
            }

            :global(.arco-input) {
                padding-top: 0;
                padding-bottom: 0;
                padding-left: 8px;
            }
        }

        //筛选select
        :global(.arco-select-size-default.arco-select-single .arco-select-view) {
            padding: 8px;
            height: auto;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            background-color: #ffffff;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            :global(.arco-select-prefix) {
                margin-right: 4px;
            }

            :global(.arco-select-view-input) {
                &::placeholder {
                    color: #d6d6d6;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                }
            }
        }

        .acpNumber {
            color: #adadad;
            font-size: 14px;
            font-weight: 400;
            white-space: nowrap;
        }
    }

    .content {
        height: calc(100vh - 205px);
        min-height: 300px;
        overflow-y: auto;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 16px;
        grid-auto-rows: min-content;
        position: relative;
        will-change: transform;
        box-sizing: border-box;
        scrollbar-width: none;
        -ms-overflow-style: none;
        &::-webkit-scrollbar {
            display: none;
        }

        // 添加媒体查询，处理小屏幕设备
        @media screen and (max-width: 1200px) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media screen and (max-width: 992px) {
            grid-template-columns: repeat(2, 1fr);
        }

        @media screen and (max-width: 576px) {
            grid-template-columns: repeat(1, 1fr);
        }

        .acpCard {
            border: 1px solid #f5f5f5;
            height: 240px;
            box-sizing: border-box;
            cursor: pointer;
            border-radius: 8px;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            min-height: 200px;
            transition: all 0.3s;
            position: relative;
            width: 100%;
            max-width: 100%;
            overflow: hidden;

            // 添加响应式内边距
            @media screen and (max-width: 1400px) {
                padding: 16px 20px;
            }

            @media screen and (max-width: 1200px) {
                padding: 14px 16px;
            }

            &:hover {
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

                .acpInfo {
                    .acpFooter {
                        .defaultInfo {
                            opacity: 0;
                        }

                        .hoverInfo {
                            opacity: 1;
                            pointer-events: auto;
                        }
                    }
                }

                .triggerBtn {
                    opacity: 1;
                }
            }

            :global(.arco-card-body) {
                padding: 0;
                display: flex;
                flex-direction: column;
                width: 100%;
                height: 100%;
            }

            .acpInfo {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 100%;

                .nameWrapper {
                    display: flex;
                    align-items: center;

                    .icon {
                        width: 48px;
                        height: 48px;
                        border-radius: 8px;

                        svg {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .name {
                        font-size: 16px;
                        font-weight: 600;
                        color: #333333;
                        cursor: pointer;
                    }
                }

                .acpDetails {
                    display: flex;
                    flex-direction: column;

                    .acpType {
                        align-self: flex-start;
                        padding: 2px 6px;
                        border: 1px solid #ebebeb;
                        border-radius: 4px;
                        font-weight: 400;
                        font-size: 12px;
                        color: #5c5c5c;
                    }

                    .description {
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 24px;
                        letter-spacing: 0%;
                        color: #5c5c5c;
                        line-clamp: 2;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                    }
                }

                .acpFooter {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 16px;
                    position: relative;

                    // 默认显示的资源数量和状态
                    .defaultInfo {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 100%;
                        opacity: 1;
                        transition: opacity 0.3s ease;

                        .resourceInfo {
                            .resourceCount {
                                font-weight: 400;
                                font-size: 12px;
                                color: #adadad;
                            }
                        }

                        .statusInfo {
                            .statusTag {
                                font-weight: 400;
                                font-size: 12px;
                                line-height: 20px;
                                border-radius: 4px;
                                padding: 2px 6px;
                                border: none;
                            }

                            .statusAvailable {
                                background-color: #f7fcfa;
                                color: #2ba471;
                            }

                            .statusUnavailable {
                                background-color: #fef8f8;
                                color: #d54941;
                            }
                        }
                    }

                    // 悬浮时显示的元信息和操作按钮
                    .hoverInfo {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-end;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                        pointer-events: none;

                        .metaText {
                            :global(.arco-typography) {
                                font-weight: 400;
                                font-size: 12px;
                                color: #adadad;
                            }
                        }
                    }
                }
            }

            .triggerBtn {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 4px;
                background-color: #ffffff;
                border-radius: 8px;
                border: 1px solid #f5f5f5;
                opacity: 0;
                transition: all 0.3s;

                &:hover {
                    background-color: #fafafa;
                }
            }
        }

        .emptyContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            grid-column: 1 / -1;
            background-color: #ffffff;

            :global(.arco-space) {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            :global(.arco-typography) {
                font-weight: 500;
                font-size: 14px;
                line-height: 24px;
                color: #5c5c5c;
                text-align: center;
            }
        }
    }

    .loadingContainer,
    .errorContainer {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        grid-column: 1 / -1;
        background-color: #ffffff;

        :global(.arco-spin) {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
}

.confirmDeleteModal {
    position: relative;
    padding: 24px;
    width: 480px;
    border-radius: 16px;

    .modalContent {
        display: flex;
        flex-direction: column;

        .modalContentText {
            font-weight: 400;
            font-size: 14px;
            color: #5c5c5c;
        }
    }

    .modalFooter {
        margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .cancelDeleteBtn,
        .confirmDeleteBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 18px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
        }

        .cancelDeleteBtn {
            background-color: #ffffff;
            color: #5c5c5c;
            border: 1px solid #ebebeb;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }
        }

        .confirmDeleteBtn {
            background-color: #d54941;
            color: #ffffff;

            &:hover {
                background-color: #cd463e;
                color: #ffffff;
            }
        }
    }

    :global(.arco-modal-header) {
        padding: 0;
        height: auto;
        border-bottom: none;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            color: #333333;
            margin-bottom: 24px;
            text-align: left;
        }
    }

    :global(.arco-modal-content) {
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 24px;
        top: 24px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
        background-color: #e9e9ff;
        color: #4d5ef3;
    }
}

.deleteBtn {
    padding: 4px 8px;
    border-radius: 4px;
    width: 60px;
    display: flex;
    justify-content: flex-start;
    background-color: #ffffff !important;
    color: #333333;

    &:hover {
        background-color: #f5f5f5 !important;
    }
}

.testBtn {
    padding: 4px 8px;
    border-radius: 4px;
    width: 80px;
    display: flex;
    justify-content: flex-start;
    background-color: #ffffff !important;
    color: #333333;

    &:hover {
        background-color: #f5f5f5 !important;
    }

    &:global(.arco-btn-loading) {
        color: #4455f2;
    }
}

:global(.arco-popover-content.arco-popover-content-right) {
    padding: 8px;
    width: 180px;
    border: none;
    border: 1px solid #f5f5f5;
    border-radius: 8px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

    :global(.arco-btn) {
        background-color: transparent;
        width: 164px;
        text-align: left;
    }
}

// 必填图标样式
.requiredIcon {
    color: #4455f2;
    font-size: 15px;
    line-height: 16px;
    margin-left: 4px;
    position: relative;
    top: -1px;
    font-weight: bold;
}

:global {
    .arco-modal-mask {
        background: rgba(0, 0, 0, 0.16);
    }
}