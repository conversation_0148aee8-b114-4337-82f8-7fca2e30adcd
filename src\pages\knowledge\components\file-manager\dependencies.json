{"production": {"react": "^18.0.0", "react-dom": "^18.0.0", "antd": "^5.0.0", "@ant-design/icons": "^5.0.0", "@tanstack/react-query": "^4.0.0 || ^5.0.0", "axios": "^1.0.0", "lodash": "^4.17.0", "react-i18next": "^12.0.0", "i18next": "^22.0.0", "react-router-dom": "^6.0.0", "less": "^4.0.0"}, "development": {"typescript": "^4.9.0 || ^5.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/lodash": "^4.14.0", "less-loader": "^11.0.0", "webpack": "^5.0.0", "babel-loader": "^9.0.0", "@babel/core": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.0.0"}, "critical_migrations": {"path_aliases": "需要重新配置 @/ 路径别名", "routing": "从 Umi 迁移到 React Router", "i18n": "配置 i18next 实例", "build_system": "配置 Webpack/Vite 构建系统"}, "internal_dependencies": {"components": ["file-manager/index.tsx", "file-upload-modal/index.tsx", "rename-modal/index.tsx", "svg-icon.tsx", "confirm-delete-dialog.tsx", "new-document-link.tsx"], "hooks": ["file-manager-hooks.ts", "common-hooks.tsx", "logic-hooks.ts", "route-hook.ts"], "services": ["file-manager-service.ts"], "utils": ["api.ts", "date.ts", "common-util.ts", "document-util.ts", "file-util.ts", "request.ts", "register-server.ts"], "interfaces": ["database/file-manager.ts", "request/file-manager.ts", "request/base.ts", "common.ts"], "constants": ["common.ts"], "locales": ["zh.ts", "en.ts"]}}