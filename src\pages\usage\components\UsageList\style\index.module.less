// index.module.less
.usageStatistics {
  background-color: #ffffff;
  border-radius: 8px;

  .title {
    font-weight: 600;
    color: #333333;
  }

  .usageHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    border-bottom: 1px solid #f5f5f5;
    padding-bottom: 16px;

    .searchInput {
      width: 240px;

      :global(.arco-input-inner-wrapper) {
        padding: 8px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        transition: all 0.2s;

        &:hover {
          background-color: #fafafa;
          border-color: #ebebeb;
        }

        ::placeholder {
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
          color: #d6d6d6;
        }

        :global(.arco-input) {
          padding-top: 0;
          padding-bottom: 0;
          padding-left: 8px;
        }
      }
    }

    .sortSelect {
      width: 200px;
    }

    //筛选select
    :global(.arco-select-size-default.arco-select-single .arco-select-view) {
      padding: 8px;
      height: auto;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #d6d6d6;
      border-radius: 8px;
      border: 1px solid #f5f5f5;
      background-color: #ffffff;
      transition: all 0.2s;

      &:hover {
        background-color: #fafafa;
        border-color: #ebebeb;
      }

      :global(.arco-select-prefix) {
        margin-right: 4px;
      }

      :global(.arco-select-view-input) {
        &::placeholder {
          color: #d6d6d6;
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
        }
      }
    }

    .appCount {
      color: #adadad;
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      text-align: right;
    }

    .periodControls {
      display: flex;
      align-items: center;

      :global(.arco-btn-group) {
        display: flex;
        padding: 4px;
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        gap: 4px;
        box-sizing: border-box;

        :global(.arco-btn-secondary:not(.arco-btn-disabled)) {
          background-color: transparent;
          color: #adadad;
        }

        :global(.arco-btn-primary:not(.arco-btn-disabled)) {
          background-color: #fafafa;
          color: #5c5c5c;
        }

        button {
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 4px;
          border: none !important;
          padding: 4px 12px;
          height: 32px;

          span {
            font-weight: 500;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
          }
        }
      }
    }
  }

  .usageContent {
    width: 100%;
    height: calc(100vh - 200px);
    overflow: hidden;
    // overflow-y: scroll;

    .usageTable {
      :global(.arco-table-th) {
        background-color: #fafafa;
        font-weight: 600;
        color: #333333;
        padding: 8px 4px 16px 4px;
      }

      :global(.arco-table-td) {
        padding: 12px 16px;
        color: #5c5c5c;
      }

      :global(.arco-table-container) {
        border: none;
      }

      :global(.arco-table-container::before) {
        display: none;
      }

      :global(.arco-table-th) {
        background-color: #ffffff !important;
        border-bottom: none;
        font-weight: 600;
        font-size: 16px;
        color: #5c5c5c;
        border-left: none;
      }

      :global(.arco-table-th-item) {
        padding: 4px 24px;
        border-radius: 8px;
        background-color: #fcfcfc;

        &:not(:last-child) {
          margin-right: 1px;
        }

        :global(.arco-table-th-item-title) {
          font-weight: 600;
          font-size: 14px;
          line-height: 24px;
          color: #adadad;
        }
      }

      :global(.arco-table-header) {
        position: sticky;
        top: 0;
        z-index: 1;
        margin-bottom: 8px;
      }

      :global(.arco-table-td) {
        padding: 16px;
        border-top: 1px solid #f5f5f5;
        border-bottom: none;
        color: #5c5c5c;
        font-size: 14px;
        font-weight: 400;
        border-left: none;
      }

      .nameColumn {
        display: flex;
        align-items: center;
        gap: 16px;
        position: relative;

        .iconWrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          svg {
            color: #4455f2;
            transition: color 0.3s ease;
          }

          // 在图标下方添加一个白色遮罩，覆盖表格线条
          &::after {
            content: '';
            position: absolute;
            bottom: -17px;
            left: 50%;
            transform: translateX(-50%);
            width: 70px;
            height: 1px;
            background-color: #ffffff;
            z-index: 2;
          }
        }

        .nameText {
          font-weight: 600;
          color: #333333;
          font-size: 14px;
          line-height: 24px;
        }
      }

      .arrowColumn {
        display: flex;
        justify-content: center;
        align-items: center;

        svg {
          transition: color 0.3s ease;
        }
      }

      :global(.arco-table-tr:hover) {
        background-color: #fafafa;

        .nameColumn .iconWrapper svg {
          color: #4455f2;
        }

        .arrowColumn svg {
          color: #4455f2;
        }
      }

      .creatorText,
      .timeText,
      .usageText,
      .tokenText {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
      }

      .creatorText,
      .timeText {
        color: #adadad;
      }

      .usageText,
      .tokenText {
        color: #5c5c5c;
      }
    }
  }

  .emptyContainer {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: 400px;

    svg {
      width: 80px;
      height: 80px;
    }

    .emptyText {
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      color: #5c5c5c;
    }
  }

  // 建设中状态容器
  .buildingContainer {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: 400px;

    svg {
      width: 80px;
      height: 80px;
    }

    .buildingText {
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      color: #5c5c5c;
      text-align: center;
    }
  }

  // 加载状态容器
  .loadingContainer {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: 400px;
    background-color: #ffffff;

    :global(.arco-spin) {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .loadingText {
      margin-top: 12px;
      color: RGBA(0, 0, 0, 0.5);
      font-size: 14px;
    }
  }
}