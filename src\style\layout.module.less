@nav-size-height: 60px;
@layout-max-width: 1100px;
@logo-text-color: #333333;
@layout-color-fill: #ffffff;

.layout {
  width: 100%;
  height: 100%;

  :global(.arco-layout-sider-light) {
    box-shadow: none;
  }

  &-sider {
    position: fixed;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 99;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    ::-webkit-scrollbar {
      width: 12px;
      height: 4px;
    }

    ::-webkit-scrollbar-thumb {
      border: 4px solid transparent;
      background-clip: padding-box;
      border-radius: 7px;
      background-color: var(--color-text-4);
    }

    ::-webkit-scrollbar-thumb:hover {
      background-color: var(--color-text-3);
    }

    &::after {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      right: -1px;
      width: 1px;
      height: 100%;
      background-color: #f5f5f5;
    }

    > :global(.arco-layout-sider-children) {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .collapse-btn {
      height: 24px;
      width: 24px;
      background-color: var(--color-fill-1);
      color: var(--color-text-3);
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      // 位置调整为相对定位，放在bottom-menu内部
      margin: 8px 12px 4px auto;
      flex-shrink: 0;

      &:hover {
        background-color: var(--color-fill-3);
      }
    }

    .bottom-menu {
      flex-shrink: 0;
      width: 100%;
      background-color: var(--color-bg-2);
      padding-bottom: 8px;

      :global(.arco-menu) {
        width: 100%;
        border: none;
      }

      :global(.arco-menu-item) {
        margin: 0;
        padding: 12px 16px;
        height: 48px;
        line-height: 26px;
        border-radius: 8px;
        transition: all 0.2s ease;

        &:hover {
          background-color: #fafafa;
        }
      }

      // 企业用户MenuItem样式
      :global(.arco-menu-item[data-key="enterprise"]),
      :global(.arco-menu-item.arco-menu-item-enterprise),
      :global(.arco-menu-item.enterprise-menu-item) {
        position: relative;
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        margin-bottom: 4px;
      }

      // 折叠状态下企业用户MenuItem样式
      :global(.arco-layout-sider-collapsed) & {

        :global(.arco-menu-item[data-key="enterprise"]),
        :global(.arco-menu-item.arco-menu-item-enterprise),
        :global(.arco-menu-item.enterprise-menu-item) {
          margin: 0px;
          padding: 12px 6px;
        }
      }

      .icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        vertical-align: middle;
      }

      .userMenuContainer {
        display: flex;
        width: 100%;
        align-items: center;
        justify-content: space-between;

        .userMenuLeft {
          display: flex;
          align-items: center;

          .userName {
            font-weight: 600;
            font-size: 14px;
            color: #333333;
          }
        }

        .avatarIcon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 8px;
          background: #f5f5f5;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .userAvatar {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .moreIcon {
          width: 24px;
          height: 24px;
        }
      }

      .enterprise {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        font-size: 14px;
        color: #333333;

        .enterprisetag {
          font-weight: 400;
          font-size: 12px;
          line-height: 20px;
          color: #5c5c5c;
          padding: 2px 6px;
          border: 1px solid #f5f5f5;
          border-radius: 4px;
        }
      }

      // 折叠状态下企业标签样式
      :global(.arco-layout-sider-collapsed) & {
        .enterprise {
          justify-content: center;

          .enterprisetag {
            position: absolute;
            right: 4px;
            top: 4px;
            padding: 0 4px;
            font-size: 9px;
          }
        }
      }
    }
  }
}

.layout-navbar {
  position: fixed;
  width: 100%;
  min-width: @layout-max-width;
  top: 0;
  left: 0;
  height: @nav-size-height;
  z-index: 100;

  &-hidden {
    height: 0;
  }
}

:global(.arco-layout-sider-collapsed) {
  .logo-box {
    padding: 12px 16px 20px 12px;
    margin: 0;

    &::after {
      left: 0%;
      width: 100%;
    }
  }

  .bottom-menu {
    :global(.arco-menu-item) {
      padding: 12px 8px;
    }
  }

  .menu-wrapper {
    :global(.arco-menu-light .arco-menu-item) {
      padding: 0px 8px;
    }

    :global(.arco-menu-vertical .arco-menu-inner) {
      padding: 4px 4px;
    }
  }

  :global(.arco-menu-vertical .arco-menu-pop-header) {
    padding: 0px 8px;
    transition: all 0.3s;

    &:hover {
      background-color: #fafafa;
    }
  }
}

.menu-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--color-text-4);
    border-radius: 3px;

    &:hover {
      background-color: var(--color-text-3);
    }
  }

  :global(.arco-menu-item-inner > a::after),
  :global(.arco-menu-item > a::after) {
    content: '';
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }

  :global(.arco-menu-item-inner) {
    display: flex !important;
    justify-content: flex-start;
    align-items: center;
  }

  :global(.arco-menu-inline-header) {
    font-weight: 400;
    font-size: 14px;
    color: #a6a6a6;
    padding: 0 16px;
    transition: all 0.3s;

    &:hover {
      background-color: #fafafa;
    }

    span {
      display: flex;
      align-items: center;

      svg {
        margin-right: 8px;
      }
    }
  }

  :global(.arco-menu-light .arco-menu-item.arco-menu-selected) {
    color: #333333;
    font-weight: 600;
    font-size: 14px;
    background-color: #fafafa;
    border-radius: 8px;
  }

  :global(.arco-menu-light .arco-menu-inline-header.arco-menu-selected) {
    color: #333333;
    font-weight: 600;
    font-size: 14px;
  }

  :global(.arco-menu-light .arco-menu-inline-header.arco-menu-selected .arco-icon) {
    color: #a6a6a6;
  }

  :global(.arco-menu-light .arco-menu-item) {
    display: flex;
    align-items: center;
    color: #a6a6a6;
    font-weight: 400;
    font-size: 14px;
    height: 48px;
    padding: 0 16px;
    border-radius: 8px;
    transition: all 0.3s;

    &:hover {
      background-color: #fafafa;
    }

    svg {
      margin-right: 8px;
    }
  }

  :global(.arco-menu-vertical .arco-menu-inner) {
    padding: 0 8px;
  }
}

.icon {
  font-size: 18px;
  vertical-align: text-bottom;
}

.layout-content {
  background-color: @layout-color-fill;
  min-width: @layout-max-width;
  min-height: 100vh;
  transition: padding-left 0.2s;
  box-sizing: border-box;
}

.layout-content-wrapper {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.layout-breadcrumb {
  margin-bottom: 16px;
}

.spin {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: calc(100vh - @nav-size-height);
}

.logo-box {
  display: flex;
  align-items: center;
  padding: 12px 16px 20px 16px;
  position: relative;
  margin: 0px 8px 8px 8px;
  flex-shrink: 0;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 3%;
    /* 左侧留出10% */
    width: 93%;
    /* 宽度设为80% */
    height: 1px;
    background-color: #f5f5f5;
  }

  .logo {
    width: 24px;
    height: 24px;
  }

  .logo-name {
    color: @logo-text-color;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    margin: 0;
    margin-left: 8px;
    white-space: nowrap;
  }
}

.breadcrumb-item {
  display: flex;
  align-items: center;

  .icon {
    margin-right: 8px;
    font-size: 16px;
  }

  .breadcrumb-text {
    font-weight: 400;
    font-size: 14px;
    color: #a6a6a6;
  }
}

// /* SettingDropdownMenu样式相关更新，确保下拉菜单相对于Sider正确定位 */
// :global(.arco-layout) {
//   position: relative;
// }

// /* 确保SettingDropdownMenu在Sider菜单底部正确定位 */
// :global(.dropdownMenu) {
//   position: absolute;
//   z-index: 1001; /* 确保在其他元素之上 */
//   left: var(--menu-width, 200px);
//   bottom: 60px;
//   transform-origin: bottom left;
// }