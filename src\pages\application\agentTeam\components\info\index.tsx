import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Tabs, Spin } from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import localStyles from './style/index.module.less';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';
import ApplicationBasic from './components/basic';
import ApplicationSettings from './components/settings';
import ApplicationTesting from './components/testing';
import ApplicationRouting from './components/routing';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import { useLocation, useNavigate } from 'react-router-dom';
import { Message } from '@arco-design/web-react';
import { getApplicationDetail, createApplication, updateApplication, ApplicationResponse, bindAgents, publishApplication, exportApplication } from '@/lib/services/application-service';
import ButtonComponent from '@arco-design/web-react/es/Button';
import Text from '@arco-design/web-react/es/Typography/text';

function AppInfo() {
  const locale = useLocale();
  const location = useLocation<{ id: string, path: string }>();
  const navigate = useNavigate();
  const { id, path } = location.state || {};
  const [loading, setLoading] = useState(false);
  const [applicationData, setApplicationData] = useState(null);
  const [newApplicationData, setNewApplicationData] = useState(null);
  const [lastFetchedId, setLastFetchedId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('appInfo');
  const [isEditing, setIsEditing] = useState(true);

  const currentApplicationIdRef = useRef(id);
  const currentPathRef = useRef(path);
  const agentSettingRef = useRef([]);

  // 从sessionStorage恢复activeTab
  const restoreActiveTabFromStorage = () => {
    if (currentApplicationIdRef.current) {
      const storedTab = sessionStorage.getItem(`agentTeam_${currentApplicationIdRef.current}_activeTab`);
      if (storedTab) {
        setActiveTab(storedTab);
        // 清理存储的状态
        sessionStorage.removeItem(`agentTeam_${currentApplicationIdRef.current}_activeTab`);
        return true;
      }
    }
    return false;
  };

  const fetchApplicationDetail = useCallback(async () => {
    if (loading && (currentApplicationIdRef.current === lastFetchedId)) {
      return;
    }

    try {
      setLoading(true);
      if (!currentApplicationIdRef.current) {
        setIsEditing(true);
        return;
      }
      const res = await getApplicationDetail(currentApplicationIdRef.current);
      setApplicationData(res);
      setNewApplicationData(res);
      setLastFetchedId(currentApplicationIdRef.current);
    } catch (error) {
      Message.error({
        content: locale['menu.application.opreate.errMsg']
      });
    } finally {
      setLoading(false);
    }
  }, [currentApplicationIdRef.current, loading, lastFetchedId, locale]);

  const handleApplicationDataUpdate = useCallback((newData: Partial<ApplicationResponse>, agentSetting?: any[], sequenceCardSetting?: any[]) => {

    setNewApplicationData(prev => {
      if (!prev) {
        return {
          id: '',
          name: 'New Application',
          description: 'New Application Description',
          profile: '',
          welcome: '',
          type: 'agent',
          provider: '',
          channel: 'openapi',
          iconUrl: null,
          installed: false,
          labels: [],
          isPublished: false,
          disabled: true,
          createdDateTime: '',
          updatedDateTime: '',
          agent: null,
          artifacts: null,
          openapi_auth_options: {
            auth_config: {
              client_id: '',
              client_secret: '',
              client_name: '',
            },
          },
        } as ApplicationResponse;
      }

      return { ...prev, ...newData };
    });

    if (agentSetting) {
      agentSettingRef.current = agentSetting;
    }

  }, []);

  useEffect(() => {
    setIsEditing(false);
    const initData = async () => {
      if (currentApplicationIdRef.current) {
        await fetchApplicationDetail();
      } else {
        setLoading(false);
        setIsEditing(true);
      }

      // 优先从sessionStorage恢复activeTab（处理从agent详情页返回的情况）
      const isRestored = restoreActiveTabFromStorage();

      if (!isRestored) {
        // 如果没有从storage恢复，则根据path设置activeTab
        if (currentPathRef.current === 'settings') {
          setActiveTab('appSettings');
        } else if (currentPathRef.current === 'routing') {
          setActiveTab('routing');
        }
      }
    };

    initData();
  }, []);

  const handleApplicationIdChange = (newId: string) => {
    currentApplicationIdRef.current = newId;
  };

  const handleCancel = () => {
    if (isEditing && applicationData?.id) {
      setIsEditing(false);

      const oldApplicationData = { ...applicationData };
      setNewApplicationData(oldApplicationData);

    } else {
      navigate('/application');
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = useCallback(async () => {
    try {
      setLoading(false);

      const currentAgentSetting = agentSettingRef.current;

      if (!newApplicationData?.id) {
        const res = await createApplication(newApplicationData as ApplicationResponse);
        setApplicationData(res);
        setNewApplicationData(res);

        if (currentAgentSetting.length > 0) {
          await bindAgents({
            applicationId: res.id,
            agentIds: [res.agent.id, ...currentAgentSetting.map(item => item.id)],
          });
        }

        handleApplicationIdChange(res.id);

        Message.success(locale['opreate.create.success']);

      } else {

        if (currentAgentSetting.length > 0) {
          await bindAgents({
            applicationId: newApplicationData.id,
            agentIds: currentAgentSetting.map(item => item.id),
          });
        }

        await updateApplication(newApplicationData.id, newApplicationData);
        Message.success(locale['opreate.save.success']);
      }

      await fetchApplicationDetail();
    } catch (error) {
      if (!newApplicationData?.id) {
        Message.error(locale['opreate.create.errMsg']);
      } else {
        Message.error(locale['opreate.save.errMsg']);
      }
    } finally {
      setLoading(false);
    }
  }, [newApplicationData, fetchApplicationDetail, locale]);

  const handlePublish = useCallback(async () => {
    try {
      setLoading(false);

      await handleSave();

      if (newApplicationData.openapi_auth_options.auth_config.client_id && newApplicationData.openapi_auth_options.auth_config.client_secret) {
        await publishApplication(newApplicationData.id);
        await fetchApplicationDetail();
        Message.success(locale['opreate.publish.success']);
      } else {
        Message.error('保存失败，请检查client_id和client_secret');
      }
    } catch (error) {
      Message.error(locale['opreate.publish.errMsg']);
    } finally {
      setLoading(false);
    }
  }, [newApplicationData, fetchApplicationDetail, locale]);

  const handleExport = useCallback(async () => {
    try {
      setLoading(true);

      const res = await exportApplication(newApplicationData.id);
      
      // 处理文件下载
      if (res && res.data) {
        // 从响应头中获取文件名
        const contentDisposition = res.headers['content-disposition'];
        let filename = 'agent_team_export.zip';
        
        if (contentDisposition) {
          const filenameMatch = contentDisposition.match(/filename\*?=['"]?([^'";\r\n]+)['"]?/);
          if (filenameMatch && filenameMatch[1]) {
            // 处理UTF-8编码的文件名
            filename = decodeURIComponent(filenameMatch[1]);
          }
        }

        // 创建Blob对象 (res.data 现在是 ArrayBuffer)
        const blob = new Blob([res.data], { type: 'application/zip' });
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        
        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }

      Message.success(locale['menu.application.opreate.export.success']);
    }
    catch (error) {
      console.error('Export error:', error);
      Message.error(locale['menu.application.opreate.export.errMsg']);
    } finally {
      setLoading(false);
    }
  }, [newApplicationData?.id, locale]);

  if (loading) {
    return (
      <div className={localStyles.customContainer} style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
        <Spin dot size={40} />
      </div>
    );
  }

  return (
    <div className={localStyles.customContainer}>
      <Tabs
        className={localStyles.tabs}
        activeTab={activeTab}
        onChange={setActiveTab}
      >
        {/* 应用页面内容 */}
        <TabPane key="appInfo" title={locale['menu.application.info.header.basic']}>
          <ApplicationBasic
            applicationData={newApplicationData}
            loading={loading}
            isEditing={isEditing}
            onApplicationDataUpdate={handleApplicationDataUpdate}
          />
        </TabPane>
        <TabPane key="appSettings" title={locale['menu.application.info.header.settings']}>
          <ApplicationSettings
            applicationData={newApplicationData}
            loading={loading}
            isEditing={isEditing}
            onApplicationDataUpdate={handleApplicationDataUpdate}
            onPublish={handlePublish}
          />
        </TabPane>
        <TabPane key="appTesting" title={locale['menu.application.info.header.testing']}>
          <ApplicationTesting applicationData={newApplicationData} loading={loading} />
        </TabPane>
        {id && (
          <TabPane key="routing" title={locale['menu.application.info.header.routingMap']}>
            <ApplicationRouting
              applicationData={newApplicationData}
              loading={loading}
              onRefresh={fetchApplicationDetail}
              onApplicationIdChange={handleApplicationIdChange}
            />
          </TabPane>
        )}
      </Tabs>

      {(activeTab === 'appInfo' || activeTab === 'appSettings') && (
        <div className={localStyles.footer}>
          <RowComponent className={localStyles.operateButGroup}>
            <ButtonComponent type='secondary' className={[localStyles.cancelBut, localStyles.but]} onClick={handleCancel}>
              <Text className={localStyles.text}>{locale['menu.application.template.setting.operate.cancel']}</Text>
            </ButtonComponent>
            {newApplicationData?.id ? (
              <>
                <ButtonComponent type='secondary' className={[localStyles.cancelBut, localStyles.but]} onClick={handleExport}>
                  <Text className={localStyles.text}>{locale['menu.application.opreate.export']}</Text>
                </ButtonComponent>
                {!isEditing ? (
                  <ButtonComponent loading={loading} onClick={handleEdit} type='primary' className={[localStyles.createBut, localStyles.but]}>
                    <Text className={localStyles.text} style={{ color: '#FFFFFF' }}>
                      {locale['editBut']}
                    </Text>
                  </ButtonComponent>
                ) : (<ButtonComponent loading={loading} onClick={handleSave} type='primary' className={[localStyles.createBut, localStyles.but]}>
                  <Text className={localStyles.text} style={{ color: '#FFFFFF' }}>
                    {locale['menu.application.opreate.save']}
                  </Text>
                </ButtonComponent>)
                }
              </>
            ) : (
              <ButtonComponent loading={loading} onClick={handleSave} type='primary' className={[localStyles.createBut, localStyles.but]}>
                <Text className={localStyles.text} style={{ color: '#FFFFFF' }}>
                  {locale['menu.application.opreate.create']}
                </Text>
              </ButtonComponent>
            )}
          </RowComponent>
        </div>
      )}
    </div>
  );
}

export default AppInfo;
