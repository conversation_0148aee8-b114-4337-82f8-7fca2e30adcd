.card {
    height: 100%;
    border: 1px solid #f5f5f5;
    border-radius: 8px;
    padding: 20px 24px;
    overflow: hidden;

    :global(.arco-card-body) {
        padding: 0;
    }

    :global(.arco-card-header) {
        padding: 0;
        height: 32px;
        margin-bottom: 16px;
    }
}

.cardHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.HeaderTag {
    display: flex;
    align-items: center;
}

.title {
    margin-left: 12px;
    color: #333333;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    white-space: nowrap;
}

.chart {
    width: 100%;
    min-height: 200px;
    max-height: 300px;
    overflow: hidden;
}

@media screen and (max-width: 768px) {
    .chart {
        height: 250px !important;
    }
}

@media screen and (max-width: 480px) {
    .chart {
        height: 200px !important;
    }
}

.legend {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 30px;
    margin-top: 10px;
}

.legendItem {
    display: flex;
    align-items: center;
    margin-right: 16px;
}

.legendItemLabel {
    margin-left: 4px;
    color: var(--color-text-2);
}

.legendItemIcon {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
}