import React, { useState, useEffect } from 'react';
import {
    Card,
    Typography,
    Input,
    Select,
    Space,
    Grid,
    Button,
    Spin,
} from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import styles from './style/index.module.less';
import IconSearch from '@/assets/sdk/IconSearch.svg';
import { useDispatch, useSelector } from 'react-redux';
import { GlobalState } from '@/store/index';
import { useNavigate } from 'react-router-dom';
import IconEmptySDK from '@/assets/sdk/IconEmptySDK.svg';
import IconBuildingSDK from '@/assets/IconBuilding.svg';
const { Row, Col } = Grid;
const { Text, } = Typography;

function SdkList() {
    const locale = useLocale();
    const [searchValue, setSearchValue] = useState('');
    const [sortValue, setSortValue] = useState('createTime');
    const [loading, setLoading] = useState(false);
    const [building, setBuilding] = useState(true);
    const sdkDetailMenuName = useSelector((state: GlobalState) => state.sdkDetailMenuName);
    const dispatch = useDispatch();
    const navigate = useNavigate();

    // 模拟SDK数据
    const sdkList = [
        // {
        //     id: 1,
        //     name: 'H5通用版SDK',
        //     icon: <IconSdk />,
        //     iconColor: '#6b7afc',
        //     version: '1.0.4',
        //     description: '支持在PC端和移动端基于浏览器对3D模型或地图级...',
        //     updateTime: '2022/03/02'
        // },
        // {
        //     id: 2,
        //     name: 'Unity插件版SDK',
        //     icon: <IconSdk />,
        //     iconColor: '#f85a5a',
        //     version: '1.0.4',
        //     description: '支持在Unity中利用ARKIT数据源对3D模型进行可...',
        //     updateTime: '2022/03/02'
        // },
        // {
        //     id: 3,
        //     name: '微信小程序版SDK',
        //     icon: <IconSdk />,
        //     iconColor: '#00c48f',
        //     version: '1.0.1',
        //     description: '支持在微信小程序中进行3D可视化应用开发，包括...',
        //     updateTime: '2022/03/02'
        // },
        // {
        //     id: 4,
        //     name: '微信小程序版SDK',
        //     icon: <IconSdk />,
        //     iconColor: '#ff9a2a',
        //     version: '1.0.1',
        //     description: '支持在微信小程序中进行3D可视化应用开发，包括...',
        //     updateTime: '2022/03/02'
        // }
    ];

    const filteredSdkList = sdkList.filter(sdk =>
        sdk.name.toLowerCase().includes(searchValue.toLowerCase())
    );

    const updateBreadcrumbData = (newBreadcrumb: {}) => {
        // 使用 dispatch 来发送 action 更新 breadcrumb
        dispatch({
            type: 'update-breadcrumb-menu-name',
            payload: { breadcrumbMenuName: newBreadcrumb }, // 设置新的breadcrumb数组
        });
        console.log(newBreadcrumb)
    };

    const gotoList = (sdk) => {
        const breadcrumbData = new Map<string, string>([[sdkDetailMenuName, sdk.name]]);
        updateBreadcrumbData(breadcrumbData);
        navigate("/sdk/detail");
    }

    const renderContent = () => {
        if (loading && filteredSdkList.length === 0) {
            return (
                <div className={styles.loadingContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ padding: 24 }}>
                            <Spin tip={locale['menu.loading'] || '加载中...'} />
                        </div>
                    </Space>
                </div>
            );
        }

        if (building && filteredSdkList.length === 0 && !searchValue) {
            return (
                <div className={styles.buildingContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <IconBuildingSDK style={{ width: 80, height: 80 }} />
                        <Text className={styles.buildingText}>
                            努力建设中，马上就来~
                        </Text>
                    </Space>
                </div>
            );
        }

        if (filteredSdkList.length === 0) {
            return (
                <div className={styles.emptyContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <IconEmptySDK style={{ width: 80, height: 80 }} />
                        <Text className={styles.emptyText}>
                            {searchValue ? '未找到匹配的SDK' : '暂无数据'}
                        </Text>
                    </Space>
                </div>
            );
        }

        return (
            <>
                {filteredSdkList.map(sdk => (
                    <Card key={sdk.id} className={styles.sdkCard} bordered={true} onClick={() => { gotoList(sdk) }}>
                        <Space direction='vertical' size={8}>
                            <Space className={styles.sdkInfo} size={12}>
                                <div className={styles.sdkIcon}>{sdk.icon}</div>
                                <Text className={styles.sdkName}>{sdk.name}</Text>
                            </Space>
                            <Text className={styles.sdkDescription}>
                                {sdk.description}
                            </Text>
                        </Space>

                        <div className={styles.sdkUpdateTime}>
                            更新日期: {sdk.updateTime}
                        </div>
                        <div className={styles.sdkActions}>
                            <Button type="primary" className={styles.sdkDownloadBtn}>下载</Button>
                            <Button className={styles.sdkDocBtn}>开发文档</Button>
                        </div>
                    </Card>
                ))}
            </>
        );
    };

    return (
        <div className={styles.container}>
            <Card bordered={false}>
                <Typography.Title heading={5} className={styles.TypographyTitle}>SDK列表</Typography.Title>
                <div className={styles.header}>
                    <Row align="center">
                        <Col xs={24} sm={12}>
                            <Space>
                                <Input
                                    placeholder="AI搜索..."
                                    value={searchValue}
                                    onChange={setSearchValue}
                                    style={{ width: '240px' }}
                                    prefix={<IconSearch />}
                                />
                                <div className={styles.SDKtatol}>共 {filteredSdkList.length} 个SDK</div>
                            </Space>
                        </Col>
                    </Row>
                </div>
                {/* Content Section - SDK List with Responsive Grid */}
                <div className={styles.sdkList}>
                    {renderContent()}
                </div>
            </Card>
        </div>
    );
}

export default SdkList;