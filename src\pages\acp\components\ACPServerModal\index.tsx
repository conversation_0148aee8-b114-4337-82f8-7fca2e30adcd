import React, { useState, useEffect } from 'react';
import { Button, Input, Typography, Space, Modal, Form, Alert, Grid, Switch, Slider, Radio, Select } from '@arco-design/web-react';
import IconClose from '@/assets/acp/IconClose.svg';
import IconDeleteTransportOptions from '@/assets/acp/IconDeleteTransportOptions.svg';
import { testAcpServerConnection, getTransportTypes } from '@/lib/services/acp-server-service';
import styles from './style/index.module.less';
import { AcpServerResponse, TransportType , AcpServer, AcpServerTestResponse} from '@/types/acpServerType';

const FormItem = Form.Item;
const { Text } = Typography;
const { Row } = Grid;

// 创建一个禁用默认必填标记的表单项组件
const FormItemWithoutRequiredMark = (props) => {
    return <FormItem {...props} requiredSymbol={false} />;
};

// 创建自定义标签组件，与SecretKey组件保持一致
const CustomLabel = ({ label, required }) => {
    return (
        <Space>
            <span>{label}</span>
            {required && <Text className={styles.requiredIcon}>*</Text>}
        </Space>
    );
};

// 扩展AcpServer接口，添加新的字段
interface ExtendedAcpServer extends AcpServer {
    is_available?: boolean;   // 是否可用
    query_params?: {
        [key: string]: string;
    };
    http_transport_model?: {
        // use_streamable_http: boolean;
        name: string;
        connection_timeout_seconds: number;
        additional_headers: {
            [key: string]: string;
        };
    };
}   

interface ACPServerModalProps {
    visible: boolean;
    existingAcps: AcpServerResponse[];
    onCancel: () => void;
    onSubmit: (values: ExtendedAcpServer) => Promise<void>;
}

function ACPServerModal({ visible, existingAcps, onCancel, onSubmit }: ACPServerModalProps) {
    const [form] = Form.useForm();
    const [testConnectionStatus, setTestConnectionStatus] = useState<'none' | 'success' | 'failed'>('none');
    const [isTestingConnection, setIsTestingConnection] = useState(false);
    const [formValid, setFormValid] = useState(false);
    const [isAvailable, setIsAvailable] = useState(false); // 用于跟踪ACP的可用性
    const originalFormValuesRef = React.useRef({});
    // Alert元素的ref引用
    const alertRef = React.useRef<HTMLDivElement>(null);
    // Query Params和Additional Headers相关状态
    const [queryParamsList, setQueryParamsList] = useState<{ key: string, value: string }[]>([]);
    const [additionalHeaders, setAdditionalHeaders] = useState<{ key: string, value: string }[]>([]);

    // 客户端传输项相关状态
    // const [useStreamingHttp, setUseStreamingHttp] = useState(false);
    const [clientName, setClientName] = useState('');
    const [connectionTimeout, setConnectionTimeout] = useState(0);
    
    // 添加传输类型状态
    const [transportTypes, setTransportTypes] = useState<TransportType>({});

    // 用于跟踪最后一次测试时的表单数据
    const [lastTestedFormData, setLastTestedFormData] = useState<any>(null);

    //  场景A: 用户填写数据 → 手动测试成功 → 直接点确定 → 不重复测试，直接保存
    // 场景B: 用户填写数据 → 手动测试失败 → 直接点确定 → 不重复测试，保存失败状态  
    // 场景C: 用户填写数据 → 手动测试成功 → 修改了URL → 点确定 → 重新测试（因为数据变了）
    // 场景D: 用户填写数据 → 没有手动测试 → 直接点确定 → 自动执行测试

    // 构建当前表单数据快照
    const buildCurrentFormSnapshot = () => {
        const formValues = form.getFieldsValue();
        return {
            name: formValues.name || '',
            description: formValues.description || '',
            location: formValues.location || '',
            transport_type: formValues.transport_type || 1,
            queryParams: JSON.stringify(queryParamsList),
            additionalHeaders: JSON.stringify(additionalHeaders),
            // useStreamingHttp: useStreamingHttp,
            clientName: clientName,
            connectionTimeout: connectionTimeout
        };
    };

    // 比较当前表单数据是否与最后一次测试时的数据相同
    const isFormDataChanged = () => {
        if (!lastTestedFormData) return true; // 如果没有测试过，认为数据已变化

        const currentSnapshot = buildCurrentFormSnapshot();
        return JSON.stringify(currentSnapshot) !== JSON.stringify(lastTestedFormData);
    };

    // 重置表单状态
    const resetFormState = () => {
        setTestConnectionStatus('none');
        setFormValid(false);
        setIsAvailable(false); // 重置可用性状态
        // 重置Query Params和Additional Headers相关状态
        setQueryParamsList([]);
        setAdditionalHeaders([]);
        // 重置客户端传输项相关状态
        // setUseStreamingHttp(false);
        setClientName('');
        setConnectionTimeout(0);
        // 重置最后测试的表单数据
        setLastTestedFormData(null);
        form.resetFields();
    };

    // 当模态框显示时，重置表单状态
    useEffect(() => {
        if (visible) {
            form.resetFields();
            form.setFieldsValue({ transport_type: 1 });
            setFormValid(false);
            setIsAvailable(false); // 创建模式下初始化为false
            setTestConnectionStatus('none');
            // 重置所有新增状态
            setQueryParamsList([]);
            setAdditionalHeaders([]);
            // setUseStreamingHttp(false);
            setClientName('');
            setConnectionTimeout(0);
            // 重置最后测试的表单数据
            setLastTestedFormData(null);
            
            // 获取传输类型列表
            fetchTransportTypes();
        }
    }, [visible, form]);
    
    // 获取传输类型列表
    const fetchTransportTypes = async () => {
        try {
            const types = await getTransportTypes();
            setTransportTypes(types);
        } catch (error) {
            console.error('获取传输类型列表失败:', error);
        }
    };

    // 处理表单值变化
    const handleFormValuesChange = (changedValues, allValues) => {
        setFormValid(checkFormValid(allValues));
    };

    // 检查表单是否有效
    const checkFormValid = (values) => {
        return values.name && values.location && values.description;
    };

    // 检查名称是否重复
    const validateName = (value: string, callback: any) => {
        if (!value) {
            callback();
            return;
        }

        // 检查是否存在同名的ACP
        const isDuplicate = existingAcps.some(acp =>
            acp.name === value
        );

        if (isDuplicate) {
            callback('该名称已存在，请使用其他名称');
        } else {
            callback();
        }
    };

    // 测试连接
    const handleTestConnection = async () => {
        setIsTestingConnection(true);
        setTestConnectionStatus('none'); // 重置状态

        // 自动滚动到Alert位置，方便用户查看测试状态
        setTimeout(() => {
            if (alertRef.current) {
                alertRef.current.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }
        }, 100); // 稍微延迟一下确保状态更新完成

        try {
            // 获取当前表单的值
            const formValues = form.getFieldsValue();

            // 检查必要字段是否填写
            if (!formValues.name || !formValues.description || !formValues.location) {
                setTestConnectionStatus('failed');
                setIsAvailable(false); // 测试失败时设置为不可用
                setIsTestingConnection(false);
                return;
            }

            // 将queryParamsList数组转换为对象格式
            const queryParamsObj: { [key: string]: string } = {};
            queryParamsList.forEach(param => {
                if (param.key && param.value) {
                    queryParamsObj[param.key] = param.value;
                }
            });

            // 将additionalHeaders数组转换为对象格式
            const additionalHeadersObj: { [key: string]: string } = {};
            additionalHeaders.forEach(header => {
                if (header.key && header.value) {
                    additionalHeadersObj[header.key] = header.value;
                }
            });

            // 调用测试连接API - 传递完整的参数与创建接口保持一致
            const testData = {
                name: formValues.name,
                description: formValues.description,
                transport_type: formValues.transport_type || 1,
                location: formValues.location,
                is_available: isAvailable,
                query_params: queryParamsObj || {},
                http_transport_model: {
                    // use_streamable_http: useStreamingHttp || false,
                    name: clientName || '',
                    connection_timeout_seconds: connectionTimeout || 30,
                    additional_headers: additionalHeadersObj || {}
                }
            };

            const response: AcpServerTestResponse = await testAcpServerConnection(testData);
            const isSuccess = response.success;
            setTestConnectionStatus(isSuccess ? 'success' : 'failed');
            setIsAvailable(isSuccess); // 根据测试结果设置可用性

            // 保存当前表单数据快照，用于后续智能判断
            setLastTestedFormData(buildCurrentFormSnapshot());
        } catch (error) {
            console.error('测试连接失败:', error);
            setTestConnectionStatus('failed');
            setIsAvailable(false); // 异常时设置为不可用

            // 即使测试失败也保存快照，避免重复测试相同的错误配置
            setLastTestedFormData(buildCurrentFormSnapshot());
        } finally {
            setIsTestingConnection(false);
        }
    };

    // 添加处理query param的函数
    const handleAddQueryParam = () => {
        setQueryParamsList([...queryParamsList, { key: '', value: '' }]);
    };

    // 处理删除query param
    const handleDeleteQueryParam = (index) => {
        const newParams = [...queryParamsList];
        newParams.splice(index, 1);
        setQueryParamsList(newParams);
    };

    // 处理更新query param的键值
    const handleQueryParamChange = (index, field, value) => {
        const newParams = [...queryParamsList];
        newParams[index][field] = value;
        setQueryParamsList(newParams);
    };

    // 在组件中添加以下函数来处理additional headers的添加
    const handleAddAdditionalHeader = () => {
        setAdditionalHeaders([...additionalHeaders, { key: '', value: '' }]);
    };

    // 处理删除additional header
    const handleDeleteAdditionalHeader = (index) => {
        const newHeaders = [...additionalHeaders];
        newHeaders.splice(index, 1);
        setAdditionalHeaders(newHeaders);
    };

    // 处理更新additional header的键值
    const handleAdditionalHeaderChange = (index, field, value) => {
        const newHeaders = [...additionalHeaders];
        newHeaders[index][field] = value;
        setAdditionalHeaders(newHeaders);
    };

    // 处理提交
    const handleSubmit = () => {
        form.validate().then(async (values) => {
            try {
                // 将queryParamsList数组转换为对象格式
                const queryParamsObj: { [key: string]: string } = {};
                queryParamsList.forEach(param => {
                    if (param.key && param.value) {
                        queryParamsObj[param.key] = param.value;
                    }
                });

                // 将additionalHeaders数组转换为对象格式
                const additionalHeadersObj: { [key: string]: string } = {};
                additionalHeaders.forEach(header => {
                    if (header.key && header.value) {
                        additionalHeadersObj[header.key] = header.value;
                    }
                });

                let finalIsAvailable = isAvailable;

                // 智能判断：检查表单数据是否变化
                if (testConnectionStatus !== 'none' && !isFormDataChanged()) {
                    // 数据没有变化，直接使用之前的测试结果
                    console.log('表单数据未变化，使用缓存的测试结果');
                } else {
                    // 数据发生变化或未测试过，需要重新测试
                    console.log('表单数据已变化或未测试过，执行重新测试');

                    setIsTestingConnection(true);
                    setTestConnectionStatus('none');

                    // 自动滚动到Alert位置，方便用户查看测试状态
                    setTimeout(() => {
                        if (alertRef.current) {
                            alertRef.current.scrollIntoView({
                                behavior: 'smooth',
                                block: 'center'
                            });
                        }
                    }, 100);

                    // 构建测试数据
                    const testData = {
                        name: values.name,
                        description: values.description,
                        transport_type: values.transport_type || 1,
                        location: values.location,
                        is_available: isAvailable,
                        query_params: queryParamsObj || {},
                        http_transport_model: {
                            // use_streamable_http: useStreamingHttp || false,
                            name: clientName || '',
                            connection_timeout_seconds: connectionTimeout || 30,
                            additional_headers: additionalHeadersObj || {}
                        }
                    };

                    // 执行测试连接
                    const response: AcpServerTestResponse = await testAcpServerConnection(testData);
                    const isSuccess = response.success;

                    setTestConnectionStatus(isSuccess ? 'success' : 'failed');
                    setIsAvailable(isSuccess);
                    finalIsAvailable = isSuccess;

                    // 保存当前表单数据快照
                    setLastTestedFormData(buildCurrentFormSnapshot());

                    setIsTestingConnection(false);
                }

                // 提交数据
                await onSubmit({
                    name: values.name || '',
                    transport_type: values.transport_type || 1,
                    location: values.location || '',
                    description: values.description || '',
                    is_available: finalIsAvailable, // 使用最终的可用性状态
                    query_params: queryParamsObj || {},
                    http_transport_model: {
                        // use_streamable_http: useStreamingHttp || false,
                        name: clientName || '',
                        connection_timeout_seconds: connectionTimeout || 30,
                        additional_headers: additionalHeadersObj || {}
                    }
                });
            } catch (error) {
                console.error('测试连接或创建ACP失败:', error);
                setTestConnectionStatus('failed');
                setIsAvailable(false);
                setIsTestingConnection(false);
            }
        }).catch(errors => {
            console.log('表单验证失败:', errors);
            if (errors && Object.keys(errors).length > 0) {
                console.log('验证错误详情:', errors);
            }
        });
    };

    // 处理取消
    const handleCancel = () => {
        resetFormState();
        onCancel();
    };

    return (
        <Modal
            visible={visible}
            title="新增ACP Server"
            onCancel={handleCancel}
            closeIcon={<IconClose />}
            className={styles.acpCreateModal}
            maskClosable={false}
        >
            <div className={styles.modalContent}>
                {/* Alert区域 - 添加ref用于自动滚动 */}
                <div ref={alertRef}>
                    {testConnectionStatus === 'success' && (
                        <Alert
                            type="success"
                            content="测试连接成功"
                            className={`${styles.testConnectionAlert} ${styles.successAlert}`}
                        />
                    )}
                    {testConnectionStatus === 'failed' && (
                        <Alert
                            type="error"
                            content="测试连接失败，请检查配置"
                            className={`${styles.testConnectionAlert} ${styles.failedAlert}`}
                        />
                    )}
                </div>
                <Form form={form} autoComplete="off" layout="vertical" onValuesChange={handleFormValuesChange} requiredSymbol={false}>
                    <FormItemWithoutRequiredMark
                        label={<CustomLabel label="名称" required={true} />}
                        field="name"
                        rules={[
                            { required: true, message: '请输入名称' },
                            { validator: validateName }
                        ]}
                        validateTrigger={['onChange', 'onBlur']}
                    >
                        <Input placeholder="请输入" />
                    </FormItemWithoutRequiredMark>

                    <FormItemWithoutRequiredMark
                        label={<CustomLabel label="描述" required={true} />}
                        field="description"
                        rules={[{ required: true, message: '请输入描述' }]}
                    >
                        <Input.TextArea
                            placeholder="用简单明了的话描述~"
                            rows={4}
                            maxLength={100}
                            className={styles.descriptionInput}
                            showWordLimit
                        />
                    </FormItemWithoutRequiredMark>

                    {/* <div style={{ marginBottom: '8px', fontSize: '12px', color: '#adadad' }}>
                        请选择连接类型
                    </div> */}
                    <FormItemWithoutRequiredMark
                        label="连接类型"
                        field="transport_type"
                        initialValue={1}
                    >
                        {/* 使用Select组件渲染传输类型选项*/}
                        <Select 
                            placeholder="请选择连接类型" 
                            style={{ width: '100%' }}
                            dropdownMenuClassName="acp-server-detail-select-popup"
                        >
                            {Object.entries(transportTypes).map(([name, value]) => {
                                // 获取中文描述
                                const getDisplayName = (key: string) => {
                                    switch (key) {
                                        case 'auto_detect_http':
                                            return '自动检测(auto_detect_http)';
                                        case 'streamable_http':
                                            return '可流式传输的HTTP(streamable_http)';
                                        case 'sse':
                                            return '服务器发送事件(sse)';
                                        default:
                                            return key;
                                    }
                                };
                                
                                return (
                                    <Select.Option key={name} value={value}>
                                        {getDisplayName(name)}
                                    </Select.Option>
                                );
                            })}
                        </Select>
                    </FormItemWithoutRequiredMark>
                    <FormItemWithoutRequiredMark
                        label={<CustomLabel label="URL" required={true} />}
                        field="location"
                        rules={[{ required: true, message: '请输入URL' }]}
                    >
                        <Input placeholder="请输入" />
                    </FormItemWithoutRequiredMark>

                    {/* 添加Query Params模块 */}
                    <FormItemWithoutRequiredMark className={styles.queryParamsFormItem}>
                        <Row justify="space-between" align="center" style={{ marginBottom: queryParamsList.length > 0 ? '8px' : '0' }}>
                            <Space className={styles.templatePlaceholder} direction="vertical" size={'mini'}>
                                <Text style={{ color: '#333333', fontWeight: '600', fontSize: '14px' }}>参数</Text>
                                <Text className={styles.labelExact}>URL扩展参数</Text>
                            </Space>
                            <Button
                                className={styles.addTagBtn}
                                onClick={handleAddQueryParam}
                            >
                                添加
                            </Button>
                        </Row>
                        {queryParamsList.length > 0 && (
                            <div className={styles.queryParamsContainer} style={{ border: '1px solid #f5f5f5', borderRadius: '8px', padding: '8px', marginTop: '8px', backgroundColor: '#fafafa' }}>
                                <Row className={styles.queryParamsHeader} style={{ marginBottom: '8px', padding: '0 16px' }}>
                                    <div style={{ display: 'flex', width: '100%', gap: '8px' }}>
                                        <Text style={{ flex: 1, color: '#adadad', fontSize: '14px' }}>名称</Text>
                                        <Text style={{ flex: 1, color: '#adadad', fontSize: '14px' }}>值</Text>
                                        <div style={{ width: '24px' }}></div>
                                    </div>
                                </Row>

                                <div className={styles.selectedItemList}>
                                    {queryParamsList.map((param, index) => (
                                        <Row key={`query-param-${index}`} className={styles.selectedItemRow} align="center" style={{ marginBottom: '8px', padding: '0 8px' }}>
                                            <div style={{ display: 'flex', width: '100%', gap: '8px', alignItems: 'center' }}>
                                                <div style={{ flex: 1, position: 'relative' }}>
                                                    <Input
                                                        value={param.key}
                                                        autoFocus={index === queryParamsList.length - 1}
                                                        onChange={(value) => {
                                                            if (value && value.length > 50) {
                                                                return;
                                                            }
                                                            handleQueryParamChange(index, 'key', value);
                                                        }}
                                                        placeholder="例如: additionalProp1"
                                                        style={{ width: '100%' }}
                                                    />
                                                    <div style={{
                                                        position: 'absolute',
                                                        right: '12px',
                                                        bottom: '8px',
                                                        fontSize: '14px',
                                                        color: '#adadad'
                                                    }}>
                                                        {param.key ? param.key.length : 0}/50
                                                    </div>
                                                </div>
                                                <div style={{ flex: 1, position: 'relative' }}>
                                                    <Input
                                                        value={param.value}
                                                        onChange={(value) => {
                                                            if (value && value.length > 50) {
                                                                return;
                                                            }
                                                            handleQueryParamChange(index, 'value', value);
                                                        }}
                                                        placeholder="例如: string"
                                                        style={{ width: '100%' }}
                                                    />
                                                    <div style={{
                                                        position: 'absolute',
                                                        right: '12px',
                                                        bottom: '8px',
                                                        fontSize: '14px',
                                                        color: '#adadad'
                                                    }}>
                                                        {param.value ? param.value.length : 0}/50
                                                    </div>
                                                </div>
                                                <Button
                                                    className={styles.deleteOptionBtn}
                                                    icon={<IconDeleteTransportOptions />}
                                                    onClick={() => {
                                                        handleDeleteQueryParam(index);
                                                    }}
                                                />
                                            </div>
                                        </Row>
                                    ))}
                                </div>
                            </div>
                        )}
                    </FormItemWithoutRequiredMark>

                    {/* 客户端传输项模块 */}
                    <div className={styles.clientTransportSection}>
                        <Text style={{ color: '#333333', fontWeight: '500', fontSize: '14px', marginBottom: '24px', padding: '4px 10px', borderRadius: '8px', backgroundColor: '#f7f7f7', display: 'inline-block' }}>
                            HTTP客户端传输选项
                        </Text>

                        {/* 使用可流式传输的HTTP */}
                        {/* <FormItemWithoutRequiredMark style={{ marginBottom: '24px' }} field="use_streamable_http">
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Text style={{ color: '#333333', fontWeight: '600', fontSize: '14px' }}>
                                    使用可流式传输的HTTP
                                </Text>
                                <Switch
                                    checked={useStreamingHttp}
                                    onChange={setUseStreamingHttp}
                                />
                            </div>
                        </FormItemWithoutRequiredMark> */}

                        {/* 客户端名称 */}
                        <FormItemWithoutRequiredMark
                            label="客户端名称"
                            field="client_name"
                        >
                            <Input
                                placeholder="请输入"
                                value={clientName}
                                onChange={setClientName}
                            />
                        </FormItemWithoutRequiredMark>

                        {/* 初始连接超时时间 */}
                        <FormItemWithoutRequiredMark
                            label="初始连接超时时间"
                            field="connection_timeout_seconds"
                        >
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }} className={styles.connectionTimeoutContainer}>
                                <Input
                                    style={{ width: '120px' }}
                                    value={connectionTimeout.toString()}
                                    onChange={(value) => {
                                        const numValue = parseInt(value) || 0;
                                        if (numValue >= 0 && numValue <= 60) {
                                            setConnectionTimeout(numValue);
                                        }
                                    }}
                                    placeholder="0"
                                />
                                <div style={{ flex: 1, paddingLeft: '16px', paddingRight: '16px' }} className={styles.sliderContainer}>
                                    <Slider
                                        value={connectionTimeout}
                                        onChange={(value) => {
                                            if (typeof value === 'number') {
                                                setConnectionTimeout(value);
                                            }
                                        }}
                                        min={0}
                                        max={60}
                                        step={1}
                                        style={{ width: '100%' }}
                                    />
                                </div>
                            </div>
                        </FormItemWithoutRequiredMark>

                        {/* 添加Additional Headers模块 */}
                        <FormItemWithoutRequiredMark className={styles.additionalHeadersFormItem} style={{ marginBottom: '0px' }} field="additional_headers">
                            <Row justify="space-between" align="center" style={{ marginBottom: additionalHeaders.length > 0 ? '8px' : '0' }}>
                                <Space className={styles.templatePlaceholder} direction="vertical" size={'mini'}>
                                    <Text style={{ color: '#333333', fontWeight: '600', fontSize: '14px' }}>自定义HTTP请求头</Text>
                                </Space>
                                <Button
                                    className={styles.addTagBtn}
                                    onClick={handleAddAdditionalHeader}
                                >
                                    添加
                                </Button>
                            </Row>

                            {additionalHeaders.length > 0 && (
                                <div className={styles.additionalHeadersContainer} style={{ border: '1px solid #f5f5f5', borderRadius: '8px', padding: '8px', marginTop: '8px', backgroundColor: '#fafafa' }}>
                                    <Row className={styles.additionalHeadersHeader} style={{ marginBottom: '8px', padding: '0 16px' }}>
                                        <div style={{ display: 'flex', width: '100%', gap: '8px' }}>
                                            <Text style={{ flex: 1, color: '#adadad', fontSize: '14px' }}>名称</Text>
                                            <Text style={{ flex: 1, color: '#adadad', fontSize: '14px' }}>值</Text>
                                            <div style={{ width: '24px' }}></div>
                                        </div>
                                    </Row>

                                    <div className={styles.selectedItemList}>
                                        {additionalHeaders.map((option, index) => (
                                            <Row key={`additional-header-${index}`} className={styles.selectedItemRow} align="center" style={{ marginBottom: '8px', padding: '0 8px' }}>
                                                <div style={{ display: 'flex', width: '100%', gap: '8px', alignItems: 'center' }}>
                                                    <div style={{ flex: 1, position: 'relative' }}>
                                                        <Input
                                                            value={option.key}
                                                            autoFocus={index === additionalHeaders.length - 1}
                                                            onChange={(value) => {
                                                                if (value && value.length > 99) {
                                                                    return;
                                                                }
                                                                handleAdditionalHeaderChange(index, 'key', value);
                                                            }}
                                                            placeholder="例如: additionalProp1"
                                                            style={{ width: '100%' }}
                                                        />
                                                        <div style={{
                                                            position: 'absolute',
                                                            right: '12px',
                                                            bottom: '8px',
                                                            fontSize: '14px',
                                                            color: '#adadad'
                                                        }}>
                                                            {option.key ? option.key.length : 0}/99
                                                        </div>
                                                    </div>
                                                    <div style={{ flex: 1, position: 'relative' }}>
                                                        <Input
                                                            value={option.value}
                                                            onChange={(value) => {
                                                                // if (value && value.length > 50) {
                                                                //     return;
                                                                // }
                                                                handleAdditionalHeaderChange(index, 'value', value);
                                                            }}
                                                            placeholder="例如: string"
                                                            style={{ width: '100%' }}
                                                        />
                                                        <div style={{
                                                            position: 'absolute',
                                                            right: '12px',
                                                            bottom: '8px',
                                                            fontSize: '14px',
                                                            color: '#adadad'
                                                        }}>
                                                            {option.value ? option.value.length : 0}
                                                        </div>
                                                    </div>
                                                    <Button
                                                        className={styles.deleteOptionBtn}
                                                        icon={<IconDeleteTransportOptions />}
                                                        onClick={() => {
                                                            handleDeleteAdditionalHeader(index);
                                                        }}
                                                    />
                                                </div>
                                            </Row>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </FormItemWithoutRequiredMark>
                    </div>
                </Form>
            </div>
            <div className={styles.modalFooter}>
                <Button
                    onClick={handleTestConnection}
                    className={styles.testConnectionBtn}
                    loading={isTestingConnection}
                >
                    测试连接
                </Button>
                <div className={styles.rightBtns}>
                    <Button onClick={handleCancel} className={styles.cancelBtn}>取消</Button>
                    <Button
                        onClick={handleSubmit}
                        className={`${styles.saveBtn} ${!formValid ? styles.disabledBtn : ''}`}
                        disabled={!formValid || isTestingConnection}
                        loading={isTestingConnection}
                        style={!formValid ? { backgroundColor: '#c3c8fa', cursor: 'not-allowed' } : {}}
                    >
                        确定
                    </Button>
                </div>
            </div>
        </Modal>
    );
}

export default ACPServerModal; 