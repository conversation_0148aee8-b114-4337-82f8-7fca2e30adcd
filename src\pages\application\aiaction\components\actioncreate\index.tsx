import React, { useState, useEffect } from 'react';
import {
  Card,
  Grid,
  Typography,
  Input,
  Button,
  Space,
  Tag,
  Upload,
  Form,
  Image,
  Message,
} from '@arco-design/web-react';
import IconUpload from '@/assets/application/IconPlus.svg';
import IconModel from '@/assets/application/ModelIocn.svg';
import IconCloseTag from '@/assets/application/IconCloseTag.svg';
import AddIcon from '@/assets/application/addIcon.svg';
import AgentIcon from '@/assets/application/agentIcon1.png';
import AgentIconSmall from '@/assets/application/AgentIconSmall.png';
import ApplicationIcon from '@/assets/application/ApplicationIcon.svg';
import WorkflowIcon from '@/assets/application/workflowIcon.png';
import styles from './style/index.module.less';
import useLocale from '@/utils/useLocale';
import TreeModal from './components/TreeModal/TreeModal';
import { getAgentList } from '@/lib/services/agent-service';
import { getApplicationList } from '@/lib/services/application-service';
import { getWorkflowList } from '@/lib/services/workflow-service';
import { IconDragDotVertical } from '@arco-design/web-react/icon';
import { ReactSortable } from 'react-sortablejs';

const { Row, Col } = Grid;
const { Title } = Typography;
const FormItem = Form.Item;
const RowComponent = Row;
const Text = Typography.Text;

// Custom label component for required fields
const CustomLabel = ({ label, required }) => {
  return (
    <Space>
      <span>{label}</span>
      {required && (
        <Typography.Text type="secondary" className={styles.required}>
          (必填)
        </Typography.Text>
      )}
    </Space>
  );
};

const AddActionConfig = () => {
  const [form] = Form.useForm();
  const [tags, setTags] = useState([]);
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const locale = useLocale();

  // State for Agent (条件触发 and 任务执行动作)
  const [agentSetting, setAgentSetting] = useState([]);
  const [agentData, setAgentData] = useState([]);

  // State for Application (条件触发)
  const [applicationSetting, setApplicationSetting] = useState([]);
  const [applicationData, setApplicationData] = useState([]);

  // State for Task Action (任务执行动作 - 工作流)
  const [taskWorkflowSetting, setTaskWorkflowSetting] = useState([]);
  const [taskWorkflowData, setTaskWorkflowData] = useState([]);

  const [loadingData, setLoadingData] = useState({
    agent: false,
    application: false,
    taskWorkflow: false,
  });
  const [isMounted, setIsMounted] = useState(true);
  const [treeData, setTreeData] = useState([]);
  const [visibleTreeModal, setVisibleTreeModal] = useState(false);
  const [checkedIds, setCheckedIds] = useState([]);
  const [modalTitle, setModalTitle] = useState('');
  const [modalType, setModalType] = useState('');
  const [processedProfiles, setProcessedProfiles] = useState<string[]>([]);

  useEffect(() => {
    fetchAgentList();
    fetchApplicationList();
    fetchTaskWorkflowList();
    setProcessedProfiles([]);
    form.setFieldsValue({ labels: [] });
    return () => {
      setIsMounted(false);
    };
  }, []);

  const handleAddTag = () => {
    if (inputValue && tags.length < 3) {
      setTags([...tags, inputValue]);
      setInputValue('');
    }
    setInputVisible(false);
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validate();
      console.log('Form submitted', {
        agentSetting,
        applicationSetting,
        taskWorkflowSetting,
        labels: values.labels,
      });
    } catch (error) {
      console.error('表单验证失败:', error);
      Message.error('请检查表单输入');
    }
  };

  const handleCancel = () => {
    console.log('Cancelled');
  };

  const chooseHitClass = (type) => {
    switch (type) {
      case 'agent':
        return agentSetting.length > 0 ? styles.count : '';
      case 'application':
        return applicationSetting.length > 0 ? styles.count : '';
      case 'task-workflow':
        return taskWorkflowSetting.length > 0 ? styles.count : '';
      default:
        return '';
    }
  };

  const fetchAgentList = async (searchValue) => {
    try {
      setLoadingData((prev) => ({ ...prev, agent: true }));
      const params = {
        Pager: {
          Page: 1,
          Size: 999,
        },
      };
      if (searchValue) {
        params.agent_name = searchValue;
      }
      const response = await getAgentList(params);
      if (response) {
        const formattedData = response.items.map((item) => ({
          id: item.id,
          title: item.name,
          applicationId: item.applicationId,
          parentId: '',
          level: 1,
          children: [],
        }));
        if (!searchValue) {
          setAgentData(formattedData);
          setTreeData(formattedData);
        }
        return formattedData;
      }
      return [];
    } catch (error) {
      console.error('获取智能体列表失败:', error);
      Message.error({
        content: locale['menu.application.agent.fetch.error'],
      });
      return [];
    } finally {
      setLoadingData((prev) => ({ ...prev, agent: false }));
    }
  };

  const fetchApplicationList = async (searchValue) => {
    try {
      setLoadingData((prev) => ({ ...prev, application: true }));
      const params = {
        Pager: {
          Page: 1,
          Size: 999,
        },
      };
      if (searchValue) {
        params.ApplicationName = searchValue;
      }
      const response = await getApplicationList(params);
      if (response) {
        const formattedData = response.items.map((item) => ({
          id: item.id,
          title: item.name,
          parentId: '',
          level: 1,
          children: [],
        }));
        if (!searchValue) {
          setApplicationData(formattedData);
          setTreeData(formattedData);
        }
        return formattedData;
      }
      return [];
    } catch (error) {
      console.error('获取应用列表失败:', error);
      Message.error({
        content: locale['menu.application.fetch.error'],
      });
      return [];
    } finally {
      setLoadingData((prev) => ({ ...prev, application: false }));
    }
  };

  const fetchTaskWorkflowList = async (searchValue) => {
    try {
      setLoadingData((prev) => ({ ...prev, taskWorkflow: true }));
      const response = await getWorkflowList();
      if (response && Array.isArray(response)) {
        const formattedData = response.map((item) => ({
          id: item.id,
          title: item.name,
          parentId: '',
          level: 1,
          children: [],
        }));
        if (!searchValue) {
          setTaskWorkflowData(formattedData);
        }
        return formattedData;
      }
      return [];
    } catch (error) {
      console.error('获取任务工作流列表失败:', error);
      Message.error({
        content: locale['menu.application.workflow.fetch.error'],
      });
      return [];
    } finally {
      setLoadingData((prev) => ({ ...prev, taskWorkflow: false }));
    }
  };

  const openChooseModal = async (type) => {
    let currentData = [];
    if (type === 'agent') {
      currentData = await fetchAgentList();
      setModalTitle(locale['menu.application.info.setting.addAgent']);
      setModalType('agent');
      setTreeData(currentData);
      setCheckedIds(agentSetting.map((item) => item.id));
    } else if (type === 'application') {
      currentData = await fetchApplicationList();
      setModalTitle(
        locale['menu.application.info.setting.addApplication'] || '添加应用'
      );
      setModalType('application');
      setTreeData(currentData);
      setCheckedIds(applicationSetting.map((item) => item.id));
    } else if (type === 'task') {
      // 任务执行动作模态框，默认显示智能体 Tab
      currentData = await fetchAgentList();
      setModalTitle(locale['menu.application.task.action'] || '');
      setModalType('task');
      setTreeData(currentData);
      setCheckedIds(agentSetting.map((item) => item.id));
    }
    setVisibleTreeModal(true);
  };

  const handleTreeConfirm = (selectedIds) => {
    if (!modalType) return;

    if (modalType === 'agent') {
      const selectedAgents = treeData
        .filter((item) => selectedIds.includes(item.id))
        .map((item) => ({
          id: item.id,
          name: item.title,
        }));
      setAgentSetting(selectedAgents);
    } else if (modalType === 'application') {
      const selectedApplications = treeData
        .filter((item) => selectedIds.includes(item.id))
        .map((item) => ({
          id: item.id,
          name: item.title,
        }));
      setApplicationSetting(selectedApplications);
    } else if (modalType === 'task') {
      const currentTab =
        modalType === 'task'
          ? modalType + '-' + (treeData === agentData ? 'agent' : 'workflow')
          : modalType;
      if (currentTab === 'task-agent') {
        const selectedAgents = treeData
          .filter((item) => selectedIds.includes(item.id))
          .map((item) => ({
            id: item.id,
            name: item.title,
          }));
        setAgentSetting(selectedAgents);
      } else if (currentTab === 'task-workflow') {
        const selectedTaskWorkflows = treeData
          .filter((item) => selectedIds.includes(item.id))
          .map((item) => ({
            id: item.id,
            name: item.title,
          }));
        setTaskWorkflowSetting(selectedTaskWorkflows);
      }
    }
    setVisibleTreeModal(false);
  };

  const handleCheck = (selectedIds) => {
    setCheckedIds(selectedIds);
  };

  const handleModalClose = () => {
    setVisibleTreeModal(false);
    setTreeData([]);
    setModalType('');
  };

  const handleSearch = async (value) => {
    if (
      modalType === 'agent' ||
      (modalType === 'task' && treeData === agentData)
    ) {
      setLoadingData((prev) => ({ ...prev, agent: true }));
      try {
        if (!value) {
          setTreeData([...agentData]);
          return;
        }
        const searchResults = await fetchAgentList(value);
        if (Array.isArray(searchResults)) {
          setTreeData(searchResults);
        }
      } catch (error) {
        console.error('搜索出错:', error);
        Message.error({
          content: locale['menu.application.agent.fetch.error'],
        });
      } finally {
        setLoadingData((prev) => ({ ...prev, agent: false }));
      }
    } else if (modalType === 'application') {
      setLoadingData((prev) => ({ ...prev, application: true }));
      try {
        if (!value) {
          setTreeData([...applicationData]);
          return;
        }
        const searchResults = await fetchApplicationList(value);
        if (Array.isArray(searchResults)) {
          setTreeData(searchResults);
        }
      } catch (error) {
        console.error('搜索出错:', error);
        Message.error({
          content: locale['menu.application.fetch.error'],
        });
      } finally {
        setLoadingData((prev) => ({ ...prev, application: false }));
      }
    } else if (modalType === 'task' && treeData === taskWorkflowData) {
      setLoadingData((prev) => ({ ...prev, taskWorkflow: true }));
      try {
        if (!value) {
          setTreeData([...taskWorkflowData]);
          return;
        }
        const searchResults = await fetchTaskWorkflowList(value);
        if (Array.isArray(searchResults)) {
          setTreeData(searchResults);
        }
      } catch (error) {
        console.error('搜索出错:', error);
        Message.error({
          content: locale['menu.application.workflow.fetch.error'],
        });
      } finally {
        setLoadingData((prev) => ({ ...prev, taskWorkflow: false }));
      }
    }
  };

  return (
    <div className={styles.addActionContainer}>
      <Title
        heading={4}
        className={styles.addActionTitle}
        style={{ marginBottom: '24px' }}
      >
        动作配置
      </Title>
      <Form form={form} autoComplete="off" layout="vertical">
        <Row gutter={[40, 0]}>
          <Col span={12}>
            <Card bordered={false} style={{ padding: '0px' }}>
              <div
                style={{
                  marginBottom: '24px',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <Typography.Text
                  className={styles.actionIcon}
                  style={{ marginBottom: '8px' }}
                >
                  图标
                </Typography.Text>
                <IconModel />
                <div style={{ marginTop: '8px' }}>
                  <Upload action="/" listType="picture-card" limit={1}>
                    <div className={styles.upload}>
                      <IconUpload />
                      <span>上传</span>
                    </div>
                  </Upload>
                </div>
              </div>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="命名"
                    field="actionName"
                    rules={[{ message: '请输入动作名称' }]}
                  >
                    <div
                      style={{
                        color: '#adadad',
                        fontSize: '12px',
                        marginBottom: '8px',
                      }}
                    >
                      设置应用的名字
                    </div>
                    <Input
                      placeholder="请输入"
                      className={styles.actionNameInput}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={24}>
                  <Form.Item label="描述" field="description">
                    <Input.TextArea
                      placeholder="用简单明了的话描述~"
                      className={styles.descriptionInput}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <FormItem label="标签" className={styles.tagItem}>
                <div
                  style={{
                    color: '#adadad',
                    fontSize: '12px',
                    marginBottom: '8px',
                  }}
                >
                  标签最多添加3个
                </div>
                <Space wrap size={[8]}>
                  {tags.map((tag, index) => (
                    <Tag
                      key={tag}
                      closable
                      closeIcon={
                        <IconCloseTag style={{ width: 24, height: 24 }} />
                      }
                      onClose={() => {
                        const newTags = [...tags];
                        newTags.splice(index, 1);
                        setTags(newTags);
                      }}
                    >
                      {tag}
                    </Tag>
                  ))}
                  {inputVisible ? (
                    <Input
                      autoFocus
                      size="large"
                      value={inputValue}
                      style={{ width: 100 }}
                      onPressEnter={handleAddTag}
                      onChange={(value) => setInputValue(value)}
                      onBlur={handleAddTag}
                    />
                  ) : (
                    <Tag
                      icon={<IconUpload />}
                      style={{ cursor: 'pointer' }}
                      onClick={() => setInputVisible(true)}
                    >
                      添加
                    </Tag>
                  )}
                </Space>
              </FormItem>
            </Card>
          </Col>

          <Col span={12} className={styles.Card}>
            <Card bordered={false} className={styles.triggerCard}>
              <Row style={{ marginBottom: '24px' }}>
                <Text className={styles.triggerCondition}>条件触发</Text>
                <Col span={24} style={{ marginBottom: '8px' }}>
                  <Text className={styles.bindApp}>绑定应用</Text>
                  {/* 渲染已选择的应用 */}
                  {applicationSetting.length > 0 && (
                    <div className={styles.selectedItemList}>
                      {applicationSetting.map((app) => (
                        <Row key={app.id} className={styles.selectedItemRow}>
                          <Col className={styles.selectedItemCol}>
                            <ApplicationIcon
                              className={styles.selectedItemIcon}
                            />
                            <Text className={styles.selectedItemText}>
                              {app.name}
                            </Text>
                            <IconCloseTag
                              className={styles.deleteIcon}
                              onClick={() => {
                                setApplicationSetting(
                                  applicationSetting.filter(
                                    (item) => item.id !== app.id
                                  )
                                );
                              }}
                            />
                          </Col>
                        </Row>
                      ))}
                    </div>
                  )}
                  <Button
                    className={styles.addApplication}
                    onClick={() => openChooseModal('application')}
                  >
                    <AddIcon />
                    <Text className={styles.operateText}>
                      {locale['menu.application.template.setting.adds']}
                    </Text>
                  </Button>
                </Col>
                <Col span={15}>
                  <Text className={styles.controlTag}>控制tag</Text>
                  <Form.List field="labels">
                    {(fields, { add, remove }) => {
                      return (
                        <div className={styles.customBlock}>
                          {fields.map((item, index) => {
                            const fieldValue = form.getFieldValue(
                              `labels.${index}`
                            );
                            return (
                              <Grid.Row key={item.key}>
                                <Form.Item
                                  field={item.field}
                                  className={styles.controlTagInput}
                                >
                                  <div
                                    style={{
                                      display: 'flex',
                                      gap: '4px',
                                      alignItems: 'center',
                                    }}
                                  >
                                    <Input
                                      placeholder={
                                        fieldValue ||
                                        locale[
                                          'menu.application.create.group.form.label.placeholder'
                                        ].replace(
                                          '{index}',
                                          (index + 1).toString()
                                        )
                                      }
                                      value={fieldValue || ''}
                                      suffix={
                                        <div
                                          style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                          }}
                                        >
                                          <span
                                            style={{
                                              fontSize: '14px',
                                              color: '#d6d6d6',
                                            }}
                                          >
                                            {fieldValue?.length || 0}/10
                                          </span>
                                        </div>
                                      }
                                      onChange={(value) => {
                                        if (value && value.length > 10) {
                                          return;
                                        }
                                        form.setFieldValue(
                                          `labels.${index}`,
                                          value
                                        );
                                        const newProfiles = [
                                          ...processedProfiles,
                                        ];
                                        newProfiles[index] = value;
                                        setProcessedProfiles(newProfiles);
                                      }}
                                      onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                          e.preventDefault();
                                          e.stopPropagation();
                                        }
                                      }}
                                      maxLength={10}
                                    />
                                    <IconCloseTag
                                      className={styles.deleteIcon}
                                      onClick={() => remove(index)}
                                    />
                                  </div>
                                </Form.Item>
                              </Grid.Row>
                            );
                          })}
                          <Button
                            className={styles.addLabelBut}
                            onClick={() => {
                              add('');
                            }}
                            icon={<AddIcon />}
                          >
                            {
                              locale[
                                'menu.application.create.group.form.label.addbutton'
                              ]
                            }
                          </Button>
                        </div>
                      );
                    }}
                  </Form.List>
                </Col>
              </Row>

              {/* Task Action with Agent and Workflow Selection */}
              <div className={styles.taskActionItem}>
                <Row
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <Space direction="vertical" size={'mini'}>
                    <Text className={styles.taskExecution}>任务执行</Text>
                    <Text className={styles.triggerEvent}>
                      设定AI action触发事件
                    </Text>
                  </Space>
                  <Button
                    className={styles.addTaskAction}
                    onClick={() => openChooseModal('task')}
                  >
                    <AddIcon />
                    <Text className={styles.operateText}>
                      {locale['menu.application.template.setting.adds']}
                    </Text>
                  </Button>
                </Row>
                {/* 渲染已选择的智能体和工作流 */}
                {(agentSetting.length > 0 ||
                  taskWorkflowSetting.length > 0) && (
                  <div className={styles.selectedItems}>
                    <ReactSortable
                      list={[...agentSetting, ...taskWorkflowSetting]}
                      setList={(newList) => {
                        // 分割新的列表，重新分配到 agentSetting 和 taskWorkflowSetting
                        const newAgentSetting = newList
                          .filter((item) =>
                            agentSetting.some((agent) => agent.id === item.id)
                          )
                          .map((item) => ({ id: item.id, name: item.name }));
                        const newTaskWorkflowSetting = newList
                          .filter((item) =>
                            taskWorkflowSetting.some(
                              (workflow) => workflow.id === item.id
                            )
                          )
                          .map((item) => ({ id: item.id, name: item.name }));
                        setAgentSetting(newAgentSetting);
                        setTaskWorkflowSetting(newTaskWorkflowSetting);
                      }}
                      handle=".drag-handle"
                      animation={150}
                      className={styles.selectedList}
                    >
                      {agentSetting.map((agent) => (
                        <Row key={agent.id} className={styles.selectedItemRow}>
                          <Col className={styles.selectedItemCol}>
                            <IconDragDotVertical
                              className={`${styles.dragHandle} drag-handle`}
                            />
                            <Image
                              src={AgentIconSmall}
                              className={styles.selectedItemIcon}
                            />
                            <Text className={styles.selectedItemText}>
                              {agent.name}
                            </Text>
                            <IconCloseTag
                              className={styles.deleteIcon}
                              onClick={() => {
                                setAgentSetting(
                                  agentSetting.filter(
                                    (item) => item.id !== agent.id
                                  )
                                );
                              }}
                            />
                          </Col>
                        </Row>
                      ))}
                      {taskWorkflowSetting.map((workflow) => (
                        <Row
                          key={workflow.id}
                          className={styles.selectedItemRow}
                        >
                          <Col className={styles.selectedItemCol}>
                            <IconDragDotVertical
                              className={`${styles.dragHandle} drag-handle`}
                            />
                            <Image
                              src={WorkflowIcon}
                              className={styles.selectedItemIcon}
                            />
                            <Text className={styles.selectedItemText}>
                              {workflow.name}
                            </Text>
                            <IconCloseTag
                              className={styles.deleteIcon}
                              onClick={() => {
                                setTaskWorkflowSetting(
                                  taskWorkflowSetting.filter(
                                    (item) => item.id !== workflow.id
                                  )
                                );
                              }}
                            />
                          </Col>
                        </Row>
                      ))}
                    </ReactSortable>
                  </div>
                )}
              </div>
            </Card>
            <Space
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                marginTop: '24px',
              }}
            >
              <Button
                type="secondary"
                onClick={handleCancel}
                className={styles.cancelButton}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={handleSubmit}
                className={styles.submitButton}
              >
                创建
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>

      <TreeModal
        type={modalType}
        title={modalTitle}
        visible={visibleTreeModal}
        onClose={handleModalClose}
        treeData={treeData}
        checkedIds={checkedIds}
        onCheck={handleCheck}
        onConfirm={handleTreeConfirm}
        onSearch={handleSearch}
        loading={
          loadingData[
            modalType === 'task'
              ? treeData === agentData
                ? 'agent'
                : 'taskWorkflow'
              : modalType
          ] || false
        }
        taskAgentData={agentData}
        taskWorkflowData={taskWorkflowData}
        setTreeData={setTreeData}
        setCheckedIds={setCheckedIds}
        taskAgentSetting={agentSetting}
        taskWorkflowSetting={taskWorkflowSetting}
      />
    </div>
  );
};

export default AddActionConfig;
