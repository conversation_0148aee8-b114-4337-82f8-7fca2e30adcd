// auth-service.js
import { userStore, getUserStore } from '@/lib/helpers/store';
import { endpoints } from './api-endpoints';
import axios from 'axios';
import axiosInstance from '@/lib/services/interceptors';
// 安全的 Base64 编码函数
function safeBtoa(data) {
    if (typeof window !== 'undefined' && window.btoa) {
        return window.btoa(data);
    } else if (typeof Buffer !== 'undefined') {
        return Buffer.from(data).toString('base64');
    }
    throw new Error('No btoa implementation available');
}

/**
 * 获取认证令牌
 * @param {string} email - 用户邮箱
 * @param {string} password - 用户密码
 * @param {() => void} onSucceed - 成功回调
 * @param {(error?: Error) => void} onError - 失败回调
 */
export async function getToken(email, password, onSucceed, onError) {
    try {
        const credentials = safeBtoa(`${email}:${password}`);
        const headers = {
            Authorization: `Basic ${credentials}`,
        };

        const response = await fetch(endpoints.tokenUrl, {
            method: 'POST',
            headers: headers,
        });

        if (!response.ok) {
            console.error('Token fetch failed:', response.statusText);
            onError(new Error(response.statusText));
            return;
        }

        const result = await response.json();
        if (!result?.access_token) {
            onError(new Error('Invalid token response'));
            return;
        }

        const user = getUserStore();
        user.token = result.access_token;
        user.expires = result.expires;
        userStore.set(user);
        onSucceed();
    } catch (error) {
        console.error('Get token error:', error);
        onError(error instanceof Error ? error : new Error('Unknown error'));
    }
}

/**
 * 设置外部令牌
 * @param {string} token - 认证令牌
 * @returns {Promise<void>}
 */
export async function setToken(token) {
    try {
        if (!token) throw new Error('Token cannot be empty');

        const user = getUserStore();
        user.token = token;
        userStore.set(user);
    } catch (error) {
        console.error('Set token error:', error);
        throw error;
    }
}

/**
 * 获取用户信息
 * @returns {Promise<import('$userTypes').UserModel>}
 */
export async function myInfo() {
    try {
        const response = await axiosInstance.get(endpoints.myInfoUrl);
        const user = getUserStore();
        user.id = response.data.id;
        user.full_name = response.data.full_name;
        userStore.set(user);
        return response.data;
    } catch (error) {
        console.error('Get user info error:', error);
        throw error;
    }
}

/**
 * 注册新用户
 * @param {string} firstName - 名
 * @param {string} lastName - 姓
 * @param {string} email - 邮箱
 * @param {string} password - 密码
 * @param {() => void} onSucceed - 成功回调
 * @returns {Promise<void>}
 */
export async function register(firstName, lastName, email, password, onSucceed) {
    try {
        const data = JSON.stringify({
            firstName,
            lastName,
            email,
            password
        });

        const response = await fetch(endpoints.usrCreationUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: data
        });

        if (!response.ok) {
            throw new Error(response.statusText);
        }

        onSucceed();
    } catch (error) {
        console.error('Registration error:', error);
        alert(error.message);
        throw error;
    }
}

/**
 * 获取 Keycloak 认证配置
 * @returns {Promise<any>}
 */
export async function getAuthConfig() {
    try {
        const response = await axios.get(endpoints.authConfigUrl);
        return response.data;
    } catch (error) {
        console.error('Get auth config error:', error);
        throw error;
    }
}

/**
 * 获取用户头像
 * @returns {Promise<string|null>} 返回头像的base64数据或URL，没有头像时返回null
 */
export async function getUserAvatar() {
    try {
        const response = await axiosInstance.get(endpoints.getUserAvatarUrl, {
            responseType: 'blob' // 指定响应类型为blob，用于处理二进制数据
        });
        
        // 检查响应是否有数据
        if (response?.data && response.data.size > 0) {
            // 将blob数据转换为data URL
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(response.data);
            });
        } else {
            // 响应体为空，表示用户没有头像
            console.log('用户尚未上传头像，响应体为空');
            return null;
        }
    } catch (error) {
        // 特殊处理404错误：用户没有头像是正常状态，不应该抛出错误
        if (error.response?.status === 404) {
            console.log('用户尚未上传头像');
            return null;
        }
        
        console.error('Get user avatar error:', error);
        throw error;
    }
}

/**
 * 上传用户头像
 * @param {File} file - 文件对象
 * @returns {Promise<any>}
 */
export async function uploadUserAvatar(file) {
    try {
        if (!file) throw new Error('File cannot be empty');

        // 将文件转换为base64格式
        const base64Data = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });

        const requestData = {
            file_name: file.name,
            file_data: base64Data
        };

        const response = await axiosInstance.post(endpoints.userAvatarUrl, requestData);
        return response?.data;
    } catch (error) {
        console.error('Upload avatar error:', error);
        throw error;
    }
}