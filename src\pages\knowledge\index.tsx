import { Route, Routes } from 'react-router-dom';
// import KnowledgeList from './components/list';
// import FileList from './components/files';
// import FileDetail from './components/details';
// import FileManager from './components/file-manager/src/components/file-manager';
import KnowledgeTabs from './components/KnowledgeTabs';
import KnowledgeDataset from './components/knowledge/dataset';
import KnowledgeChunk from './components/knowledge/chunk';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import zhCN from 'antd/locale/zh_CN';
import { ConfigProvider } from 'antd';

// 创建 QueryClient 实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 1000 * 60 * 5, // 5 minutes
      cacheTime: 1000 * 60 * 10, // 10 minutes (React Query v4 用 cacheTime)
      refetchOnWindowFocus: false,
    },
    mutations: {
      retry: 1,
    },
  },
});

/**
 * 知识库路由配置组件
 * @returns JSX元素
 */
const KnowledgeRenderRoutes = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider locale={zhCN}>
        <Routes>
          {/* 知识库主页面，包含子路由 */}
          <Route path="/knowledge/*" element={<KnowledgeTabs />} />
          {/* 数据集相关路由 */}
          <Route path="/knowledge/dataset" element={<KnowledgeDataset />} />
          <Route path="/knowledge/dataset/chunk" element={<KnowledgeChunk />} />
        </Routes>
      </ConfigProvider>
    </QueryClientProvider>
  );
};

export default KnowledgeRenderRoutes;
