.container {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
    position: relative;

    .titleContainer {
        border-bottom: 1px solid RGBA(0, 0, 0, 0.08);
        padding-bottom: 16px;

        .title {
            font-size: 20px;
            font-weight: 500;
            color: RGBA(0, 0, 0, 0.8);
        }
    }

    .customContainer {
        display: flex;
        height: calc(100vh - 200px);
        overflow: hidden;
        position: relative;

        @media screen and (max-width: 1200px) {
            flex-direction: column;
            height: auto;
        }

        .leftContainer {
            flex: 1;
            width: 55%;
            background: #fff;
            padding-right: 24px;
            border-right: 1px solid RGBA(0, 0, 0, 0.08);
            display: flex;
            flex-direction: column;

            .content {
                padding-top: 16px;
                flex: 1;
                display: flex;
                flex-direction: column;
                min-height: 0;
                overflow: hidden;

                .title {
                    font-size: 14px;
                    font-weight: 500;
                    color: RGBA(0, 0, 0, 0.8);
                    margin-bottom: 8px;
                }

                .text {
                    font-size: 14px;
                    color: #4E5969;
                    line-height: 1.6;
                    border: 1px solid rgba(224, 224, 224, 0.8);
                    border-radius: 8px;
                    transition: all 0.3s ease;
                    flex: 1;
                    overflow-y: auto;
                    width: 100%;
                    box-sizing: border-box;
                    height: 100%;
                    display: flex;
                    flex-direction: column;

                    .textContent {
                        height: 100%;
                        resize: none;
                        padding: 10px;
                        // border-radius: 8px;
                        border: 0;
                        font-size: 14px;
                        color: #000000A3;
                        font-weight: 400;
                        line-height: 24px;

                        &:disabled {
                            background-color: RGBA(0, 0, 0, 0.01);
                        }
                    }

                    &.editing {
                        background: #FFFFFF;
                        cursor: text;

                        .textContent {
                            &:focus {
                                outline: none;
                            }
                        }
                    }
                }

                .segmentsContainer {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 16px;
                    overflow-y: auto;
                    padding: 8px 0;
                    max-height: 100%;
                    min-height: 0;

                    .segmentCard {
                        box-sizing: border-box;
                        display: flex;
                        flex-direction: column;
                        align-items: flex-start;
                        padding: 8px 12px;
                        gap: 8px;
                        width: 100%;
                        min-height: 120px;
                        border: 1px solid RGBA(0, 0, 0, 0.08);
                        border-radius: 8px;
                        flex: none;
                        align-self: stretch;
                        flex-shrink: 0;
                        transition: all 0.3s ease;

                        .segmentHeader {
                            display: flex;
                            flex-direction: row;
                            justify-content: space-between;
                            align-items: center;
                            padding: 0px;
                            gap: 8px;
                            width: 100%;
                            height: 24px;
                            flex: none;
                            order: 0;
                            align-self: stretch;
                            flex-grow: 0;

                            .segmentNumber {
                                margin: 0;
                                height: 22px;
                                font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                                font-style: normal;
                                font-weight: 500;
                                font-size: 13px;
                                line-height: 22px;
                                color: #000;
                                background: none;
                                padding: 0;
                                border-radius: 0;
                                flex: 1;
                                order: 0;
                                min-width: 0;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }

                            .buttonGroup {
                                display: flex;
                                align-items: center;
                                gap: 2px;
                                flex: none;
                                order: 1;
                                flex-shrink: 0;
                                margin-left: 8px;
                            }

                            .toggleButton {
                                margin: 0 auto;
                                width: 24px;
                                height: 24px;
                                background: none;
                                border: none;
                                cursor: pointer;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                padding: 0;

                                .toggleIcon {
                                    width: 24px;
                                    height: 24px;
                                }
                            }

                            .editButton {
                                margin: 0 auto;
                                width: 24px;
                                height: 24px;
                                background: none;
                                border: none;
                                cursor: pointer;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                padding: 0;
                                transition: all 0.2s ease;

                                &:hover {
                                    background-color: rgba(68, 85, 242, 0.1);
                                    border-radius: 4px;
                                }

                                .editIcon {
                                    width: 24px;
                                    height: 24px;
                                }
                            }

                            .saveButton {
                                margin: 0 auto;
                                width: 32px;
                                height: 32px;
                                background: none;
                                border: none;
                                cursor: pointer;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                padding: 0;
                                transition: all 0.2s ease;

                                &:hover {
                                    background-color: rgba(22, 163, 74, 0.1);
                                    border-radius: 4px;
                                }

                                .saveIcon {
                                    width: 32px;
                                    height: 32px;
                                }
                            }

                            .cancelButton {
                                margin: 0 auto;
                                width: 32px;
                                height: 32px;
                                background: none;
                                border: none;
                                cursor: pointer;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                padding: 0;
                                transition: all 0.2s ease;

                                &:hover {
                                    background-color: rgba(239, 68, 68, 0.1);
                                    border-radius: 4px;
                                }

                                .cancelIcon {
                                    width: 32px;
                                    height: 32px;
                                }
                            }

                            .deleteButton {
                                margin: 0 auto;
                                width: 24px;
                                height: 24px;
                                background: none;
                                border: none;
                                cursor: pointer;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                padding: 0;
                                transition: all 0.2s ease;

                                &:hover {
                                    background-color: rgba(239, 68, 68, 0.1);
                                    border-radius: 4px;
                                }

                                .deleteIcon {
                                    width: 24px;
                                    height: 24px;
                                }
                            }
                        }

                        .segmentContent {
                            padding: 0;
                            display: flex;
                            flex-direction: column;

                            .editableTextarea {
                                width: 100%;
                                min-height: 120px;
                                padding: 8px 16px;
                                border: 1px solid #4455F2;
                                border-radius: 6px;
                                font-size: 14px;
                                color: #000000A3;
                                font-weight: 400;
                                line-height: 24px;
                                resize: vertical;
                                background: #fff;
                                transition: all 0.3s ease;

                                &:focus {
                                    outline: none;
                                    border-color: #4455F2;
                                    box-shadow: 0 0 0 2px rgba(68, 85, 242, 0.1);
                                }
                            }

                            .textDisplay {
                                padding: 0px 16px;
                                font-size: 14px;
                                color: #000000A3;
                                font-weight: 400;
                                line-height: 24px;
                                white-space: pre-wrap;
                                word-break: break-all;
                                min-height: 72px;
                                transition: all 0.3s ease;

                                &.collapsed {
                                    max-height: 72px;
                                    overflow: hidden;
                                    position: relative;
                                }

                                &.editable {
                                    cursor: text;
                                    background: transparent;
                                    border: none;
                                    outline: none;

                                    &:focus {
                                        background: #fff;
                                        outline: none;
                                    }

                                    &:hover {
                                        background: rgba(0, 0, 0, 0.02);
                                    }
                                }
                            }
                        }
                    }
                }

                .question {
                    padding-top: 8px;
                    padding-bottom: 16px;
                    width: 100%;
                    box-sizing: border-box;

                    .textContent {
                        height: 40px;
                        width: 100%;
                        box-sizing: border-box;
                        resize: none;
                        padding: 8px 12px;
                        border-radius: 8px;
                        font-size: 14px;
                        line-height: 22px;
                        color: #1D2129;
                        border: 1px solid RGBA(0, 0, 0, 0.08);

                        &:disabled {
                            background-color: RGBA(0, 0, 0, 0.01);
                        }
                    }

                    &.editing {
                        background: #FFFFFF;
                        cursor: text;

                        .textContent {
                            &:focus {
                                outline: none;
                            }
                        }
                    }

                }
            }
        }

        .rightContainer {
            width: 45%;
            background: #fff;
            border-radius: 8px;
            overflow-y: auto;
            padding-left: 24px;

            @media screen and (max-width: 1200px) {
                width: 100%;
            }

            .fileInfo {
                flex-direction: column;
                display: flex;
                align-items: flex-start;
                padding-top: 16px;
                width: 100%;

                .title {
                    font-size: 14px;
                    font-weight: 500;
                    color: RGBA(0, 0, 0, 0.8);
                }

                .text {
                    padding-top: 8px;
                    padding-bottom: 16px;
                    width: 100%;
                    box-sizing: border-box;

                    .name {
                        height: 40px;
                        width: 100%;
                        box-sizing: border-box;
                        resize: none;
                        padding: 8px 12px;
                        border-radius: 8px;
                        font-size: 14px;
                        line-height: 22px;
                        color: #1D2129;
                        background-color: RGBA(0, 0, 0, 0.01);
                        border: 1px solid RGBA(0, 0, 0, 0.08);
                    }
                }

                .info {
                    margin-top: 8px;
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    padding: 8px 12px;
                    gap: 8px;
                    width: 100%;
                    border: 1px solid RGBA(0, 0, 0, 0.08);
                    background: linear-gradient(0deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.01)), #FFFFFF;
                    border-radius: 8px;

                    .infoBar {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;
                        padding: 0px;
                        gap: 8px;
                        width: 100%;
                        height: 22px;
                        box-sizing: border-box;

                        .label {
                            margin: 0;
                            width: 50%;
                            font-style: normal;
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 22px;
                            flex: none;
                            order: 0;
                            flex-grow: 1;
                            color: rgba(0, 0, 0, 0.32);
                        }

                        .value {
                            margin: 0;
                            width: 50%;
                            font-style: normal;
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 22px;
                            flex: none;
                            order: 1;
                            flex-grow: 1;
                        }
                    }
                }
            }


        }
    }

    .footer {
        position: fixed;
        bottom: 0;
        right: 0;
        left: 0;
        padding: 16px 24px;
        background: #fff;
        border-top: 1px solid #E5E6EB;
        display: flex;
        justify-content: flex-end;
        gap: 16px;
        // z-index: 100;

        .operateButGroup {
            display: flex;
            gap: 16px;

            .text {
                font-size: 14px;
                font-weight: 600;
            }

            .but {
                border-radius: 4px;
                width: 76px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                transition: all 0.3s ease;
                position: relative;
                z-index: 101;
            }

            .editBut {
                background-color: #4455F2;
                color: #ffffff;
                opacity: 1;
                visibility: visible;
                display: flex;
                border: none;
                cursor: pointer;
                border-radius: 8px;

                &:hover {
                    background-color: #0E42D2;
                }

                .text {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: 600;
                }
            }

            .cancelBut {
                background-color: #F2F3F5;
                color: #4E5969;
                border-radius: 8px;

                &:hover {
                    background-color: #E5E6EB;
                }
            }

            .saveBut {
                background-color: #4455F2;
                color: #fff;
                border-radius: 8px;

                .text {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: 600;
                }

                &:hover {
                    background-color: #0E42D2;
                }
            }
        }
    }
}

.deleteModal {
    width: 400px !important;
    border-radius: 8px;
    height: 180px;

    :global(.arco-modal-header) {
        padding: 24px 24px 16px 24px;
        border-bottom: 0;
        text-align: left;
        height: 73px;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            color: #333333;
            text-align: left;
        }
    }

    :global(.arco-modal-content) {
        padding: 0 24px;
        font-size: 14px;
        color: #595959;
        line-height: 22px;
        display: flex;

        p {
            margin: 0;
            font-size: 14px;
            color: #595959;
            line-height: 22px;
        }
    }

    :global(.arco-modal-footer) {
        padding: 16px 24px;
        border-top: 0;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        height: 73px;
    }

    .submitBtn {
        background-color: #f53f3f;
        border-color: #f53f3f;
        border-radius: 8px;
        width: 74px;
        height: 40px;

        &:hover {
            background-color: #f76965;
            border-color: #f76965;
        }
    }

    .cancelBtn {
        border-color: #ebebeb;
        color: #595959;
        border-radius: 8px;
        width: 74px;
        height: 40px;

        &:hover {
            background-color: #f5f5f5;
        }
    }
}