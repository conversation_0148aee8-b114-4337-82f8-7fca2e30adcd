  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
    /* 对于基于 WebKit 的浏览器 */
  }

  /* 对于IE和Edge */
  html {
    -ms-overflow-style: none;
    /* IE 和 Edge */
  }

  /* 对于Firefox */
  * {
    scrollbar-width: none;
    /* Firefox */
  }

  .pluginContainer {
    display: flex;
    flex-direction: column;
    background-color: var(--color-bg-2);
    min-height: calc(100vh - 168px);

    .pluginTitleDom {
      font-weight: 600;
      font-size: 20px;
      line-height: 32px;
      color: #333333;
      margin-bottom: 8px;
    }

    .pluginListHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f5f5f5;

      .addPluginBtn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 24px;
        background-color: #4455f2;
        font-weight: 500;
        font-size: 14px;
        color: #ffffff;
        border-radius: 8px;
        transition: all 0.3s;
        height: 40px;

        &:hover {
          background-color: #3144f1;
        }
      }

      //搜索输入框
      :global(.arco-input-inner-wrapper) {
        padding: 8px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        transition: all 0.2s;


        &:hover {
          background-color: #fafafa;
          border-color: #ebebeb;
        }

        ::placeholder {
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
          color: #d6d6d6;
        }

        :global(.arco-input) {
          padding-top: 0;
          padding-bottom: 0;
          padding-left: 8px;
        }
      }

      //筛选select
      :global(.arco-select-size-default.arco-select-single .arco-select-view) {
        padding: 8px;
        height: auto;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #d6d6d6;
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        background-color: #ffffff;
        transition: all 0.2s;

        &:hover {
          background-color: #fafafa;
          border-color: #ebebeb;
        }

        :global(.arco-select-prefix) {
          margin-right: 4px;
        }

        :global(.arco-select-view-input) {
          &::placeholder {
            color: #d6d6d6;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
          }
        }
      }

      .count {
        color: #adadad;
        font-size: 14px;
        font-weight: 400;
        white-space: nowrap;
      }
    }

    .content {
      height: calc(100vh - 205px);
      min-height: 300px;
      overflow-y: auto;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 16px;
      grid-auto-rows: min-content;
      position: relative;
      will-change: transform;
      box-sizing: border-box;

      // 添加媒体查询，处理小屏幕设备
      @media screen and (max-width: 1200px) {
        grid-template-columns: repeat(3, 1fr);
      }

      @media screen and (max-width: 992px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media screen and (max-width: 576px) {
        grid-template-columns: repeat(1, 1fr);
      }

      .itemCard {
        height: 240px;
        box-sizing: border-box;
        cursor: pointer;
        border: 1px solid #f5f5f5;
        border-radius: 8px;
        padding: 20px 24px;
        display: flex;
        justify-content: space-between;
        min-height: 200px;
        transition: all 0.3s;
        position: relative;
        width: 100%;
        max-width: 100%;
        overflow: hidden;

        // 添加响应式内边距
        @media screen and (max-width: 1400px) {
          padding: 16px 20px;
        }

        @media screen and (max-width: 1200px) {
          padding: 14px 16px;
        }

        &:hover {
          box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
        }

        :global(.arco-card-body) {
          padding: 0;
          display: flex;
          flex-direction: column;
          width: 100%;
          justify-content: space-between;
          height: 100%;
        }

        .pluginInfo {

          .pluginIcon {
            width: 48px;
            height: 48px;

            img {
              max-width: 100%;
              max-height: 100%;
              width: auto;
              height: auto;
            }
          }

          .itemTitle {
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: #333333;
          }

          :global(.arco-typography) {
            margin-bottom: 0;
          }

          .itemDescription {
            color: #5c5c5c;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            display: -webkit-box;
            /* 必须设置为 -webkit-box 以支持 line-clamp */
            -webkit-line-clamp: 2;
            /* 限制显示两行 */
            -webkit-box-orient: vertical;
            /* 设置盒子方向为垂直 */
            overflow: hidden;
            /* 隐藏超出部分 */
            text-overflow: ellipsis;
            /* 超出部分显示省略号 */
          }
        }

        .itemTypes {
          display: flex;
          justify-content: flex-end;

          .typeTag {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            line-height: 20px;
          }

          .enabledTag {
            background: #f7fcfa;
            color: #2ba471;
          }

          .disabledTag {
            background-color: #fef8f8;
            color: #d54941;
          }

          .publicTag {
            background: #f8f8ff;
            color: #4455f2;
          }

          .privateTag {
            background-color: #fef9f0;
            color: #cb9039;
          }
        }

        .pluginFooter {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          .versionText {
            font-weight: 400;
            font-size: 12px;
            line-height: 20px;
            color: #adadad;
          }
        }
      }
    }
  }

  .uploadModal {
    position: relative;
    padding: 24px;
    width: 640px;
    border-radius: 16px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

    :global(.arco-modal-header) {
      padding: 0;
      height: auto;
      border-bottom: none;

      :global(.arco-modal-title) {
        font-weight: 600;
        font-size: 18px;
        line-height: 24px;
        color: #333333;
        margin-bottom: 16px;
        text-align: left;
      }
    }

    :global(.arco-modal-close-icon) {
      position: absolute;
      right: 24px;
      top: 24px;
      cursor: pointer;
    }

    :global(.arco-modal-content) {
      background-color: #fafafa;
      min-height: 224px;
      padding: 0;
      width: 100%;
      height: 100%;
      border: 1px solid #f5f5f5;
      border-radius: 8px;
    }

    :global(.arco-modal-footer) {
      display: none;
    }

    .uploadContent {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 224px;
      gap: 8px;

      .uploadText {
        display: flex;
        align-items: center;
        gap: 8px;

        .mainText {
          font-weight: 600;
          font-size: 16px;
          color: #4d5ef3;
        }
      }

      .subText {
        font-weight: 400;
        font-size: 14px;
        text-align: center;
        color: #a6a6a6;
      }
    }
  }

  :global {
    .arco-modal-mask {
      background: rgba(0, 0, 0, 0.08);
    }
  }

  /* 状态显示容器样式 */
  .statusContainer {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    text-align: center;
    padding: 24px;

    .statusText {
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      color: #5c5c5c;
      margin-top: 16px;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 200px;
    }
  }