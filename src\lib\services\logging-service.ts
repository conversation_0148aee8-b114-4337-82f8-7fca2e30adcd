import axiosInstance from './interceptors';
import { endpoints } from './api-endpoints';

export interface LoggingContentListResponse {
  items: Array<{
    conversation_id: string;
    message_id: string;
    name: string | null;
    agent_id: string | null;
    role: string;
    source: string;
    content: string;
    created_at: string;
  }>;
}

export interface LoggingStateListResponse {
  items: Array<{
    conversation_id: string;
    message_id: string;
    states: {
      channel: string;
      temperature: string;
      sampling_factor: string;
      provider: string;
      model: string;
      max_tokens: string;
      prompt_total: string;
      completion_total: string;
      llm_total_cost: string;
      language: string;
    };
    created_at: string;
  }>;
}

/**
 * 获取Content日志列表
 * @param conversationId 会话ID
 * @returns Promise<LoggingContentListResponse>
 */
export async function getLoggingContentList(
  conversationId: string
): Promise<LoggingContentListResponse> {
  try {
    const response = await axiosInstance.get(endpoints.loggingContentLogUrl.replace('{conversationId}', conversationId));
    return response.data;
  } catch (error) {
    console.error('获取Content日志列表失败:', error);
    throw error;
  }
}

/**
 * 获取State日志列表
 * @param conversationId 会话ID
 * @returns Promise<LoggingStateListResponse>
 */
export async function getLoggingStateList(
  conversationId: string
): Promise<LoggingStateListResponse> {
  try {
    const response = await axiosInstance.get(endpoints.loggingStateLogUrl.replace('{conversationId}', conversationId));
    return response.data;
  } catch (error) {
    console.error('获取State日志列表失败:', error);
    throw error;
  }
}
