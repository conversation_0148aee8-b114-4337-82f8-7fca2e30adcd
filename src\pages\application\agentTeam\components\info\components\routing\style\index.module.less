  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
    /* 对于基于 WebKit 的浏览器 */
  }

  /* 对于IE和Edge */
  html {
    -ms-overflow-style: none;
    /* IE 和 Edge */
  }

  /* 对于Firefox */
  * {
    scrollbar-width: none;
    /* Firefox */
  }

  .container {
    display: flex;
    height: calc(100vh - 115px);
    flex-direction: column;
    flex: 1;
    overflow: auto;
    position: relative;
  }

  // 隐藏 ReactFlow 版权归属信息
  :global(.react-flow__attribution) {
    display: none !important;
  }