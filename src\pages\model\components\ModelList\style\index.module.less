  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none; /* 对于基于 WebKit 的浏览器 */
}

  /* 对于IE和Edge */
html {
    -ms-overflow-style: none; /* IE 和 Edge */
}

  /* 对于Firefox */
  * {
    scrollbar-width: none; /* Firefox */
}

.modelList {
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f5;
        box-sizing: border-box;


        .addModelBtn {  
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px 24px;
            background-color: #4455f2;
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
            border-radius: 8px;
            transition: all 0.3s;
            height: 40px;

            &:hover {
                background-color: #4152e9;
            }
        }

        .modelNumber {
            color: #adadad;
            font-size: 14px;
            font-weight: 400;
            white-space: nowrap;
        }

        //搜索输入框
        :global(.arco-input-inner-wrapper) {
            padding: 8px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            ::placeholder {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                color: #d6d6d6;
            }

            :global(.arco-input) {
                padding-top: 0;
                padding-bottom: 0;
                padding-left: 8px;
            }
        }

        //筛选select
        :global(.arco-select-size-default.arco-select-single .arco-select-view) {
            padding: 8px;
            height: auto;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            background-color: #ffffff;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            :global(.arco-select-prefix) {
                margin-right: 4px;
            }

            :global(.arco-select-view-input) {
                &::placeholder {
                    color: #d6d6d6;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                }
            }
        }
    }

    .content {
        height: calc(100vh - 205px);
        min-height: 300px;
        overflow-y: auto;
        // 修改为固定四列布局
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 16px;
        grid-auto-rows: min-content;
        position: relative;
        will-change: transform;
        box-sizing: border-box;

        // 添加媒体查询，处理小屏幕设备
        @media screen and (max-width: 1200px) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media screen and (max-width: 992px) {
            grid-template-columns: repeat(2, 1fr);
        }

        @media screen and (max-width: 576px) {
            grid-template-columns: repeat(1, 1fr);
        }

        .modelCard {
            border: 1px solid #f5f5f5;
            border-radius: 8px;
            padding: 20px 24px;
            transition: all 0.3s;
            min-height: 200px;
            // 移除最小宽度限制
            // min-width: 360px;
            height: 240px;
            box-sizing: border-box;
            cursor: pointer;
            width: 100%;
            max-width: 100%;
            overflow: hidden;

            // 添加响应式内边距
            @media screen and (max-width: 1400px) {
                padding: 16px 20px;
            }

            @media screen and (max-width: 1200px) {
                padding: 14px 16px;
            }

            :global(.arco-card-body) {
                padding: 0;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                width: 100%;
                height: 100%;
            }

            &:hover {
                box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

                .status {
                    .actionWrapper {
                        .statusIndicator {
                            opacity: 0;
                        }

                        .triggerBtn {
                            opacity: 1;
                        }
                    }
                }
            }

            .modelInfo {
                .infoTag {
                    display: flex;
                    align-items: center;

                    .icon {
                        width: 48px;
                        height: 48px;

                        svg {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }

                .provider,
                .name {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .provider {
                    font-weight: 400;
                    font-size: 12px;
                    color: #4d5ef3;
                }

                .name {
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 24px;
                    color: #333333;
                }

                .details {
                    display: flex;
                    flex-direction: column;

                    :global(.arco-space-item) {
                        height: 24px;
                    }

                    .multiModal,
                    .imageGeneration {
                        display: flex;
                        align-items: center;
                    }
                }

                .tags{
                    // margin-bottom: 16px; 

                    :global(.arco-tag-checked){
                        background-color: transparent;
                        border-radius: 4px;
                        border: 1px solid #ebebeb;
                        font-weight: 400;
                        font-size: 12px;
                        color: #5c5c5c;
                    }
                }
            }

            .status {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
                margin-top: 16px;

                .metaInfo {
                    color: #595959;
                    font-size: 14px;

                    :global(.arco-typography) {
                        color: #adadad;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 20px;
                    }
                }

                .actionWrapper {
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-end;
                    min-width: 32px;

                    .statusIndicator {
                        display: flex;
                        align-items: center;
                        padding: 0 8px;
                        height: 20px;
                        opacity: 1;
                        transition: all 0.3s;
                    }
                }

                .enabled,
                .disabled {
                    display: flex;
                    align-items: center;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20px;
                    padding: 2px 6px;
                    border-radius: 4px;
                }

                .enabled {
                    color: #36a978;
                    background-color: #f7fcfa;
                }

                .disabled {
                    color: #d54941;
                    background-color: #fef8f8;
                }

                .actionBtn {
                    width: 120px;
                }

                .triggerBtn {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 4px;
                    background-color: #ffffff;
                    border-radius: 8px;
                    border: 1px solid #f5f5f5;
                    position: absolute;
                    bottom: 20px;
                    right: 24px;
                    opacity: 0;
                    transition: all 0.3s;

                    &:hover {
                        background: #fafafa;
                        border: 1px solid #f5f5f5;
                    }
                }
            }
        }

        :global(.arco-popover-content-right) {
            color: var(--color-text-2);
            background-color: var(--color-bg-popup);
            box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.12);
            max-width: none;
            width: 100%;
            font-size: 14px;
            border-radius: var(--border-radius-medium);
            line-height: 1.5715;
            box-sizing: border-box;
            border: none;

        }
        
        // 添加空状态容器样式
        .emptyContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            grid-column: 1 / -1;
            background-color: #ffffff;

            :global(.arco-space) {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            :global(.arco-typography) {
                font-weight: 500;
                font-size: 14px;
                line-height: 24px;
                color: #5c5c5c;
                text-align: center;
            }
        }
    }
    
    // 添加加载状态容器样式
    .loadingContainer {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        grid-column: 1 / -1;
        background-color: #ffffff;
        
        :global(.arco-spin) {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }
}

.confirmDeleteModal {
    position: relative;
    padding: 24px;
    width: 480px;
    border-radius: 16px;

    .modalContent {
        display: flex;
        flex-direction: column;

        .modalContentText {
            font-weight: 400;
            font-size: 14px;
            color: #5c5c5c;
        }
    }

    .modalFooter {
        margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .cancelDeleteBtn,
        .confirmDeleteBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 18px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
        }

        .cancelDeleteBtn {
            background-color: #ffffff;
            color: #5c5c5c;
            border: 1px solid #ebebeb;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }
        }

        .confirmDeleteBtn {
            background-color: #d54941;
            color: #ffffff;

            &:hover {
                background-color: #cd463e;
                color: #ffffff;
            }
        }
    }

    :global(.arco-modal-header) {
        padding: 0 !important;
        height: auto;
        border-bottom: none !important;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            color: #333333;
            margin-bottom: 16px;
            text-align: left;
        }
    }

    :global(.arco-modal-content) {
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 24px;
        top: 24px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
        background-color: #e9e9ff;
        color: #4d5ef3;
    }
}

.enableBtn,
.disableBtn,
.deleteBtn {
    padding: 4px 8px;
    border-radius: 4px;
    width: 144px;
    display: flex;
    justify-content: flex-start;
}

.enableBtn {
    color: #2ba471 !important;
    background: #ffffff !important;

    &:hover {
        background: #f7fcfa !important;
    }
}

.disableBtn {
    color: #d54941 !important;
    background-color: #ffffff !important;

    &:hover {
        background: #fef8f8 !important;
    }
}

.deleteBtn {
    background-color: #ffffff !important;
    color: #333333 !important;

    &:hover {
        background: #f5f5f5 !important;
    }
}

:global(.arco-popover-content-right) {
    color: var(--color-text-2);
    background-color: #ffffff;
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.12);
    max-width: none;
    font-size: 14px;
    border-radius: 4px;
    line-height: 1.5715;
    box-sizing: border-box;
    border: none;
    padding: 8px;
}