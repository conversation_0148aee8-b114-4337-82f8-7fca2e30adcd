.templateModal {
    position: relative;
    padding: 24px;
    border-radius: 16px;

    :global(.arco-modal-header) {
        padding: 0;
        height: auto;
        border-bottom: none;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 20px;
            line-height: 32px;
            color: #333333;
            text-align: left;
            padding-bottom: 16px;
            border-bottom: 1px solid #ebebeb;
        }
    }

    :global(.arco-modal-content) {
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 32px;
        top: 32px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    .templateContainer {
        max-height: 500px;
        overflow-y: auto;
        padding: 8px 0;
        padding-top: 16px;

        .templateCard {
            transition: all 0.3s;

            :global(.arco-card-header) {
                height: auto;
                padding: 0;

                :global(.arco-card-header-title) {
                    font-weight: 600;
                    font-size: 14px;
                    color: #5c5c5c;
                    margin-bottom: 8px;
                }
            }

            :global(.arco-card-body) {
                background-color: #fcfcfc;
                border-radius: 8px;
                border: 1px solid #ebebeb;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 270px;
                position: relative;

                &:hover {
                    .templateHover {
                        opacity: 1;
                    }
                }
            }

            .templatePreview {
                display: flex;
                padding: 16px;
                background-color: #ffffff;
                overflow: hidden;
                box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
                border-radius: 16px;
                width: 75%;

                .textCardTemplatePreview,
                .fileCardTemplatePreview {
                    height: 128px;
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                }

                .imageCardTemplatePreview,
                .videoCardTemplatePreview,
                .audioCardTemplatePreview,
                .linkCardTemplatePreview,
                .fileCardTemplatePreview {
                    width: 100%;
                }

                .tagList {
                    display: flex;
                    gap: 4px;

                    :global(.arco-tag) {
                        padding: 6px 10px;
                        background-color: #ffffff;
                        border-radius: 4px;
                        border: 1px solid #ebebeb;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        color: #5c5c5c;
                    }
                }

                .previewTitle {
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 24px;
                    
                    color: #333333;
                }

                .previewParagraph {
                    font-weight: 400;
                    font-size: 12px;
                    color: #5c5c5c;
                }

                .imagePlaceholder,
                .videoPlaceholder {
                    width: 100%;
                    height: 144px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 8px;
                    margin-bottom: 12px;
                    transition: all 0.3s;
                    background-color: #fcfcfc;
                    border: 1px solid #ebebeb;
                    position: relative;
                }

                .videoPlayBtn,
                .audioPlayBtn {
                    width: 40px;
                    height: 40px;
                    background-color: #ffffff;
                    border-radius: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
                    position: absolute;
                    bottom: 16px;
                    left: 16px;
                }

                .audioPlaceholder {
                    height: 80px;
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-bottom: 12px;
                    background-color: #fcfcfc;
                    border: 1px solid #ebebeb;
                    position: relative;
                    border-radius: 4px;
                }

                .fileItem {
                    background-color: #fef9f0;
                    padding: 6px 10px;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    align-self: flex-start;

                    :global(.arco-typography) {
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                        color: #df9f3f;
                    }
                }

                .linkItem {
                    display: flex;
                    align-items: center;
                    background-color: #f7fcfa;
                    border-radius: 4px;
                    padding: 6px 10px;
                    align-self: flex-start;

                    :global(.arco-typography) {
                        color: #2ba471;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20px;
                    }
                }
            }

            .templateHover {
                position: absolute;
                bottom: 16px;
                right: 20px;
                opacity: 0;
                transition: opacity 0.3s;

                .useButton {
                    padding: 8px 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: #4455f2;
                    border-radius: 8px;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 24px;
                    color: #ffffff;

                    &:hover {
                        background-color: #4152e9 !important;
                    }
                }
            }
        }
    }
}