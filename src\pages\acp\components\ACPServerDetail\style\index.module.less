.acpServerDetail {
    background-color: #ffffff;
    border-radius: 8px;

    .tabs {
        :global(.arco-tabs-header-nav::before) {
            display: none;
        }

        :global(.arco-tabs-header-ink) {
            display: none;
        }

        :global(.arco-tabs-header-title) {
            // padding: 0;
            font-weight: 600;
            font-size: 20px;
            line-height: 32px;
            color: #adadad;

            &:hover {
                color: #adadad;
            }
        }

        :global(.arco-tabs-header-title-active) {
            font-weight: 600;
            font-size: 20px;
            line-height: 32px;
            color: #333333;

            &:hover {
                color: #333333;
            }
        }

        :global(.arco-tabs-content) {
            padding-top: 8px;
        }

        :global(.arco-tabs-header-nav-line.arco-tabs-header-nav-horizontal > .arco-tabs-header-scroll .arco-tabs-header-title:first-of-type) {
            margin-left: 0;
        }

        :global(.arco-tabs-header-nav-line .arco-tabs-header-title) {
            margin: 0 12px;
        }
    }

    .resourcesListContainer {
        width: 100%;
    }

    .resourcesHeader {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .searchInput {
            width: 240px;

            :global(.arco-input-inner-wrapper) {
                padding: 8px;
                background-color: #ffffff;
                border-radius: 8px;
                border: 1px solid #f5f5f5;
                transition: all 0.2s;

                &:hover {
                    background-color: #fafafa;
                    border-color: #ebebeb;
                }

                ::placeholder {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                    color: #d6d6d6;
                }

                :global(.arco-input) {
                    padding-top: 0;
                    padding-bottom: 0;
                    padding-left: 8px;
                }
            }
        }

        .resourceCount {
            color: #adadad;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            text-align: right;
            margin-left: 8px;
        }
    }

    .resourceTable {

        /* 隐藏滚动条但保留滚动功能 */
        :global(.arco-table-body) {
            &::-webkit-scrollbar {
                display: none;
            }

            /* 兼容Firefox */
            scrollbar-width: none;

            /* 兼容IE */
            -ms-overflow-style: none;
        }

        :global(.arco-table-th) {
            background-color: #fafafa;
            font-weight: 600;
            color: #333333;
            padding: 12px 16px;
        }

        :global(.arco-table-td) {
            padding: 12px 16px;
            color: #5c5c5c;
        }

        :global(.arco-table-container) {
            border: none;
        }

        :global(.arco-table-container::before) {
            display: none;
        }

        :global(.arco-table-th) {
            background-color: #ffffff !important;
            border-bottom: none;
            font-weight: 600;
            font-size: 16px;
            color: #5c5c5c;
            border-left: none;
        }

        :global(.arco-table-th-item) {
            padding: 4px 24px;
            border-radius: 8px;
            background-color: #fcfcfc;
            // margin-right: 8px;

            &:not(:last-child) {
                margin-right: 1px;
            }

            :global(.arco-table-th-item-title) {
                font-weight: 600;
                font-size: 14px;
                line-height: 24px;
                color: #adadad;
            }
        }

        :global(.arco-table-header) {
            position: sticky;
            top: 0;
            z-index: 1;
            margin-bottom: 8px;
        }

        :global(.arco-table-td) {
            padding: 16px;
            border-top: 1px solid #f5f5f5;
            border-bottom: none;
            color: #5c5c5c;
            font-size: 14px;
            font-weight: 400;
            border-left: none;
        }

        .resourceType {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 13px;

            &.systemResource {
                background-color: #f4f5ff;
                color: #4455f2;
            }

            &.dataResource {
                background-color: #f3faf7;
                color: #2ba471;
            }
        }

        .iconColumn {
            display: flex;
            justify-content: center;
            align-items: center;

            svg {
                color: #adadad;
                transition: color 0.3s ease;
            }
        }

        .nameColumn {
            display: flex;
            align-items: center;
            gap: 16px;
            position: relative;

            .iconWrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;

                svg {
                    color: #4455f2;
                    transition: color 0.3s ease;
                }

                // 在图标下方添加一个白色遮罩，覆盖表格线条
                &::after {
                    content: '';
                    position: absolute;
                    bottom: -17px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 70px;
                    height: 1px;
                    background-color: #ffffff;
                    z-index: 2;
                }
            }

            .nameText {
                font-weight: 600;
                color: #333333;
                font-size: 14px;
                line-height: 24px;
            }
        }

        :global(.arco-table-tr:hover) {
            background-color: #fafafa;

            .iconColumn svg {
                color: #4455f2;
            }

            .nameColumn .iconWrapper svg {
                color: #4455f2;
            }
        }

        .description {
            display: inline-block;
            margin-bottom: 0px;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #5c5c5c;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            // display: -webkit-box;
            // -webkit-line-clamp: 2;
            // -webkit-box-orient: vertical;
        }
    }

    .settingsForm {
        max-width: 600px;
        height: calc(100vh - 236px);
        overflow-y: auto;
        padding-bottom: 24px;

        /* 隐藏滚动条但保留滚动功能 */
        &::-webkit-scrollbar {
            display: none;
        }

        /* 兼容Firefox */
        scrollbar-width: none;

        /* 兼容IE */
        -ms-overflow-style: none;

        
        :global(.arco-select-size-default.arco-select-single .arco-select-view) {
            padding: 8px;
            height: auto;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #adadad;
            border-radius: 8px;
            border: 1px solid #ebebeb;
            background-color: #fcfcfc;
            transition: all 0.2s;
    
            &:hover {
                background-color: #fafafa;
            }
        }
    
        :global(.arco-select-popup) {
            padding: 8px !important;
            box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            background-color: #ffffff;
    
            :global(.arco-select-option) {
                padding: 8px 12px;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                color: #333333;
                border-radius: 8px;
                transition: all 0.2s;
                margin-bottom: 4px;
    
                &:last-child {
                    margin-bottom: 0;
                }
    
                &:hover {
                    background-color: #fafafa;
                }
            }
    
            :global(.arco-select-option-selected) {
                background-color: #fafafa;
            }
            
            :global(.arco-select-option-active) {
                background-color: #fafafa;
            }
        }

        :global(.arco-form-item) {
            margin-bottom: 24px;

            :global(.arco-form-item > .arco-form-label-item) {
                font-weight: 600;
                font-size: 14px;
                line-height: 24px;
                color: #595959;
            }
        }

        :global(.arco-form-label-item > label) {
            font-weight: 600;
            font-size: 14px;
            line-height: 24px;
            color: #333333;
        }

        :global(.arco-input) {
            padding: 8px 12px;
            border: 1px solid #ebebeb;
            border-radius: 8px;
            background-color: #ffffff;

            &::placeholder {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                color: #d6d6d6;
            }
        }

        // 输入框禁用状态
        :global(.arco-input-disabled) {
            background-color: #fcfcfc;
            border-color: #ebebeb;
            color: #adadad;
            cursor: not-allowed;

            &::placeholder {
                color: #adadad !important;
            }
        }

        :global(.arco-textarea) {
            background-color: transparent;
            min-height: 64px;
            border: 1px solid #ebebeb;
            border-radius: 8px;
            resize: none;

            &::placeholder {
                color: #d6d6d6;
            }
        }

        :global(.arco-textarea-disabled) {
            background-color: #fcfcfc;
        }

        :global(.arco-textarea-word-limit) {
            color: #adadad;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
        }

        :global(.arco-input-disabled),
        :global(.arco-textarea-disabled) {
            background-color: #fcfcfc;
            color: #adadad;
            border-color: #ebebeb;
        }
        
        .queryParamsFormItem {
            margin-bottom: 24px;

            .queryParamsContainer {
                .queryParamsHeader {
                    margin-bottom: 8px;
                    padding: 0 16px;
                }

                .selectedItemList {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                }

                .selectedItemRow {
                    margin-bottom: 8px;
                    padding: 0 8px;
                }
            }

            :global(.arco-input) {
                padding: 8px 56px 8px 12px;
            }
        }

        .additionalHeadersFormItem {
            :global(.arco-input) {
                padding: 8px 56px 8px 12px;
            }
        }

        .deleteOptionBtn {
            border: none !important;
            background: transparent !important;
            padding: 0 !important;
            width: 24px !important;
            height: 24px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer;

            &:hover {
                background-color: #f5f5f5 !important;
                border-radius: 4px !important;
            }

            &:disabled {
                cursor: not-allowed !important;
                opacity: 0.5;

                &:hover {
                    background-color: transparent !important;
                }
            }

            :global(.arco-btn-icon) {
                margin-right: 0 !important;
            }
        }

        .addTagBtn {
            cursor: pointer;
            padding: 8px 24px;
            background-color: #ffffff !important;
            border: 1px solid #ebebeb !important;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 14px;
            line-height: 24px;
            color: #5c5c5c;
            height: 40px;

            &:disabled {
                cursor: not-allowed !important;
                opacity: 0.5;
                background-color: #fcfcfc !important;
                color: #adadad !important;
                border-color: #ebebeb !important;

                &:hover {
                    background-color: #fcfcfc !important;
                    color: #adadad !important;
                }
            }
        }

        .labelExact {
            font-weight: 400;
            font-size: 12px;
            color: #adadad;
        }

        .templatePlaceholder {
            display: flex;
            flex-direction: column;
        }

        .clientTransportSection {
            padding-top: 24px;
            border-top: 1px solid #f5f5f5;

            .additionalHeadersFormItem {
                margin-top: 24px;

                :global(.arco-input) {
                    padding: 8px 56px 8px 12px;
                }
            }

            .connectionTimeoutContainer {

                .sliderContainer {
                    width: 100%;
                    padding: 0 16px;
                    background-color: transparent;
                    border: 1px solid #ebebeb;
                    border-radius: 8px;
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            //Switch组件样式
            :global(.arco-switch) {
                background-color: #d6d6d6;
            }

            :global(.arco-switch-checked) {
                background-color: #4455f2;
            }

            // 非编辑模式下关闭的Switch背景颜色
            :global(.arco-switch[disabled]:not(.arco-switch-checked)) {
                background-color: #e8e8e8;
                opacity: 1;
            }

            :global(.arco-switch[disabled]) {
                background-color: #c3c8fa;
            }

            :global(.arco-slider) {
                .arco-slider-track {
                    background-color: #4455f2;
                }

                .arco-slider-handle {
                    border: 2px solid #4455f2;
                    background-color: #ffffff;

                    &:hover {
                        border-color: #4152e9;
                    }
                }
            }

            :global(.arco-slider-bar) {
                height: 4px;
                background-color: #4455f2;
            }

            :global(.arco-slider-road::before) {
                background-color: #ebebeb;
                height: 4px;
            }

            :global(.arco-slider-button::after) {
                background-color: #ffffff;
                width: 12px;
                height: 24px;
                border: 1px solid #ebebeb;
                box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.04);
                border-radius: 6px;
                top: -6px;
            }

            :global(.arco-slider-disabled) {
                :global(.arco-slider-track) {
                    background-color: #d6d6d6;
                }

                :global(.arco-slider-handle) {
                    border-color: #d6d6d6;
                }

                :global(.arco-slider-bar) {
                    background-color: #d6d6d6;
                }
            }
            
        }
    }
}

.formFooter {
    // margin-top: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    padding-top: 24px;
    border-top: 1px solid #f5f5f5;

    .leftActions {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .rightActions {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .editButton,
    .cancelButton,
    .saveButton,
    .testConnectionButton {
        padding: 8px 24px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .editButton {
        background-color: #4455f2;
        color: #ffffff;
        border: none;

        &:hover {
            background-color: #4152e9 !important;
        }

        &:disabled {
            cursor: not-allowed !important;
            opacity: 0.5;
            background-color: #d6d6d6 !important;

            &:hover {
                background-color: #d6d6d6 !important;
            }
        }
    }

    .cancelButton {
        background-color: #ffffff;
        color: #5c5c5c;
        border: 1px solid #ebebeb !important;

        &:hover {
            background-color: #fafafa !important;
        }

        &:disabled {
            cursor: not-allowed !important;
            opacity: 0.5;
            background-color: #fcfcfc !important;
            color: #adadad !important;

            &:hover {
                background-color: #fcfcfc !important;
                color: #adadad !important;
            }
        }
    }

    .saveButton {
        background-color: #4455f2;
        color: #ffffff;
        border: none;

        &:hover {
            background-color: #4152e9 !important;
        }

        &:disabled {
            cursor: not-allowed !important;
            opacity: 0.5;
            background-color: #d6d6d6 !important;

            &:hover {
                background-color: #d6d6d6 !important;
            }
        }
    }

    .testConnectionButton {
        background-color: #ffffff;
        color: #5c5c5c;
        border: 1px solid #ebebeb !important;

        &:hover {
            background-color: #fafafa !important;
        }

        &:disabled {
            cursor: not-allowed !important;
            opacity: 0.5;
            background-color: #fcfcfc !important;
            color: #adadad !important;
            border-color: #ebebeb !important;

            &:hover {
                background-color: #fcfcfc !important;
                color: #adadad !important;
                border-color: #ebebeb !important;
            }
        }

        // 加载状态下的样式
        &:global(.arco-btn-loading) {
            background-color: #ffffff !important;
            color: #5c5c5c !important;
            border-color: #ebebeb !important;
        }
    }
}

// 必填图标样式
.requiredIcon {
    color: #4455f2;
    font-size: 15px;
    line-height: 16px;
    margin-left: 4px;
    position: relative;
    top: -1px;
    font-weight: bold;
}

// 资源详情模态框样式
.resourceDetailModal {
    position: relative;
    padding: 24px;
    width: 640px;
    max-height: 90vh;
    border-radius: 16px;
    border: 1px solid #f5f5f5;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;

    :global(.arco-modal-header) {
        padding: 0 !important;
        height: auto;
        border-bottom: none !important;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 20px;
            line-height: 32px;
            color: #333333;
            margin-bottom: 16px;
            text-align: left;
            padding-bottom: 16px;
            border-bottom: 1px solid #f5f5f5;
        }
    }

    :global(.arco-modal-content) {
        min-height: 160px;
        padding: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 24px;
        top: 24px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    .modalContent {
        flex: 1;
        max-height: 70vh;
        overflow-y: auto;

        // 隐藏滚动条但保持滚动功能
        &::-webkit-scrollbar {
            display: none;
        }

        // Firefox隐藏滚动条
        scrollbar-width: none;

        // IE隐藏滚动条
        -ms-overflow-style: none;

        .section {
            margin-bottom: 24px;

            :global(.arco-textarea) {
                background-color: #fcfcfc;
                min-height: 88px;
                border: 1px solid #ebebeb;
                border-radius: 8px;
                resize: none;
                color: #5c5c5c;
                padding: 8px 12px;

                &::placeholder {
                    color: #5c5c5c;
                }
            }

            .sectionLabel {
                display: block;
                font-weight: 600;
                font-size: 14px;
                line-height: 24px;
                color: #333333;
                margin-bottom: 8px;
            }

            .nameSection {
                display: flex;
                align-items: center;
                gap: 12px;

                :global(.arco-input) {
                    padding: 8px 12px;
                    border: 1px solid #ebebeb;
                    border-radius: 8px;
                    background-color: #fcfcfc;
                    color: #5c5c5c;

                    &::placeholder {
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 24px;
                        color: #d6d6d6;
                    }
                }

                .nameInput {
                    flex: 1;
                }
            }

            .parameterHeader {
                display: flex;
                flex-direction: column;
                margin-bottom: 8px;

                .parameterDescription {
                    font-size: 12px;
                    color: #adadad;
                    display: block;
                    font-weight: 400;
                    line-height: 20px;
                }
            }

            .parameterList {
                padding: 8px;
                border: 1px solid #f5f5f5;
                background-color: #fcfcfc;
                border-radius: 8px;

                .parameterItem {
                    margin-bottom: 32px;
                    position: relative;

                    // 为除了最后一个parameterItem之外的所有项添加分隔线
                    &:not(:last-child)::after {
                        content: '';
                        position: absolute;
                        bottom: -16px;
                        left: 0;
                        right: 0;
                        height: 1px;
                        background-color: #f5f5f5;
                    }

                    // 最后一个parameterItem不需要下边距
                    &:last-child {
                        margin-bottom: 0;
                    }

                    :global(.arco-input) {
                        padding: 8px 12px;
                        border: 1px solid #ebebeb;
                        border-radius: 8px;
                        background-color: #ffffff;
                        color: #5c5c5c;

                        &::placeholder {
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 24px;
                            color: #5c5c5c;
                        }
                    }

                    .parameterRow {
                        display: flex;
                        gap: 8px;
                        margin-bottom: 12px;

                        .parameterField {
                            flex: 1;

                            .fieldLabel {
                                display: block;
                                font-size: 14px;
                                color: #5c5c5c;
                                margin-bottom: 8px;
                                font-weight: 400;
                                line-height: 24px;
                                padding: 4px 12px;
                                background-color: #f9f9f9;
                                border-radius: 8px;
                            }

                            // .parameterInput {
                            //     :global(.arco-input) {
                            //         padding: 8px 12px;
                            //         border: 1px solid #ebebeb;
                            //         border-radius: 8px;
                            //         background-color: #ffffff;
                            //         font-size: 14px;
                            //         color: #333333;

                            //         &::placeholder {
                            //             font-weight: 400;
                            //             font-size: 14px;
                            //             line-height: 24px;
                            //             color: #d6d6d6;
                            //         }
                            //     }
                            // }
                        }
                    }

                    .parameterDescRow {
                        display: flex;
                        align-items: flex-end;
                        gap: 16px;

                        .parameterField {
                            flex: 1;

                            .fieldLabel {
                                display: block;
                                font-size: 14px;
                                color: #5c5c5c;
                                margin-bottom: 8px;
                                font-weight: 400;
                                line-height: 24px;
                                padding: 4px 12px;
                                background-color: #f9f9f9;
                                border-radius: 8px;
                            }
                        }
                    }
                }
            }
        }
    }

    // 表单相关样式
    :global(.arco-form-label-item) {
        font-weight: 600;
        font-size: 14px;
        color: #333333;
        line-height: 24px;
    }

    :global(.arco-form-item) {
        margin-bottom: 24px;

        :global(.arco-form-item > .arco-form-label-item) {
            font-weight: 600;
            font-size: 14px;
            color: #333333;
            line-height: 24px;
        }
    }

    :global(.arco-input-disabled) {
        background-color: #fcfcfc;
        color: #adadad;
        border-color: #ebebeb;
    }

    :global(.arco-textarea-disabled) {
        background-color: #fcfcfc;
    }

    :global(.arco-textarea-word-limit) {
        color: #adadad;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
    }
}

// 全局模态框遮罩样式
:global {
    .arco-modal-mask {
        background: rgba(0, 0, 0, 0.08);
    }
}