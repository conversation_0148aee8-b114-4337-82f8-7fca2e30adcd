import { Flex, Form, InputNumber, Slider } from 'antd';

interface IProps {
  initialValue?: number;
  max?: number;
}

const MaxTokenNumber = ({ initialValue = 512, max = 2048 }: IProps) => {
  return (
    <Form.Item
      label={'建议文本块大小'}
      tooltip={
        '建议的生成文本块的 token 数阈值。如果切分得到的小文本段 token 数达不到这一阈值就会不断与之后的文本段合并，直至再合并下一个文本段会超过这一阈值为止，此时产生一个最终文本块。如果系统在切分文本段时始终没有遇到文本分段标识符，即便文本段 token 数已经超过这一阈值，系统也不会生成新文本块。'
      }
    >
      <Flex gap={20} align="center">
        <Flex flex={1}>
          <Form.Item
            name={['parser_config', 'chunk_token_num']}
            noStyle
            initialValue={initialValue}
            rules={[{ required: true, message: '块Token数是必填项' }]}
          >
            <Slider max={max} style={{ width: '100%' }} />
          </Form.Item>
        </Flex>
        <Form.Item
          name={['parser_config', 'chunk_token_num']}
          noStyle
          rules={[{ required: true, message: '块Token数是必填项' }]}
        >
          <InputNumber max={max} min={0} />
        </Form.Item>
      </Flex>
    </Form.Item>
  );
};

export default MaxTokenNumber;
