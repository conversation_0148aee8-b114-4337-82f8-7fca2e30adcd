# TreePicker 自定义树组件

一个功能完整的自定义树组件，用于替代 Ant Design 的 Tree 组件，支持单选、多选、checkStrictly 模式以及自定义节点渲染等功能。

## 功能特性

- ✅ **单选模式**: 支持单个节点选择
- ✅ **多选模式**: 支持多个节点选择
- ✅ **复选框模式**: 支持复选框选择，包括半选状态
- ✅ **严格模式**: checkStrictly 模式，父子节点选中状态不关联
- ✅ **自定义渲染**: 支持自定义节点标题渲染
- ✅ **异步加载**: 支持动态加载子节点数据
- ✅ **展开/收起**: 支持节点展开和收起
- ✅ **图标显示**: 支持自定义节点图标
- ✅ **禁用状态**: 支持节点禁用
- ✅ **加载状态**: 异步加载时显示加载动画
- ✅ **样式自定义**: 支持自定义样式和类名

## 基本用法

```tsx
import TreePicker, { TreeNode } from './tree-picker';

const treeData: TreeNode[] = [
  {
    key: '1',
    title: '父节点',
    children: [
      {
        key: '1-1',
        title: '子节点',
        isLeaf: true,
      },
    ],
  },
];

function App() {
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  return (
    <TreePicker
      treeData={treeData}
      selectedKeys={selectedKeys}
      expandedKeys={expandedKeys}
      onSelect={(keys) => setSelectedKeys(keys)}
      onExpand={(keys) => setExpandedKeys(keys)}
      showIcon
    />
  );
}
```

## API

### TreePickerProps

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| treeData | 树形数据 | `TreeNode[]` | `[]` |
| checkable | 是否显示复选框 | `boolean` | `false` |
| checkStrictly | 是否严格模式（父子节点选中状态不关联） | `boolean` | `false` |
| multiple | 是否允许多选 | `boolean` | `false` |
| checkedKeys | 选中的节点 key 数组 | `string[] \| CheckedKeys` | `[]` |
| selectedKeys | 选中的节点 key 数组 | `string[]` | `[]` |
| expandedKeys | 展开的节点 key 数组 | `string[]` | `[]` |
| showIcon | 是否显示图标 | `boolean` | `true` |
| showLine | 是否显示连接线 | `boolean` | `false` |
| onCheck | 节点勾选回调 | `(checkedKeys, info) => void` | - |
| onSelect | 节点选择回调 | `(selectedKeys, info) => void` | - |
| onExpand | 节点展开回调 | `(expandedKeys, info) => void` | - |
| loadData | 异步加载数据 | `(node) => Promise<void>` | - |
| titleRender | 自定义渲染树节点 | `(nodeData) => ReactNode` | - |
| className | 自定义样式类名 | `string` | `''` |
| style | 自定义样式 | `CSSProperties` | `{}` |
| nodeHeight | 节点高度 | `number` | `32` |
| indent | 缩进距离 | `number` | `24` |

### TreeNode

| 属性 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| key | 节点唯一标识 | `string` | - |
| title | 节点标题 | `string` | - |
| icon | 节点图标 | `ReactNode` | - |
| isLeaf | 是否为叶子节点 | `boolean` | `false` |
| children | 子节点数组 | `TreeNode[]` | - |
| disabled | 是否禁用 | `boolean` | `false` |
| checkable | 是否可勾选 | `boolean` | `true` |
| selectable | 是否可选择 | `boolean` | `true` |

### CheckedKeys

| 属性 | 说明 | 类型 |
|------|------|------|
| checked | 选中的节点 key 数组 | `string[]` |
| halfChecked | 半选的节点 key 数组 | `string[]` |

## 使用示例

### 1. 基础单选模式

```tsx
<TreePicker
  treeData={treeData}
  selectedKeys={selectedKeys}
  onSelect={(keys) => setSelectedKeys(keys)}
  showIcon
/>
```

### 2. 复选框模式

```tsx
<TreePicker
  treeData={treeData}
  checkable
  checkedKeys={checkedKeys}
  onCheck={(keys) => setCheckedKeys(keys)}
  showIcon
/>
```

### 3. 严格模式

```tsx
<TreePicker
  treeData={treeData}
  checkable
  checkStrictly
  checkedKeys={checkedKeys}
  onCheck={(keys) => setCheckedKeys(keys)}
  showIcon
/>
```

### 4. 多选模式

```tsx
<TreePicker
  treeData={treeData}
  multiple
  selectedKeys={selectedKeys}
  onSelect={(keys) => setSelectedKeys(keys)}
  showIcon
/>
```

### 5. 自定义节点渲染

```tsx
const customTitleRender = (nodeData: TreeNode) => {
  return (
    <span style={{ color: nodeData.isLeaf ? '#666' : '#1890ff' }}>
      {nodeData.title}
      {nodeData.isLeaf && <span style={{ color: '#ccc' }}>(文件)</span>}
    </span>
  );
};

<TreePicker
  treeData={treeData}
  titleRender={customTitleRender}
  showIcon
/>
```

### 6. 异步加载数据

```tsx
const loadData = async (node: TreeNode): Promise<void> => {
  // 模拟异步加载
  const response = await fetch(`/api/tree/${node.key}/children`);
  const children = await response.json();
  
  // 更新节点数据
  node.children = children;
};

<TreePicker
  treeData={treeData}
  loadData={loadData}
  showIcon
/>
```

## 样式自定义

组件提供了多个 CSS 类名用于样式自定义：

- `.tree-picker`: 树组件根容器
- `.tree-node`: 树节点容器
- `.tree-node-selected`: 选中的树节点
- `.tree-node-disabled`: 禁用的树节点
- `.tree-switcher`: 展开/收起按钮
- `.tree-node-title`: 节点标题
- `.tree-children`: 子节点容器

```css
.tree-picker .tree-node {
  padding: 4px 8px;
  border-radius: 4px;
}

.tree-picker .tree-node:hover {
  background-color: #f5f5f5;
}

.tree-picker .tree-node-selected {
  background-color: #e6f7ff;
}
```

## 注意事项

1. **受控组件**: 组件支持受控和非受控两种模式，建议使用受控模式以获得更好的状态管理
2. **性能优化**: 对于大量数据，建议使用虚拟滚动或分页加载
3. **异步加载**: 使用 `loadData` 时，确保正确处理加载状态和错误情况
4. **键值唯一性**: 确保每个节点的 `key` 值在整个树中是唯一的
5. **样式兼容**: 组件使用了现代 CSS 特性，请确保目标浏览器支持

## 依赖要求

- React >= 16.8.0
- Ant Design >= 4.0.0 (仅用于 Checkbox 组件和图标)
- TypeScript >= 4.0.0 (可选，但推荐)

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础的树形结构展示
- 支持单选、多选、复选框模式
- 支持 checkStrictly 严格模式
- 支持自定义节点渲染
- 支持异步数据加载
- 支持样式自定义