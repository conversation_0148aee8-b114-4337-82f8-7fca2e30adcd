const i18n = {
  'en-US': {
    'menu.user': 'Personal Center',
    'menu.user.info': 'User Center',
    'userInfo.title.project': 'My project',
    'userInfo.title.news': 'Latest News',
    'userInfo.title.team': 'My team',
    'userInfo.title.notice': 'In-site Notification',
    'userInfo.btn.more': 'More',
    'userInfo.btn.all': 'All',
    'userInfo.notice.empty': 'No Data',
    'userInfo.personal.info': 'Personal Information',
    'userInfo.account': 'Account',
    'userInfo.name': 'Name',
    'userInfo.email': 'Email',
    'userInfo.edit': 'Edit',
    'userInfo.save': 'Save',
    'userInfo.cancel': 'Cancel',
    'userInfo.delete.account': 'Delete Account',
    'userInfo.account.data': 'Your account and user data',
    'userInfo.update.success': 'User information updated successfully',
  },
  'zh-CN': {
    'menu.user': '个人中心',
    'menu.user.info': '用户中心',
    'userInfo.title.project': '我的项目',
    'userInfo.title.news': '最新动态',
    'userInfo.title.team': '我的团队',
    'userInfo.title.notice': '站内通知',
    'userInfo.btn.more': '查看更多',
    'userInfo.btn.all': '查看全部',
    'userInfo.notice.empty': '暂无数据',
    'userInfo.personal.info': '个人信息',
    'userInfo.account': '账户',
    'userInfo.name': '名称',
    'userInfo.email': '邮箱',
    'userInfo.edit': '修改',
    'userInfo.save': '保存',
    'userInfo.cancel': '取消',
    'userInfo.delete.account': '删除账户',
    'userInfo.account.data': '你的账户和用户数据',
    'userInfo.update.success': '用户信息更新成功',
  },
};

export default i18n;
