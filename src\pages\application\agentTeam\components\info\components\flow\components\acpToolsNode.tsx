import React, { memo, useContext, useEffect, useState } from 'react';
import { Modal } from '@arco-design/web-react';
import type { customNodeInterface } from '../types';
import { Handle, Position } from '@xyflow/react';

import icon16 from '@/assets/flow/icon_16.png';
import icon17 from '@/assets/flow/icon_17.png';
import icon3 from '@/assets/flow/icon_3.png';
import icon5 from '@/assets/flow/icon_5.png';

import styles from '../style/acpToolsNode.module.less';

import { FlowContext } from '../context';

// import MorePop from './morePop';

// 预先检测是否为客户端环境
const isClientSide = typeof window !== 'undefined';

const AcpToolsNode = (props: Partial<customNodeInterface>) => {
  const { data, isConnectable } = props as any;
  // 删除节点
  const { handleDeleteNode, isClickBlank, setIsClickBlank } = useContext(FlowContext);

  // 是否显示删除按钮
  const [isShowPopover, setIsShowPopover] = useState(false);

  // 点击更多
  const handleClickMore = (e: any) => {
    e.stopPropagation();
    setIsClickBlank(true);
    setTimeout(() => {
      setIsShowPopover(true);
    }, 100);
  };

  // 删除节点
  const handleDeleteNodeFC = () => {
    Modal.confirm({
      title: '提示',
      content: '确认要删除此智能体吗?',
      wrapClassName: 'deleteModal',
      okButtonProps: {
        status: 'danger',
      },
      onOk: () => {
        handleDeleteNode(data);
      },
    });
  };

  useEffect(() => {
    if (isClickBlank) {
      setIsShowPopover(false);
      setIsClickBlank(false);
    }
  }, [isClickBlank, setIsShowPopover, setIsClickBlank]);

  return (
    <div>
      {data?.content && (
        <div className={styles.RightNodeContainer}>
          <div className={styles.RightNodeTopContent}>
            <div className={styles.RightNodeContent}>
              <img className={styles.RightNodeLogo} src={data.content.tool_type == '1' ? icon16: icon17} />
              <div className={styles.RightNodeTitleBox}>
                <span className={styles.RightNodeTitleText}>{data.content?.name || 'agent_name'}</span>
              </div>
            </div>
            {/* <div className="relative">
              <img className={`${styles.more} ${isShowPopover ? styles.moreActive : ''}`} src={icon5} onClick={(e) => handleClickMore(e)} />
              {isShowPopover && <MorePop handleDeleteNodeFC={handleDeleteNodeFC} />}
            </div> */}
          </div>
          <div className={styles.RightNodeDescBox}>
            <span className={styles.RightNodeDescText}>{data.content?.description || 'agent_description'}</span>
          </div>
          <div className={styles.RightNodeBottomBox}>
            {data.content?.agent_tags &&
              data.content?.agent_tags.length &&
              data.content?.agent_tags.slice(0, 2).map((item: any, i: number) => (
                <div className={styles.RightNodeBottomBtn} key={i}>
                  <span className={styles.RightNodeBottomBtnText}>{item}</span>
                </div>
              ))}

            {/* 超过两项时 */}
            {data.content?.agent_tags.length > 2 && (
              <div className={styles.RightNodeBottomBtn}>
                <span className={styles.RightNodeBottomBtnText}>+{Math.max(data.content?.agent_tags.length - 2, 0)}</span>
              </div>
            )}
          </div>
        </div>
      )}
      {isClientSide && (
        <Handle
          type="source"
          isConnectable={isConnectable}
          position={Position.Left}
          style={{
            backgroundImage: `url(${icon3})`,
            backgroundSize: '100% 100%',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            width: '24px',
            height: '24px',
            backgroundColor: 'transparent',
            border: 'none',
          }}
        />
      )}
    </div>
  );
};

AcpToolsNode.displayName = 'RightNode';

export default memo(AcpToolsNode);
