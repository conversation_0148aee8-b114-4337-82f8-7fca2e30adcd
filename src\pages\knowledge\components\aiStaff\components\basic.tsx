import React, { useEffect, useRef } from 'react';
import {
  Form,
  Input,
  Button,
  Grid,
  Typography,
  Select,
  Switch,
} from '@arco-design/web-react';
import styles from '../style/index.module.less';
import { IconClose } from '@arco-design/web-react/icon';
const { Row, Col } = Grid;
const { Text } = Typography;
const FormItem = Form.Item;
const TextArea = Input.TextArea;

const permissionOptions = [
  { label: '所有人可访问', value: 'public' },
  { label: '仅自己可见', value: 'private' },
];

export default function AiStaffBasic({ formData, setFormData, isEditing }) {
  const [form] = Form.useForm();
  const isFirstMount = useRef(true);

  useEffect(() => {
    if (isFirstMount.current) {
      form.setFieldsValue({
        name: formData.name || '',
        description: formData.description || '',
        tags: formData.tags || [],
        permission: formData.permission,
        enabled: !!formData.enabled,
      });
      isFirstMount.current = false;
    }
  }, []); // 只在首次挂载时执行

  // 同步到父组件
  const syncForm = () => {
    const values = form.getFieldsValue();
    setFormData({ ...formData, ...values });
  };

  return (
    <div className={styles.container}>
      <Form
        form={form}
        layout="vertical"
        autoComplete="off"
        style={{ flex: 1, display: 'flex', flexDirection: 'column' }}
        onChange={syncForm}
      >
        <Row style={{ marginTop: 16 }}>
          <Col span={24}>
            <FormItem
              label={
                <span>
                  <span style={{ color: '#f53f3f' }}>*</span> 名称
                </span>
              }
              field="name"
              rules={[{ required: true, message: '请输入名称' }]}
            >
              <Input
                placeholder="请输入"
                disabled={!isEditing}
                maxLength={30}
              />
            </FormItem>
          </Col>
        </Row>
        <Row style={{ marginTop: 16 }}>
          <Col span={24}>
            <FormItem label="描述" field="description">
              <TextArea
                placeholder="请输入"
                disabled={!isEditing}
                maxLength={200}
                showWordLimit
                style={{ height: 60 }}
              />
            </FormItem>
          </Col>
        </Row>
        <Row className={styles.titleRow} style={{ marginTop: 16 }}>
          <div className={styles.titleContent}>
            <Text className={styles.subtitle}>标签</Text>
            <Text className={styles.subtitlePlaceholder}>
              标签至多添加 3 个
            </Text>
          </div>
          <Form.List field="tags">
            {(fields, { add, remove }) => (
              <>
                <div className={styles.selectedItemList}>
                  {fields.map((field, idx) => (
                    <Row key={field.key} className={styles.selectedItemRow}>
                      <FormItem
                        field={field.field}
                        style={{ marginBottom: 0, flex: 1 }}
                        rules={[
                          { required: true, message: '请输入标签' },
                          { maxLength: 10, message: '最多10个字符' },
                        ]}
                      >
                        <Input
                          placeholder={`标签${idx + 1}`}
                          disabled={!isEditing}
                          maxLength={10}
                          suffix={
                            <IconClose
                              className={styles.deleteIcon}
                              style={{
                                cursor: !isEditing ? 'not-allowed' : 'pointer',
                              }}
                              onClick={() => isEditing && remove(idx)}
                            />
                          }
                          className={styles.selectedItemCol}
                        />
                      </FormItem>
                    </Row>
                  ))}
                </div>
                <Button
                  className={styles.addLabelBut}
                  disabled={fields.length >= 3 || !isEditing}
                  onClick={() => isEditing && add('')}
                  style={{
                    opacity: fields.length >= 3 || !isEditing ? 0.5 : 1,
                    cursor:
                      fields.length >= 3 || !isEditing
                        ? 'not-allowed'
                        : 'pointer',
                  }}
                >
                  添加
                </Button>
              </>
            )}
          </Form.List>
        </Row>
        <Row style={{ marginTop: 16 }}>
          <Col span={24}>
            <FormItem label="权限" field="permission">
              <Select placeholder="请选择" disabled={!isEditing}>
                {permissionOptions.map((opt) => (
                  <Select.Option key={opt.value} value={opt.value}>
                    {opt.label}
                  </Select.Option>
                ))}
              </Select>
            </FormItem>
          </Col>
        </Row>
        {/* <Row style={{ marginTop: 16 }}>
          <Col span={24}>
            <FormItem
              label="是否启用"
              field="enabled"
              triggerPropName="checked"
            >
              <Switch disabled={!isEditing} />
            </FormItem>
          </Col>
        </Row> */}
      </Form>
    </div>
  );
}
