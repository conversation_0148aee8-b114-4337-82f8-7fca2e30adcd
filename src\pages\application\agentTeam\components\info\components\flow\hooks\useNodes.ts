import { useNodesState, useEdgesState } from '@xyflow/react';
import { useState, useEffect, useRef } from 'react';
import { nanoid } from 'nanoid';

import LeftNode from '../components/leftNode';
import RightNode from '../components/rightNode';
import AcpToolsNode from '../components/acpToolsNode';
import { useDispatch } from 'react-redux';

// import {
//   getArtifactsList,
//   getTimeSeriesCardByArtifactId,
//   getHistoryVersionApi,
//   addTimeSeriesCard,
//   deleteAgentApi,
// } from '@/services/api/artifact';
import { getAgentList } from '@/lib/services/agent-service';

import { TimeSeriesCard, ApplicationDataCard } from '../types';

const nodeTypes = {
  leftNode: LeftNode,
  rightNode: RightNode,
  AcpToolsNode: AcpToolsNode,
};

const initialFitViewOptions = {
  padding: 3,
  minZoom: 0.5,
  maxZoom: 4,
};

// 最大最小的高度
const maxMinHeightArr = [198, 174];

const loadingNodeId = nanoid();

export const useNodes = (data) => {
  // console.log(data,'3355555555555555')
  const dispatch = useDispatch();

  // loading加载
  const [isLoading, setIsLoading] = useState(true);

  // 白板页面是否初始化完成
  const [isInitialized, setIsInitialized] = useState(false);

  const [fitViewOptions, setFitViewOptions] = useState(initialFitViewOptions);

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  const [nodeName, setNodeName] = useState('Node 1');
  const [nodeBg, setNodeBg] = useState('#fff');
  const [nodeHidden, setNodeHidden] = useState(false);
  const [isRequestAddAgent, setRequestAddStatus] = useState(false);

  // 选中节点
  const [selectedNode, setSelectedNode] = useState([]);

  // 保存首页时序卡片
  const applicationData = useRef<ApplicationDataCard | null>(null);
  applicationData.current =  data;
  // 当前选中节点的版本数据
  const [currentNodeVersionData, setCurrentNodeVersionData] = useState<Partial<TimeSeriesCard>[]>([]);

  // 是否点击空白区域
  const [isClickBlank, setIsClickBlank] = useState(false);

  useEffect(() => {
    setNodes((nds) =>
      nds.map((node: any) => {
        if (node.id === '1') {
          // it's important that you create a new node object
          // in order to notify react flow about the change
          return {
            ...node,
            data: {
              ...node.data,
              label: nodeName,
            },
          };
        }

        return node;
      })
    );
  }, [nodeName, setNodes]);

  useEffect(() => {
    setNodes((nds) =>
      nds.map((node: any) => {
        // if (node.id === '1') {
        //   // it's important that you create a new node object
        //   // in order to notify react flow about the change
        //   return {
        //     ...node,
        //     style: {
        //       ...node?.style,
        //       backgroundColor: nodeBg,
        //     },
        //   };
        // }

        return node;
      })
    );
  }, [nodeBg, setNodes]);

  useEffect(() => {
    setNodes((nds) =>
      nds.map((node) => {
        // if (node.id === '1') {
        //   // it's important that you create a new node object
        //   // in order to notify react flow about the change
        //   return {
        //     ...node,
        //     hidden: nodeHidden,
        //   };
        // }

        return node;
      })
    );
    setEdges((eds) =>
      eds.map((edge) => {
        if (edge.id === 'e1-2') {
          return {
            ...edge,
            hidden: nodeHidden,
          };
        }

        return edge;
      })
    );
  }, [nodeHidden, setNodes, setEdges]);

  // 监听节点选中状态
  useEffect(() => {
    setSelectedNode(nodes.filter((node: any) => node.selected));
  }, [nodes]);

  // 监听节点选中状态
  useEffect(() => {
    if (!selectedNode.length) {
      setCurrentNodeVersionData([]);
      resetEdge();
    }
  }, [selectedNode, dispatch, setCurrentNodeVersionData]);

  // 监听节点选中状态agent_id的变化
  useEffect(() => {
    if (selectedNode[0]?.data) {
      // getVersionListByNode();
      handleUpdateEdge(selectedNode[0]?.data);
    }
  }, [selectedNode[0]?.data?.id]);

  // 监听鼠标移入节点事件
  const handleMouseEnterNode = (e: any) => {
    console.log(e);
  };

  // 获取二级node的高度
  const getSecondNodeHeight = (node: any) => {
    return node?.content?.agent_description?.length > 25 ? maxMinHeightArr[0] : maxMinHeightArr[1];
  };

  // 新增节点
  const handleAddNode = (data: any) => {
    // console.log('nodes', nodes);
    // 获取最后一个二级节点样式
    if (!data) return;
    const filterNodes = nodes.filter((item: any) => item.data.nodeType === '2');
    const { measured, position } = filterNodes[filterNodes.length - 1];
    // 获取父节点
    const parentNode = applicationData.current[0];

    // 新节点id
    const newNodeId = nanoid();
    
    setNodes((nds) => [
      ...nds.filter((item) => item.id !== loadingNodeId),
      {
        id: newNodeId,
        type: 'rightNode',
        position: { x: position.x, y: position.y + measured.height + 40 },
        data,
      },
    ]);

    setEdges((eds) => [
      ...eds.filter((edge) => edge.source !== loadingNodeId && edge.target !== loadingNodeId),
      {
        id: `el-${parentNode.artifact_source}-${newNodeId}`,
        source: parentNode.artifact_source,
        target: newNodeId,
        style: { stroke: '#4455F2', strokeWidth: 2 },
      },
    ]);
  };

  // 新增工具节点
  const handleAddToolsNode = (data: any) => {
    // 新节点id
    const newNodeId = nanoid();
    // 获取最后一个三级节点样式
    const filterNodes = nodes.filter((item: any) => item.data.nodeType === '3');

    // 若不存在三级接待你列表
    if (!filterNodes.length) {
      console.log(data);
    } else {
      const { measured, position } = filterNodes[filterNodes.length - 1];

      // 获取父节点
      const parentNode = applicationData.current[0];

      setNodes((nds) => [
        ...nds.filter((item) => item.id !== loadingNodeId),
        {
          id: newNodeId,
          type: 'rightNode',
          position: { x: position.x, y: position.y + measured.height + 40 },
          data,
        },
      ]);

      setEdges((eds) => [
        ...eds.filter((edge) => edge.source !== loadingNodeId && edge.target !== loadingNodeId),
        {
          id: `el-${parentNode.artifact_source}-${newNodeId}`,
          source: parentNode.artifact_source,
          target: newNodeId,
          style: { stroke: '#4455F2', strokeWidth: 2 },
        },
      ]);
    }
  };

  // 更新节点
  const handleUpdateNode = (data: any) => {
    const newNodes = nodes.map((node) => {
      if (data && node.data.content.id === data.content.id) {
        return {
          ...node,
          data: {
            ...node.data,
            content: data.content,
          },
        };
      }
      return node;
    });

    // 获取三级数据
    const thirdNodes = newNodes.filter((item: any) => item.data.nodeType === '3');

    const thirdNodesByNodeName = thirdNodes.map((item: any) => item.name);

    if (!thirdNodes.length || !data.content.acp_tools.find((item: any) => thirdNodesByNodeName.includes(item.name))) {
      // handleAddToolsNode(data);
    }

    setNodes(newNodes);
  };

  // 更新节点的边
  const handleUpdateEdge = (data: any) => {
    setEdges(
      edges.map((edge) => {
        if (edge.source === data.id || edge.target === data.id) {
          return {
            ...edge,
            style: {
              stroke: data.edgesColor ? data.edgesColor : '#4455F2',
              strokeWidth: 2,
            },
          };
        } else {
          return {
            ...edge,
            style: { stroke: '#00000029', strokeWidth: 2 },
          };
        }
      })
    );
  };

  // 重置节点的边
  const resetEdge = () => {
    setEdges(
      edges.map((edge) => ({
        ...edge,
        style: { stroke: '#00000029', strokeWidth: 2 },
      }))
    );
  };

  // 重置节点选中状态
  const resetNodeStatus = () => {
    setNodes(nodes.map((node) => ({ ...node, selected: false })));
  };

  // 获取路由
  const getSecondRouteCard = async () => {
    const params: any = {
      Pager: {
        Page: 1,
        Size: 12,
      },
      applicationId: applicationData.current?.id
    };
    const { items: children } = await getAgentList(params);

    const routerAgent = children.find((item: any) => item.type === 'routing');
    
    const otherAgents = children.filter((item: any) => item.type !== 'routing');

    const leftNodeData = routerAgent || children[0];
    const rightNodesData = routerAgent ? otherAgents : children.slice(1);

    // 计算二级nodes的总高度
    const totalHeight = rightNodesData.reduce((acc, curr) => {
      const height = getSecondNodeHeight(curr);
      return acc + height;
    }, 0);

    // 计算当前节点之前的总高度
    const prevTotalHeight = (i: number) => {
      return rightNodesData.reduce((acc, curr, index) => {
        if (index < i) {
          return acc + getSecondNodeHeight(curr);
        }
        return acc;
      }, 0);
    };

    // 计算右节点区域的起始Y和结束Y
    const rightNodesStartY = -50;
    const rightNodesTotalHeight = totalHeight + (rightNodesData.length - 1) * 60; // 包括间距
    const rightNodesCenterY = rightNodesStartY + rightNodesTotalHeight / 2;

    // 左节点卡片的估算高度（可以根据实际情况调整）
    const leftNodeHeight = getSecondNodeHeight(leftNodeData) || 200;

    const childrenNodes = rightNodesData.map((item, index) => {
      return {
        id: item.id,
        data: {
          ...item,
          nodeType: '2',
        },
        type: 'rightNode',
        position: {
          x: 800,
          y: rightNodesCenterY - rightNodesTotalHeight / 2 + prevTotalHeight(index) + index * 60,
        },
      };
    });

    const childrenEdges = rightNodesData.map((item) => {
      return {
        id: `el-${item.id}`,
        source: leftNodeData?.id,
        target: item.id,
        style: { stroke: '#00000029', strokeWidth: 2 },
      };
    });

    setNodes([
      {
        id: leftNodeData?.id,
        data: leftNodeData,
        type: 'leftNode',
        position: { x: 100, y: rightNodesCenterY - leftNodeHeight / 2 },
        selectable: false,
      },
      ...childrenNodes
    ]);
    setEdges(childrenEdges)
  };

  return {
    isRequestAddAgent,
    nodes,
    edges,
    currentNodeVersionData,
    setCurrentNodeVersionData,
    onNodesChange,
    onEdgesChange,
    fitViewOptions,
    nodeTypes,
    nodeBg,
    selectedNode,
    setNodes,
    setEdges,
    handleUpdateEdge,
    resetEdge,
    handleAddNode,
    handleUpdateNode,
    resetNodeStatus,
    getSecondRouteCard,
    isClickBlank,
    setIsClickBlank,
    handleMouseEnterNode,
    setIsLoading,
    isLoading,
    isInitialized,
    setIsInitialized,
    handleAddToolsNode,
  };
};
