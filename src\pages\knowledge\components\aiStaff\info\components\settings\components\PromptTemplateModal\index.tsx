import { Modal, Button, Input, Grid, Select, Checkbox } from '@arco-design/web-react';
import Option from '@arco-design/web-react/es/Select/option';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import styles from './style/index.module.less';
import { useState, useEffect } from 'react';
import IconClose from '@/assets/application/close.svg';
import useLocale from '@/utils/useLocale';
import ButtonComponent from '@arco-design/web-react/es/Button';
import Text from '@arco-design/web-react/es/Typography/text';

const { TextArea } = Input;

const PromptTemplateModel = ({ visible, onClose, data, currentData }) => {
    const locale = useLocale();

    useEffect(() => {
        setPromptTemplateName(currentData.name || '');
        setPromptTemplateContent(currentData.content || '');
    }, [currentData]);

    const [promptTemplateName, setPromptTemplateName] = useState('');
    const [promptTemplateContent, setPromptTemplateContent] = useState('');
    // 关闭模态框
    const handleClose = () => {
        // 重置表单数据
        setPromptTemplateName('');
        setPromptTemplateContent('');
        onClose();
    };

    const footContainer = () => {
        // 判断是否可以提交表单
        const canSubmit = promptTemplateName && promptTemplateContent;
        
        return (
            <RowComponent className={styles.operateButGroup}>
                <ButtonComponent
                    type="secondary"
                    style={{ marginRight: 12 }}
                    className={[styles.cancelBut, styles.but]}
                    onClick={handleClose}
                >
                    <Text className={styles.text}>
                        取消
                    </Text>
                </ButtonComponent>
                <ButtonComponent
                    type="primary"
                    className={[styles.createBut, styles.but, !canSubmit && styles.disabled]}
                    disabled={!canSubmit}
                    onClick={() => {                        
                        // 检查是否是编辑现有数据
                        if (currentData && currentData.name) {
                            // 编辑模式：更新现有数据
                            const index = data.findIndex(item => item.name === currentData.name);
                            if (index !== -1) {
                                data[index] = {
                                    name: promptTemplateName,
                                    content: promptTemplateContent
                                };
                            }
                        } else {
                            // 新增模式：添加到数据中
                            data.push({
                                name: promptTemplateName,
                                content: promptTemplateContent
                            });
                        }
                        
                        handleClose();
                    }}
                >
                    <Text className={styles.text}>
                        确认
                    </Text>
                </ButtonComponent>
            </RowComponent>
        )
    }

    return (
        <Modal
            visible={visible}
            onCancel={handleClose}
            footer={footContainer()}
            maskClosable={false}
            className="prompt-template-modal"
            wrapClassName="prompt-template-modal-wrap"
            closeIcon={<IconClose />}
        >
            <div className="prompt-template-modal-title">{locale['menu.application.info.setting.placeholder.addPromptTemplate']}</div>
            <div className="prompt-template-modal-description">用于存储每个用户使用项目过程中，需要持久化存储和读取的数据</div>
            <div className="prompt-template-modal-content">

                {/* 名称 */}
                <RowComponent style={{ marginTop: 16 }}>
                    <Text className={styles.subtitle}>{locale['menu.application.info.basic.names']}</Text>
                </RowComponent>
                <RowComponent style={{ marginTop: 8 }}>
                    <div style={{ position: 'relative', width: '100%' }}>
                        <TextArea
                            placeholder={locale['menu.application.info.basic.names']}
                            maxLength={50}
                            value={promptTemplateName}
                            onChange={(value) => {
                                setPromptTemplateName(value);
                            }}
                            style={{
                                backgroundColor: '#fff',
                                border: '1px solid #e5e6eb',
                                width: '100%',
                                resize: 'none',
                                height: '40px',
                                borderRadius: '8px',
                                padding: '8px 12px',
                                lineHeight: '24px',
                                paddingRight: '40px'
                            }}
                        />
                        <div style={{
                            position: 'absolute',
                            top: '50%',
                            right: '12px',
                            transform: 'translateY(-50%)',
                            fontSize: '12px',
                            color: 'rgba(0, 0, 0, 0.45)',
                            pointerEvents: 'none'
                        }}>
                            {promptTemplateName.length}/50
                        </div>
                    </div>
                </RowComponent>

                {/* 内容 */}
                <RowComponent style={{ marginTop: 16 }}>
                    <Text className={styles.subtitle}>{locale['menu.application.info.setting.response.content']}</Text>
                </RowComponent>
                <RowComponent style={{ marginTop: 8 }}>
                    <div style={{ position: 'relative', width: '100%' }}>
                        <TextArea
                            placeholder='请输入'
                            maxLength={2000}
                            value={promptTemplateContent}
                            onChange={(value) => {
                                setPromptTemplateContent(value);
                            }}
                            style={{
                                backgroundColor: '#fff',
                                border: '1px solid #e5e6eb',
                                width: '100%',
                                resize: 'none',
                                height: '416px',
                                borderRadius: '8px',
                                padding: '8px 12px',
                                paddingRight: '40px'
                            }}
                        />
                        <div style={{
                            position: 'absolute',
                            bottom: '12px',
                            right: '12px',
                            fontSize: '12px',
                            color: 'rgba(0, 0, 0, 0.45)',
                            pointerEvents: 'none'
                        }}>
                            {promptTemplateContent.length}/2000
                        </div>
                    </div>
                </RowComponent>

            </div>
        </Modal>)
};

export default PromptTemplateModel;


