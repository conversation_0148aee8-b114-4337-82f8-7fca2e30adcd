import React, { useEffect, useRef, useState } from 'react';
import { Card, Spin } from '@arco-design/web-react';
import { Column } from '@antv/g2plot';
import styles from './style/TokenCostMonthly.module.less';
import IconTokenCostMonthly from '@/assets/dashboard/TokenCostMonthly.svg'

type DataType = {
    date: string;
    count: number;
};

const TokenCostMonthly = () => {
    const chartRef = useRef(null);
    const containerRef = useRef(null);
    const [loading, setLoading] = useState(false);
    const [chartHeight, setChartHeight] = useState(300); // 默认高度

    // 月度数据，根据设计稿提供
    const monthlyData: DataType[] = [
        { date: '1月', count: 600 },
        { date: '2月', count: 200 },
        { date: '3月', count: 660 },
        { date: '4月', count: 1800 },
        { date: '5月', count: 260 },
        { date: '6月', count: 600 },
        { date: '7月', count: 280 },
        { date: '8月', count: 400 },
        { date: '9月', count: 560 },
        { date: '10月', count: 760 },
        { date: '11月', count: 260 },
        { date: '12月', count: 560 },
    ];

    const updateChartSize = () => {
        if (!containerRef.current) return;

        // 根据容器宽度计算适当的高度
        const parent = containerRef.current.parentElement;
        const containerWidth = parent.clientWidth;

        // 使用比例来确定高度，并确保最小高度为200px
        const calculatedHeight = Math.max(containerWidth * 0.33, 200);

        setChartHeight(calculatedHeight);

        // 如果图表已经初始化，更新其尺寸
        if (chartRef.current) {
            chartRef.current.changeSize(containerWidth, calculatedHeight);
        }
    };

    const initChart = () => {
        if (!containerRef.current) return;

        // 获取容器尺寸
        const containerWidth = containerRef.current.clientWidth;

        // 找出最大值，用于设置Y轴刻度
        const maxValue = Math.max(...monthlyData.map(item => item.count));
        // 计算合适的Y轴最大值，避免顶部空白过多
        // 根据最大值计算合适的刻度间隔和最大值
        const roundToNice = (value) => {
            // 将值舍入到"漂亮"的数字，便于刻度显示
            const magnitude = Math.pow(10, Math.floor(Math.log10(value)));
            const normalized = value / magnitude;
            
            if (normalized <= 1.2) return magnitude;
            if (normalized <= 2.5) return 2.5 * magnitude;
            if (normalized <= 5) return 5 * magnitude;
            return 10 * magnitude;
        };
        
        // 计算最大刻度值，保证是一个"漂亮"的数字
        const niceMaxValue = Math.ceil(maxValue / roundToNice(maxValue)) * roundToNice(maxValue);

        const column = new Column(containerRef.current, {
            data: monthlyData,
            padding: [20, 10, 20, 20],
            xField: 'date',
            yField: 'count',
            color: '#4455f2',
            autoFit: true,
            xAxis: {
                grid: null,
                line: {
                    style: {
                        stroke: '#f5f5f5', // 设置X轴线条的颜色
                    },
                },
                label: {
                    style: {
                        fill: '#adadad', // 文字颜色
                        fontSize: 12, // 文字大小
                        fontWeight: 'normal', // 文字粗细
                    },
                    offset: 8, // 与轴线的距离
                    autoRotate: false, // 禁止自动旋转
                    autoHide: true, // 自动隐藏重叠的标签
                },
            },
            yAxis: {
                grid: { 
                    line: {
                        style: {
                            stroke: '#f5f5f5',
                        },
                    },
                },
                // 设置固定刻度
                min: 0,
                max: niceMaxValue,
                // 使用自定义刻度而不是tickCount
                tickInterval: niceMaxValue / 4, // 将最大值平均分成4份
                label: {
                    // 优化格式化逻辑，确保单位统一
                    formatter: (val) => {
                        const value = parseFloat(val);
                        // 确保0显示为0
                        if (value === 0) return '0';
                        // 1000及以上的值用k表示
                        if (value >= 1000) {
                            return `${(value / 1000).toFixed(0)}k`;
                        }
                        // 小于1000的值直接显示
                        return `${value}`;
                    },
                }
            },
            columnStyle: {
                radius: [4, 4, 4, 4], // 柱状图顶部圆角
            },
            tooltip: {
                showMarkers: false,
                formatter: (datum) => {
                    return {
                        name: '费用',
                        value: datum.count,
                    };
                }
            },
            state: {
                active: {
                    style: {
                        fill: '#6b7afb', // 悬浮时的颜色
                    },
                },
            },
            // 添加柱状图的间距设置
            columnWidthRatio: 0.6, // 增加柱状图宽度占比
            minColumnWidth: 20, // 最小柱宽
            maxColumnWidth: 40, // 最大柱宽
            // 设置初始高度
            height: chartHeight,
        });

        chartRef.current = column;
        column.render();
    };

    useEffect(() => {
        // 初始设置
        updateChartSize();
        initChart();

        // 添加窗口大小变化监听
        const handleResize = () => {
            updateChartSize();
        };

        window.addEventListener('resize', handleResize);

        // 清理函数
        return () => {
            window.removeEventListener('resize', handleResize);
            if (chartRef.current) {
                chartRef.current.destroy();
            }
        };
    }, []);

    return (
        <Card
            title={
                <div className={styles.cardHeader}>
                    <div className={styles.HeaderTag}>
                        <IconTokenCostMonthly />
                        <div className={styles.title}>每月Token费用统计（¥）</div>
                    </div>
                </div>
            }
            className={styles.card}
        >
            <Spin loading={loading} style={{ width: '100%' }}>
                <div
                    ref={containerRef}
                    className={styles.chart}
                    style={{ height: `${chartHeight}px` }}
                />
            </Spin>
        </Card>
    );
};

export default TokenCostMonthly; 