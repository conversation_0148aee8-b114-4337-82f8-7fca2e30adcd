.container {

  .TypographyTitle {
    color: #333333;
    font-weight: 600;
    font-size: 20px;
    margin-bottom: 8px;
  }

  :global(.arco-card-body) {
    padding: 0;
  }

  .header {
    margin-bottom: 8px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f5f5f5;

    //搜索输入框
    :global(.arco-input-inner-wrapper) {
      padding: 8px;
      background-color: #ffffff;
      border-radius: 8px;
      border: 1px solid #f5f5f5;
      transition: all 0.2s;


      &:hover {
        background-color: #fafafa;
        border-color: #ebebeb;
      }

      ::placeholder {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #d6d6d6;
      }

      :global(.arco-input) {
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 8px;
      }
    }

    :global(.arco-select-size-default.arco-select-single .arco-select-view) {
      padding: 8px;
      height: auto;
      line-height: 24px;
      font-weight: 400;
      font-size: 14px;
      color: #595959;
      border-radius: 4px;
      border: 1px solid #ebebeb;
      background-color: transparent;
    }

    .SDKtatol {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      text-align: right;
      color: #adadad;
    }
  }

  .sdkList {
    height: calc(100vh - 205px);
    min-height: 300px;
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 16px;
    grid-auto-rows: min-content;
    position: relative;
    will-change: transform;
    box-sizing: border-box;

    // 添加媒体查询，处理小屏幕设备
    @media screen and (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media screen and (max-width: 992px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media screen and (max-width: 576px) {
      grid-template-columns: repeat(1, 1fr);
    }

    .sdkCard {
      height: 240px;
      box-sizing: border-box;
      cursor: pointer;
      border: 1px solid #f5f5f5;
      border-radius: 8px;
      padding: 20px 24px;
      display: flex;
      justify-content: space-between;
      min-height: 200px;
      transition: all 0.3s;
      position: relative;
      width: 100%;
      max-width: 100%;
      overflow: hidden;

      // 添加响应式内边距
      @media screen and (max-width: 1400px) {
        padding: 16px 20px;
      }

      @media screen and (max-width: 1200px) {
        padding: 14px 16px;
      }

      &:hover {
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

        .sdkActions {
          opacity: 1;
        }

        .sdkUpdateTime {
          opacity: 0;
        }
      }

      :global(.arco-card-body) {
        padding: 0;
        display: flex;
        flex-direction: column;
        width: 100%;
        justify-content: space-between;
        height: 100%;
      }

      .sdkInfo {
        .sdkVersion {
          color: #a6a6a6;
          font-size: 12px;
          font-weight: 400;
        }

        .sdkName {
          font-weight: 600;
          font-size: 16px;
          line-height: 24px;
          color: #333333;
        }

        .sdkIcon {
          width: 48px;
          height: 48px;
        }
      }

      .sdkDescription {
        color: #5c5c5c;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        display: -webkit-box;
        /* 必须设置为 -webkit-box 以支持 line-clamp */
        -webkit-line-clamp: 2;
        /* 限制显示两行 */
        -webkit-box-orient: vertical;
        /* 设置盒子方向为垂直 */
        overflow: hidden;
        /* 隐藏超出部分 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
      }

      .sdkUpdateTime {
        color: #adadad;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        margin-bottom: 0;
        transition: opacity 0.3s ease;
        position: absolute;
        bottom: 24px;
        left: 24px;
        right: 24px;

        @media screen and (max-width: 1400px) {
          bottom: 20px;
          left: 20px;
          right: 20px;
        }

        @media screen and (max-width: 1200px) {
          bottom: 16px;
          left: 16px;
          right: 16px;
        }
      }

      .sdkActions {
        display: flex;
        gap: 8px;
        font-weight: 600;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s ease;
        position: absolute;
        bottom: 24px;
        left: 24px;
        right: 24px;

        @media screen and (max-width: 1400px) {
          bottom: 20px;
          left: 20px;
          right: 20px;
        }

        @media screen and (max-width: 1200px) {
          bottom: 16px;
          left: 16px;
          right: 16px;
        }

        .sdkDownloadBtn,
        .sdkDocBtn {
          flex: 1;
          height: 32px;
          border-radius: 8px;
          font-weight: 500;
          font-size: 14px;
          line-height: 24px;
          transition: all 0.3s ease;
        }

        .sdkDownloadBtn {
          background-color: #4455f2;
          color: #ffffff;
          border: 1px solid #4455f2;

          &:hover{
            background-color: #4152e9;
          }
        }

        .sdkDocBtn {
          color: #5c5c5c;
          background-color: #fff;
          border: 1px solid #ebebeb;

          &:hover{
            background-color: #fafafa;
          }
        }
      }
    }

    // 空状态容器
    .emptyContainer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #ffffff;
      grid-column: 1 / -1;

      svg {
        width: 80px;
        height: 80px;
      }

      .emptyText {
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;
        text-align: center;
      }
    }

    // 建设中状态容器
    .buildingContainer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #ffffff;
      grid-column: 1 / -1;

      svg {
        width: 80px;
        height: 80px;
      }

      .buildingText {
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;
        text-align: center;
      }
    }

    // 加载状态容器
    .loadingContainer {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 1;
      background-color: #ffffff;
      grid-column: 1 / -1;

      :global(.arco-spin) {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .loadingText {
        margin-top: 12px;
        color: RGBA(0, 0, 0, 0.5);
        font-size: 14px;
      }
    }
  }
}