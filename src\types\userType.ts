// src/types/userType.ts

/**
 * 用户相关类型定义
 */

/**
 * 用户信息接口
 */
export interface UserInfo {
    id?: string;
    user_name?: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string | null;
    type?: string;
    role?: string;
    full_name?: string;
    source?: string;
    external_id?: string;
    avatar?: string;
    avatar_url?: string;
    permissions?: string[];
    agent_actions?: string[];
    create_date?: string;
    update_date?: string;
    regionCode?: string;
    tenant_id?: string;
    tenant_name?: string;
} 