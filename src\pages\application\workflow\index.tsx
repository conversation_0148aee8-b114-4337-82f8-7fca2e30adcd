import React from 'react';
import { Tabs } from '@arco-design/web-react';
import useLocale from '@/utils/useLocale';
import styles from '../style/index.module.less';
import WorkflowList from './components/workflowList';
import TabPane from '@arco-design/web-react/es/Tabs/tab-pane';
import { useEffect} from 'react';
import axios from 'axios';

function workflow() {
  const locale = useLocale();

  const login = async () => {
    try {
      const response = await axios.post(
        '/elsa/api/identity/login',
        {
          username: 'admin', // 账号字段名需确认（可能是 username/userName）
          password: 'password' // 密码字段名需确认（可能是 password/pwd）
        },
        {
          headers: {
            'Content-Type': 'application/json; charset=utf-8', // 必须与请求头完全一致[4](@ref)
          }
        }
      );
      console.log('登录成功:', response.data);
      localStorage.setItem('accessToken', response.data.accessToken);
      localStorage.setItem('refreshToken', response.data.refreshToken);
      localStorage.setItem('BlazorCulture', 'zh-CN');
      // let timeout = setTimeout(() => {
      //   setIframeUrl('/workflows/definitions');
      //   clearTimeout(timeout);
      // }, 100); 
      // 通常此处会保存返回的 token 到 Redux/Context
    } catch (error) {
      console.error('登录失败:', error.response?.data || error.message);
    }
  };

  useEffect(() => {
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      // setIframeUrl('/workflows/definitions');
    } else {
      login();
    }
  }, []);

  return (
    <div className={styles.container}>
      <Tabs className={styles.tabs}>
        <TabPane
          key="workflowList"
          title={locale['menu.application.header.workflowList']}
        >
          <WorkflowList />
        </TabPane>
      </Tabs>
    </div>
  );
}

export default workflow;
