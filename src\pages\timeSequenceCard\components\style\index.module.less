  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
    /* 对于基于 WebKit 的浏览器 */
  }

  /* 对于IE和Edge */
  html {
    -ms-overflow-style: none;
    /* IE 和 Edge */
  }

  /* 对于Firefox */
  * {
    scrollbar-width: none;
    /* Firefox */
  }

  .container {

    //基础信息与配置tab
    .tabs {
      :global(.arco-tabs-header-nav::before) {
        display: none;
      }

      :global(.arco-tabs-header-ink) {
        display: none;
      }

      :global(.arco-tabs-header-title) {
        padding: 0;
        font-weight: 600;
        font-size: 20px;
        color: #a6a6a6;
        margin-left: 7px !important;

        &:hover {
          color: #a6a6a6;
        }
      }

      :global(.arco-tabs-header-title-active) {
        color: #333333;

        &:hover {
          color: #333333;
        }
      }

      :global(.arco-tabs-content) {
        padding-top: 24px;
      }
    }


    .customContainer {
      display: flex;
      flex-direction: column;

      .headerText {
        font-weight: 600;
        font-size: 20px;
        line-height: 32px;
        color: #333333;
        margin-bottom: 8px;
      }

      .createTimeSequenceCard {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #4455f2;
        color: #FFFFFF;
        border-radius: 8px;
        padding: 8px 24px;
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        height: 40px;

        &:hover {
          color: #FFFFFF !important;
          background-color: RGBA(71, 86, 223, 1) !important;
        }
      }

      //搜索输入框
      :global(.arco-input-inner-wrapper) {
        padding: 8px;
        background-color: #ffffff;
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        transition: all 0.2s;

        &:hover {
          background-color: #fafafa;
          border-color: #ebebeb;
        }

        ::placeholder {
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
          color: #d6d6d6;
        }

        :global(.arco-input) {
          padding-top: 0;
          padding-bottom: 0;
          padding-left: 8px;
        }
      }

      //筛选select
      :global(.arco-select-size-default.arco-select-single .arco-select-view) {
        padding: 8px;
        height: auto;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #d6d6d6;
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        background-color: #ffffff;
        transition: all 0.2s;

        &:hover {
          background-color: #fafafa;
          border-color: #ebebeb;
        }

        :global(.arco-select-prefix) {
          margin-right: 4px;
        }

        :global(.arco-select-view-input) {
          &::placeholder {
            color: #d6d6d6;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
          }
        }
      }

      .content {
        height: calc(100vh - 205px);
        min-height: 300px;
        overflow-y: auto;
        // 修改为固定四列布局
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 16px;
        grid-auto-rows: min-content;
        position: relative;
        will-change: transform;
        box-sizing: border-box;

        // 添加媒体查询，处理小屏幕设备
        @media screen and (max-width: 1200px) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media screen and (max-width: 992px) {
            grid-template-columns: repeat(2, 1fr);
        }

        @media screen and (max-width: 576px) {
            grid-template-columns: repeat(1, 1fr);
        }

        .timeSequenceCard {
          border: 1px solid #f5f5f5;
          // 移除最小宽度限制
          // min-width: 360px;
          height: 240px;
          box-sizing: border-box;
          cursor: pointer;
          border-radius: 8px;
          padding: 20px 24px;
          display: flex;
          justify-content: space-between;
          min-height: 200px;
          transition: all 0.3s;
          position: relative;
          width: 100%;
          max-width: 100%;
          overflow: hidden;

          // 添加响应式内边距
          @media screen and (max-width: 1400px) {
              padding: 16px 20px;
          }

          @media screen and (max-width: 1200px) {
              padding: 14px 16px;
          }

          &:hover {
            box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

            .cardContent {
              .cardFooter {
                .actionWrapper {
                  .statusIndicator {
                    opacity: 0;
                  }

                  .triggerBtn {
                    opacity: 1;
                  }
                }
              }
            }
          }

          :global(.arco-card-body) {
            padding: 0;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            width: 100%;
            height: 100%;
          }

          .cardContent {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;

            .timeSequenceCardInfo {
              display: flex;
              flex-direction: column;

              .nameWrapper {
                display: flex;
                align-items: center;

                .icon {
                  width: 48px;
                  height: 48px;
                  border-radius: 8px;

                  svg {
                    width: 100%;
                    height: 100%;
                  }
                }

                .timeSequenceCardName {
                  font-weight: 600;
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  cursor: pointer;
                }
              }

              .cardDescription {
                color: #5c5c5c;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
              }


              .description {
                color: #595959;
                margin-bottom: 16px;
                font-size: 14px;
                font-weight: 400;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.5;
                /* 行高可以根据需要调整 */
                margin: 8px 0;
                /* 可选：调整上下间距 */
              }

              .tags {
                display: flex;
                flex-wrap: wrap;

                :global(.arco-btn) {
                  border-radius: 4px;
                  padding: 2px 6px;
                  font-size: 12px;
                  background-color: transparent;
                  border: 1px solid #00000014;
                  color: #000000A3;
                  font-weight: 400;
                }
              }
            }

            .cardFooter {
              display: flex;
              align-items: center;
              margin-top: 8px;

              .creatorInfo {
                :global(.arco-space-item) {
                  :global(.arco-typography) {
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 20px;
                    color: #adadad;
                  }
                }
              }

              .actionWrapper {
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                min-width: 32px;
                position: relative;

                .statusIndicator {
                  display: flex;
                  align-items: center;
                  padding: 0 8px;
                  height: 20px;
                  opacity: 1;
                  transition: all 0.3s;
                }

                .enabled,
                .disabled {
                  display: flex;
                  align-items: center;
                  font-size: 12px;
                  font-weight: 400;
                  line-height: 20px;
                  padding: 2px 6px;
                  border-radius: 4px;
                }

                .enabled {
                  color: #36a978;
                  background-color: #f7fcfa;
                }

                .disabled {
                  color: #d54941;
                  background-color: #fef8f8;
                }

                .triggerBtn {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  padding: 4px;
                  background-color: #ffffff;
                  border-radius: 8px;
                  border: 1px solid #f5f5f5;
                  position: absolute;
                  right: 0;
                  top: 0;
                  opacity: 0;
                  transition: all 0.3s;

                  &:hover {
                    background: #fafafa;
                  }
                }
              }
            }
          }

          .triggerBtn {
            display: none;
          }

          .actionBtn {
            background-color: #333333;
            width: 120px;
          }

          .enableBtn {
            color: #2ba471 !important;
            background: #ffffff !important;

            &:hover {
              background: #f7fcfa !important;
            }
          }

          .disableBtn {
            color: #d54941 !important;
            background-color: #ffffff !important;

            &:hover {
              background: #fef8f8 !important;
            }
          }

          .copyBtn {
            color: #4455f2 !important;
            background-color: #ffffff !important;

            &:hover {
              background: #f5f7ff !important;
            }
          }

          .deleteBtn {
            background-color: #ffffff !important;
            color: #333333 !important;

            &:hover {
              background: #f5f5f5 !important;
            }
          }
        }



        .emptyContainer {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          grid-column: 1 / -1;
          background-color: #ffffff;

          :global(.arco-space) {
            display: flex;
            flex-direction: column;
            align-items: center;
          }

          :global(.arco-typography) {
            font-weight: 500;
            font-size: 14px;
            line-height: 24px;
            color: #5c5c5c;
            text-align: center;
          }
        }

        .loadingContainer {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          grid-column: 1 / -1;
          background-color: #ffffff;
        }

        .errorContainer {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          grid-column: 1 / -1;
          background-color: #ffffff;

          :global(.arco-typography) {
            font-weight: 500;
            font-size: 14px;
            line-height: 24px;
            color: #ff4d4f;
            text-align: center;
          }
        }
      }

      .rowEndCenter {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }

      .countAppText {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #adadad;
      }

    }
  }

  //删除确认Model
  .confirmDeleteModal {
    position: relative;
    padding: 24px;
    width: 480px;
    border-radius: 16px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

    :global(.arco-modal-header) {
      padding: 0;
      height: auto;
      border-bottom: none;

      :global(.arco-modal-title) {
        font-weight: 600;
        font-size: 18px;
        line-height: 24px;
        color: #333333;
        margin-bottom: 16px;
        text-align: left;
      }
    }

    :global(.arco-modal-content) {
      padding: 0;
      width: 100%;
      height: 100%;
    }

    :global(.arco-modal-footer) {
      display: none;
    }

    :global(.arco-modal-close-icon) {
      position: absolute;
      right: 24px;
      top: 24px;
      cursor: pointer;
    }

    .modalContent {
      display: flex;
      flex-direction: column;

      .modalContentText {
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;
      }
    }

    .modalFooter {
      margin-top: 24px;
      display: flex;
      justify-content: flex-end;
      gap: 8px;

      .cancelDeleteBtn,
      .confirmDeleteBtn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 24px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        height: 40px;
        transition: all 0.3s;
      }

      .cancelDeleteBtn {
        background-color: #ffffff;
        color: #5c5c5c;
        border: 1px solid #ebebeb;

        &:hover {
          background-color: #fafafa;
          border-color: #ebebeb;
        }
      }

      .confirmDeleteBtn {
        background-color: #d54941;
      }
    }

    :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
      background-color: #cd463e;
      color: #ffffff;
    }
  }

  :global(.arco-popover-content.arco-popover-content-right) {
    padding: 8px;
    width: 160px;
    border: none;
    border: 1px solid #f5f5f5;
    border-radius: 8px; 
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

    :global(.arco-btn) {
      background-color: transparent;
      width: 144px;
      text-align: left;
    }
  }

  .enableBtn,
  .disableBtn,
  .deleteBtn,
  .copyBtn {
    padding: 4px 8px;
    border-radius: 4px;
    width: 144px;
    display: flex;
    justify-content: flex-start;
  }

  .enableBtn {
    color: #2ba471 !important;
    background: #ffffff !important;

    &:hover {
      background: #f7fcfa !important;
    }
  }

  .disableBtn {
    color: #d54941 !important;
    background-color: #ffffff !important;

    &:hover {
      background: #fef8f8 !important;
    }
  }

  .deleteBtn,.copyBtn {
    background-color: #ffffff !important;
    color: #333333 !important;

    &:hover {
      background: #f5f5f5 !important;
    }
  }