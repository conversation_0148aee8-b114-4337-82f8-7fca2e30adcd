import { <PERSON><PERSON><PERSON>onnection, HubCon<PERSON><PERSON><PERSON><PERSON><PERSON>, LogLevel } from '@microsoft/signalr';
import { endpoints } from '@/lib/services/api-endpoints';
import { getUserStore } from '@/lib/helpers/store';

// 定义接口
export interface Conversation {
  id: string;
  title: string;
}

export interface Sender {
  role: string;
}
export interface IRichContent {
  rich_type: string;
  text: string;
  options: any[];
  buttons: any[];
  elements: any[];
  quick_replies: any[];
}

export interface RichContent {
  messaging_type: string;
  fill_postback: boolean;
  editor: string;
  editor_attributes?: string;
  message: IRichContent;
}


export interface Message {
  conversation_id: string;
  sender: UserModel;
  message_id: string;
  text: string;
  editor?: string;
  function?: string;
  rich_content?: RichContent;
  post_action_disclaimer?: string;
  data?: string;
  created_at?: Date;
  has_message_files?: boolean;
  is_chat_message?: boolean;
}

export interface ConversationLog {
  conversation_id: string;
  message_id: string;
  name: string;
  agent_id: string;
  role: string;
  source: string;
  content: string;
  created_at: Date;
}

export interface ConversationStateLog {
  conversation_id: string;
  message_id: string;
  before_value: string;
  before_active_rounds?: number;
  after_value: string;
  after_active_rounds?: number;
  data_type: string;
  source: string;
  created_at: Date;
}

export interface ConversationModel {
  id: string;
  title: string;
  user: UserModel;
  agent_id: string;
  agent_name: string;
  channel: string;
  task_id?: string;
  status: string;
  states: object[];
  updated_time: Date;
  created_time: Date;
}

export interface UserModel {
  id: string;
  user_name?: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  email?: string;
  source: string;
  external_id?: string;
  create_date?: string;
  update_date?: string;
  role?: string;
  avatar?: string;
  color?: string;
  token?: string;
}

export interface AgentQueueLog {
  conversation_id: string;
  log: string;
  created_at: Date;
}

export interface ConversationSenderAction {
  conversation_id: string;
  sender_action: number;
  indication?: string;
}

export interface ConversationMessageDelete {
  conversation_id: string;
  message_id: string;
}

interface SignalRService {
  onConversationInitFromClient: (conversation: ConversationModel) => void;
  onMessageReceivedFromClient: (message: Message) => void;
  onMessageReceivedFromCsr: (message: Message) => void;
  onMessageReceivedFromAssistant: (message: Message) => void;
  onStreamMessageReceivedFromAssistant: (message: Message) => void;
  onConversationContentLogGenerated: (log: ConversationLog) => void;
  onConversationStateLogGenerated: (log: ConversationLog) => void;
  onStateChangeGenerated: (log: ConversationStateLog) => void;
  onAgentQueueChanged: (log: AgentQueueLog) => void;
  onSenderActionGenerated: (data: ConversationSenderAction) => void;
  onConversationMessageDeleted: (data: ConversationMessageDelete) => void;
  start: (conversationId: string) => Promise<void>;
  stop: () => Promise<void>;
}

// 创建一个连接对象
let connection: HubConnection | null = null;

// 创建 SignalR 服务对象
export const signalr: SignalRService = {
  onConversationInitFromClient: () => {},
  onMessageReceivedFromClient: () => {},
  onMessageReceivedFromCsr: () => {},
  onMessageReceivedFromAssistant: () => {},
  onStreamMessageReceivedFromAssistant: () => {},
  onConversationContentLogGenerated: () => {},
  onConversationStateLogGenerated: () => {},
  onStateChangeGenerated: () => {},
  onAgentQueueChanged: () => {},
  onSenderActionGenerated: () => {},
  onConversationMessageDeleted: () => {},

  async start(conversationId: string) {
    const user = getUserStore();
    connection = new HubConnectionBuilder()
      .withUrl(endpoints.chatHubUrl + `?conversationId=${conversationId}&access_token=${user.token}`)
      .withAutomaticReconnect()
      .configureLogging(LogLevel.Information)
      .build();

    try {
      await connection.start();
      console.log('Connected to SignalR hub');
    } catch (err) {
      console.error(err);
    }

    connection.on('OnConversationInitFromClient', (conversation: Conversation) => {
      if (conversationId === conversation.id) {
        console.log(`[OnConversationInitFromClient] ${conversation.id}: ${conversation.title}`);
        this.onConversationInitFromClient(conversation);
      }
    });

    connection.on('OnMessageReceivedFromClient', (message: Message) => {
      if (conversationId === message.conversation_id) {
        this.onMessageReceivedFromClient(message);
      }
    });

    connection.on('OnMessageReceivedFromCsr', (message: Message) => {
      if (conversationId === message.conversation_id) {
        this.onMessageReceivedFromCsr(message);
      }
    });

    connection.on('OnMessageReceivedFromAssistant', (json: string) => {
      const message: Message = JSON.parse(json);
      if (conversationId === message.conversation_id) {
        this.onMessageReceivedFromAssistant(message);
      }
    });

    connection.on('OnStreamMessageReceivedFromAssistant', (json: string) => {
      const message: Message = JSON.parse(json);
      if (conversationId === message.conversation_id) {
        this.onStreamMessageReceivedFromAssistant(message);
      }
    });

    connection.on('OnConversationContentLogGenerated', (log: string) => {
      const jsonLog: ConversationLog = JSON.parse(log);
      if (conversationId === jsonLog?.conversation_id) {
        this.onConversationContentLogGenerated(jsonLog);
      }
    });

    connection.on('OnConversateStateLogGenerated', (log: string) => {
      const jsonData: ConversationLog = JSON.parse(log);
      if (conversationId === jsonData?.conversation_id) {
        this.onConversationStateLogGenerated(jsonData);
      }
    });

    connection.on('OnStateChangeGenerated', (log: string) => {
      const jsonData: ConversationLog = JSON.parse(log);
      if (conversationId === jsonData?.conversation_id) {
        this.onStateChangeGenerated(jsonData);
      }
    });

    connection.on('OnAgentQueueChanged', (log: string) => {
      const jsonData: ConversationLog = JSON.parse(log);
      if (conversationId === jsonData?.conversation_id) {
        this.onAgentQueueChanged(jsonData);
      }
    });

    connection.on('OnSenderActionGenerated', (data: ConversationLog) => {
      if (conversationId === data?.conversation_id) {
        this.onSenderActionGenerated(data);
      }
    });

    connection.on('OnMessageDeleted', (data: ConversationLog) => {
      if (conversationId === data?.conversation_id) {
        this.onConversationMessageDeleted(data);
      }
    });
  },

  async stop() {
    if (connection) {
      try {
        await connection.stop();
        console.log('Disconnected from SignalR hub');
      } catch (err) {
        console.error(err);
      }
    }
  }
};
