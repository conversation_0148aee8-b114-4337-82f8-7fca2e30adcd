import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Input, Typography, Space, Select, Spin, Modal, Form, Message, Popover, Tag } from '@arco-design/web-react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import IconSearch from '@/assets/acp/IconSearch.svg';
import IconAcp from '@/assets/acp/IconAcp.svg';
// import IconAcp2 from '@/assets/acp/IconAcp2.svg';
// import IconAcp3 from '@/assets/acp/IconAcp3.svg';
// import IconAcp4 from '@/assets/acp/IconAcp4.svg';
import IconClose from '@/assets/acp/IconClose.svg';
import IconAction from '@/assets/acp/IconAction.svg';
import IconSortType from '@/assets/application/IconSortType.svg';
import IconEmptyAcp from '@/assets/acp/IconEmptyAcp.svg';
import styles from './style/index.module.less';
import { 
    saveAcpServer, 
    getAcpServerList, 
    deleteAcpServer, 
    checkAcpServerAvailable,
} from '@/lib/services/acp-server-service';
import { AcpServer, AcpServerResponse } from '@/types/acpServerType';
import ACPServerModal from '../ACPServerModal';

const { Text } = Typography;
const Option = Select.Option;

// 扩展AcpServer接口，添加arguments和transport_options字段
type ExtendedAcpServer = AcpServer

function ACPServer() {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [acps, setAcps] = useState<AcpServerResponse[]>([]);
    const [filteredAcps, setFilteredAcps] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [form] = Form.useForm(); // 创建Form实例
    const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
    const [acpToDelete, setAcpToDelete] = useState(null);
    const [searchText, setSearchText] = useState('');
    const [sortType, setSortType] = useState('default');
    const [testingConnections, setTestingConnections] = useState<{ [key: string]: boolean }>({});
    // const acpIcons = [IconAcp, IconAcp2, IconAcp3, IconAcp4];
    const acpIcons = [IconAcp];



    // 更新面包屑数据
    const updateBreadcrumbData = (newBreadcrumb: Map<string, string>) => {
        dispatch({
            type: 'update-breadcrumb-menu-name',
            payload: { breadcrumbMenuName: newBreadcrumb },
        });
    };

    // 获取ACP图标的函数
    const getAcpIcon = (id) => {
        // 使用简单的哈希函数生成索引
        const hash = id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
        const index = hash % acpIcons.length;
        const IconComponent = acpIcons[index];
        return <IconComponent />;
    };

    // 根据transport_type获取传输类型显示文本
    const getTransportTypeText = (transportType: number) => {
        switch (transportType) {
            case 1:
                return 'auto_detect_http';
            case 2:
                return 'streamable_http';
            case 3:
                return 'sse';
            default:
                return 'unknown';
        }
    };

    useEffect(() => {
        fetchAcps();
    }, []);

    // 当acps或搜索/排序条件改变时，更新过滤后的ACP列表
    useEffect(() => {
        filterAndSortAcps();
    }, [acps, searchText, sortType]);

    //使用getAcpServerList获取ACP列表
    const fetchAcps = async () => {
        try {
            setLoading(true);
            const data = await getAcpServerList();
            setAcps(data);
            setError(null);
        } catch (err) {
            console.error('获取ACP列表失败:', err);
            setError('获取ACP数据失败，请稍后重试');
        } finally {
            setLoading(false);
        }
    };


    // 处理模态框提交
    const handleModalSubmit = async (values: ExtendedAcpServer): Promise<void> => {
        try {
            const result = await saveAcpServer(values);
            if (result) {
                Message.success('ACP创建成功！');
                fetchAcps(); // 重新获取ACP列表
                setModalVisible(false);
            }
        } catch (error) {
            console.error('创建ACP失败:', error);
            Message.error('创建ACP失败，请检查网络或联系管理员！');
            throw error; // 重新抛出错误以便在组件中处理
        }
    };

    // 删除Acp
    const handleConfirmDelete = async () => {
        if (acpToDelete) {
            try {
                const result = await deleteAcpServer(acpToDelete.id || '');
                if (result) {
                    Message.success('ACP删除成功！');
                    fetchAcps(); // 重新获取ACP列表
                } else {
                    Message.error('删除ACP失败！');
                }
            } catch (error) {
                console.error('删除ACP失败:', error);
                Message.error('删除ACP失败，请检查网络或联系管理员！');
            } finally {
                setConfirmDeleteVisible(false);
                setAcpToDelete(null);
            }
        }
    };

    // 筛选和排序ACP列表
    const filterAndSortAcps = () => {
        let result = [...acps];

        // 搜索过滤
        if (searchText) {
            const lowerCaseSearch = searchText.toLowerCase();
            result = result.filter(acp =>
                acp.name.toLowerCase().includes(lowerCaseSearch) ||
                acp.location?.toLowerCase().includes(lowerCaseSearch) ||
                (acp.id?.toLowerCase() || '').includes(lowerCaseSearch)
            );
        }

        // 排序
        switch (sortType) {
            case 'createTime':
                result.sort((a, b) => {
                    const timeA = a.created_time ? new Date(a.created_time).getTime() : 0;
                    const timeB = b.created_time ? new Date(b.created_time).getTime() : 0;
                    return timeB - timeA; // 降序排列，最新的在前
                });
                break;
            case 'updateTime':
                result.sort((a, b) => {
                    const timeA = a.updated_time ? new Date(a.updated_time).getTime() : 0;
                    const timeB = b.updated_time ? new Date(b.updated_time).getTime() : 0;
                    return timeB - timeA; // 降序排列，最新的在前
                });
                break;
            case 'name':
                result.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
                break;
            case 'default':
            default:
                // 保持原始顺序，不进行排序
                break;
        }

        setFilteredAcps(result);
    };

    // 处理排序变化
    const handleSort = (value) => {
        setSortType(value);
    };

    // 处理搜索
    const handleSearch = (value) => {
        setSearchText(value);
    };


    // 处理"添加ACP"按钮
    const handleAddAcp = () => {
        form.resetFields(); // 重置表单字段
        form.setFieldsValue({ type: 'sse' }); // 默认选择sse连接类型
        setModalVisible(true);
    };

    // 处理"查看"ACP详情
    const handleViewAcp = (acp) => {
        // 构造完整的路径 - 使用RESTful风格
        const fullPath = `/acp/servers/${acp.id}`;
        
        // 更新面包屑，显示当前ACP的名称
        const breadcrumbData = new Map<string, string>([
            [fullPath, acp.name],
        ]);
        updateBreadcrumbData(breadcrumbData);

        // 导航到详情页面，传递ID参数
        navigate(fullPath);
    };

    // 处理删除确认
    const handleDeleteConfirm = (acp) => {
        setAcpToDelete(acp);
        setConfirmDeleteVisible(true);
    };

    // 取消删除
    const handleCancelDelete = () => {
        setConfirmDeleteVisible(false);
        setAcpToDelete(null);
    };

    // 处理取消按钮
    const handleModalCancel = () => {
        // 关闭模态框
        setModalVisible(false);
        form.resetFields();
    };

    // 处理测试连接
    const handleTestConnection = async (acp: AcpServerResponse) => {
        if (!acp.id) return;
        
        setTestingConnections(prev => ({ ...prev, [acp.id]: true }));
        
        try {
            const result = await checkAcpServerAvailable(acp.id);
            if (result.success) {
                Message.success(result.message || '测试连接成功');
                // 测试连接成功后重新获取列表数据，刷新状态
                fetchAcps();
            } else {
                Message.error(result.message || '测试连接失败，请检查配置');
            }
        } catch (error) {
            console.error('测试连接失败:', error);
            Message.error('测试连接失败，请检查配置');
        } finally {
            setTestingConnections(prev => ({ ...prev, [acp.id]: false }));
        }
    };

    // 渲染内容
    const renderContent = () => {
        if (loading) {
            return (
                <div className={styles.loadingContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ padding: 24 }}>
                            <Spin tip="加载中..." />
                        </div>
                    </Space>
                </div>
            );
        }

        if (error) {
            return (
                <div className={styles.errorContainer}>
                    <Text type="error">{error}</Text>
                </div>
            );
        }

        if (filteredAcps.length === 0) {
            return (
                <div className={styles.emptyContainer}>
                    <Space direction='vertical' size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <IconEmptyAcp style={{ width: 80, height: 80 }} />
                        <Text type="secondary">{searchText ? '未找到匹配的ACP Server' : '未找到ACP Server'}</Text>
                    </Space>
                </div>
            );
        }

        return (
            <>
                {filteredAcps.map((acpserver, index) => (
                    <Card key={acpserver.id || index} className={styles.acpCard} onClick={() => handleViewAcp(acpserver)}>
                        <div className={styles.acpInfo}>
                            <Space direction='vertical' size={8}>
                                <Space className={styles.nameWrapper} size={12}>
                                    <span className={styles.icon}>
                                        {getAcpIcon(acpserver.id)}
                                    </span>
                                    <Text className={styles.name}>{acpserver.name}</Text>
                                </Space>
                                <Space className={styles.acpDetails} direction='vertical' size={8}>
                                    <Text className={styles.description}>{acpserver.description}</Text>
                                    <Text className={styles.acpType}>{getTransportTypeText(acpserver.transport_type)}</Text>
                                </Space>
                            </Space>    
                            <div className={styles.acpFooter}>
                                {/* 默认显示的资源数量和状态 */}
                                <div className={styles.defaultInfo}>
                                    <Space className={styles.resourceInfo}>
                                        {/* <Text className={styles.resourceCount}>8 个资源</Text> */}
                                    </Space>
                                    <div className={styles.statusInfo}>
                                        <Tag
                                            className={`${styles.statusTag} ${acpserver.is_available ? styles.statusAvailable : styles.statusUnavailable}`}
                                            color={acpserver.is_available ? "green" : "red"}
                                        >
                                            {acpserver.is_available ? "可用" : "不可用"}
                                        </Tag>
                                    </div>
                                </div>

                                {/* 悬浮时显示的元信息和操作按钮 */}
                                <div className={styles.hoverInfo}>
                                    <Space className={styles.metaText}>
                                        <Text className={styles.creator}>@AI4C</Text>
                                        <Text>|</Text>
                                        <Text className={styles.timeInfo}>
                                            {acpserver.updated_time
                                                ? `更新时间：${new Date(acpserver.updated_time).toLocaleDateString('zh-CN')}`
                                                : `创建时间：${new Date(acpserver.created_time).toLocaleDateString('zh-CN')}`
                                            }
                                        </Text>
                                    </Space>
                                    <Popover
                                        trigger="click"
                                        position="right"
                                        className={styles.popoverContent}
                                        content={
                                            <Space className={styles.popoverContent} direction='vertical' size={'mini'}>
                                                <Button
                                                    className={`${styles.actionBtn} ${styles.testBtn}`}
                                                    loading={testingConnections[acpserver.id] || false}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleTestConnection(acpserver);
                                                    }}
                                                >
                                                    测试连接
                                                </Button>
                                                <Button
                                                    className={`${styles.actionBtn} ${styles.deleteBtn}`}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        handleDeleteConfirm(acpserver)
                                                    }}
                                                >
                                                    删除
                                                </Button>
                                            </Space>
                                        }
                                    >
                                        <Button
                                            className={styles.triggerBtn}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                            }}>
                                            <IconAction />
                                        </Button>
                                    </Popover>
                                </div>
                            </div>
                        </div>
                    </Card>
                ))}
            </>
        );
    };

    return (
        <div className={styles.acp}>
            <Text className={styles.headerText}>ACP Server</Text>
            <div className={styles.header}>
                <Button type="primary" className={styles.addAcpBtn} onClick={handleAddAcp}>新增ACP Server</Button>
                <Space size={'small'}>
                    <Text className={styles.acpNumber}>
                        共 {filteredAcps.length} 个ACP server
                    </Text>
                    <Input
                        prefix={<IconSearch />}
                        placeholder="AI搜索..."
                        style={{ width: 240 }}
                        onChange={(value) => handleSearch(value)}
                        allowClear
                    />
                    <Select
                        prefix={<IconSortType />}
                        placeholder="默认排序"
                        style={{ width: 160 }}
                        onChange={handleSort}
                        value={sortType}
                    >
                        <Option value="default">默认排序</Option>
                        <Option value="createTime">按创建时间排序</Option>
                        <Option value="updateTime">按更新时间排序</Option>
                        <Option value="name">按名称排序</Option>
                    </Select>
                </Space>
            </div>

            <div className={styles.content}>
                {renderContent()}
            </div>

            {/* 使用封装的ACPServerModal组件 */}
            <ACPServerModal
                visible={modalVisible}
                existingAcps={acps}
                onCancel={handleModalCancel}
                onSubmit={handleModalSubmit}
            />

            {/* 删除确认弹窗 */}
            <Modal
                visible={confirmDeleteVisible}
                title="删除ACP Server"
                onCancel={handleCancelDelete}
                closeIcon={<IconClose />}
                className={styles.confirmDeleteModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <Text className={styles.modalContentText}>
                        {`ACP Server ${acpToDelete?.name || ''} 将被删除，请确认您是否要删除？`}
                    </Text>
                </div>
                <div className={styles.modalFooter}>
                    <Space>
                        <Button onClick={handleCancelDelete} className={styles.cancelDeleteBtn}>取消</Button>
                        <Button onClick={handleConfirmDelete} className={styles.confirmDeleteBtn}>
                            删除
                        </Button>
                    </Space>
                </div>
            </Modal>
        </div>
    );
}

export default ACPServer;