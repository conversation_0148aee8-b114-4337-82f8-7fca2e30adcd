# 文件选择组件 (FilePicker)

一个基于 Ant Design 和 TanStack React Query 的树形文件选择组件，支持文件和文件夹的选择。

## 功能特性

- 🌳 **树形结构展示** - 以树形结构展示文件和文件夹
- 🔍 **搜索功能** - 支持按文件名搜索
- 📁 **懒加载** - 动态加载文件夹内容，提升性能
- ✅ **多种选择模式** - 支持单选、多选、仅文件、仅文件夹
- 🎨 **美观界面** - 基于 Ant Design 设计，界面美观
- ⚡ **高性能** - 使用 TanStack React Query 进行数据管理

## 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `visible` | `boolean` | - | 是否显示弹窗 |
| `onCancel` | `() => void` | - | 关闭弹窗回调 |
| `onConfirm` | `(selectedFiles: FileItem[]) => void` | - | 确认选择回调 |
| `multiple` | `boolean` | `false` | 是否允许多选 |
| `folderOnly` | `boolean` | `false` | 是否只允许选择文件夹 |
| `fileOnly` | `boolean` | `false` | 是否只允许选择文件 |
| `title` | `string` | `'选择文件'` | 弹窗标题 |
| `width` | `number` | `600` | 弹窗宽度 |

## 数据类型

### FileItem

```typescript
interface FileItem {
  id: string;              // 文件/文件夹 ID
  name: string;            // 文件/文件夹名称
  type: 'file' | 'folder'; // 类型：文件或文件夹
  parent_id?: string;      // 父级 ID
  size?: number;           // 文件大小（字节）
  created_at?: string;     // 创建时间
  updated_at?: string;     // 更新时间
}
```

## 使用示例

### 基础用法

```tsx
import React, { useState } from 'react';
import { Button, message } from 'antd';
import FilePicker, { type FileItem } from '@/pages/knowledge/components/file-picker';

const MyComponent: React.FC = () => {
  const [visible, setVisible] = useState(false);

  const handleConfirm = (selectedFiles: FileItem[]) => {
    console.log('选中的文件:', selectedFiles);
    message.success(`已选择: ${selectedFiles[0]?.name}`);
    setVisible(false);
  };

  return (
    <div>
      <Button onClick={() => setVisible(true)}>
        选择文件
      </Button>
      
      <FilePicker
        visible={visible}
        onCancel={() => setVisible(false)}
        onConfirm={handleConfirm}
        title="选择文件"
      />
    </div>
  );
};
```

### 多选模式

```tsx
<FilePicker
  visible={visible}
  onCancel={() => setVisible(false)}
  onConfirm={handleConfirm}
  multiple
  title="选择多个文件"
/>
```

### 仅选择文件夹

```tsx
<FilePicker
  visible={visible}
  onCancel={() => setVisible(false)}
  onConfirm={handleConfirm}
  folderOnly
  title="选择文件夹"
/>
```

### 仅选择文件

```tsx
<FilePicker
  visible={visible}
  onCancel={() => setVisible(false)}
  onConfirm={handleConfirm}
  fileOnly
  title="选择文件"
/>
```

## 依赖要求

- React 18+
- Ant Design 5+
- TanStack React Query 4+
- 需要配置好文件管理服务接口

## 注意事项

1. **接口依赖**: 组件依赖 `fileManagerService.listFile` 接口，确保接口正常可用
2. **权限控制**: 组件不处理权限控制，需要在上层组件中处理
3. **错误处理**: 组件内置了基本的错误处理，会显示错误提示
4. **性能优化**: 使用了懒加载和查询缓存，适合大量文件的场景

## 完整示例

查看 `example.tsx` 文件获取完整的使用示例。