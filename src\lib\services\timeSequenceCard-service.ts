import axiosInstance from './interceptors';
import { endpoints } from './api-endpoints';
import { 
    TimeSequenceCardMetadata, 
    TimeSequenceCardMetadataListParams, 
    TimeSequenceCardMetadataListResponse 
} from '@/types/timeSequenceCardType';

/**
 * 创建时序卡片元数据
 * @param data 时序卡片元数据
 * @returns 创建结果
 */
export const createTimeSequenceCardMetadata = async (data: TimeSequenceCardMetadata) => {
    try {
        const response = await axiosInstance.post(endpoints.timeSequenceCardMetadataCreateUrl, data);
        return response.data;
    } catch (error) {
        console.error('创建时序卡片元数据失败:', error);
        throw error;
    }
};

/**
 * 获取时序卡片元数据列表
 * @param params 查询参数
 * @returns Promise<TimeSequenceCardMetadataListResponse>
 */
export async function getTimeSequenceCardMetadataList(
    params: TimeSequenceCardMetadataListParams = {}
): Promise<TimeSequenceCardMetadataListResponse> {
    try {
        const response = await axiosInstance.get(endpoints.timeSequenceCardMetadataListUrl, {
            params: {
                'pager.page': params.pager?.page || 1,
                'pager.size': params.pager?.size || 60,
                'pager.sort': params.pager?.sort || null,
                'pager.order': params.pager?.order || 'asc',
                name: params.name || null,
                is_default: params.is_default || null,
                is_enabled: params.is_enabled || null,
                code: params.code || null,
            },
        });
        return response.data;
    } catch (error) {
        console.error('获取时序卡片元数据列表失败:', error);
        throw error;
    }
}

/**
 * 获取时序卡片元数据详情
 * @param id 时序卡片元数据ID
 * @returns 时序卡片元数据详情
 */
export const getTimeSequenceCardMetadataDetail = async (id: string) => {
    try {
        const url = endpoints.timeSequenceCardMetadataDetailUrl.replace('{id}', id);
        const response = await axiosInstance.get(url);
        return response.data;
    } catch (error) {
        console.error('获取时序卡片元数据详情失败:', error);
        throw error;
    }
};

/**
 * 更新时序卡片元数据
 * @param id 时序卡片元数据ID
 * @param data 更新的时序卡片元数据
 * @returns 更新结果
 */
export const updateTimeSequenceCardMetadata = async (id: string, data: Partial<TimeSequenceCardMetadata>) => {
    try {
        const url = endpoints.timeSequenceCardMetadataUpdateUrl.replace('{id}', id);
        const response = await axiosInstance.put(url, data);
        return response.data;
    } catch (error) {
        console.error('更新时序卡片元数据失败:', error);
        throw error;
    }
};

/**
 * 删除时序卡片元数据
 * @param code 时序卡片代码标识
 * @param isDeleted 是否删除，默认为 true
 * @returns 删除结果
 */
export const deleteTimeSequenceCardMetadata = async (code: string, isDeleted = true) => {
    try {
        const url = endpoints.timeSequenceCardMetadataDeleteUrl
            .replace('{code}', code)
            .replace('{isDeleted}', isDeleted ? 'true' : 'false');
        const response = await axiosInstance.delete(url);
        return response.data;
    } catch (error) {
        console.error('删除时序卡片元数据失败:', error);
        throw error;
    }
};

/**
 * 生成ContentSchema
 * @param structureData 结构数据数组
 * @returns 标准化的ContentSchema
 */
export const generateContentSchema = async (structureData: any[]) => {
    try {
        // 构建新的数据结构，符合parts格式
        const requestBody = {
            type: "object",
            properties: {
                parts: structureData.map((item, index) => {
                    // 如果已有partData，直接使用；否则根据type生成默认结构
                    if (item.partData) {
                        return item.partData;
                    } else {
                        // 生成默认的part结构
                        const currentTime = new Date().toISOString();
                        // 使用item.id如果存在，否则生成统一格式的ID
                        const baseId = item.id || `${item.type}_${(index + 1).toString().padStart(3, '0')}`;
                        
                        // 根据组件类型生成对应的part结构
                        switch (item.type) {
                            case 'icon':
                            case 'title':
                            case 'text':
                            case 'tag':
                            case 'link':
                                return {
                                    type: "text",
                                    id: baseId,
                                    parent_id: null,
                                    created_time: currentTime,
                                    generated_by: "system",
                                    text: `这是一个${item.name || item.type}`,
                                    part_sort: -1,
                                    metadata: {
                                        text_type: item.type
                                    }
                                };
                            case 'file':
                                return {
                                    type: "file",
                                    id: baseId,
                                    parent_id: null,
                                    created_time: currentTime,
                                    generated_by: "system",
                                    file: {
                                        name: "这是一个文件",
                                        mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                                        bytes: null,
                                        uri: null,
                                        size: null
                                    },
                                    part_sort: -1,
                                    metadata: {
                                        file_type: "document"
                                    }
                                };
                            case 'image':
                                return {
                                    type: "file",
                                    id: baseId,
                                    parent_id: null,
                                    created_time: currentTime,
                                    generated_by: "system",
                                    file: {
                                        name: "这是一个图片",
                                        mimeType: "image/png",
                                        bytes: null,
                                        uri: null,
                                        size: null
                                    },
                                    part_sort: -1,
                                    metadata: {
                                        file_type: "image"
                                    }
                                };
                            case 'video':
                                return {
                                    type: "file",
                                    id: baseId,
                                    parent_id: null,
                                    created_time: currentTime,
                                    generated_by: "system",
                                    file: {
                                        name: "这是一个视频",
                                        mimeType: "video/mp4",
                                        bytes: null,
                                        uri: null,
                                        size: null
                                    },
                                    part_sort: -1,
                                    metadata: {
                                        file_type: "video"
                                    }
                                };
                            case 'audio':
                                return {
                                    type: "file",
                                    id: baseId,
                                    parent_id: null,
                                    created_time: currentTime,
                                    generated_by: "system",
                                    file: {
                                        name: "这是一个音频",
                                        mimeType: "audio/mpeg",
                                        bytes: null,
                                        uri: null,
                                        size: null
                                    },
                                    part_sort: -1,
                                    metadata: {
                                        file_type: "audio"
                                    }
                                };
                            case 'custom':
                                return {
                                    type: "data",
                                    id: baseId,
                                    parent_id: null,
                                    created_time: currentTime,
                                    generated_by: "system",
                                    data: {
                                        key1: "value1",
                                        key2: "value2",
                                        key3: "value3"
                                    },
                                    part_sort: -1,
                                    metadata: {
                                        data_type: "custom"
                                    }
                                };
                            default:
                                return {
                                    type: "data",
                                    id: baseId,
                                    parent_id: null,
                                    created_time: currentTime,
                                    generated_by: "system",
                                    data: {
                                        key1: "value1",
                                        key2: "value2",
                                        key3: "value3"
                                    },
                                    part_sort: -1,
                                    metadata: {
                                        data_type: "custom"
                                    }
                                };
                        }
                    }
                })
            },
            required: ["parts"]
        };

        // 调用标准化API
        const response = await axiosInstance.post(
            endpoints.timeSequenceCardContentSchemaUrl,
            requestBody,
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
        return response.data;
    } catch (error) {
        console.error('生成ContentSchema失败:', error);
        throw error;
    }
};

/**
 * 提交完整的时序卡片数据（包含基本信息和结构信息）
 * @param basicInfo 基本信息（可能包含content_schema）
 * @param structureItems 结构信息（当basicInfo不包含content_schema时使用）
 * @returns 创建或更新结果
 */
export const submitTimeSequenceCardData = async (basicInfo: any, structureItems: any[]) => {
    try {
        let contentSchema;

        // 如果basicInfo中已包含content_schema，直接使用
        if (basicInfo.content_schema) {
            contentSchema = basicInfo.content_schema;
        } else {
            // 否则根据structureItems构建content_schema
            const structureData = Array.isArray(structureItems) ? structureItems : [];

            // 构建要校验的JSON数据结构，符合parts格式
            contentSchema = {
                type: "object",
                properties: {
                    parts: structureData.map((item, index) => {
                        // 如果已有partData，直接使用；否则根据type生成默认结构
                        if (item.partData) {
                            return item.partData;
                        } else {
                            // 生成默认的part结构
                            const currentTime = new Date().toISOString();
                            // 使用item.id如果存在，否则生成统一格式的ID
                            const baseId = item.id || `${item.type}_${(index + 1).toString().padStart(3, '0')}`;
                            
                            // 根据组件类型生成对应的part结构
                            switch (item.type) {
                                case 'icon':
                                case 'title':
                                case 'text':
                                case 'tag':
                                case 'link':
                                    return {
                                        type: "text",
                                        id: baseId,
                                        parent_id: null,
                                        created_time: currentTime,
                                        generated_by: "system",
                                        text: `这是一个${item.name || item.type}`,
                                        part_sort: -1,
                                        metadata: {
                                            text_type: item.type
                                        }
                                    };
                                case 'file':
                                    return {
                                        type: "file",
                                        id: baseId,
                                        parent_id: null,
                                        created_time: currentTime,
                                        generated_by: "system",
                                        file: {
                                            name: "这是一个文件",
                                            mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                                            bytes: null,
                                            uri: null,
                                            size: null
                                        },
                                        part_sort: -1,
                                        metadata: {
                                            file_type: "document"
                                        }
                                    };
                                case 'image':
                                    return {
                                        type: "file",
                                        id: baseId,
                                        parent_id: null,
                                        created_time: currentTime,
                                        generated_by: "system",
                                        file: {
                                            name: "这是一个图片",
                                            mimeType: "image/png",
                                            bytes: null,
                                            uri: null,
                                            size: null
                                        },
                                        part_sort: -1,
                                        metadata: {
                                            file_type: "image"
                                        }
                                    };
                                case 'video':
                                    return {
                                        type: "file",
                                        id: baseId,
                                        parent_id: null,
                                        created_time: currentTime,
                                        generated_by: "system",
                                        file: {
                                            name: "这是一个视频",
                                            mimeType: "video/mp4",
                                            bytes: null,
                                            uri: null,
                                            size: null
                                        },
                                        part_sort: -1,
                                        metadata: {
                                            file_type: "video"
                                        }
                                    };
                                case 'audio':
                                    return {
                                        type: "file",
                                        id: baseId,
                                        parent_id: null,
                                        created_time: currentTime,
                                        generated_by: "system",
                                        file: {
                                            name: "这是一个音频",
                                            mimeType: "audio/mpeg",
                                            bytes: null,
                                            uri: null,
                                            size: null
                                        },
                                        part_sort: -1,
                                        metadata: {
                                            file_type: "audio"
                                        }
                                    };
                                case 'custom':
                                    return {
                                        type: "data",
                                        id: baseId,
                                        parent_id: null,
                                        created_time: currentTime,
                                        generated_by: "system",
                                        data: {
                                            key1: "value1",
                                            key2: "value2",
                                            key3: "value3"
                                        },
                                        part_sort: -1,
                                        metadata: {
                                            data_type: "custom"
                                        }
                                    };
                                default:
                                    return {
                                        type: "data",
                                        id: baseId,
                                        parent_id: null,
                                        created_time: currentTime,
                                        generated_by: "system",
                                        data: {
                                            key1: "value1",
                                            key2: "value2",
                                            key3: "value3"
                                        },
                                        part_sort: -1,
                                        metadata: {
                                            data_type: "custom"
                                        }
                                    };
                            }
                        }
                    })
                },
                required: ["parts"]
            };

            // 先进行JSON数据校验
            await validateTimeSequenceCardContentSchema(contentSchema);
        }

        // 校验通过后，组合成完整的提交数据
        const submitData: any = {
            name: basicInfo?.name || '时序卡片',
            display_name: basicInfo?.display_name || basicInfo?.name || '时序卡片',
            description: basicInfo?.description || '',
            license: basicInfo?.license || '',
            tags: basicInfo?.tags || [],
            version: basicInfo?.version,
            is_default: basicInfo?.is_default !== undefined ? basicInfo.is_default : false,
            is_enabled: basicInfo?.is_enabled !== undefined ? basicInfo.is_enabled : true,
            content_schema: contentSchema // 使用最终确定的content_schema
        };

        // 如果存在ID，添加到提交数据中
        if (basicInfo?.id) {
            submitData.id = basicInfo.id;
        }

        // 无论是创建还是更新操作，都使用同一个接口
        const response = await createTimeSequenceCardMetadata(submitData);
        return response;
    } catch (error) {
        console.error('提交时序卡片数据失败:', error);
        // 如果是校验错误，提供更友好的错误信息
        if (error.response?.status === 400) {
            throw new Error('时序卡片数据格式校验失败，请检查配置是否正确');
        }
        throw error;
    }
};

/**
 * 启用或禁用时序卡片
 * @param code 时序卡片代码标识
 * @param isEnabled 是否启用
 * @returns 操作结果
 */
export const toggleTimeSequenceCardEnabled = async (code: string, isEnabled: boolean) => {
    try {
        const url = `${endpoints.timeSequenceCardMetadataToggleUrl.replace('{code}', code).replace('{isEnabled}', isEnabled ? 'true' : 'false')}`;
        const response = await axiosInstance.patch(url);
        return response.data;
    } catch (error) {
        console.error(`${isEnabled ? '启用' : '禁用'}时序卡片失败:`, error);
        throw error;
    }
};

/**
 * 创建时序卡片副本
 * @param cardId 要复制的时序卡片ID
 * @returns 创建结果
 */
export const createTimeSequenceCardCopy = async (cardId: string) => {
    try {
        // 首先获取原始时序卡片的详细信息
        const originalCard = await getTimeSequenceCardMetadataDetail(cardId);
        
        // 复制所需字段并修改名称
        const copyData: TimeSequenceCardMetadata = {
            name: `${originalCard.name}(副本)`,
            display_name: originalCard.display_name || `${originalCard.name}(副本)`,
            description: originalCard.description || '',
            license: originalCard.license || '',
            tags: originalCard.tags,
            version: originalCard.version,
            is_enabled: originalCard.is_enabled,
            content_schema: originalCard.content_schema
        };
        
        // 调用创建API
        const response = await createTimeSequenceCardMetadata(copyData);
        return response;
    } catch (error) {
        console.error('创建时序卡片副本失败:', error);
        throw error;
    }
};

/**
 * 获取Content的JsonSchema示例
 * @returns JsonSchema示例数据
 */
export const getTimeSequenceCardContentExample = async () => {
    try {
        const response = await axiosInstance.get(endpoints.timeSequenceCardContentExampleUrl);
        return response.data;
    } catch (error) {
        console.error('获取Content的JsonSchema示例失败:', error);
        throw error;
    }
};

/**
 * 验证Content的JsonSchema
 * @param schema 要验证的JsonSchema对象
 * @returns 验证结果
 */
export const validateTimeSequenceCardContentSchema = async (schema: any) => {
    try {
        const response = await axiosInstance.post(
            endpoints.timeSequenceCardContentSchemaValidateUrl,
            schema,
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );
        return response.data;
    } catch (error) {
        console.error('验证Content的JsonSchema失败:', error);
        throw error;
    }
};
