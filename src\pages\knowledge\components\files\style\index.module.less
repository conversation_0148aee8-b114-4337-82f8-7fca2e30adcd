.titleSection {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .titleDom {
        margin: 0;
        margin-right: 16px;
    }

    .tabs {
        :global(.arco-tabs-header-nav::before) {
            display: none;
        }

        :global(.arco-tabs-header) {
            width: 100%;
        }

        :global(.arco-tabs-header-ink) {
            display: none;
        }

        :global(.arco-tabs-header-title) {
            padding: 0;
            font-weight: 600;
            font-size: 20px;
            color: RGBA(0, 0, 0, .35);
            margin-left: 0px !important;
            margin-right: 24px;

            &:hover {
                color: RGBA(0, 0, 0, .8);
                background: none !important;
            }
        }

        :global(.arco-tabs-header-title-active) {
            color: #333333;

            &:hover {
                color: #333333;
            }
        }

        :global(.arco-tabs-content) {
            padding-top: 24px;
        }
    }
}

.fileList {

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 16px;
        border-bottom: 1px solid RGBA(0, 0, 0, 0.08);

        :global(.arco-input-inner-wrapper) {
            padding: 8px;
            background-color: transparent;
            border-radius: 4px;

            ::placeholder {
                font-weight: 400;
                font-size: 14px;
                color: #595959;
            }

            :global(.arco-input) {
                padding-top: 0;
                padding-bottom: 0;
                padding-left: 8px;
            }
        }

        :global(.arco-select-size-default.arco-select-single .arco-select-view) {
            padding: 8px;
            height: auto;
            line-height: 24px;
            font-weight: 400;
            font-size: 14px;
            color: #595959;
            border-radius: 4px;
            background-color: transparent;
        }

        .addBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            background-color: #4d5ef3;
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
            border-radius: 8px;
            transition: all 0.3s;
            height: 40px;

            &:hover {
                background-color: #3144f1;
            }
        }

        .rowEndCenter {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .countAppText {
                font-size: 14px;
                font-weight: 400;
                color: RGBA(0, 0, 0, 0.35);
                margin-right: 16px;
            }

            .searchBox {
                width: 240px;
                height: 38px;
                border-radius: 8px;
                outline: none;
                border: 1px solid RGBA(0, 0, 0, 0.08);
                font-size: 14px;
                font-weight: 400;
                background-color: #ffffff;
                margin-right: 16px;
                transition: all 0.2s ease;

                &:hover {
                    background-color: RGBA(0, 0, 0, 0.02);
                }

                &:focus {
                    background-color: #ffffff;
                }

                :global(.arco-input-inner-wrapper) {
                    background-color: transparent;
                    border-radius: 8px;
                    height: 40px;
                    padding: 0 8px;
                }

                :global(.arco-input) {
                    height: 40px;
                    line-height: 40px;
                }

                input::placeholder {
                    color: RGBA(0, 0, 0, 0.25);
                }
            }

            .selectBox {
                height: 40px;
                width: 160px;
                border-radius: 8px;
                border: 1px solid RGBA(0, 0, 0, 0.08);
                transition: all 0.2s ease;

                &:hover {
                    background-color: RGBA(0, 0, 0, 0.02);
                }

                &:focus-within {
                    background-color: #ffffff;
                }

                :global(.arco-select-view) {
                    height: 40px;
                    width: 160px;
                    border-radius: 8px;
                    background-color: transparent;
                    display: flex;
                    align-items: center;
                }

                :global(.arco-select-view-value) {
                    color: RGBA(0, 0, 0, 0.8);
                    display: flex;
                    align-items: center;
                }

                :global(.arco-select-view-value[title='']) {
                    color: RGBA(0, 0, 0, 0.25);
                }
            }
        }

        .bar {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-top: 8px;
            gap: 8px;
            width: 100%;
            height: 40px;

            >div {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                height: 32px;
                background: linear-gradient(0deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.01)), #FFFFFF;
                border-radius: 8px;

                span {
                    display: inline-block;
                    width: 100%;
                    font-style: normal;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color: rgba(0, 0, 0, 0.32);
                }
            }

            .name {
                width: 32%;
                min-width: 200px;
                padding: 4px 12px 4px 24px;
            }

            .creator {
                width: 15%;
                min-width: 100px;
                padding: 4px 12px;
            }

            .creation_time {
                width: 15%;
                min-width: 120px;
                padding: 4px 12px;
            }

            .turnover_time {
                width: 15%;
                min-width: 120px;
                padding: 4px 12px;
            }

            .controls {
                width: 10%;
                min-width: 100px;
                padding: 4px 12px;
            }
        }
    }

    .tableContainer {
        border-radius: 4px;
        overflow: hidden;
        margin-top: 8px;

        .table {
            :global(.arco-table-container) {
                border: none;
            }

            :global(.arco-table-td) {
                padding: 16px 12px;
                border-bottom: 1px solid #ebebeb;
                color: #595959;
                font-size: 14px;
                font-weight: 400;
                border-left: none;
                background-color: #ffffff;

                &:first-child {
                    padding-left: 24px;
                }

                &:last-child {
                    padding-right: 24px;
                }
            }

            :global(.arco-table-tr) {
                &:hover {
                    td {
                        background-color: #fafafa;
                    }
                }
            }

            .fileCell {
                display: flex;
                align-items: center;
                gap: 16px;
                cursor: pointer;
                transition: all 0.3s ease;

                &:hover {
                    .fileName {
                        color: #4d5ef3;
                    }
                }

                .fileIcon {
                    width: 40px;
                    height: 40px;
                    border-radius: 4px;
                    flex-shrink: 0;

                    svg {
                        width: 40px;
                        height: 40px;
                    }
                }

                .fileName {
                    color: #333333;
                    font-size: 14px;
                    font-weight: 400;
                    flex: 1;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    transition: color 0.3s ease;
                }
            }

            .charactersText {
                color: #595959;
                font-size: 14px;
                font-weight: 400;
            }

            .creatorText {
                color: #595959;
                font-size: 14px;
                font-weight: 400;
            }

            .timeText {
                color: #595959;
                font-size: 14px;
                font-weight: 400;
            }

            .operations {
                display: flex;
                gap: 8px;

                .operationBtn {
                    padding: 4px 24px;
                    border: none;
                    cursor: pointer;
                    font-weight: 400;
                    font-size: 14px;
                    border-radius: 6px;
                    height: 32px;
                    line-height: 20px;
                }

                .viewBtn {
                    background-color: #eef8f4;
                    color: #36a978;

                    &:hover {
                        background-color: darken(#eef8f4, 5%);
                    }
                }

                .deleteBtn {
                    background-color: #D5494114;
                    color: #D54941;

                    &:hover {
                        background-color: darken(#fcf1f0, 5%);
                    }
                }

                .downloadBtn {
                    background-color: #f8f8ff;
                    color: #4d5ef3;

                    &:hover {
                        background-color: darken(#f8f8ff, 5%);
                    }
                }
            }

            :global(.arco-table-empty) {
                height: 100%;
            }

            .emptyContainer {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: calc(100vh - 300px);
                min-height: 400px;

                svg {
                    width: 120px;
                    height: 120px;
                    margin-bottom: 16px;
                }

                .emptyText {
                    font-size: 14px;
                    color: RGBA(0, 0, 0, 0.45);
                    line-height: 22px;
                }
            }
        }
    }
}

.settingsSection {
    width: calc(51% - 24px);
    height: calc(100vh - 200px);
    padding-right: 24px;
    position: relative;

    .subtitle {
        font-size: 14px;
        font-weight: 600;
        color: RGBA(0, 0, 0, .65);

        // &.required::after {
        //     content: '*';
        //     color: #f53f3f;
        //     margin-left: 4px;
        // }
    }

    .subtitlePlaceholder {
        font-size: 12px;
        font-weight: 400;
        color: RGBA(0, 0, 0, .35);
    }

    .iconContainer {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;

        .iconKnowledge {
            width: 72px;
            height: 72px;
            border-radius: 4px;
            flex-shrink: 0;

            svg {
                width: 72px;
                height: 72px;
            }
        }

        .divider {
            width: 1px;
            height: 72px;
            background-color: rgba(0, 0, 0, 0.08);
            margin: 0 24px;
            flex-shrink: 0;
        }

        .nameContainer {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;

            :global(.arco-form-item) {
                width: 100%;
                margin-bottom: 0px;

                :global(.arco-form-item-control-children) {
                    width: 100%;
                }

                :global(.arco-input) {
                    width: 100%;
                }
            }
        }
    }

    .labelContainer {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        width: 50%;
        border-radius: 8px;
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
        margin-top: 8px;


        .selectedItemList {
            box-sizing: border-box;
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 8px;
            border-radius: 4px;
            min-height: 48px;
            background-color: RGBA(0, 0, 0, 0.01);
            transition: all 0.3s ease;
            border: 1px solid RGBA(0, 0, 0, .08);
            border-radius: 8px;

            &:hover {
                border-color: RGBA(0, 0, 0, 0.15);
            }

            .selectedItemRow {
                width: 100%;
                display: flex;
                align-items: center;
                border-radius: 8px;

                .selectedItemCol {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    width: 100%;

                    :global(.arco-input-inner-wrapper) {
                        height: 40px;
                        border-radius: 8px;
                        background-color: #fff;
                        border: 1px solid RGBA(0, 0, 0, .08);
                    }

                    .deleteIcon {
                        width: 24px;
                        height: 24px;
                        cursor: pointer;
                        opacity: 0.65;
                        transition: opacity 0.2s ease;

                        &:hover {
                            opacity: 1;
                        }
                    }
                }
            }
        }
    }

    .titleRow {
        margin-top: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        width: 100%;

        .titleContent {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .switchContainer {
            display: flex;
            align-items: center;
            gap: 8px;
            height: 40px;
            width: 76px;
            justify-content: center;
            border-radius: 8px;
            background-color: transparent;
            padding: 0 2px;

            :global(.arco-switch) {
                min-width: 40px;
                height: 24px;
            }

            :global(.arco-switch-checked) {
                background-color: RGBA(13, 41, 254, 0.95) !important;
            }
        }

        .addLabelBut {
            width: 76px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid RGBA(0, 0, 0, 0.08);
            border-radius: 8px;
            cursor: pointer;
            background-color: transparent;
            padding: 0 2px;

            :global(.arco-icon) {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                flex-shrink: 0;
            }

            .operateText {
                font-size: 14px;
                font-weight: 500;
                line-height: 20px;
                display: flex;
                align-items: center;
                height: 100%;
                color: #00000052;
            }

            &:hover {
                background-color: RGBA(0, 0, 0, 0.02);
            }
        }
    }

    .operateButGroup {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
        margin-top: 16px;
        margin-bottom: 16px;

        .text {
            font-size: 14px;
            font-weight: 600;
        }

        .but {
            border-radius: 8px;
            width: 76px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .cancelBut {
            background-color: #FFFFFF;
            border: 1px solid #00000014;

            &:hover {
                background-color: RGBA(245, 245, 245, 1);
            }

            .text {
                color: RGBA(0, 0, 0, .65);
            }
        }

        .createBut {
            background-color: RGBA(13, 41, 254, .95);

            &:hover {
                background-color: RGBA(13, 41, 254, 1);
                box-shadow: 0 4px 8px rgba(13, 41, 254, 0.2);
            }

            .text {
                color: white;
            }

            &:disabled {
                opacity: 0.32;
            }
        }
    }
}

.loading {
    text-align: center;
    padding: 16px 0;
    color: #86909c;
}

.noMore {
    text-align: center;
    padding: 16px 0;
    color: #86909c;
    font-size: 14px;
}

/* 文件上传弹窗样式 */
.uploadModal {
    border-radius: 8px;
    height: 700px;
    width: 700px;
    position: relative;
    overflow: hidden;

    :global(.arco-modal-header) {
        padding: 24px;
        border-bottom: 1px solid #ebebeb;
        text-align: left;
        height: 73px;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            color: #333333;
            text-align: left;
        }
    }

    :global(.arco-modal-content) {
        padding: 24px;
        height: calc(100% - 145px);
        overflow: auto;
    }

    :global(.arco-modal-footer) {
        padding: 16px 24px;
        border-top: 1px solid #ebebeb;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    :global(.arco-form-item-label) {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
    }

    :global(.arco-textarea) {
        padding: 12px;
        border-radius: 4px;
        border: 1px solid #ebebeb;
        transition: all 0.3s;
        min-height: 120px;
        font-size: 14px;

        &:hover,
        &:focus {
            border-color: #4d5ef3;
            box-shadow: 0 0 0 2px rgba(77, 94, 243, 0.1);
        }
    }

    .title {
        font-size: 18px;
        color: RGBA(0, 0, 0, 0.8);
        margin: 0;
    }

    .uploadContainer {
        margin-top: 24px;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 592px;

        :global(.arco-upload-drag) {
            width: 100%;
            height: 224px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            background: RGBA(0, 0, 0, 0.02);
            transition: all 0.3s;

            &:hover {
                background-color: RGBA(0, 0, 0, 0.02);
            }
        }

        .uploadContent {
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .uploadTitle {
                font-size: 16px;
                font-weight: 600;
                color: rgb(68, 85, 242);
                margin-bottom: 4px;

                .uploadIcon {
                    width: 24px;
                    height: 24px;
                }
            }
        }

    }

    .inputQuestionTextArea {
        border-radius: 8px;
        resize: none;
        background-color: #fff;
    }

    .inputTextArea {
        border-radius: 8px;
        resize: none;
        height: 100%;
        min-height: 300px;
        background-color: #fff;
        display: flex;
        flex-direction: column;

        :global(.arco-textarea) {
            flex: 1;
            height: 100%;
        }
    }

    .textCounter {
        text-align: right;
        color: #a6a6a6;
        font-size: 12px;
        margin-top: 4px;
    }

    .submitBtn {
        background-color: #4d5ef3;
        border-color: #4d5ef3;
        border-radius: 8px;
        width: 74px;
        height: 40px;
    }

    .cancelBtn {
        border-color: #ebebeb;
        color: #595959;
        border-radius: 8px;
        width: 74px;
        height: 40px;
    }
}

.deleteModal {
    width: 400px !important;
    border-radius: 8px;
    height: 180px;

    :global(.arco-modal-header) {
        padding: 24px 24px 16px 24px;
        border-bottom: 0;
        text-align: left;
        height: 73px;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            color: #333333;
            text-align: left;
        }
    }

    :global(.arco-modal-content) {
        padding: 0 24px;
        font-size: 14px;
        color: #595959;
        line-height: 22px;
        display: flex;

        p {
            margin: 0;
            font-size: 14px;
            color: #595959;
            line-height: 22px;
        }
    }

    :global(.arco-modal-footer) {
        padding: 16px 24px;
        border-top: 0;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        height: 73px;
    }

    .submitBtn {
        background-color: #f53f3f;
        border-color: #f53f3f;
        border-radius: 8px;
        width: 74px;
        height: 40px;

        &:hover {
            background-color: #f76965;
            border-color: #f76965;
        }
    }

    .cancelBtn {
        border-color: #ebebeb;
        color: #595959;
        border-radius: 8px;
        width: 74px;
        height: 40px;

        &:hover {
            background-color: #f5f5f5;
        }
    }
}

.backToTop {
    position: fixed;
    left: 55%;
    bottom: 40px;
    width: 130px;
    transform: translateX(-50%);
    height: 38px;
    border-radius: 24px;
    background: #fff;
    border: 1px solid RGBA(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    z-index: 9999;
    gap: 8px;

    span {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.64);
        font-weight: 500;
    }

    &:hover {
        background: #f2f3f5;
    }

    &.hidden {
        opacity: 0;
        pointer-events: none;
    }
}

.uploadSubtitle{
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0%;
    text-align: center;
    color: #00000052;
}
