import React, { useState } from 'react';
import {
    Card,
    Typography,
    Button,
    Avatar,
    Space,
    Table,
    Input,
    Modal,
    Form,
    Select,
    Spin,
} from '@arco-design/web-react';
import IconSettings from '@/assets/enterprise/IconSettings.svg';
import IconEnterprise from '@/assets/enterprise/EnterpriseIcon.svg';
import IconSearch from '@/assets/enterprise/IconSearch.svg';
import IconClose from '@/assets/enterprise/IconClose.svg';
import IconBuildingEnterprise from '@/assets/IconBuilding.svg';
// import IconEmptyEnterprise from '@/assets/enterprise/IconEmpty.svg';
import styles from './style/index.module.less';

const { Title, Text } = Typography;
const { Option } = Select;
const FormItem = Form.Item;

function EnterpriseOverview() {
    const [inviteModalVisible, setInviteModalVisible] = useState(false);
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [building, setBuilding] = useState(true); // 新增建设中状态
    const [searchValue, setSearchValue] = useState('');

    // 成员数据
    // const members = [
    //     {
    //         key: '1',
    //         name: '杨鹏飞',
    //         email: '<EMAIL>',
    //         joinTime: '2024/10/25',
    //         role: '管理员',
    //     },
    //     {
    //         key: '2',
    //         name: 'Ai4c默认用户',
    //         email: '<EMAIL>',
    //         joinTime: '2024/10/25',
    //         role: '管理员',
    //     },
    //     {
    //         key: '3',
    //         name: 'Ai4c默认用户',
    //         email: '<EMAIL>',
    //         joinTime: '2024/10/25',
    //         role: '成员',
    //     },
    // ];
    const members = [];

    // 成员列表列定义
    const columns = [
        {
            title: '成员',
            dataIndex: 'name',
            render: (text) => (
                <Space>
                    <Avatar size={32} />
                    <Text className={styles.indexName}>{text}</Text>
                </Space>
            ),
        },
        {
            title: '邮箱',
            dataIndex: 'email',
            render: (text) => <Text className={styles.indexEmail}>{text}</Text>,
        },
        {
            title: '加入时间',
            dataIndex: 'joinTime',
            render: (text) => <Text className={styles.joinTime}>{text}</Text>,
        },
        {
            title: '权限',
            dataIndex: 'role',
            render: (text) => <Text className={styles.indexRole}>{text}</Text>,
        },
    ];

    // 打开邀请用户弹窗
    const showInviteModal = () => {
        setInviteModalVisible(true);
    };

    // 关闭邀请用户弹窗
    const hideInviteModal = () => {
        setInviteModalVisible(false);
        form.resetFields();
    };

    // 确认邀请
    const handleInviteConfirm = () => {
        form
            .validate()
            .then((values) => {
                console.log('邀请用户:', values);
                // 这里可以处理邀请逻辑
                hideInviteModal();
            })
            .catch((errors) => {
                console.log('表单验证失败:', errors);
            });
    };

    // 渲染内容
    const renderContent = () => {
        if (loading && members.length === 0) {
            return (
                <div className={styles.loadingContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ padding: 24 }}>
                            <Spin tip="加载中..." />
                        </div>
                    </Space>
                </div>
            );
        }

        // 建设中状态
        if (building && members.length === 0 && !searchValue) {
            return (
                <div className={styles.buildingContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <IconBuildingEnterprise style={{ width: 80, height: 80 }} />
                        <Text className={styles.buildingText}>
                            努力建设中，马上就来~
                        </Text>
                    </Space>
                </div>
            );
        }

        // 搜索无结果状态
        if (members.length === 0) {
            return (
                <div className={styles.emptyContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        {/* <IconEmptyEnterprise style={{ width: 80, height: 80 }} /> */}
                        <Text className={styles.emptyText}>
                            {searchValue ? '未找到匹配的成员' : '暂无成员'}
                        </Text>
                    </Space>
                </div>
            );
        }

        return (
            <Table
                columns={columns}
                data={members}
                pagination={false}
                border={false}
                className={styles.MembersTable}
            />
        );
    };

    return (
        <div className={styles.container}>
            {/* 组织详情头部 */}
            <div className={styles.header} style={{marginBottom: 8}}>
                <Text style={{ fontSize: 20, fontWeight: 600, color: '#333333'}}>组织详情</Text>
            </div>

            {/* 成员列表 */}
            <div className={styles.memberListCard}>
                {renderContent()}
            </div>

            {/* 邀请用户弹窗 */}
            {/* <Modal
                title="邀请用户"
                visible={inviteModalVisible}
                closeIcon={<IconClose />}
                onCancel={hideInviteModal}
                className={styles.inviteModal}
            >
                <div style={{ marginBottom: 24 }}>
                    <Text type="secondary" className={styles.inviteTag}>
                        我们将会给他/她发送邮件，请确保邮箱是否准确
                    </Text>
                </div>
                <Form form={form} layout="vertical">
                    <FormItem label="邮箱" field="email">
                        <Input placeholder=".com" />
                    </FormItem>
                    <FormItem label="权限" field="role" initialValue="成员">
                        <Select style={{ width: 160 }}>
                            <Option value="成员">成员</Option>
                            <Option value="管理员">管理员</Option>
                        </Select>
                    </FormItem>
                </Form>
                <Space
                    size={'small'}
                    style={{ display: 'flex', justifyContent: 'flex-end' }}
                >
                    <Button onClick={hideInviteModal} className={styles.cancelButton}>
                        取消
                    </Button>
                    <Button
                        type="primary"
                        onClick={handleInviteConfirm}
                        className={styles.confirmButton}
                    >
                        确定
                    </Button>
                </Space>
            </Modal> */}
        </div>
    );
}

export default EnterpriseOverview; 