import Keycloak from 'keycloak-js';
import { useState, useCallback, useEffect } from 'react';
import { getUserStore, userStore } from '@/lib/helpers/store';

const createStore = (initialValue) => {
    let value = initialValue;
    const subscribers = new Set();

    return {
        subscribe: (callback) => {
            subscribers.add(callback);
            callback(value);
            return () => subscribers.delete(callback);
        },
        set: (newValue) => {
            value = newValue;
            // subscribers.forEach(callback => useCallback(value));
        },
        get: () => value
    };
};

// 创建响应式状态存储
export const isAuthenticated = createStore(false);
export const userProfile = createStore({});

export let keycloakInstance;

/**
 * 将UTC时间转换为中国时区时间字符串
 * @param {number} timestamp - 毫秒级时间戳
 * @returns {string} 格式化的中国时区时间字符串
 */
function formatDateToChinaTimezone(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', { 
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    });
}

export function initializeKeycloak(config, autoCheckSSO = true) {
    keycloakInstance = new Keycloak({
        url: config.Authority,
        realm: config.Realm,
        clientId: config.ClientId,
    });

    // 根据参数决定是否自动检查SSO
    const initOptions = autoCheckSSO
        ? {
            onLoad: 'check-sso',
            checkLoginIframe: true,
            // 明确指定response_type以获取id_token
            responseType: 'code id_token token',
            scope: 'openid profile email organization'
        }
        : {
            onLoad: 'none',
            checkLoginIframe: false,
            // 即使不自动检查SSO，也指定response_type
            responseType: 'code id_token token',
            scope: 'openid profile email organization'
        };

    keycloakInstance.init(initOptions)
        .then((authenticated) => {
            // 保留原有的状态设置逻辑
            isAuthenticated.set(authenticated);

            if (authenticated) {
                // 获取并更新用户 store
                const user = getUserStore();
                user.token = keycloakInstance.token;

                // 计算并设置过期时间（毫秒时间戳）
                if (keycloakInstance.tokenParsed && keycloakInstance.tokenParsed.exp) {
                    user.expires = keycloakInstance.tokenParsed.exp * 1000; // 转换为毫秒时间戳
                    console.log("Token 过期时间设置为:", formatDateToChinaTimezone(user.expires));
                }

                // 保存idToken到localStorage，确保即使刷新页面也能使用
                if (keycloakInstance.idToken) {
                    user.idToken = keycloakInstance.idToken;
                    console.log("已获取并保存idToken");
                } else {
                    console.warn("认证成功但未获取idToken");
                }

                userStore.set(user);

                //如果用户已认证，加载用户信息
                keycloakInstance.loadUserProfile().then((profile) => {
                    console.log(profile);
                    userProfile.set(profile);
                    console.log('User profile loaded');
                    localStorage.setItem('userStatus', 'login');
                    window.location.href = '/';
                });
            }
        })
        .catch((err) => console.error("Keycloak init error:", err));

    // Token 过期处理逻辑保持不变
    keycloakInstance.onTokenExpired = () => {
        //尝试刷新Token
        keycloakInstance.updateToken(90)
            .then((refreshed) => {
                if (refreshed) {
                    console.log('Token successfully refreshed');
                    // 更新用户 store 中的 token
                    const user = getUserStore();
                    user.token = keycloakInstance.token;

                    // 更新过期时间
                    if (keycloakInstance.tokenParsed && keycloakInstance.tokenParsed.exp) {
                        user.expires = keycloakInstance.tokenParsed.exp * 1000; // 转换为毫秒时间戳
                        console.log("更新后的 Token 过期时间:", formatDateToChinaTimezone(user.expires));
                    }

                    // 刷新时也更新idToken
                    if (keycloakInstance.idToken) {
                        user.idToken = keycloakInstance.idToken;
                    }

                    userStore.set(user);
                } else {
                    console.warn('Token not refreshed, valid for existing duration');
                }
            })
            .catch((err) => {
                console.error('Failed to refresh token', err);
            });
    };
}

export function triggerLogin() {
    if (keycloakInstance) {
        keycloakInstance.login().catch((err) => {
            console.error("Keycloak login error:", err);
        });
    }
}

/**
 * 登出并清除用户状态
 * @param {string} redirectUri 登出后重定向的URL
 */
export function triggerLogout(redirectUri, config) {
    if (typeof window !== 'undefined') {

        // 如果keycloakInstance不存在，先创建并初始化
        if (!keycloakInstance && config) {
            console.log("初始化新的Keycloak实例用于登出");
            keycloakInstance = new Keycloak({
                url: config.Authority,
                realm: config.Realm,
                clientId: config.ClientId,
            });

            // 从存储中恢复用户信息
            const user = getUserStore();

            // 使用高级初始化选项
            const initOptions: any = {
                onLoad: 'check-sso',
                checkLoginIframe: true,
                // 明确指定response_type以获取id_token
                responseType: 'code id_token token',
                scope: 'openid profile email organization'
            }
            keycloakInstance.init(initOptions)

            if (user && user.token) {
                console.log("使用存储的token初始化Keycloak");
                initOptions.token = user.token;
                initOptions.idToken = user.idToken;
            }

            // 调用Keycloak登出
            if (keycloakInstance) {

                console.log(redirectUri)
                // keycloakInstance.logout({ redirectUri: redirectUri })
                //     .then(() => {
                //         // window.location.href = '/login';
                //     })
                //     .catch((err) => {
                //         console.error("Keycloak logout error:", err);
                //     });
                let logoutUrl = keycloakInstance.createLogoutUrl({ redirectUri: redirectUri })
                logoutUrl = logoutUrl + "&id_token_hint=" + user.idToken



                keycloakInstance.clearToken();
                // 设置登出标志，以便登录页面知道用户刚刚登出
                localStorage.setItem('userJustLoggedOut', 'true');
                // 清除本地存储中的用户信息
                localStorage.removeItem('user');
                localStorage.removeItem('userStatus');
                // 重置状态
                isAuthenticated.set(false);
                userProfile.set({});
                // 重置用户store，确保过期时间也被清零
                userStore.set({ id: "", full_name: "", expires: 0, token: null, idToken: null });
                window.location.href = logoutUrl
            }
            else {
                console.warn("Keycloak instance not initialized, redirecting manually");
                window.location.href = redirectUri || '/login';
            }
        }
    }
}

/**
 * 检查用户令牌是否已过期
 * @returns {boolean} 如果令牌已过期或不存在则返回true，否则返回false
 */
export function isTokenExpired() {
    const user = getUserStore();
    
    // 如果没有token或过期时间，则认为已过期
    if (!user || !user.token || !user.expires) {
        return true;
    }
    
    const now = Date.now();
    // 如果当前时间大于过期时间，则令牌已过期
    const isExpired = now >= user.expires;
    
    if (isExpired) {
        console.log("令牌已过期，过期时间为:", formatDateToChinaTimezone(user.expires));
        console.log("当前时间:", formatDateToChinaTimezone(now));
    }
    
    return isExpired;
}

export const useKeycloakAuth = () => {
    const [authenticated, setAuthenticated] = useState(false);
    const [profile, setProfile] = useState({});

    useEffect(() => {
        const unsubscribeAuth = isAuthenticated.subscribe(setAuthenticated);
        const unsubscribeProfile = userProfile.subscribe(setProfile);

        return () => {
            unsubscribeAuth();
            unsubscribeProfile();
        };
    }, []);

    return {
        isAuthenticated: authenticated,
        userProfile: profile,
        login: triggerLogin
    };
};