import axiosInstance from './interceptors';
import { endpoints } from './api-endpoints';

// 定义应用列表请求参数接口
export interface AgentListParams {
  Pager?: {
    Page: number;
    Size: number;
  };
  agent_name?: string;
  label?: string;
  applicationId?: string;
  type?: string;
}

export interface AgentResponse {
  agent_id: string;
  id: string;
  name: string;
  description: string;
  type: string;
  instruction: string;
  channel_instructions: any[];
  templates: any[];
  functions: any[];
  responses: any[];
  samples: any[];
  merge_utility: boolean;
  utilities: any[];
  knowledge_bases: any[];
  rules: any[];
  is_public: boolean;
  is_host: boolean;
  is_editable: boolean;
  disabled: boolean;
  icon_url: string | null;
  profiles: string[];
  routing_rules: any[];
  llm_config: {
    is_inherit: boolean;
    provider: string;
    model: string;
    max_recursion_depth: number;
    max_tokens: number | null;
    temperature: number | null;
  };
  plugin: {
    id: string;
    name: string;
    description: string;
    assembly: string;
    is_core: boolean;
    icon_url: string | null;
    agent_ids: string[];
    module: string | null;
    enabled: boolean;
  };
  workflowId: string;
  actions: any | null;
  applicationId: string;
  inputVariables: any[];
  outputVariables: any[];
  created_time: string;
  updated_time: string;
  labels: string[];
  acp_tools: any[];
  ts_cards: string[];
}

// 定义应用列表响应数据接口
export interface AgentListResponse {
  items: Array<AgentResponse>;
  count: number;
}

/**
 * 获取智能体列表
 * @param params 查询参数
 * @returns Promise<AgentListResponse>
 */
export async function getAgentList(
  params: AgentListParams = {}
): Promise<AgentListResponse> {
  try {
    const response = await axiosInstance.get(endpoints.agentListUrl, {
      params: {
        'Pager.Page': params.Pager?.Page || 1,
        'Pager.Size': params.Pager?.Size || 16,
        agentName: params.agent_name || '',
        labels: params.label || undefined,
        applicationId: params.applicationId || '',
        type: params.type || '',
      },
    });
    return response.data;
  } catch (error) {
    console.error('获取智能体列表失败:', error);
    throw error;
  }
}

/**
 * 创建智能体
 * @param data 智能体数据
 */
export async function createAgent(agentData: AgentResponse): Promise<AgentResponse> {
  try {
    const response = await axiosInstance.post(endpoints.agentUrl, agentData);
    return response.data;
  } catch (error) {
    console.error('创建智能体失败:', error);
    throw error;
  }
}

/**
 * 更新智能体
 * @param id 智能体ID
 * @param data 更新数据
 */
export async function updateAgent(
  id: string,
  agentData: AgentResponse
) {
  return axiosInstance.put(`${endpoints.agentUrl}/${id}`, {
    name: agentData.name,
    description: agentData.description,
    type: agentData.type,
    instruction: agentData.instruction,
    templates: agentData.templates,
    functions: agentData.functions,
    responses: agentData.responses,
    samples: agentData.samples,
    utilities: agentData.utilities,
    knowledge_bases: agentData.knowledge_bases,
    rules: agentData.rules,
    routing_rules: agentData.routing_rules,
    is_public: agentData.is_public,
    disabled: agentData.disabled,
    icon_url: agentData.icon_url,
    profiles: agentData.profiles,
    llm_config: {
      is_inherit: agentData.llm_config.is_inherit,
      provider: agentData.llm_config.provider,
      model: agentData.llm_config.model,
      max_recursion_depth: agentData.llm_config.max_recursion_depth,
      max_tokens: agentData.llm_config.max_tokens,
      temperature: agentData.llm_config.temperature,
    },
    applicationId: agentData.applicationId,
    labels: agentData.labels,
    ts_cards: agentData.ts_cards,
    acp_tools: agentData.acp_tools,
  });
}

/**
 * 删除智能体
 * @param id 智能体ID
 */
export async function deleteAgent(id: string) {
  return axiosInstance.delete(`${endpoints.agentUrl}/${id}`);
}

/**
 * 获取智能体详情
 * @param id 智能体ID
 */
export async function getAgentDetail(id: string) {
  return axiosInstance.get(`${endpoints.agentUrl}/${id}`);
}

/**
 * 发布智能体
 * @param id 智能体ID
 */
export async function publishAgent(id: string) {
  return axiosInstance.post(`${endpoints.agentUrl}/${id}/publish`);
}

/**
 * 取消发布智能体
 * @param id 智能体ID
 */
export async function unpublishAgent(id: string) {
  return axiosInstance.post(`${endpoints.agentUrl}/${id}/unpublish`);
}

/**
 * 获取智能体标签列表
 */
export async function getAgentLabelOptions() {
  return axiosInstance.get(endpoints.agentLabelOptionsUrl);
}

/**
 * 智能体AI助手
 */
export async function AIPrompt(data: {
  agentId?: string;
  requirements: string;
}) {
  return axiosInstance.post(endpoints.agentAIPrompt, data);
}

export interface AcpToolsReq {
  acpTools: {
    name: string;
    description: string;
    server_id: string;
    disabled: boolean;
    functions: {
      name: string;
    }[];
  }[]
}

/**
 * 绑定Acp工具
 */
export async function bindAcpTools(agentId: string, acpTools: AcpToolsReq) {
  return axiosInstance.post(endpoints.agentAcpToolsUrl.replace('{agentId}', agentId), acpTools);
}

/**
 * 检查智能体名称是否存在
 * @param name 智能体名称
 */
export async function checkAgentName(name: string) {
  return axiosInstance.get(endpoints.agentNameCheckUrl, {
    params: {
      name: name
    }
  });
}