.secretKey {
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .addKeyBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 16px 24px;
            background-color: #4455f2;
            font-weight: 600;
            font-size: 14px;
            color: #ffffff;
            border-radius: 8px;
            transition: all 0.3s;
            height: 40px;

            &:hover {
                background-color: #4152e9;
            }
        }

        .keyNumber {
            color: #adadad;
            font-size: 14px;
            font-weight: 400;
            white-space: nowrap;
        }

        //搜索输入框
        :global(.arco-input-inner-wrapper) {
            padding: 8px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            ::placeholder {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                color: #d6d6d6;
            }

            :global(.arco-input) {
                padding-top: 0;
                padding-bottom: 0;
                padding-left: 8px;
            }
        }

        //筛选select
        :global(.arco-select-size-default.arco-select-single .arco-select-view) {
            padding: 8px;
            height: auto;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            background-color: #ffffff;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            :global(.arco-select-prefix) {
                margin-right: 4px;
            }

            :global(.arco-select-view-input) {
                &::placeholder {
                    color: #d6d6d6;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                }
            }
        }
    }

    .tableContainer {
        // border: 1px solid #ebebeb;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
        min-height: 200px;

        .table {
            :global(.arco-table-container) {
                border: none;
            }

            :global(.arco-table-container::before) {
                display: none;
            }

            :global(.arco-table-th) {
                background-color: #ffffff !important;
                border-bottom: none;
                font-weight: 600;
                font-size: 16px;
                color: #5c5c5c;
                border-left: none;
            }

            :global(.arco-table-th-item) {
                padding: 4px 24px;
                border-radius: 8px;
                background-color: #fcfcfc;
                margin-right: 8px;

                &:not(:last-child) {
                    margin-right: 1px;
                }

                :global(.arco-table-th-item-title) {
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 24px;
                    color: #adadad;
                }
            }

            :global(.arco-table-header) {
                position: sticky;
                top: 0;
                z-index: 1;
                margin-bottom: 8px;
            }

            :global(.arco-table-td) {
                padding: 16px;
                border-top: 1px solid #f5f5f5;
                border-bottom: none;
                color: #5c5c5c;
                font-size: 14px;
                font-weight: 400;
                border-left: none;
            }

            .indexCell {
                color: #a6a6a6;
                font-size: 14px;
                font-weight: 400;
            }

            .keyCell {
                display: flex;
                align-items: center;
                gap: 16px;

                .keyIcon {
                    width: 40px;
                    height: 40px;
                    border-radius: 4px;

                    svg {
                        width: 40px;
                        height: 40px;
                    }
                }

                .keyText {
                    color: #5c5c5c;
                    font-weight: 600;
                    font-size: 15px;
                    line-height: 24px;
                    cursor: pointer;
                    transition: all 0.3s;

                    &:hover {
                        color: #4b5cf2;
                    }
                }
            }

            .timeText {
                color: #a6a6a6;
                font-size: 14px;
                font-weight: 400;
            }

            .viewBtn,
            .deleteBtn {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 6px 16px;
                border-radius: 4px;
                font-weight: 400;
                font-size: 14px;
            }

            .viewBtn {
                background-color: #eef8f4;
                color: #2ba471;
            }

            .deleteBtn {
                background-color: #fcf1f0;
                color: #d54941;
            }
        }
        
        // 添加空状态容器样式
        .emptyContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #ffffff;
            padding: 40px 0;
            
            :global(.arco-space) {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            :global(.arco-typography) {
                font-weight: 500;
                font-size: 14px;
                line-height: 24px;
                color: #5c5c5c;
                text-align: center;
            }
        }
        
        // 添加加载状态容器样式
        .loadingContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #ffffff;
            padding: 40px 0;
            
            :global(.arco-spin) {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
        }
        
        // 添加错误状态容器样式
        .errorContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #ffffff;
            padding: 40px 0;
            
            :global(.arco-typography) {
                color: #d54941;
                font-size: 14px;
                text-align: center;
            }
        }
    }
}

.confirmDeleteModal {
    position: relative;
    padding: 24px;
    width: 480px;
    border-radius: 16px;

    .keyDetailIcon {
        width: 80px;
        height: 80px;

        svg {
            width: 100%;
            height: 100%;
        }
    }

    .modalContent {
        display: flex;
        flex-direction: column;

        .modalContentText {
            font-weight: 400;
            font-size: 14px;
            color: #5c5c5c;
        }
    }

    .modalFooter {
        margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .cancelDeleteBtn,
        .confirmDeleteBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 18px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s;
        }

        .cancelDeleteBtn {
            background-color: #ffffff;
            border: 1px solid #ebebeb;
            color: #5c5c5c;

            &:hover {
                background-color: #fafafa !important;
                border-color: #f5f5f5 !important;
            }
        }

        .confirmDeleteBtn {
            background-color: #d54941;
            color: #ffffff;

            &:hover {
                background-color: #cd463e;
                color: #ffffff;
            }
        }
    }

    :global(.arco-modal-header) {
        padding: 0 !important;
        height: auto;
        border-bottom: none !important;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            color: #333333;
            margin-bottom: 16px;
            text-align: left;
        }
    }

    :global(.arco-modal-content) {
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 24px;
        top: 24px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }
}

.secretDetailModal {
    position: relative;
    padding: 24px;
    width: 640px;
    border-radius: 16px;

    .modalContent {
        display: flex;
        flex-direction: column;
    }

    :global(.arco-select-size-default.arco-select-single .arco-select-view) {
        padding: 8px;
        height: auto;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #d6d6d6;
        border-radius: 8px;
        border: 1px solid #ebebeb;
        background-color: #ffffff;
        transition: all 0.2s;

        &:hover {
            background-color: #fafafa;
            border-color: #ebebeb;
        }

        :global(.arco-select-prefix) {
            margin-right: 4px;
        }

        :global(.arco-select-view-input) {
            &::placeholder {
                color: #d6d6d6;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
            }
        }
    }

    :global(.arco-select-disabled .arco-select-view) {
        background-color: #fcfcfc !important;
    }

    .keyHeader {
        display: flex;
        margin-bottom: 24px;
        width: 100%;

        .iconAndName {
            display: flex;
            align-items: center;
            width: 100%;

            .keyIconWrapper {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 72px;
                height: 72px;
                border-radius: 8px;
                background-color: #f5f5f5;
                flex-shrink: 0;

                .keyDetailIcon {
                    width: 72px;
                    height: 72px;
                }
            }

            .divider {
                width: 1px;
                height: 72px;
                background-color: #f5f5f5;
                margin: 0 24px;
            }

            .nameFormContainer {
                flex: 1;

                .nameFormItem {
                    margin-bottom: 0;
                }
            }
        }
    }

    .modalFooter {
        // margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .cancelBtn,
        .saveBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s;
            height: 40px;
        }

        .cancelBtn {
            background-color: #ffffff;
            border: 1px solid #ebebeb;
            color: #5c5c5c;
            margin-right: 8px;

            &:hover {
                background-color: #fafafa !important;
                border-color: #f5f5f5 !important;
            }
        }

        .saveBtn {
            background-color: #4b5cf2;
            color: #ffffff;

            &:hover {
                background-color: #4152e9 !important;
                color: #ffffff !important;
            }
        }

        .saveBtnDisabled {
            background-color: #c3c8fa !important;
            color: #ffffff !important;
            cursor: not-allowed;

            &:hover {
                background-color: #c3c8fa !important;
            }
        }
    }

    .required {
        font-weight: 400;
        font-size: 12px;
        color: #4b5cf2;
    }

    .requiredIcon {
        color: #4455f2;
        font-size: 15px;
        line-height: 16px;
        margin-left: 4px;
        position: relative;
        top: -1px;
        font-weight: bold;
    }

    :global(.arco-form-item) {
        margin-bottom: 24px;

        // 隐藏 Arco Design 默认的必填标识
        :global(.arco-form-item-symbol) {
            display: none;
        }

        // 始终禁用的输入框（如创建时间和更新时间）
        &:has(input[disabled]) {
            .arco-input-inner-wrapper {
                background-color: #fcfcfc;

                .arco-input {
                    background-color: #fcfcfc;
                    color: #666666;
                }
            }
        }
    }

    :global(.arco-space-item) {
        margin-right: 0 !important;
    }

    // 更通用的方法，直接为禁用输入框添加样式
    :global(.arco-input-disabled) {
        background-color: #fcfcfc !important;
        color: #666666 !important;
    }

    :global(.arco-input-inner-wrapper-disabled) {
        background-color: #fcfcfc !important;
    }

    :global(.arco-form-label-item) {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: #333333;
    }

    :global(.arco-modal-header) {
        padding: 0;
        height: auto;
        border-bottom: none;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            color: #333333;
            margin-bottom: 24px;
            text-align: left;
        }
    }

    :global(.arco-input) {
        padding: 8px 12px;
        border: 1px solid #ebebeb;
        border-radius: 8px;
        background-color: transparent;
        transition: all 0.3s;

        &:hover:not(:disabled) {
            background-color: #fafafa;
            border-color: #ebebeb;
        }

        &::placeholder {
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
        }
    }

    :global(.arco-modal-content) {
        min-height: 160px;
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 32px;
        top: 32px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
        background-color: #e9e9ff;
        color: #4d5ef3;
    }

    /* 覆盖 Arco Design 默认的禁用按钮样式 */
    :global(.arco-btn.arco-btn-primary[disabled]) {
        background-color: #c3c8fa !important;
        color: #ffffff !important;
        border-color: #c3c8fa !important;
    }
}

:global {
    .arco-modal-mask {
        background: rgba(0, 0, 0, 0.16);
    }
}