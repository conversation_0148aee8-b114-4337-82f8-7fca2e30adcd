.folderCreateModal {
  :global {
    .ant-modal-content {
      border-radius: 16px;
    }

    .ant-modal-header {
      margin-bottom: 16px;
    }

    .ant-modal-title {
      font-size: 20px;
      color: #333333;
    }
  }
}

.formContainer {
  display: flex;
  flex-direction: column;
  gap: 24px;

  .topSection {
    display: flex;
    align-items: flex-start;
    gap: 24px;

    .iconSection {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 72px;
      height: 72px;
      flex-shrink: 0;

      .folderIcon {
        width: 100%;
        height: 100%;
      }
    }

    .divider {
      width: 1px;
      height: 72px;
      background-color: #f5f5f5;
      flex-shrink: 0;
    }

    .nameFormSection {
      flex: 1;

      :global {
        .ant-form-item {
          margin-bottom: 0;
        }

        .ant-form-item-label {
          padding-bottom: 8px;

          >label {
            font-size: 14px;
            font-weight: 500;
            color: #333333;
          }
        }

        .ant-input {
          border-radius: 8px;
          border: 1px solid #ebebeb;
          padding: 8px 12px;
          font-size: 14px;

          &:focus {
            border-color: #4455f2;
            box-shadow: none;
          }

          &::placeholder {
            color: #d6d6d6;
            font-size: 14px;
            line-height: 24px;
          }
        }
      }
    }
  }

  .bottomSection {

    :global {
      .ant-input-outlined:focus-within {
        box-shadow: none;
        border-color: #ebebeb !important;
      }

      .ant-input-outlined:hover {
        border-color: #ebebeb !important;
      }

      .ant-input-outlined {
        border-color: #ebebeb !important;
      }
    }


    :global {
      .ant-form-item {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .ant-form-item-label {
        padding-bottom: 8px;

        >label {
          font-size: 14px;
          font-weight: 500;
          color: #333333;
        }
      }

      .ant-textarea {
        border-radius: 8px;
        border: 1px solid #ebebeb;
        padding: 8px 12px;
        font-size: 14px;
        min-height: 88px;
        resize: none;

        &:focus {
          border-color: #4455f2;
          box-shadow: none;
        }

        &::placeholder {
          color: #d6d6d6;
          font-size: 14px;
          line-height: 24px;
        }
      }
    }
  }

  // 标签相关样式
  .tagFormItem {
    .tagHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .tagLabel {
        color: #5c5c5c;
        font-weight: 600;
        font-size: 14px;
      }

      .tagHint {
        font-weight: 400;
        font-size: 12px;
        color: #adadad;
      }

      .addTagBtn {
        height: 40px;
        cursor: pointer;
        padding: 8px 24px;
        background-color: #ffffff !important;
        border: 1px solid #ebebeb !important;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;

        &:hover {
          background-color: #fcfcfc !important;
          border-color: #ebebeb !important;
        }

        &:disabled {
          background-color: #f5f5f5 !important;
          color: #adadad !important;
          cursor: not-allowed !important;
        }
      }
    }

    .selectedItemList {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 8px;
      background-color: #fcfcfc;
      border: 1px solid #f5f5f5;
      border-radius: 8px;

      .selectedItemRow {
        width: 100%;

        :global(.arco-input) {
          padding: 0;
          border: none;
          border-radius: 0;
          background-color: transparent;
        }

        :global(.arco-input-inner-wrapper) {
          padding: 8px 8px 8px 12px;
          border: 1px solid #f5f5f5;
          border-radius: 8px;
          background-color: #ffffff;

          &:hover {
            background-color: #ffffff;
          }
        }
      }

      .deleteIcon {
        cursor: pointer;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  // 自定义required标识样式
  .labelWithRequired {
    position: relative;
    display: inline-block;
  }

  .requiredStar {
    color: #4455f2;
    position: absolute;
    top: 1px;
    right: -12px;
    font-size: 16px;
    line-height: 1;
  }
}

.customFooter {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;

  // 按钮共同样式
  .cancelButton,
  .confirmButton {
    height: 40px !important;
    padding: 8px 24px !important;
    border-radius: 8px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease-in-out !important;
    box-shadow: none !important;
  }

  // 取消按钮特有样式
  .cancelButton {
    border: 1px solid #e8e8e8 !important;
    background-color: #ffffff !important;
    color: #666666 !important;

    &:hover,
    &:focus {
      border-color: #d9d9d9 !important;
      background-color: #fafafa !important;
      color: #333333 !important;
    }
  }

  // 确认按钮特有样式
  .confirmButton {
    border: none !important;
    background-color: #4455f2 !important;
    color: #ffffff !important;

    &:hover,
    &:focus {
      background-color: #3441d9 !important;
      color: #ffffff !important;
    }

    &:disabled {
      background-color: #c3c8fa !important;
      cursor: not-allowed !important;
      color: #ffffff !important;
      border: none !important;
    }
  }
}