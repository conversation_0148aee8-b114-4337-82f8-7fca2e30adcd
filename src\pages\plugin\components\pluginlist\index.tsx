import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Typography, Space, Grid, Modal, Upload, Message, Select, Empty } from '@arco-design/web-react';
import IconUpload from '@/assets/plugin/IconUpload.svg';
import IconSearch from '@/assets/plugin/IconSearch.svg';
import PluginIcon from '@/assets/plugin/PluginIcon.svg';
import IconClose from '@/assets/plugin/IconClose.svg';
import EmptyPluginIcon from '@/assets/plugin/EmptyPluginIcon.svg';
import styles from './style/index.module.less';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { GlobalState } from '@/store/index';
import { getPluginList } from '@/lib/services/plugin-service';
import { Spin } from 'antd';
import { IconExclamationCircle } from '@arco-design/web-react/icon';

const { Text } = Typography;
const { Row, Col } = Grid;
const Option = Select.Option;

// 定义状态展示组件
const StatusDisplay = ({ status, errorMessage = '获取插件列表失败' }) => {
  if (status === 'loading') {
    return (
      <div className={styles.statusContainer}>
        <Spin size="large" />
        <Text className={styles.statusText}>加载中...</Text>
      </div>
    );
  }
  
  if (status === 'error') {
    return (
      <div className={styles.statusContainer}>
        <IconExclamationCircle style={{ fontSize: 48, color: 'rgb(var(--danger-6))' }} />
        <Text className={styles.statusText}>{errorMessage}</Text>
      </div>
    );
  }

  if (status === 'empty') {
    return (
      <div className={styles.statusContainer}>
        <EmptyPluginIcon />
        <Text className={styles.statusText}>未找到插件</Text>
      </div>
    );
  }
  
  return null;
};

function PluginList() {
  const [visible, setVisible] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [plugins, setPlugins] = useState([]);
  const [filteredPlugins, setFilteredPlugins] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [total, setTotal] = useState(0);
  const [searchText, setSearchText] = useState('');
  const [sortOption, setSortOption] = useState('createTime');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const pluginDetailMenuName = useSelector(
    (state: GlobalState) => state.pluginDetailMenuName
  );

  // 获取插件列表数据
  const fetchPlugins = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getPluginList();
      if (response && response.items) {
        setPlugins(response.items);
        setFilteredPlugins(response.items);
        setTotal(response.count || 0);
      } else {
        setPlugins([]);
        setFilteredPlugins([]);
        console.warn('API返回数据为空');
      }
    } catch (error) {
      setError(error.message || '获取插件列表失败');
      Message.error('获取插件列表失败');
      console.error('获取插件列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPlugins();
  }, []);

  // 根据搜索文本过滤插件
  useEffect(() => {
    if (!searchText) {
      setFilteredPlugins(plugins);
      return;
    }
    
    const filtered = plugins.filter(plugin => 
      plugin.name?.toLowerCase().includes(searchText.toLowerCase()) || 
      plugin.description?.toLowerCase().includes(searchText.toLowerCase())
    );
    
    setFilteredPlugins(filtered);
  }, [searchText, plugins]);

  // 获取当前内容状态
  const getContentStatus = () => {
    if (loading) return 'loading';
    if (error) return 'error';
    if (filteredPlugins.length === 0) return 'empty';
    return 'content';
  };

  // 处理上传前的验证
  const beforeUpload = (file) => {
    const isZipOrRar =
      file.type === 'application/zip' ||
      file.type === 'application/x-rar-compressed';
    const isLt2G = file.size / 1024 / 1024 < 2048;

    if (!isZipOrRar) {
      Message.error('只能上传 ZIP/RAR 格式的文件！');
      return false;
    }
    if (!isLt2G) {
      Message.error('文件大小不能超过 2GB！');
      return false;
    }
    return true;
  };

  // 处理上传状态改变
  const onChange = (fileList, file) => {
    setFileList(fileList);
  };

  // 处理上传
  const onSubmit = () => {
    if (fileList.length === 0) {
      Message.warning('请先选择要上传的文件');
      return;
    }
    Message.success('上传成功');
    setVisible(false);
    setFileList([]);
  };

  const updateBreadcrumbData = (newBreadcrumb: {}) => {
    // 使用 dispatch 来发送 action 更新 breadcrumb
    dispatch({
      type: 'update-breadcrumb-menu-name',
      payload: { breadcrumbMenuName: newBreadcrumb },
    });
    console.log(newBreadcrumb);
  };

  const gotoPluginDetail = (item) => {
    const breadcrumbData = new Map<string, string>([
      [pluginDetailMenuName, item.name],
    ]);
    updateBreadcrumbData(breadcrumbData);
    // 将插件数据存储到localStorage
    localStorage.setItem('currentPluginData', JSON.stringify(item));
    navigate('/plugin/detail');
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
  };

  // 清空搜索
  const clearSearch = () => {
    setSearchText('');
  };

  // 处理排序方式改变
  const handleSortChange = (value) => {
    setSortOption(value);
  };

  // 获取插件状态标签
  const getPluginTags = (plugin) => {
    const tags = [];
    if (plugin.enabled) {
      tags.push('启用');
    } else {
      tags.push('禁用');
    }

    if (plugin.is_core) {
      tags.push('私有');
    } else {
      tags.push('公共');
    }
    return tags;
  };

  // 从assembly字段中提取版本信息
  const extractVersion = (assembly) => {
    if (!assembly) return '';
    const versionMatch = assembly.match(/Version=([\d\.]+)/);
    if (versionMatch && versionMatch[1]) {
      return `V${versionMatch[1]}`;
    }
    return '';
  };

  // 处理标签样式的函数
  const getTagStyle = (tag) => {
    switch (tag) {
      case '启用':
        return styles.enabledTag;
      case '禁用':
        return styles.disabledTag;
      case '公共':
        return styles.publicTag;
      case '私有':
        return styles.privateTag;
      default:
        return '';
    }
  };

  // 重试加载
  const handleRetry = () => {
    fetchPlugins();
  };

  const currentStatus = getContentStatus();

  return (
    <div className={styles.pluginContainer}>
      <Text className={styles.pluginTitleDom}>插件列表</Text>
      <Row className={styles.pluginListHeader}>
        <Button
          type="primary"
          className={styles.addPluginBtn}
          onClick={() => setVisible(true)}
        >
          导入插件
        </Button>
        <Space size={'small'}>
          <Text className={styles.count}>共 {filteredPlugins.length} 个插件</Text>
          <Input
            prefix={<IconSearch />}
            placeholder="AI搜索..."
            style={{ width: 240 }}
            value={searchText}
            onChange={handleSearch}
            allowClear
            onClear={clearSearch}
          />
        </Space>
      </Row>
      <div className={styles.content}>
        {currentStatus !== 'content' ? (
          <StatusDisplay 
            status={currentStatus} 
            errorMessage={error}
          />
        ) : (
          filteredPlugins.map((item) => (
            <Card
              key={item.id}
              className={styles.itemCard}
              hoverable
              onClick={() => gotoPluginDetail(item)}
            >
              <Space className={styles.pluginInfo} direction='vertical' size={8}>
                <Space size={12}>
                  <div className={styles.pluginIcon}>
                    {item.icon_url ? (
                      <img src={item.icon_url} alt={item.name} />
                    ) : (
                      <PluginIcon className={styles.pluginIcon} />
                    )}
                  </div>
                  <Text className={styles.itemTitle}>
                    {item.name}
                  </Text>
                </Space>
                <Text className={styles.itemDescription}>
                  {item.description || ''}
                </Text>
              </Space>
              <Row className={styles.pluginFooter}>
                <Text className={styles.versionText}>
                  版本：{extractVersion(item.assembly)}
                </Text>
                <Space className={styles.itemTypes} size={8}>
                  {getPluginTags(item).map((tag, index) => (
                    <Text
                      key={index}
                      className={`${styles.typeTag} ${getTagStyle(tag)}`}
                    >
                      {tag}
                    </Text>
                  ))}
                </Space>
              </Row>
            </Card>
          ))
        )}
      </div>
      {/* 上传插件弹窗 */}
      <Modal
        title="上传插件"
        visible={visible}
        onCancel={() => setVisible(false)}
        closeIcon={<IconClose />}
        className={styles.uploadModal}
      >
        <Upload
          drag
          accept=".zip,.rar"
          multiple={false}
          fileList={fileList}
          beforeUpload={beforeUpload}
          onChange={onChange}
          onDrop={(e) => {
            console.log('drop', e);
          }}
          tip="支持 .zip/.rar 格式，文件最大不超过 2GB"
          className={styles.uploader}
        >
          <div className={styles.uploadContent}>
            <div className={styles.uploadText}>
              <IconUpload />
              <Text className={styles.mainText}>点击 / 拖拽上传</Text>
            </div>
            <Text className={styles.subText}>
              支持.zip/.rar格式，文件最大支持 2Gb
            </Text>
          </div>
        </Upload>
      </Modal>
    </div>
  );
}

export default PluginList;
