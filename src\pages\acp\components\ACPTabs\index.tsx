// src/components/ACPTabs.tsx
import React, { useState } from 'react';
import { Tabs } from '@arco-design/web-react';
import ACPServer from '../ACPServer';
import ACPTools from '../ACPTools';
import styles from './style/index.module.less';
import { useLocation } from 'react-router-dom';

const TabPane = Tabs.TabPane;

function ACPTabs() {
    const location = useLocation();
    const [activeTab, setActiveTab] = useState(() => {
        return location.state?.fromACP ? 'acpServer' : 'acpTools';
    });

    return (
        <div className={styles.container}>
            <Tabs
                activeTab={activeTab}
                onChange={setActiveTab}
                type="line"
                className={styles.tabs}
            >
                <TabPane key="acpServer" title="ACP Server">
                    <ACPServer />
                </TabPane>
                {/* <TabPane key="acpTools" title="ACP Tools">
                    <ACPTools />
                </TabPane> */}
            </Tabs>
        </div>
    );
}

export default ACPTabs;