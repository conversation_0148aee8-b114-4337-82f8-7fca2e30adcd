import styles from './style/index.module.less';
import { useEffect, useRef } from "react";
import { ReactFlow, Background, ReactFlowProvider } from '@xyflow/react';

import '@xyflow/react/dist/style.css';
import '../flow/style/theme.less';
import LoadingSetting from '../flow/components/loadingSetting';

import { useNodes } from '../flow/hooks/useNodes';

import { FlowContext } from '../flow/context';
interface ApplicationDetailData {
  id: string;
  name: string;
  description: string;
  type: string;
  provider: string;
  channel: string;
  iconUrl: string | null;
  installed: boolean;
  isPublished: boolean;
  disabled: boolean;
  createdDateTime: string;
  updatedDateTime: string;
  labels: string[];
  welcome: string;
}
interface ApplicationRoutingProps {
  applicationData: ApplicationDetailData | null;
  loading: boolean;
  onRefresh: (force?: boolean) => Promise<void>;
  onApplicationIdChange?: (id: string) => void;
}

function ApplicationRouting({ applicationData, loading: parentLoading, onRefresh, onApplicationIdChange }: ApplicationRoutingProps) {
  const isMountedRef = useRef(false);

  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    fitViewOptions,
    nodeTypes,
    selectedNode,
    nodeBg,
    handleUpdateNode,
    resetNodeStatus,
    currentNodeVersionData,
    setCurrentNodeVersionData,
    isRequestAddAgent,
    isClickBlank,
    setIsClickBlank,
    handleUpdateEdge,
    resetEdge,
    isLoading,
    setIsLoading,
    isInitialized,
    setIsInitialized,
    getSecondRouteCard
  } = useNodes(applicationData);

   // 传递的数据
   const flowContextData = {
    // handleUpdateNode,
    // selectedNodes: selectedNode,
    // resetNodeStatus,
    // isRequestAddAgent,
    // nodes,
    // currentNodeVersionData,
    // setCurrentNodeVersionData,
    // isClickBlank,
    // setIsClickBlank,
    // handleDeleteNode,
    // handleUpdateEdge,
    // resetEdge,
    // setIsLoading,
    // isLoading,
    // isInitialized,
    // setIsInitialized,
  };

  useEffect(() => {
    // if (isMountedRef.current) {
    //   return;
    // }
    // isMountedRef.current = true;

    if (applicationData) {
      getSecondRouteCard();
    }
  }, [applicationData]);

  return (
      <div className={styles.container} >
        <FlowContext.Provider value={flowContextData}>
          <ReactFlowProvider>
            {/* {isLoading && <LoadingSetting />} */}
            <ReactFlow
              nodes={nodes}
              edges={edges}
              nodeTypes={nodeTypes}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onPaneClick={() => setIsClickBlank(true)}
              style={{ background: nodeBg }}
              // attributionPosition="bottom-left" // ReactFlow 版权归属信息位置
              minZoom={0.5}
              fitView={false}
              defaultViewport={{ x: 150, y: 150, zoom: 0.8 }}
            >
              <Background />
            </ReactFlow>
          </ReactFlowProvider>
        </FlowContext.Provider>
      </div>
  );
}

export default ApplicationRouting;
