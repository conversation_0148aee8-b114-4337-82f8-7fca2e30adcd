/* 新登录页面样式 - 三栏布局设计 */

.container {
  display: flex;
  width: 100vw;
  height: 100vh;
  overflow: hidden;

  /* 当Agent Team功能项被悬浮时的效果 */
  &.agentTeamHovered {
      .showcaseArea {
          .showcaseImage:last-child {
              /* AgentTeamImage移动到AcpCardImage的位置 */
              top: 15%;
              right: 0%;
              z-index: 1;
              width: 90%; /* 调整宽度与AcpCardImage一致 */
          }
          
          .agentTeamCreateImage {
              /* 显示AgentTeamCreateImage在原位置 - 延迟显示 */
              opacity: 1;
              transform: scale(1);
              transition-delay: 0.3s; /* 延迟0.3秒后开始显示动画 */
          }
      }
  }
}

/* 最左侧边栏区域 (10-15%) */
.sidebar {
  width: 12%;
  // background: linear-gradient(180deg, #f8f9fa 0%, #f1f3f4 100%);
  background: #fafafb;
  border-right: 1px solid #ebebeb;
  flex-shrink: 0;
  position: relative;

  /* 顶部45°斜线阴影区域 */
  &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 15%;
      background-image: repeating-linear-gradient(-45deg,
              transparent,
              transparent 8px,
              #ebebeb 8px,
              #ebebeb 9px,
              transparent 9px,
              transparent 17px);
      background-size: 12px 12px;
      image-rendering: crisp-edges;
      pointer-events: none;
      z-index: 1;
      border-bottom: 1px solid #ebebeb;
  }
}

/* 左侧内容区域 (35-40%) */
.contentArea {
  width: 45%;
  background: #fafafb;
  border-right: 1px solid #ebebeb;
  display: flex;
  flex-direction: column;
  position: relative;
  flex-shrink: 0;
  height: 100vh;
}

/* 顶部空白区域 */
.topSpacerArea {
  height: 15%;
  flex-shrink: 0;
  border-bottom: 1px solid #ebebeb;
}

/* 底部空白区域 */
.bottomSpacerArea {
  height: 15%;
  flex-shrink: 0;
  border-top: 1px solid #ebebeb;
  margin-top: auto;
  /* 推到底部 */
}

/* 品牌区域 */
.brandSection {
  margin-bottom: 0;
  padding: 1.25rem 5rem; /* 20px 80px -> 1.25rem 5rem */
  border-bottom: 1px solid #ebebeb;
  flex: 2;
  /* 占据2倍的空间 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* 内容垂直居中 */
}

.logoSection {
  text-align: left;

  .logoContainer {
      display: inline-flex;
      /* 内容自适应宽度 */
      align-items: center;
      gap: 0.5rem; /* 8px -> 0.5rem */
      padding: 0.75rem 1rem; /* 12px 16px -> 0.75rem 1rem */
      border-radius: 0.5rem; /* 8px -> 0.5rem */
      background-color: #f6f7fa;
      margin-bottom: 0.5rem; /* 8px -> 0.5rem */
      width: fit-content;
      /* 确保宽度自适应内容 */

      .logoImage {
          width: 1.5rem; /* 24px -> 1.5rem */
          height: 1.5rem; /* 24px -> 1.5rem */
      }

      .logoText {
          font-size: 1rem; /* 16px -> 1rem */
          font-weight: 600;
          color: #333333;
      }
  }
}

.mainTitle {
  font-weight: 600;
  font-size: 2rem; /* 32px -> 2rem */
  line-height: 3.5rem; /* 56px -> 3.5rem */
  letter-spacing: 0%;
  color: #333333;
  margin: 0 0 0.5rem 0; /* 8px -> 0.5rem */
  line-height: 1.2;
}

.subtitle {
  font-weight: 400;
  font-size: 1rem; /* 16px -> 1rem */
  line-height: 1.75rem; /* 28px -> 1.75rem */
  letter-spacing: 0%;
  color: #adadad;
  margin: 0;
}

/* 操作按钮区域 */
.actionSection {
  margin-bottom: 0;
  padding: 1.25rem 0 0 0; /* 20px -> 1.25rem */
  flex-shrink: 0;
  /* 保持固定大小 */
}

.experienceBtn {
  background: #333333;
  color: white;
  border: none;
  border-radius: 1.5rem; /* 24px -> 1.5rem */
  padding: 0.75rem 1.5rem; /* 12px 24px -> 0.75rem 1.5rem */
  font-weight: 500;
  font-size: 0.875rem; /* 14px -> 0.875rem */
  line-height: 1.5rem; /* 24px -> 1.5rem */
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(55, 65, 81, 0.15);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  width: auto;
  // min-width: 120px; /* 设置最小宽度 */

  .btnText {
      transition: all 0.3s ease;
  }

  .experienceBtnArrow {
      opacity: 0;
      transform: translateX(-0.5rem); /* -8px -> -0.5rem */
      transition: all 0.3s ease;
      margin-left: 0;
      width: 0;
      height: 1.5rem; /* 24px -> 1.5rem */
      /* 设置SVG高度 */
      display: inline-block;
      overflow: hidden;
      flex-shrink: 0;
  }

  &:hover {
      background: #333333;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(55, 65, 81, 0.25);

      .experienceBtnArrow {
          opacity: 1;
          transform: translateX(0);
          margin-left: 0.5rem; /* 8px -> 0.5rem */
          width: auto;
      }
  }

  &:active {
      transform: translateY(0);
  }
}

/* 功能模块展示区 */
.featuresSection {
  display: flex;
  flex-direction: column;
  gap: 0;
  flex: 1;
  /* 占满剩余空间 */
  // justify-content: center; /* 垂直居中 */
}

.featureItem {
  display: flex;
  // align-items: center;
  flex-direction: column;
  gap: 0.5rem; /* 8px -> 0.5rem */
  padding: 1.875rem 5rem; /* 30px 80px -> 1.875rem 5rem */
  border-bottom: 1px solid #ebebeb;
  flex: 1;
  /* 占满剩余空间 */
  justify-content: center;
  /* 垂直居中 */
  transition: all 0.3s ease;
  position: relative; /* 为箭头提供定位参考 */
  cursor: pointer;

  &:last-child {
      border-bottom: none;
  }

  &:hover {
      background: #ffffff;
      
      .featureIcon {
          opacity: 0;
          transform: translateY(-10px);
      }
      
      .featureDescription {
          max-height: none;
          -webkit-line-clamp: unset;
      }
      
      .featureArrow {
          opacity: 1;
          transform: translateX(0);
      }
  }


}

.featureIcon {
  width: 2rem; /* 32px -> 2rem */
  height: 2rem; /* 32px -> 2rem */
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.featureContent {
  flex: 1;
}

.featureTitle {
  font-weight: 600;
  font-size: 1rem; /* 16px -> 1rem */
  line-height: 1.75rem; /* 28px -> 1.75rem */
  color: #333333;
  margin: 0 0 0.25rem 0; /* 4px -> 0.25rem */
}

.featureDescription {
  font-weight: 400;
  font-size: 0.875rem; /* 14px -> 0.875rem */
  line-height: 1.5rem; /* 24px -> 1.5rem */
  color: #adadad;
  margin: 0;
  transition: all 0.3s ease;
  
  /* 默认状态限制显示内容 */
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 显示2行，大约60%的内容 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.featureArrow {
  position: absolute;
  top: 1.5rem; /* 24px，距离顶部位置 */
  right: 1.5rem; /* 24px，距离右侧位置 */
  width: 1.5rem; /* 24px */
  height: 1.5rem; /* 24px */
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateX(0.5rem); /* 8px，初始向右偏移 */
  transition: all 0.3s ease;
  color: #adadad;
  
  svg {
      width: 1.5rem; /* 24px */
      height: 1.5rem; /* 24px */
      fill: currentColor;
  }
}

/* 右侧展示区域 (45-50%) */
.showcaseArea {
  width: 40%;
  background: #fafafb;
  padding: 60px 40px;
  display: block; /* 改为block，使用绝对定位布局 */
  position: relative; /* 为子元素提供定位参考 */
  flex-shrink: 0;

  /* 添加与sidebar相同的斜线背景 */
  &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image: repeating-linear-gradient(-45deg,
          transparent,
          transparent 8px,
          #ebebeb 8px,
          #ebebeb 9px,
          transparent 9px,
          transparent 17px);
      background-size: 12px 12px;
      image-rendering: crisp-edges;
      pointer-events: none;
      z-index: 0;
      // opacity: 0.3; /* 添加透明度，让斜线更subtle */
  }

  /* 确保内容在斜线之上 */
  > * {
      position: relative;
      z-index: 1;
  }
}

.showcaseImage {
  width: 70%; /* 调整宽度 */
  position: absolute; /* 使用绝对定位 */
  transition: all 0.5s ease; /* 增加过渡时间以便看到移动效果 */

  // 第一个图片 (AcpServerImage) - 调整宽度使其看起来更大
  &:first-child {
      width: 90%; /* 增加宽度来补偿其原始尺寸更宽导致的显示较小问题 */
  }

  // 第二个图片 (AgentTeamImage) 保持默认宽度
  &:last-child {
      width: 75%;
  }

  &:hover {
      transform: translateY(-4px);
  }
  
  /* ACP图片 - 第一个图片在上方，稍微靠左 */
  &:first-child {
      top: 15%; /* 距离顶部15% */
      right: 0%; /* 距离右边15%，让图片稍微靠左 */
      z-index: 1; /* 确保在上层 */
  }
  
  /* Agent Team图片 - 第二个图片在下方，靠右 */
  &:last-child {
      top: 36%; /* 距离顶部30% */
      right: 0%; /* 完全靠右 */
      z-index: 2; /* 在上层，形成覆盖效果 */
  }
}

/* 新增：AgentTeamCreateImage的样式 */
.agentTeamCreateImage {
  width: 70%;
  position: absolute;
  top: 35%; /* 在AgentTeamImage的原位置 */
  right: 0%;
  left: auto !important; /* 覆盖overlayImage的left定位 */
  z-index: 2;
  opacity: 0; /* 默认隐藏 */
  transform: scale(0.95); /* 默认稍微缩小 */
  transition: all 0.5s ease; /* 平滑过渡效果 */
  pointer-events: none; /* 避免影响其他交互 */
}

/* 当AgentTeamCreateImage使用overlayImage类时的特殊定位 */
.overlayImage.agentTeamCreateImage {
  top: 30% !important;
  right: 0% !important;
  left: auto !important;
  width: 80% !important;
}

.realImage {
  width: 100%;
  height: auto;
  transition: all 0.3s ease;
  
  // &:hover {
  //     box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
  // }
}

.fadeIn {
  opacity: 1;
  transform: scale(1);
}

.fadeOut {
  opacity: 0;
  transform: scale(0.95);
}

.imageContainer {
  position: relative;
  width: 100%;
  height: auto;
}

.overlayImage {
  position: absolute;
  top: 0;
  left: -5%; /* 向左偏移10%，可以根据需要调整 */
  width: 90%; /* 宽度缩小至90%，可以根据需要调整 */
}

// 保留imagePlaceholder作为备用
.imagePlaceholder {
  width: 100%;
  height: 280px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease;

  &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
  }

  &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  }
}

.imageContent {
  text-align: center;
  color: white;
  z-index: 1;
  position: relative;

  h4 {
      font-size: 20px;
      font-weight: 600;
      margin: 0 0 12px 0;
  }

  p {
      font-size: 14px;
      opacity: 0.9;
      margin: 0;
      line-height: 1.5;
  }
}

/* 简化的响应式设计（使用rem后） */
@media (max-width: 1200px) {
  .sidebar {
      width: 8%;
      
      &::before {
          height: 10%;
      }
  }
  
  .contentArea {
      width: 42%;
      
      .topSpacerArea {
          height: 10%;
      }
      
      .bottomSpacerArea {
          height: 10%;
      }
  }
  
  .showcaseArea {
      width: 50%;
      padding: 2.5rem 1.875rem; /* 40px 30px -> 2.5rem 1.875rem */
  }
  
  /* 减少一些padding */
  .brandSection {
      padding: 1rem 3.75rem; /* 15px 60px -> 1rem 3.75rem */
  }
  
  .featureItem {
      padding: 1.5rem 3.75rem; /* 25px 60px -> 1.5rem 3.75rem */
  }
}

@media (max-width: 768px) {
  .container {
      flex-direction: column;
  }
  
  .sidebar {
      width: 100%;
      height: 2.5rem; /* 40px -> 2.5rem */
      
      &::before {
          height: 6%;
      }
  }
  
  .contentArea {
      width: 100%;
      padding: 1.25rem; /* 20px -> 1.25rem */
      flex: 1;
      height: auto;
      
      .topSpacerArea {
          height: 6%;
      }
      
      .bottomSpacerArea {
          height: 6%;
      }
  }
  
  .showcaseArea {
      width: 100%;
      padding: 1.25rem; /* 20px -> 1.25rem */
      flex: 1;
      display: block; /* 移动端也使用绝对定位 */
      position: relative;
  }
  
  .brandSection {
      padding: 0.625rem 1.25rem; /* 10px 20px -> 0.625rem 1.25rem */
      flex: 1.5;
  }
  
  .mainTitle {
      font-size: 1.5rem !important; /* 24px -> 1.5rem，覆盖clamp() */
      line-height: 2rem !important; /* 32px -> 2rem，覆盖clamp() */
  }
  
  .subtitle {
      font-size: 0.875rem !important; /* 14px -> 0.875rem，覆盖clamp() */
      line-height: 1.375rem !important; /* 22px -> 1.375rem，覆盖clamp() */
  }
  
  .actionSection {
      padding: 1.25rem 0 0 0; /* 20px -> 1.25rem */
  }
  
  .experienceBtn {
      padding: 0.625rem 1.25rem; /* 10px 20px -> 0.625rem 1.25rem */
      font-size: 0.75rem; /* 12px -> 0.75rem */
  }
  
  .featureItem {
      padding: 1.25rem; /* 20px -> 1.25rem */
      gap: 0.375rem; /* 6px -> 0.375rem */
  }
  
  .featureIcon {
      width: 1.75rem; /* 28px -> 1.75rem */
      height: 1.75rem; /* 28px -> 1.75rem */
  }
  
  .featureTitle {
      font-size: 0.875rem !important; /* 14px -> 0.875rem，覆盖clamp() */
      line-height: 1.5rem !important; /* 24px -> 1.5rem，覆盖clamp() */
  }
  
  .featureDescription {
      font-size: 0.75rem !important; /* 12px -> 0.75rem，覆盖clamp() */
      line-height: 1.25rem !important; /* 20px -> 1.25rem，覆盖clamp() */
      -webkit-line-clamp: 2; /* 移动端显示2行 */
  }
  
  .featureArrow {
      top: 1rem; /* 16px，移动端调整位置 */
      right: 1rem; /* 16px，移动端调整位置 */
      width: 1.25rem; /* 20px，移动端稍小 */
      height: 1.25rem; /* 20px */
      
      svg {
          width: 1.125rem; /* 18px，移动端图标 */
          height: 1.125rem; /* 18px */
      }
  }
  
  .showcaseImage {
      width: 90%; /* 移动端增加宽度 */
      left: auto; /* 取消居中 */
      transform: none; /* 取消居中变换 */
      right: auto; /* 取消右对齐 */
      
      &:first-child {
          top: 10%; /* 移动端调整位置 */
          left: 5%; /* 移动端稍微靠左 */
      }
      
      &:last-child {
          top: 35%; /* 移动端调整位置 */
          right: 5%; /* 移动端稍微靠右 */
      }
      
      &:hover {
          transform: translateY(-4px); /* 简化悬浮效果 */
      }
  }
  
  .realImage {
      border-radius: 12px; /* 移动端减少圆角 */
  }
  
  .imagePlaceholder {
      height: 11.25rem; /* 180px -> 11.25rem */
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .contentArea {
      .topSpacerArea {
          height: 4%;
      }
      
      .bottomSpacerArea {
          height: 4%;
      }
  }
  
  .sidebar {
      &::before {
          height: 4%;
      }
  }
  
  .brandSection {
      padding: 0.5rem 0.9375rem; /* 8px 15px -> 0.5rem 0.9375rem */
  }
  
  .actionSection {
      padding: 0.9375rem 0 0 0; /* 15px -> 0.9375rem */
  }
  
  .featureItem {
      padding: 0.9375rem; /* 15px -> 0.9375rem */
  }
  
  .mainTitle {
      font-size: 1.25rem; /* 20px -> 1.25rem */
      line-height: 1.75rem; /* 28px -> 1.75rem */
  }
  
  .experienceBtn {
      padding: 0.5rem 1rem; /* 8px 16px -> 0.5rem 1rem */
      font-size: 0.6875rem; /* 11px -> 0.6875rem */
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
      opacity: 0;
      transform: translateY(20px);
  }

  to {
      opacity: 1;
      transform: translateY(0);
  }
}

.brandSection,
.actionSection,
.featuresSection {
  animation: fadeInUp 0.6s ease-out;
}

.actionSection {
  animation-delay: 0.2s;
}

.featuresSection {
  animation-delay: 0.4s;
}

.showcaseImage:first-child {
  animation: fadeInUp 0.6s ease-out 0.6s both;
}

.showcaseImage:last-child {
  animation: fadeInUp 0.6s ease-out 0.8s both;
}

/* 大屏幕优化 */
@media (min-width: 1920px) {
  .brandSection {
      padding: 1.5rem 6rem; /* 增加大屏幕的padding */
  }
  
  .mainTitle {
      font-size: 2.5rem; /* 40px，27寸屏幕适合的大小 */
      line-height: 3rem; /* 48px */
  }
  
  .subtitle {
      font-size: 1.125rem; /* 18px，稍微增大副标题 */
      line-height: 2rem; /* 32px */
  }
  
  .logoContainer {
      padding: 1rem 1.25rem; /* 16px 20px，增大logo容器 */
      
      .logoImage {
          width: 1.75rem; /* 28px，增大logo尺寸 */
          height: 1.75rem;
      }
      
      .logoText {
          font-size: 1.125rem; /* 18px，增大logo文字 */
      }
  }
  
  .experienceBtn {
      padding: 1rem 2rem; /* 16px 32px，增大按钮 */
      font-size: 1rem; /* 16px，增大按钮文字 */
      
      .experienceBtnArrow {
          height: 1.75rem; /* 28px，增大箭头 */
      }
  }
  
  .featureItem {
      padding: 2.5rem 6rem; /* 40px 96px，增大间距 */
      gap: 0.75rem; /* 12px */
  }
  
  .featureTitle {
      font-size: 1.125rem; /* 18px，增大功能标题字体 */
      line-height: 2rem; /* 32px */
  }
  
  .featureDescription {
      font-size: 1rem; /* 16px，增大功能描述字体 */
      line-height: 1.75rem; /* 28px */
      -webkit-line-clamp: 3; /* 大屏幕下显示3行 */
  }
  
  .featureIcon {
      width: 2.5rem; /* 40px，增大图标 */
      height: 2.5rem;
  }
  
  .featureArrow {
      top: 2rem; /* 32px，大屏幕调整位置 */
      right: 2rem; /* 32px，大屏幕调整位置 */
      width: 2rem; /* 32px，大屏幕增大 */
      height: 2rem; /* 32px，大屏幕增大 */
      
      svg {
          width: 1.75rem; /* 28px，大屏幕增大图标 */
          height: 1.75rem; /* 28px */
      }
  }
  
  .showcaseArea {
      padding: 4rem 3rem; /* 64px 48px，增大右侧间距 */
  }
  
  .showcaseImage {
      width: 75%; /* 大屏幕下稍微增加图片宽度 */
  }
}

@media (min-width: 2560px) {
  /* 4K及超大屏幕 */
  .mainTitle {
      font-size: 3rem; /* 48px */
      line-height: 4.5rem; /* 72px */
  }
  
  .subtitle {
      font-size: 1.25rem; /* 20px */
      line-height: 2.25rem; /* 36px */
  }
  
  .brandSection {
      padding: 2rem 8rem; /* 进一步增大间距 */
  }
  
  .featureItem {
      padding: 3rem 8rem; /* 48px 128px */
  }
  
  .experienceBtn {
      padding: 1.25rem 2.5rem; /* 20px 40px */
      font-size: 1.125rem; /* 18px */
  }
}

/* 简化的响应式设计（使用rem后） */ 