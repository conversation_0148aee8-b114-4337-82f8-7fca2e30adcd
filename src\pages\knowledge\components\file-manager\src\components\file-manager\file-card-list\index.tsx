import { IFile } from '@/pages/knowledge/components/file-manager/src/interfaces/database/file-manager';
import { Row, Col } from 'antd';
import FileCard from '../file-card';
import styles from './index.module.less';

interface FileCardListProps {
    files: IFile[];
    onNavigate: (folderId: string) => void;
    onRename: (file: IFile) => void;
    onDelete: (fileId: string, parentId: string, fileName: string) => void;
}

const FileCardList = ({ files, onNavigate, onRename, onDelete }: FileCardListProps) => {
    if (!files || files.length === 0) {
        return null;
    }

    return (
        <Row gutter={[16, 16]} className={styles.cardContainer}>
            {files.map((file) => (
                <Col xs={24} sm={12} md={8} lg={6} key={file.id}>
                    <FileCard
                        file={file}
                        onNavigate={onNavigate}
                        onRename={onRename}
                        onDelete={onDelete}
                    />
                </Col>
            ))}
        </Row>
    );
};

export default FileCardList; 