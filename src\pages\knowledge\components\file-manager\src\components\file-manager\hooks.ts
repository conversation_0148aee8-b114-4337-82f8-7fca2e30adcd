import { useSetModalState, useShowDeleteConfirm, useShowFileManagerDeleteConfirm } from '@/pages/knowledge/components/file-manager/src/hooks/common-hooks';
import {
  useConnectToKnowledge,
  useCreateFolder,
  useDeleteFile,
  useEditFile,
  useFetchParentFolderList,
  useMoveFile,
  useRenameFile,
  useUploadFile,
} from '@/pages/knowledge/components/file-manager/src/hooks/file-manager-hooks';
import { IFile } from '@/pages/knowledge/components/file-manager/src/interfaces/database/file-manager';
import { TableRowSelection } from 'antd/es/table/interface';
import { UploadFile } from 'antd/lib';
import { useCallback, useMemo, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

// 兼容 React Router v5 的 useSearchParams 实现
const useSearchParams = () => {
  const location = useLocation();
  
  const searchParams = useMemo(() => {
    return new URLSearchParams(location.search);
  }, [location.search]);

  return [searchParams] as const;
};

export const useGetFolderId = () => {
  const [searchParams] = useSearchParams();
  const id = searchParams.get('folderId') as string;

  return id ?? '';
};

export const useGetRowSelection = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const rowSelection: TableRowSelection<IFile> = {
    selectedRowKeys,
    getCheckboxProps: (record) => {
      return { disabled: record.source_type === 'knowledgebase' };
    },
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return { rowSelection, setSelectedRowKeys };
};

export const useNavigateToOtherFolder = () => {
  const navigate = useNavigate();
  const navigateToOtherFolder = useCallback(
    (folderId: string) => {
      navigate(`/knowledge/files?folderId=${folderId}`);
    },
    [navigate],
  );

  return navigateToOtherFolder;
};

export const useRenameCurrentFile = () => {
  const [file, setFile] = useState<IFile>({} as IFile);
  const {
    visible: fileRenameVisible,
    hideModal: hideFileRenameModal,
    showModal: showFileRenameModal,
  } = useSetModalState();
  const { renameFile, loading } = useRenameFile();

  const onFileRenameOk = useCallback(
    async (name: string) => {
      const ret = await renameFile({
        fileId: file.id,
        name,
      });

      if (ret === 0) {
        hideFileRenameModal();
      }
    },
    [renameFile, file, hideFileRenameModal],
  );

  const handleShowFileRenameModal = useCallback(
    async (record: IFile) => {
      setFile(record);
      showFileRenameModal();
    },
    [showFileRenameModal],
  );

  return {
    fileRenameLoading: loading,
    initialFileName: file.name,
    onFileRenameOk,
    fileRenameVisible,
    hideFileRenameModal,
    showFileRenameModal: handleShowFileRenameModal,
  };
};

export const useEditCurrentFile = () => {

  const {
    visible: fileEditVisible,
    hideModal: hideFileEditModal,
    showModal: showFileEditModal,
  } = useSetModalState();
  const { editFile, loading } = useEditFile();
  const [file, setFile] = useState<IFile>({} as IFile);

  const onFileEditOk = useCallback(
    async ({ new_name, description, tags }: { new_name: string; description?: string; tags?: string[] }) => {
      const ret = await editFile({
        fileId: file.id,
        name: new_name,
        description,
        tags: tags || []
      });
      if (ret === 0) {
        hideFileEditModal();
      }
    },
    [editFile, file, hideFileEditModal],
  );


  const handleShowFileEditModal = useCallback(
    async (record: IFile) => {
      setFile(record);
      showFileEditModal();
    },
    [showFileEditModal],
  );

  return {
    fileEditLoading: loading,
    onFileEditOk,
    fileEditVisible,
    hideFileEditModal,
    showFileEditModal: handleShowFileEditModal,
    initialName: file.name,
    initialDescription: file.description,
    initialTags: Array.isArray(file.tags)
      ? file.tags
      : typeof file.tags === 'string' && file.tags
        ? file.tags.split(',').map(t => t.trim()).filter(Boolean)
        : [],
  };
};

export const useSelectBreadcrumbItems = () => {
  const parentFolderList = useFetchParentFolderList();

  // 如果没有父文件夹数据，返回空数组
  if (parentFolderList.length === 0) {
    return [];
  }

  // 总是显示面包屑，包括根目录
  const breadcrumbItems = parentFolderList.map((x) => ({
    title: x.name === '/' ? 'root' : x.name,
    path: `/knowledge?folderId=${x.id}`,
  }));
  return breadcrumbItems;
};

export const useHandleCreateFolder = () => {
  const {
    visible: folderCreateModalVisible,
    hideModal: hideFolderCreateModal,
    showModal: showFolderCreateModal,
  } = useSetModalState();
  const { createFolder, loading } = useCreateFolder();
  const id = useGetFolderId();

  const onFolderCreateOk = useCallback(
    async (name: string, description?: string, tags?: string[]) => {
      const ret = await createFolder({ 
        parentId: id, 
        name,
        description,
        tags: tags || []
      });

      if (ret === 0) {
        hideFolderCreateModal();
      }
    },
    [createFolder, hideFolderCreateModal, id],
  );

  return {
    folderCreateLoading: loading,
    onFolderCreateOk,
    folderCreateModalVisible,
    hideFolderCreateModal,
    showFolderCreateModal,
  };
};

export const useHandleDeleteFile = (
  fileIds: string[],
  setSelectedRowKeys: (keys: string[]) => void,
  fileList?: { id: string; name: string }[],
  onExitBatchMode?: () => void
) => {
  const { deleteFile: removeDocument } = useDeleteFile();
  const showDeleteConfirm = useShowFileManagerDeleteConfirm();
  const parentId = useGetFolderId();

  let fileNames = '';
  if (fileList && fileList.length > 0) {
    fileNames = fileList
      .filter(f => fileIds.includes(f.id))
      .map(f => f.name)
      .join('、');
  }

  const handleRemoveFile = () => {
    showDeleteConfirm({
      title: '删除文件',
      content: fileNames
        ? `“${fileNames}”删除后无法恢复，请确认是否删除？`
        : '删除后无法恢复，请确认是否删除？',
      onOk: async () => {
        const code = await removeDocument({ fileIds, parentId });
        if (code === 0) {
          setSelectedRowKeys([]);
          if (onExitBatchMode) onExitBatchMode(); 
        }
        return;
      },
    });
  };

  return { handleRemoveFile };
};

export const useHandleUploadFile = () => {
  const {
    visible: fileUploadVisible,
    hideModal: hideFileUploadModal,
    showModal: showFileUploadModal,
  } = useSetModalState();
  const { uploadFile, loading } = useUploadFile();
  const id = useGetFolderId();

  const onFileUploadOk = useCallback(
    async (fileList: UploadFile[]): Promise<number | undefined> => {
      if (fileList.length > 0) {
        const ret: number = await uploadFile({ fileList, parentId: id });
        if (ret === 0) {
          hideFileUploadModal();
        }
        return ret;
      }
    },
    [uploadFile, hideFileUploadModal, id],
  );

  return {
    fileUploadLoading: loading,
    onFileUploadOk,
    fileUploadVisible,
    hideFileUploadModal,
    showFileUploadModal,
  };
};

export const useHandleConnectToKnowledge = () => {
  const {
    visible: connectToKnowledgeVisible,
    hideModal: hideConnectToKnowledgeModal,
    showModal: showConnectToKnowledgeModal,
  } = useSetModalState();
  const { connectFileToKnowledge: connectToKnowledge, loading } =
    useConnectToKnowledge();
  const [record, setRecord] = useState<IFile>({} as IFile);

  const initialValue = useMemo(() => {
    return Array.isArray(record?.kbs_info)
      ? record?.kbs_info?.map((x) => x.kb_id)
      : [];
  }, [record?.kbs_info]);

  const onConnectToKnowledgeOk = useCallback(
    async (knowledgeIds: string[]) => {
      const ret = await connectToKnowledge({
        fileIds: [record.id],
        kbIds: knowledgeIds,
      });

      if (ret === 0) {
        hideConnectToKnowledgeModal();
      }
      return ret;
    },
    [connectToKnowledge, hideConnectToKnowledgeModal, record.id],
  );

  const handleShowConnectToKnowledgeModal = useCallback(
    (record: IFile) => {
      setRecord(record);
      showConnectToKnowledgeModal();
    },
    [showConnectToKnowledgeModal],
  );

  return {
    initialValue,
    connectToKnowledgeLoading: loading,
    onConnectToKnowledgeOk,
    connectToKnowledgeVisible,
    hideConnectToKnowledgeModal,
    showConnectToKnowledgeModal: handleShowConnectToKnowledgeModal,
  };
};

export const useHandleBreadcrumbClick = () => {
  const navigate = useNavigate();

  const handleBreadcrumbClick = useCallback(
    (path?: string) => {
      if (path) {
        navigate(path);
      }
    },
    [navigate],
  );

  return { handleBreadcrumbClick };
};

export const useHandleMoveFile = (
  setSelectedRowKeys: (keys: string[]) => void,
) => {
  const {
    visible: moveFileVisible,
    hideModal: hideMoveFileModal,
    showModal: showMoveFileModal,
  } = useSetModalState();
  const { moveFile, loading } = useMoveFile();
  const [sourceFileIds, setSourceFileIds] = useState<string[]>([]);

  const onMoveFileOk = useCallback(
    async (targetFolderId: string) => {
      const ret = await moveFile({
        src_file_ids: sourceFileIds,
        dest_file_id: targetFolderId,
      });

      if (ret === 0) {
        setSelectedRowKeys([]);
        hideMoveFileModal();
      }
      return ret;
    },
    [moveFile, hideMoveFileModal, sourceFileIds, setSelectedRowKeys],
  );

  const handleShowMoveFileModal = useCallback(
    (ids: string[]) => {
      setSourceFileIds(ids);
      showMoveFileModal();
    },
    [showMoveFileModal],
  );

  return {
    initialValue: '',
    moveFileLoading: loading,
    onMoveFileOk,
    moveFileVisible,
    hideMoveFileModal,
    showMoveFileModal: handleShowMoveFileModal,
  };
};

// 处理外层卡片删除
export const useHandleCardDelete = () => {
  const { deleteFile: removeDocument } = useDeleteFile();
  const showDeleteConfirm = useShowFileManagerDeleteConfirm();

  const handleCardDelete = useCallback((fileId: string, parentId: string, fileName?: string) => {
    showDeleteConfirm({
      title: '删除文件组',
      content: `"${fileName || '文件'}"删除后无法恢复,请确认是否删除?`,
      onOk: async () => {
        const code = await removeDocument({ fileIds: [fileId], parentId });
        if (code === 0) {
          // 删除成功后，数据会自动刷新
        }
        return;
      },
    });
  }, [removeDocument, showDeleteConfirm]);

  return { handleCardDelete };
};
