/* 美化滚动条样式 */
::-webkit-scrollbar {
  display: block;
  width: 6px;
  height: 6px;
}

/* 错误状态的选择框样式 */
.selectError {
  :global(.arco-select-view) {
    border: 1px solid #ff4d4f !important;

    &:hover {
      border-color: #ff4d4f !important;
    }

    &.arco-select-view-focus {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
    }
  }

  // 为级联选择器添加错误样式
  :global(.arco-cascader-view) {
    border: 1px solid #ff4d4f !important;

    &:hover {
      border-color: #ff4d4f !important;
    }

    &.arco-cascader-view-focus {
      border-color: #ff4d4f !important;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
    }
  }
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

/* 对于IE和Edge */
html {
  -ms-overflow-style: none;
  /* IE 和 Edge */
}

/* 对于Firefox */
* {
  scrollbar-width: none;
  /* Firefox */
}

:global(.aiAssistantModel) {
  height: 640px;
  width: 640px;
  border-radius: 10px;

  .operateButGroup {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .text {
      font-size: 14px;
      font-weight: 600;
    }

    .but {
      border-radius: 4px;
      width: 76px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .cancelBut {
      background-color: RGBA(250, 250, 250, 1);

      .text {
        color: RGBA(0, 0, 0, 0.65);
      }
    }

    .createBut {
      background-color: #4455F2;

      .text {
        color: white;
      }

      &.disabled {
        opacity: 0.32;
      }
    }
  }

  :global(.aiAssistant .arco-modal-header) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 16px 24px;
  }

  :global(.aiAssistant .arco-modal-title) {
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
  }

  :global(.aiAssistant .arco-modal-content) {
    padding: 32px;
    border: none !important;
    border-top: none !important;
    box-shadow: none !important;
  }

  :global(.ai-modal-title) {
    font-size: 18px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    display: block;
  }

  :global(.ai-instruction-text) {
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.75);
    text-align: left;
    margin-top: 20px;
  }

  :global(.ai-input-container) {
    position: relative;
    padding-top: 8px;
    display: flex;
    width: 100%;
  }

  :global(.ai-input-container .ant-input) {
    border-radius: 6px;
    padding: 12px 40px 12px 16px;
    height: 88px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    resize: none;
    background-color: rgba(0, 0, 0, 0.02);
    transition: all 0.3s ease;
    font-size: 14px;
    line-height: 1.6;
    width: 100%;
    word-break: break-all;

    &:focus {
      border-color: rgba(68, 85, 242, 0.6);
      background-color: white;
      box-shadow: 0 0 0 2px rgba(68, 85, 242, 0.1);
    }

    &::placeholder {
      color: rgba(0, 0, 0, 0.35);
    }
  }

  :global(.ai-send-button) {
    position: absolute;
    right: 12px;
    bottom: 12px;
    background: transparent;
    border: none;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    border-radius: 50%;
    z-index: 1;
  }

  :global(.ai-send-button:hover) {
    background-color: rgba(68, 85, 242, 0.1);
  }

  :global(.ai-send-button svg) {
    width: 24px;
    height: 24px;
    fill: rgba(68, 85, 242, 0.9);
  }

}

/* ACP服务器展开/收起箭头样式 */
.expandArrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 2px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  svg {
    display: block;
  }
}

/* ACP工具子节点列表样式 */
.childNodesList {
  border-left: 2px solid #f2f3f5;
  margin-left: 8px;
  width: calc(100% - 28px);
  overflow: hidden;

  .selectedItemRow {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 8px;
    border: 1px solid RGBA(0, 0, 0, 0.08);
    background-color: #FFFFFF;
    margin-bottom: 4px;
    
    &:last-child {
      margin-bottom: 0 !important;
    }

    .selectedItemCol {
      display: flex;
      align-items: center;
      width: 100%;
      
      // 重写内部布局
      > div {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        
        // 图标容器
        > div:first-child {
          flex: 0 0 32px;
          margin-right: 8px;
        }
      }
      
      // 删除按钮
      .deleteIcon {
        flex: 0 0 24px;
        margin-left: 8px;
      }
    }
  }
}

:global(.responseModel) {
  height: 640px;
  width: 640px;
  border-radius: 10px;

  .operateButGroup {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .text {
      font-size: 14px;
      font-weight: 600;
    }

    .but {
      border-radius: 4px;
      width: 76px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .cancelBut {
      background-color: RGBA(250, 250, 250, 1);

      .text {
        color: RGBA(0, 0, 0, 0.65);
      }
    }

    .createBut {
      background-color: #4455F2;

      .text {
        color: white;
      }

      &.disabled {
        opacity: 0.32;
      }
    }
  }
}

:global(.promptTemplateModel) {
  height: 640px;
  width: 640px;
  border-radius: 10px;

  .operateButGroup {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .text {
      font-size: 14px;
      font-weight: 600;
    }

    .but {
      border-radius: 4px;
      width: 76px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .cancelBut {
      background-color: RGBA(250, 250, 250, 1);

      .text {
        color: RGBA(0, 0, 0, 0.65);
      }
    }

    .createBut {
      background-color: #4455F2;

      .text {
        color: white;
      }

      &.disabled {
        opacity: 0.32;
      }
    }
  }
}

.container {
  display: flex;
  height: calc(100vh - 115px);
  flex-direction: column;
  flex: 1;
  overflow: auto;
  position: relative;

  .tabs {
    :global(.arco-tabs-header-nav::before) {
      display: none;
    }

    :global(.arco-tabs-header) {
      width: 100%;
    }

    :global(.arco-tabs-header-ink) {
      display: none;
    }

    :global(.arco-tabs-header-title) {
      padding: 0;
      font-weight: 600;
      font-size: 20px;
      color: #a6a6a6;
      margin-left: 7px !important;
      width: 100%;
      padding-bottom: 16px;
      border-bottom: 1px solid RGBA(0, 0, 0, 0.08);

      &:hover {
        color: #a6a6a6;
      }
    }

    :global(.arco-tabs-header-title-active) {
      color: #333333;

      &:hover {
        color: #333333;
      }
    }

    :global(.arco-tabs-content) {
      padding-top: 24px;
    }
  }

  :global(.arco-popover-content) {
    padding: 0 12px 8px 12px;

    :global(.arco-popover-inner-content) {
      p {
        cursor: pointer;
        padding-left: 8px;
        display: flex;
        align-items: center;
        height: 34px !important;
        width: 104px !important;
        text-align: left;
        color: RGBA(0, 0, 0, 0.65);
        border-radius: 4px;

        &:hover {
          background-color: RGBA(0, 0, 0, 0.02);
        }
      }

      .red {
        color: RGBA(213, 73, 65, 0.95) !important;
      }
    }
  }

  .customContainer {
    width: 100%;
    padding: 8px 0 0 8px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    padding-top: 16px;
    height: calc(100vh - 155px);
    overflow: hidden;

    .subtitle {
      font-size: 14px;
      font-weight: 600;
      color: RGBA(0, 0, 0, 0.65);
    }

    .subtitlePlaceholder {
      font-size: 12px;
      font-weight: 400;
      color: RGBA(0, 0, 0, 0.35);
    }

    .inputrow {
      width: 100%;
      border-radius: 4px;
      border: 1px solid RGBA(0, 0, 0, 0.08);
      height: 40px;
    }

    .leftContainer {
      width: calc(51% - 24px);
      height: calc(100% - 60px);
      border-right: 1px solid RGBA(0, 0, 0, 0.08);
      padding-right: 24px;
      position: relative;
      overflow-y: auto;
      overflow-x: hidden;

      .selectedItemContainer {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        width: 100%;
        border-radius: 8px;
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
        margin-top: 8px;
        background-color: #fff;

        .selectedItemList {
          box-sizing: border-box;
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 8px;
          padding: 8px;
          border: 1px solid RGBA(0, 0, 0, 0.08);
          border-radius: 4px;
          min-height: 48px;
          background-color: rgba(0, 0, 0, 0.01);
          transition: all 0.3s ease;
          border-radius: 8px;

          &:hover {
            border-color: RGBA(0, 0, 0, 0.15);
          }

          .selectedItemRow {
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: 8px;
            border: 1px solid RGBA(0, 0, 0, 0.08);
            background-color: #FFFFFF;

            .selectedItemCol {
              display: flex;
              align-items: center;
              gap: 8px;
              width: 100%;

              .agentIcon {
                width: 24px;
                height: 24px;
                border-radius: 4px;
                object-fit: cover;
              }

              .selectedItemText {
                width: 200px;
                font-size: 14px;
                color: RGBA(0, 0, 0, 0.65);
                line-height: 20px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .selectedChildItemText {
                flex: 0 0 120px;
                font-size: 14px;
                color: RGBA(0, 0, 0, 0.65);
                line-height: 20px;
                font-weight: 500;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .selectedChildItemTextContent {
                flex: 1 1 auto;
                margin-left: 12px;
                margin-right: 12px;
                font-size: 14px;
                color: RGBA(0, 0, 0, 0.45);
                line-height: 20px;
                font-weight: 400;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .selectedItemName {
                font-size: 14px;
                color: RGBA(0, 0, 0, 0.65);
                line-height: 24px;
                font-weight: 500;
              }

              .selectedItemTextContent {
                flex: 1;
                font-size: 14px;
                color: RGBA(0, 0, 0, 0.32);
                line-height: 20px;
                font-weight: 400;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .deleteIcon {
                width: 24px;
                height: 24px;
                cursor: pointer;
                opacity: 0.65;
                transition: opacity 0.2s ease;

                &:hover {
                  opacity: 1;
                }
              }
            }
          }
        }
      }

      .titleRow {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;

        .titleContent {
          display: flex;
          flex-direction: column;
          gap: 6px;

          .subtitle {
            font-size: 14px;
            font-weight: 600;
            color: RGBA(0, 0, 0, 0.65);
          }

          .subtitlePlaceholder {
            font-size: 12px;
            font-weight: 400;
            color: RGBA(0, 0, 0, 0.35);
          }
        }

        .addApplication {
          width: 80px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid RGBA(0, 0, 0, 0.08);
          border-radius: 4px;
          cursor: pointer;
          background-color: transparent;
          padding: 0 2px;

          :global(.arco-icon) {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            flex-shrink: 0;
          }

          .operateText {
            font-size: 14px;
            font-weight: 400;
            color: RGBA(0, 0, 0, 0.65) !important;
            line-height: 20px;
            display: flex;
            align-items: center;
            height: 100%;
          }

          &:hover {
            background-color: RGBA(0, 0, 0, 0.02);
          }
        }
      }

      .chooseRowBox {
        cursor: pointer;
        height: 56px;
        width: calc(100% - 24px);
        border-radius: 4px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 12px;

        .chooseRowCol {
          display: flex;
          justify-content: flex-start;
          align-items: center;

          &.left {
            .chooseRowColImg {
              img {
                width: 32px;
                height: 32px;
              }
            }

            .chooseName {
              font-size: 14px;
              font-weight: 600;
              color: RGBA(0, 0, 0, 0.65);
              margin-left: 8px;
            }

            .chooseCount {
              font-size: 14px;
              font-weight: 400;
              color: RGBA(0, 0, 0, 0.35);
              margin-left: 24px;

              &.count {
                color: RGBA(68, 85, 242, 0.95);
              }
            }
          }

          &.right {
            .operateText {
              font-size: 12px;
              font-weight: 500;
              color: RGBA(0, 0, 0, 0.65) !important;
            }
          }
        }
      }

      .selectRowBox {
        height: 40px;
        width: 50%;
        border-radius: 4px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        display: flex;
        justify-content: space-between;
        align-items: center;

        :global(.arco-select-view) {
          background: transparent;
          height: 40px;
          width: 100%;
          padding-right: 6px;
          align-items: center;

          :global(.arco-select-suffix) {
            width: 20px;
          }
        }

        :global(.arco-cascader) {
          width: 100%;
        }

        :global(.arco-cascader-view) {
          background: transparent;
          height: 40px;
          width: 100%;
          padding-right: 6px;

          :global(.arco-cascader-suffix) {
            width: 20px;
          }

          :global(.arco-cascader-view-value) {
            align-content: center
          }
        }
      }

      .titleRow {
        margin-top: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        width: 100%;

        .titleContent {
          display: flex;
          flex-direction: column;
          gap: 6px;

          .subtitle {
            font-size: 14px;
            font-weight: 600;
            color: rgba(0, 0, 0, 0.65);
            display: block;
          }

        }

        .switchContainer {
          display: flex;
          align-items: center;
          gap: 8px;
          height: 40px;
          width: 76px;
          justify-content: center;
          border-radius: 8px;
          background-color: transparent;
          padding: 0 2px;

          :global(.arco-switch) {
            min-width: 40px;
            height: 24px;
          }

          :global(.arco-switch-checked) {
            background-color: RGBA(13, 41, 254, 0.95) !important;
          }
        }

        .addParamsBut {
          width: 76px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid RGBA(0, 0, 0, 0.08);
          border-radius: 8px;
          cursor: pointer;
          background-color: transparent;
          padding: 0 2px;

          :global(.arco-icon) {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            flex-shrink: 0;
          }

          .operateText {
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            display: flex;
            align-items: center;
            height: 100%;
          }

          &:hover {
            background-color: RGBA(0, 0, 0, 0.02);
          }
        }
      }

      .paramsContainer {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        width: 100%;
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.01)), #FFFFFF;
        border: 1px solid RGBA(0, 0, 0, 0.04);
        border-radius: 8px;
        flex: none;
        order: 1;
        align-self: stretch;
        flex-grow: 0;
        margin-top: 8px;

        .headerRow {
          width: 100%;
          height: 32px;
          display: flex;
          align-items: center;
          padding: 0 8px;
          gap: 8px;
          margin-bottom: 8px;

          .headerName {
            width: 183px;
            flex: 1 1 0;
            padding-left: 12px;
            font-size: 14px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
          }

          .headerDescription {
            width: 183px;
            flex: 1 1 0;
            font-size: 14px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
          }

          .headerType {
            width: 183px;
            flex: 1 1 0;
            font-size: 14px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
          }

          .headerSelect {
            width: 183px;
            flex: 1 1 0;
            font-size: 14px;
            font-weight: 400;
            color: rgba(0, 0, 0, 0.45);
          }
        }

        .selectedItemList {
          box-sizing: border-box;
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 8px;
          padding: 0 8px 8px 8px;
          border-radius: 4px;
          min-height: 48px;
          transition: all 0.3s ease;
          border-radius: 8px;

          &:hover {
            border-color: RGBA(0, 0, 0, 0.15);
          }

          .selectedItemRow {
            width: 100%;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            border-radius: 8px;

            :global(.arco-checkbox) {
              margin-right: 8px;
            }

            .selectedItemCol {
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              padding: 0px;
              gap: 8px;
              width: 183px;
              flex: 1 1 0;
            }

            :global(.arco-input-inner-wrapper) {
              height: 40px;
              border-radius: 8px;
              background-color: #fff;
              border: 1px solid RGBA(0, 0, 0, .08);
            }

            :global(.arco-select-view) {
              height: 40px;
              border-radius: 8px;
              background-color: #fff;
              border: 1px solid RGBA(0, 0, 0, .08);
              display: flex;
              align-items: center;
              padding: 0 12px;
            }

            .deleteIcon {
              width: 24px;
              height: 24px;
              cursor: pointer;
              opacity: 0.65;
              transition: opacity 0.2s ease;

              &:hover {
                opacity: 1;
              }
            }
          }
        }
      }
    }

    .rightContainer {
      width: calc(49% - 34px);
      height: 100%;
      padding-left: 24px;
      overflow-y: hidden;
      position: relative;
      display: flex;
      flex-direction: column;

      .title {
        margin-top: 8px;
        padding: 0 16px;
        width: calc(100% - 32px);
        height: 40px;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        background-color: RGBA(235, 235, 235, 1);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .textarea {
        width: 100%;
        height: calc(100vh - 340px);
        resize: none;
        padding: 10px;
        border-radius: 8px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        font-size: 14px;

        &::placeholder {
          color: RGBA(0, 0, 0, 0.35);
          font-weight: 400;
        }
      }

      .aiAssistantButton {
        border-radius: 20px;
        background-color: rgba(68, 85, 242, 0.1);
        color: rgba(68, 85, 242, 0.95);
        border: none;
        padding: 0 16px;
        height: 32px;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(68, 85, 242, 0.15);
        }
      }
    }
  }
}

:global(.agent-info-select-popup .arco-select-popup) {
  width: 100%;
  margin-right: 24px;
  border-radius: 8px;

  :global(.arco-select-popup-inner) {
    width: 300px;
    max-height: 300px;
    overflow-y: auto;

    /* 恢复滚动条样式 */
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    :global(.arco-select-option) {
      height: 40px;
      width: 300px;
      line-height: 40px;
      margin: 4px 8px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.02) !important;
        border-radius: 8px;
        transition: all 0.2s ease;
      }
    }
  }
}

/* Cascader下拉框样式 */
:global(.arco-cascader-popup) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  border-radius: 4px !important;
  border: 1px solid rgba(0, 0, 0, 0.08) !important;
}

:global(.arco-cascader-list) {
  max-height: 280px;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 4px;
}

:global(.arco-cascader-list-wrapper) {
  display: flex;
  align-items: flex-start;
  background-color: #fff;
  border-radius: 4px;
  padding: 4px 0;
  min-width: 120px;
}

:global(.arco-cascader-list-column) {
  min-width: 120px;
  border-right: 1px solid rgba(0, 0, 0, 0.08);
}

:global(.arco-cascader-list-item) {
  padding: 8px 12px;
  line-height: 20px;
  height: 36px;
  transition: background-color 0.2s;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
}

:global(.arco-cascader-list-item):hover {
  background-color: rgba(0, 0, 0, 0.03);
}

:global(.arco-cascader-list-select) {
  :global(.arco-cascader-list-selected-item) {
    background-color: rgba(68, 85, 242, 0.1);
  }
}

:global(.arco-cascader-list-empty) {
  color: rgba(0, 0, 0, 0.35);
  padding: 8px 12px;
  font-size: 14px;
  text-align: center;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Arco Design Tooltip 样式覆盖 */
:global {
  .arco-tooltip {
    .arco-tooltip-content {
      background-color: #fff !important;
      color: rgba(0, 0, 0, 0.65) !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
      padding: 8px 12px !important;
      border-radius: 8px !important;
      font-weight: 400 !important;
      font-size: 12px !important;
      line-height: 20px !important;
    }

    .arco-tooltip-arrow {
      background-color: #fff !important;
    }
  }
}

// ACP工具样式
.toolList {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
}

.toolRow {
  background: #F7F8FA;
  border-radius: 4px;
  margin-bottom: 4px;
  padding: 8px 12px;
}

.toolCol {
  display: flex;
  align-items: center;
}

.toolIcon {
  width: 16px;
  height: 16px;
  background-color: #E5E6EB;
  border-radius: 4px;
  margin-right: 8px;
}

.toolName {
  font-weight: 500;
  font-size: 12px;
  line-height: 20px;
  color: #1D2129;
  margin-right: 8px;
  white-space: nowrap;
}

.toolDescription {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #86909C;
  flex: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.deleteIcon {
  cursor: pointer;
}

.toggleButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  margin: 0px -8px -8px -8px;
  background: #ffffff;
  border-top: 1px solid RGBA(0, 0, 0, 0.08);
  border-radius: 0 0 6px 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 8px;
  box-sizing: border-box;
  height: 40px;

  &:hover {
    border-color: RGBA(0, 0, 0, 0.15);
    background: #fafbfc;
  }

  .toggleText {
    font-size: 12px;
    color: #86909c;
    font-weight: 400;
  }

  .toggleArrow {
    display: flex;
    align-items: center;
    transition: transform 0.2s ease;
  }

  &:hover {
    .toggleArrow {
      transform: translateY(1px);
    }
  }
}