import { IModalManagerChildrenProps } from '@/pages/knowledge/components/file-manager/src/components/modal-manager';
import { useTranslate } from '@/pages/knowledge/components/file-manager/src/hooks/common-hooks';
import { Button, Form, Input, Modal, Space, Typography } from 'antd';
import { useState } from 'react';
import styles from './index.module.less';
import FileIcon from '@/assets/knowledge/FileIcon.svg';
import IconCloseTag from '@/assets/close.svg';
import React from 'react';

const { Text } = Typography;

interface IProps extends Omit<IModalManagerChildrenProps, 'showModal'> {
    loading: boolean;
    onOk: (name: string, description?: string, tags?: string[]) => void;
    initialData?: {
        name: string;
        description?: string;
        tags?: string[] | string;
    };
}

const FileEditModal = ({ visible, hideModal, loading, onOk, initialData }: IProps) => {
    const [form] = Form.useForm();
    const { t } = useTranslate('common');
    const [formValues, setFormValues] = useState<{ name?: string; description?: string }>({});
    const [tags, setTags] = useState<string[]>([]);

    type FieldType = {
        name?: string;
        description?: string;
    };

    // 初始化表单数据
    React.useEffect(() => {
        if (visible && initialData) {
            form.setFieldsValue({
                name: initialData.name,
                description: initialData.description,
            });

            // 处理tags数据
            let tagsArray: string[] = [];
            if (typeof initialData.tags === 'string') {
                tagsArray = initialData.tags.split(',').filter(tag => tag.trim());
            } else if (Array.isArray(initialData.tags)) {
                tagsArray = initialData.tags;
            }
            setTags(tagsArray);
        }
    }, [visible, initialData, form]);

    const handleOk = async () => {
        const ret = await form.validateFields();
        const nonEmptyTags = tags.filter((tag) => tag.trim() !== '');
        return onOk(ret.name, ret.description, nonEmptyTags);
    };

    // 监听表单值变化
    const handleFormValuesChange = (changedValues: any, allValues: any) => {
        setFormValues(allValues);
    };

    // 处理Modal关闭
    const handleCancel = () => {
        form.resetFields();
        setFormValues({});
        setTags([]);
        hideModal();
    };

    // 判断确认按钮是否应该禁用
    const isConfirmDisabled = !formValues.name?.trim() || loading;

    // 新增标签输入项
    const showTagInput = () => {
        if (tags.length >= 3) {
            // 这里可以添加提示信息
            return;
        }
        setTags([...tags, '']);
    };

    // 处理标签变化
    const handleTagChange = (newTags: string[]) => {
        setTags(newTags);
    };

    // 自定义footer
    const customFooter = (
        <div className={styles.customFooter}>
            <Button
                className={styles.cancelButton}
                onClick={handleCancel}
            >
                取消
            </Button>
            <Button
                type="primary"
                className={styles.confirmButton}
                loading={loading}
                disabled={isConfirmDisabled}
                onClick={handleOk}
            >
                保存
            </Button>
        </div>
    );

    return (
        <Modal
            title={'编辑文件'}
            open={visible}
            onCancel={handleCancel}
            footer={customFooter}
            className={styles.fileEditModal}
        >
            <div className={styles.formContainer}>
                <Form
                    name="fileEditForm"
                    layout="vertical"
                    autoComplete="off"
                    form={form}
                    onValuesChange={handleFormValuesChange}
                >
                    <div className={styles.topSection}>
                        <div className={styles.iconSection}>
                            <FileIcon className={styles.fileIcon} />
                        </div>
                        <div className={styles.divider}></div>
                        <div className={styles.nameFormSection}>
                            <Form.Item<FieldType>
                                label={
                                    <span className={styles.labelWithRequired}>
                                        名称
                                        <span className={styles.requiredStar}>*</span>
                                    </span>
                                }
                                name="name"
                                required={false}
                                rules={[{ required: true, message: t('namePlaceholder') }]}
                            >
                                <Input placeholder="请输入" />
                            </Form.Item>
                        </div>
                    </div>

                    <div className={styles.bottomSection}>
                        <Form.Item<FieldType>
                            label="描述"
                            name="description"
                        >
                            <Input.TextArea
                                placeholder="请输入"
                                rows={4}
                                maxLength={100}
                            />
                        </Form.Item>

                        <Form.Item
                            className={styles.tagFormItem}
                        >
                            <div className={styles.tagHeader}>
                                <Space direction="vertical" size="small">
                                    <Text className={styles.tagLabel}>标签</Text>
                                    <Text className={styles.tagHint}>标签至多添加3个</Text>
                                </Space>
                                <Button
                                    className={styles.addTagBtn}
                                    onClick={showTagInput}
                                >
                                    添加
                                </Button>
                            </div>

                            {tags.length > 0 && (
                                <div className={styles.selectedItemList}>
                                    {tags.map((tag, index) => (
                                        <div key={`tag-${index}`} className={styles.selectedItemRow}>
                                            <Input
                                                autoFocus={tag === ''}
                                                value={tag}
                                                onChange={(e) => {
                                                    const value = e.target.value;
                                                    if (value && value.length > 20) {
                                                        return;
                                                    }
                                                    const newTags = [...tags];
                                                    newTags[index] = value;
                                                    handleTagChange(newTags);
                                                }}
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter') {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                    }
                                                }}
                                                placeholder={`标签 ${index + 1}`}
                                                suffix={
                                                    <IconCloseTag
                                                        className={styles.deleteIcon}
                                                        onClick={() => {
                                                            const newTags = [...tags];
                                                            newTags.splice(index, 1);
                                                            handleTagChange(newTags);
                                                        }}
                                                    />
                                                }
                                            />
                                        </div>
                                    ))}
                                </div>
                            )}
                        </Form.Item>
                    </div>
                </Form>
            </div>
        </Modal>
    );
};

export default FileEditModal; 