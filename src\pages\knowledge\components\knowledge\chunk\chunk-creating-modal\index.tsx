import EditTag from '../../components/edit-tag';
import { useFetchChunk } from '../../hooks/chunk-hooks';
import { IModalProps } from '../../interfaces/common';
import { IChunk } from '../../interfaces/database/knowledge';
import { DeleteOutlined } from '@ant-design/icons';
import { Divider, Form, Input, Modal, Space, Switch } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { useDeleteChunkByIds } from '../hooks';
import {
  transformTagFeaturesArrayToObject,
  transformTagFeaturesObjectToArray,
} from '../utils';
import { TagFeatureItem } from './tag-feature-item';

type FieldType = Pick<
  IChunk,
  'content_with_weight' | 'tag_kwd' | 'question_kwd' | 'important_kwd'
>;

interface kFProps {
  doc_id: string;
  chunkId: string | undefined;
  parserId: string;
}

const ChunkCreatingModal: React.FC<IModalProps<any> & kFProps> = ({
  doc_id,
  chunkId,
  hideModal,
  onOk,
  loading,
  parserId,
}) => {
  const [form] = Form.useForm();
  const [checked, setChecked] = useState(false);
  const { removeChunk } = useDeleteChunkByIds();
  const { data, isLoading: fetchLoading } = useFetchChunk(chunkId);
  const isTagParser = parserId === 'tag';
  
  const handleOk = useCallback(async () => {
    try {
      const values = await form.validateFields();
      console.log('🚀 ~ handleOk ~ values:', values);

      onOk?.({
        ...values,
        tag_feas: transformTagFeaturesArrayToObject(values.tag_feas),
        available_int: checked ? 1 : 0, // available_int
      });
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
  }, [checked, form, onOk]);

  const handleRemove = useCallback(() => {
    if (chunkId) {
      return removeChunk([chunkId], doc_id);
    }
  }, [chunkId, doc_id, removeChunk]);

  const handleCheck = useCallback(() => {
    setChecked(!checked);
  }, [checked]);

  useEffect(() => {
    // 只有当数据加载完成且有有效数据时才处理
    const chunkData = data?.data;
    if (!fetchLoading && chunkData?.code === 0) {
      const { available_int, tag_feas } = chunkData.data;
      form.setFieldsValue({
        ...(chunkData.data || {}),
        tag_feas: transformTagFeaturesObjectToArray(tag_feas),
      });

      setChecked(available_int !== 0);
    }
  }, [data, form, chunkId, fetchLoading]);

  return (
    <Modal
      title={`${chunkId ? '编辑' : '创建'}`}
      open={true}
      width={800}
      centered
      okText="保存"
      onOk={handleOk}
      onCancel={hideModal}
      okButtonProps={{ loading }}
      destroyOnHidden
    >
      <Form form={form} autoComplete="off" layout={'vertical'}>
        <Form.Item<FieldType>
          name="content_with_weight"
          rules={[{ required: true, message: '请输入值！' }]}
        >
          <Input.TextArea autoSize={{ minRows: 8, maxRows: 12 }} showCount maxLength={2048} />
        </Form.Item>

        {/* <Form.Item<FieldType> label={'关键词分析'} name="important_kwd">
          <EditTag></EditTag>
        </Form.Item>
        <Form.Item<FieldType>
          label={'问题'}
          name="question_kwd"
          tooltip={'如果有给定的问题，则块的嵌入将基于它们。'}
        >
          <EditTag></EditTag>
        </Form.Item>
        {isTagParser && (
          <Form.Item<FieldType> label={'标签'} name="tag_kwd">
            <EditTag></EditTag>
          </Form.Item>
        )}

        {!isTagParser && <TagFeatureItem></TagFeatureItem>} */}
      </Form>

      {/* {chunkId && (
        <section>
          <Divider></Divider>
          <Space size={'large'}>
            <Switch
              checkedChildren={'启用'}
              unCheckedChildren={'禁用'}
              onChange={handleCheck}
              checked={checked}
            />

            <span onClick={handleRemove}>
              <DeleteOutlined /> {'删除'}
            </span>
          </Space>
        </section>
      )} */}
    </Modal>
  );
};
export default ChunkCreatingModal;
