import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';
import { vitePluginForArco } from '@arco-plugins/vite-react';
import setting from './src/settings.json';
import tailwindcss from '@tailwindcss/vite';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  return {
    resolve: {
      alias: [{ find: '@', replacement: '/src' }],
    },
    plugins: [
      tailwindcss(),
      react(),
      svgr({
        include: [/\.svg$/],
      }),
      vitePluginForArco({
        theme: '@arco-themes/react-arco-pro',
        modifyVars: {
          'arcoblue-6': setting.themeColor, // 主题色
          'gray-3': '#f5f5f5', // 边框颜色
        },
      }),
    ],
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
      },
    },
    server: {
      proxy: {
        '/api': {
          target: 'https://bs-test-llm.ai4c.cn',
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(/^\/api/, '') // 移除/api前缀
        },
        '/v1': {
          target: 'http://**********:9380', 
          changeOrigin: true,
          ws: true,
          // rewrite: (path) => path.replace(/^\/v1/, '') // 移除/v1前缀
        }
      },
    },
  };
});
