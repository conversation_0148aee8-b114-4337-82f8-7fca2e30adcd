import defaultSettings from '../settings.json';
import { UserInfo } from '@/types/userType';
import { LlmModel, LlmModelConfig } from '@/types/llmModelType';


export interface GlobalState {
  settings?: typeof defaultSettings;
  userInfo?: UserInfo;
  userLoading?: boolean;
  breadcrumbMenuName?: Map<string, string>;
  applicationListMenuName: string;
  applicationDetailMenuName: string;
  agentListMenuName: string;
  agentDetailMenuName: string;
  createTimeSequenceCradMenuName: string;
  sdkDetailMenuName: string;
  usageDetailMenuName: string;
  pluginDetailMenuName: string;
  createModelMenuName: string;
  projectDetailMenuName: string;
  folderDetailMenuName: string;
  fileDetailMenuName: string;
  modelDetailMenuName: string;
  modelListMenuName: string;
  createModelConfigMenuName: string;
  modelConfigDetailMenuName: string;
  timeSequenceCardDetailMenuName: string;
  aiactionCreateMenuName: string;
  aiactionDetailMenuName: string;
  workflowDetailMenuName: string;
  acpServerDetailMenuName: string;
  selectedModel?: LlmModel | null;
  selectedModelConfig?: LlmModelConfig | null;
  selectedTemplateItems?: any[] | null;
  timeSequenceCardDetail?: any | null;
}

function getBreadcrumbMenuName (): Map<string, string> {
  const str = localStorage.getItem("breadcrumbMenuNameMap");
  console.log(str)
  if (str) {
    try {
      // 如果存在缓存，解析成 Map 格式
      const obj = JSON.parse(str);
      return new Map(Object.entries(obj));
    } catch (e) {
      console.error('Error parsing breadcrumbMenuNameMap from localStorage:', e);
    }
  }
  return new Map<string, string>();
}

const initialState: GlobalState = {
  settings: defaultSettings,
  userInfo: {} as UserInfo,
  breadcrumbMenuName: getBreadcrumbMenuName(),
  applicationListMenuName: '/application/list',
  applicationDetailMenuName: '/application/info',
  agentListMenuName: '/agent/list',
  agentDetailMenuName: '/agent/info',
  createTimeSequenceCradMenuName: '/timeSequenceCard/create',
  createModelMenuName: '/model/create',
  modelDetailMenuName: '/model/detail',
  modelListMenuName: '/model/list',
  sdkDetailMenuName: '/sdk/detail',
  usageDetailMenuName: '/usage/detail',
  pluginDetailMenuName: '/plugin/detail',
  projectDetailMenuName: '/knowledge/project',
  folderDetailMenuName: '/knowledge/project/folder',
  fileDetailMenuName: '/knowledge/project/folder/detail',
  createModelConfigMenuName: '/model/configcreate',
  modelConfigDetailMenuName: '/model/configdetail',
  timeSequenceCardDetailMenuName: '/timeSequenceCard/cards',
  aiactionCreateMenuName: '/aiaction/create',
  aiactionDetailMenuName: '/aiaction/detail',
  workflowDetailMenuName: '/workflow/detail',
  acpServerDetailMenuName: '/acp/servers',
  selectedModel: null,
  selectedModelConfig: null,
  selectedTemplateItems: null, 
  timeSequenceCardDetail: null,
};

export default function store(state = initialState, action) {
  console.log('Redux action:', action.type, action.payload);
  switch (action.type) {
    case 'update-settings': {
      const { settings } = action.payload;
      return {
        ...state,
        settings,
      };
    }
    case 'update-userInfo': {
      const { userInfo = initialState.userInfo, userLoading } = action.payload;
      return {
        ...state,
        userLoading,
        userInfo,
      };
    }
    case 'update-breadcrumb-menu-name': { // 添加新的case来处理breadcrumb更新
      const { breadcrumbMenuName } = action.payload;
      const obj = Object.fromEntries(breadcrumbMenuName);
      localStorage.setItem('breadcrumbMenuNameMap', JSON.stringify(obj));
      return {
        ...state,
        breadcrumbMenuName,
      };
    }
    case 'update-selected-model': {
      const { selectedModel } = action.payload;
      return {
        ...state,
        selectedModel,
      };
    }
    case 'update-selected-modelconfig': {
      const { selectedModelConfig } = action.payload;
      return {
        ...state,
        selectedModelConfig,
      };
    }
    case 'update-selected-template-items': { //处理所选模板数据更新
      const { selectedTemplateItems } = action.payload;
      return {
        ...state,
        selectedTemplateItems,
      };
    }
    case 'update-timeSequence-card-detail': { //处理时序卡片详情数据更新
      const { timeSequenceCardDetail } = action.payload;
      return {
        ...state,
        timeSequenceCardDetail,
      };
    }
    default:
      return state;
  }
}

