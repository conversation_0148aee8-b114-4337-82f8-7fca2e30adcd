import useLocale from '@/utils/useLocale';
import styles from './style/index.module.less';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import {
    Card,
    Image,
    Input,
    Message,
    Modal,
    Popover,
    Form,
    Button,
    Tooltip,
    Space,
    Spin,
} from '@arco-design/web-react';
import WorkflowIcon from '@/assets/application/workflow.svg';
import TopIcon from '@/assets/top.svg';
import { useEffect, useState, useCallback, useRef } from 'react';
import { Typography } from '@arco-design/web-react';
import {
    IconClose,
    IconMore,
} from '@arco-design/web-react/icon';
import { useNavigate } from 'react-router-dom';
import IconSearch from '@/assets/application/search.svg';
import IconKnowledge from '@/assets/knowledge/IconKnowledge.png';
import NotWorkflow from '@/assets/flow/NotWorkflow.svg';
import IconBuildingWorkflow from '@/assets/IconBuilding.svg';

import {
    getAgentTeamWorkflowList,
    isWorkflowExist,
    addAgentTeamWorkflow,
    deleteAgentTeamWorkflow,
} from '@/lib/services/workflow-service';

const { Text } = Typography;
const TextArea = Input.TextArea;
interface WorkflowLink {
    href: string;
    method: string;
    rel: string;
}
interface WorkflowListClass {
    createdAt: string;
    definitionId: string;
    isLatest: boolean;
    isPublished: boolean;
    isReadonly: boolean;
    links: WorkflowLink[];
    materializerName: string;
    name: string;
    toolVersion: string;
    version: string | number;
    id: string;
    description: string;
    popoverVisible?: boolean;
}

const FormItem = Form.Item;

function WorkflowList() {
    const navigate = useNavigate();
    const locale = useLocale();
    const [listData, setListData] = useState<WorkflowListClass[]>([]);
    const [form] = Form.useForm();
    const labelsState = Form.useFormState('labels', form) || {};

    // 添加分页相关状态
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [hasMore, setHasMore] = useState<boolean>(true);
    const [loading, setLoading] = useState<boolean>(false);
    const [searchValue, setSearchValue] = useState<string>('');
    const pageSize = 16;
    const cardBoxRef = useRef<HTMLDivElement>(null);

    const [count, setCount] = useState<number>(0);
    const [showBackToTop, setShowBackToTop] = useState(false);

    const [visible, setVisible] = useState(false);
    const [collectionName, setCollectionName] = useState<string>('');
    const [description, setDescription] = useState('');
    
    // 添加删除确认相关状态
    const [confirmDeleteVisible, setConfirmDeleteVisible] = useState(false);
    const [workflowToDelete, setWorkflowToDelete] = useState<{id?: string, definitionId?: string, name?: string} | null>(null);

    // 处理搜索输入变化
    const handleSearchChange = (value: string) => {
        setSearchValue(value);
        setCurrentPage(1);
        setHasMore(true);
        setListData([]);
    };

    // 加载更多数据
    const loadMoreData = useCallback(async () => {
        if (loading || !hasMore) return;

        setLoading(true);
        try {
            const nextPage = currentPage + 1;
            const params: any = {
                Pager: {
                    Page: nextPage,
                    Size: pageSize,
                },
            };

            if (searchValue) {
                params.SearchTerm = searchValue;
            }

            const response = await getAgentTeamWorkflowList(params);

            if (response.items && response.items.length > 0) {
                const newData: WorkflowListClass[] = response.items;
                // 合并新旧数据
                setListData((prevData) => [...prevData, ...newData]);
                setCurrentPage(nextPage);

                if (response.items.length < pageSize) {
                    setHasMore(false);
                }
            } else {
                setHasMore(false);
            }
        } catch (error) {
            Message.error({
                content: locale['menu.application.opreate.errMsg'],
            });
            setHasMore(false);
        } finally {
            setLoading(false);
        }
    }, [currentPage, loading, hasMore, pageSize, locale, searchValue]);

    // 滚动监听函数
    const handleScroll = useCallback(() => {
        if (!cardBoxRef.current) return;

        const container = cardBoxRef.current;
        const { scrollTop, scrollHeight, clientHeight } = container;

        // 处理回到顶部按钮显示
        setShowBackToTop(scrollTop > 300);

        // 处理加载更多数据
        if (!loading && hasMore && scrollHeight - scrollTop - clientHeight < 150) {
            loadMoreData();
        }
    }, [loading, hasMore, loadMoreData]);

    // 独立监测容器和卡片位置关系
    useEffect(() => {
        if (!cardBoxRef.current || loading || !hasMore) return;

        const checkCardPosition = () => {
            const container = cardBoxRef.current;
            if (!container) return;

            const lastCard = container.querySelector(
                `.${styles.customCard}:last-child`
            );
            if (lastCard) {
                const containerBottom = container.getBoundingClientRect().bottom;
                const lastCardBottom = lastCard.getBoundingClientRect().bottom;
                const distanceToBottom = containerBottom - lastCardBottom;

                // 如果最后一个卡片到容器底部的距离大于100px
                if (distanceToBottom > 100) {
                    loadMoreData();
                }
            }
        };

        // 创建 ResizeObserver 监听容器大小变化
        const resizeObserver = new ResizeObserver(() => {
            checkCardPosition();
        });

        // 监听容器大小变化
        resizeObserver.observe(cardBoxRef.current);

        // 初始检查
        checkCardPosition();

        return () => {
            resizeObserver.disconnect();
        };
    }, [loading, hasMore, loadMoreData, listData.length]);

    // 回到顶部
    const scrollToTop = useCallback(() => {
        if (!cardBoxRef.current) return;

        setShowBackToTop(false);

        cardBoxRef.current.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    }, []);

    useEffect(() => {
        fetchWorkflowList();
    }, [searchValue, pageSize, locale]);

    const fetchWorkflowList = useCallback(async () => {
        setLoading(true);
        try {
            const params: any = {
                Pager: {
                    Page: 0,
                    Size: pageSize,
                },
            };

            if (searchValue) {
                params.SearchTerm = searchValue;
            }

            const response = await getAgentTeamWorkflowList(params);

            setCount(response.totalCount);

            setListData(response.items);
            setCurrentPage(0);

            if (response.items.length < pageSize) {
                setHasMore(false);
            }
        } catch (error) {
            Message.error({
                content: locale['menu.application.opreate.errMsg'],
            });
            setHasMore(false);
        } finally {
            setLoading(false);
        }
    }, [pageSize, searchValue, locale]);

    // 单独处理滚动事件监听
    useEffect(() => {
        const container = cardBoxRef.current;
        if (!container) return;

        // 使用防抖处理滚动事件，避免频繁触发
        let scrollTimeout: NodeJS.Timeout;

        const scrollListener = () => {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }

            scrollTimeout = setTimeout(() => {
                handleScroll();
            }, 50);
        };

        container.addEventListener('scroll', scrollListener);

        return () => {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            container.removeEventListener('scroll', scrollListener);
        };
    }, [handleScroll]);
    

    // 处理删除确认
    const handleDeleteConfirm = (event, workflow) => {
        event.stopPropagation();
        setWorkflowToDelete(workflow);
        setConfirmDeleteVisible(true);
    };

    // 确认删除
    const handleConfirmDelete = async () => {
        if (workflowToDelete && workflowToDelete.definitionId) {
            try {
                await deleteAgentTeamWorkflow(workflowToDelete.definitionId);
                Message.success({
                    content: locale['menu.application.opreate.okMsg'],
                });
                // 删除成功后刷新列表
                await fetchWorkflowList();
            } catch (error) {
                Message.error({
                    content: locale['menu.application.opreate.errMsg'],
                });
                console.error(error);
            } finally {
                setConfirmDeleteVisible(false);
                setWorkflowToDelete(null);
            }
        }
    };

    // const comfirmDel = (event, workflowId) => {
    //     event.stopPropagation();
    //     const title = locale['menu.application.workflow.delete.title'];
    //     const content = locale['menu.application.agent.delete.content'];
    //     Modal.confirm({
    //         title: (
    //             <div style={{ textAlign: 'left', fontSize: '16px', fontWeight: 600 }}>
    //                 {title}
    //             </div>
    //         ),
    //         content: <div style={{ color: '#86909C' }}>{content}</div>,
    //         cancelText: locale['cancelBut'],
    //         okText: locale['deleteBut'],
    //         okButtonProps: {
    //             status: 'danger',
    //         },
    //         icon: null,
    //         footer: (
    //             <div style={{ textAlign: 'right' }}>
    //                 <Button
    //                     style={{ height: 40, width: 76 }}
    //                     onClick={() => Modal.destroyAll()}
    //                 >
    //                     {locale['cancelBut']}
    //                 </Button>
    //                 <Button
    //                     status="danger"
    //                     style={{ height: 40, width: 76, marginLeft: 8 }}
    //                     onClick={() => {
    //                         return new Promise<void>((resolve, reject) => {
    //                             deleteAgentTeamWorkflow(workflowId)
    //                                 .then(() => {
    //                                     resolve();
    //                                     Message.success({
    //                                         content: locale['menu.application.opreate.okMsg'],
    //                                     });
    //                                     // 删除成功后刷新列表
    //                                     fetchWorkflowList();
    //                                     Modal.destroyAll();
    //                                 })
    //                                 .catch((e) => {
    //                                     reject(e);
    //                                     Message.error({
    //                                         content: locale['menu.application.opreate.errMsg'],
    //                                     });
    //                                     console.error(e);
    //                                 });
    //                         });
    //                     }}
    //                 >
    //                     {locale['deleteBut']}
    //                 </Button>
    //             </div>
    //         ),
    //     });
    // };

    // 取消删除
    const handleCancelDelete = () => {
        setConfirmDeleteVisible(false);
        setWorkflowToDelete(null);
    };

    // 保留原有的comfirmDel函数以兼容旧代码，但修改为调用新函数
    const comfirmDel = (event, workflowId) => {
        const workflow = { definitionId: workflowId };
        handleDeleteConfirm(event, workflow);
    };

    const formItemLayout = {
        labelCol: {
            span: 4,
        },
        wrapperCol: {
            span: 20,
        },
    };

    const gotoInfoPage = (definitionId: string) => {
        history.push('/workflow/detail', { definitionId });
        // const url = `https://elasa-agent-team.ai4c.cn/workflows/definitions/${id}/edit`
        // window.open(url);
    };

    const renderContent = () => {
        if (loading && listData.length === 0) {
            return (
                <div className={styles.loadingContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        <div style={{ padding: 24 }}>
                            <Spin tip={locale['menu.loading'] || '加载中...'} />
                        </div>
                    </Space>
                </div>
            );
        }

        if (listData.length === 0) {
            return (
                <div className={styles.emptyContainer}>
                    <Space direction="vertical" size={16} style={{ display: 'flex', alignItems: 'center' }}>
                        {/* <NotWorkflow style={{ width: 80, height: 80 }} /> */}
                        <IconBuildingWorkflow style={{ width: 80, height: 80 }} />
                        <Text className={styles.emptyText}>
                            努力建设中，马上就来~
                        </Text>
                    </Space>
                </div>
            );
        }

        return (
            <>
                {listData.map((item, i) => (
                    <Card
                        key={item.id}
                        className={styles.customCard}
                        hoverable
                        onClick={() => {
                            gotoInfoPage(item.definitionId);
                        }}
                    >
                        <div className={styles.workflowInfo}>
                            <div className={styles.cardContent}>
                                <Space direction="vertical" size={8}>
                                    <Space className={styles.nameWrapper} size={12}>
                                        <span className={styles.icon}>
                                            <WorkflowIcon />
                                        </span>
                                        <Text className={styles.name}>{item.name}</Text>
                                    </Space>

                                    <div className={styles.description}>
                                        {item.description}
                                    </div>
                                </Space>
                            </div>

                            <div className={styles.cardFooter}>
                                <div className={styles.metaInfo}>
                                    <Text className={styles.metaText}>
                                        创建时间：{' '}
                                        {new Date(item.createdAt)
                                            .toLocaleDateString('zh-CN', {
                                                year: 'numeric',
                                                month: '2-digit',
                                                day: '2-digit',
                                            })
                                            .replace(/\//g, '/')}
                                    </Text>
                                </div>
                                <Popover
                                    trigger="hover"
                                    position="right"
                                    onVisibleChange={(visible) => {
                                        setListData((prevData) =>
                                            prevData.map((application) =>
                                                application.id === item.id
                                                    ? { ...application, popoverVisible: visible }
                                                    : application
                                            )
                                        );
                                    }}

                                    content={
                                        <Space className={styles.popoverContent} direction='vertical' size={'mini'}>
                                            <Button
                                                className={`${styles.actionBtn} ${styles.deleteBtn}`}
                                                onClick={(event) => {
                                                    handleDeleteConfirm(event, item);
                                                }}
                                            >
                                                {locale['menu.application.opreate.del']}
                                            </Button>
                                        </Space>
                                    }
                                >

                                    <Button
                                        className={styles.triggerBtn}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                        }}>
                                        <IconMore />
                                    </Button>
                                </Popover>
                            </div>
                        </div>
                    </Card>
                ))}
                {loading && listData.length > 0 && (
                    <div className={styles.loadingContainer} style={{ position: 'static', height: 'auto', padding: '20px 0' }}>
                        <Text className={styles.loadingText}>{locale['menu.loading'] || '加载中...'}</Text>
                    </div>
                )}
            </>
        );
    };

    const handleCreateWorkflow = async () => {
        try {
            setLoading(true);

            // 检查知识库是否存在
            const { isUnique } = await isWorkflowExist(collectionName);
            if (!isUnique) {
                Message.error('工作流名称已存在');
                return;
            }
            // 创建知识库
            await addAgentTeamWorkflow({
                model: {
                    definitionId: null,
                    description: description,
                    name: collectionName,
                    toolVersion: '*******',
                    variables: null,
                    inputs: null,
                    outputs: null,
                    outcomes: null,
                    customProperties: null,
                    isReadonly: false,
                    options: null,
                    root: {
                        id: '8a62657a9703effc',
                        type: 'Elsa.Flowchart',
                        version: 1,
                        name: 'Flowchart1',
                    },
                    links: null,
                    version: 1,
                    isLatest: true,
                    isPublished: false,
                    id: null,
                },
                publish: null,
            });

            // if (isSubscribed.current) {
            Message.success('创建成功');
            // 刷新列表
            await fetchWorkflowList();
            // 关闭弹窗并重置表单
            setVisible(false);
            form.resetFields();
            // }
        } catch (error) {
            // if (isSubscribed.current) {
            //     console.error('创建知识库失败:', error);
            //     Message.error('创建失败');
            // }
        } finally {
            // if (isSubscribed.current) {
            //     setLoading(false);
            // }
        }
    };

    return (
        <>
            <div className={styles.customContainer}>
                <RowComponent
                    style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        paddingBottom: 16,
                        borderBottom: '1px solid #f5f5f5',
                    }}
                >
                    <RowComponent>
                        <Button
                            type="primary"
                            className={styles.addBtn}
                            onClick={() => setVisible(true)}
                            disabled={true}
                        >
                            {locale['menu.application.opreate.create'] +
                                locale['menu.application.workflow']}
                        </Button>
                    </RowComponent>
                    <RowComponent className={styles.rowEndCenter}>
                        <Text className={styles.countAppText}>
                            {locale['menu.application.workflow.count']?.replace(
                                '{count}',
                                count
                            )}
                        </Text>
                        <Input
                            className={styles.searchBox}
                            prefix={<IconSearch />}
                            placeholder={
                                locale['menu.application.workflow.search.placeholder']
                            }
                            value={searchValue}
                            onChange={handleSearchChange}
                            allowClear
                        />
                    </RowComponent>
                </RowComponent>
                <div className={styles.customCardBox} ref={cardBoxRef}>
                    {renderContent()}
                </div>
            </div>
            <div
                className={`${styles.backToTop} ${!showBackToTop ? styles.hidden : ''}`}
                onClick={scrollToTop}
            >
                <TopIcon />
                <span>返回顶部</span>
            </div>

            <Modal
                visible={visible}
                onOk={handleCreateWorkflow}
                onCancel={() => {
                    setVisible(false);
                    form.resetFields();
                }}
                className={styles.customModal}
                confirmLoading={loading}
                footer={
                    <RowComponent className={styles.operateButGroup}>
                        <Button
                            className={[styles.cancelBut, styles.but]}
                            onClick={() => {
                                setVisible(false);
                                form.resetFields();
                            }}
                        >
                            {locale['cancelBut']}
                        </Button>
                        <Button
                            type="primary"
                            className={`${styles.createBut} ${styles.but} ${!collectionName ? styles.disabled : ''
                                }`}
                            onClick={handleCreateWorkflow}
                            loading={loading}
                            disabled={!collectionName}
                        >
                            {locale['menu.application.opreate.create']}
                        </Button>
                    </RowComponent>
                }
                style={{
                    borderRadius: '8px',
                    width: 640,
                }}
            >
                <RowComponent>
                    <Text className={styles.subtitleTitle}>
                        {locale['menu.application.opreate.create'] +
                            locale['menu.application.workflow']}
                    </Text>
                </RowComponent>
                <RowComponent style={{ marginTop: 16 }}>
                    <div className={styles.iconContainer}>
                        {/* 图标 */}
                        <div>
                            <Image src={IconKnowledge} />
                        </div>

                        <div className={styles.divider}></div>

                        {/* 命名 */}
                        <div className={styles.nameContainer}>
                            <RowComponent>
                                <Text className={styles.subtitle}>
                                    {locale['menu.application.info.basic.names']}
                                    <span style={{ color: '#D54941', marginLeft: 4 }}>*</span>
                                </Text>
                            </RowComponent>
                            <RowComponent style={{ width: '100%' }}>
                                <FormItem
                                    field="collection_name"
                                    validateTrigger={['onBlur', 'onChange']}
                                    style={{ marginBottom: 0 }}
                                >
                                    <div style={{ position: 'relative', width: '100%' }}>
                                        <TextArea
                                            placeholder={locale['menu.application.info.basic.names']}
                                            maxLength={50}
                                            value={collectionName}
                                            onChange={(value) => {
                                                setCollectionName(value);
                                                form.setFieldValue('collection_name', value);
                                            }}
                                            style={{
                                                backgroundColor: '#fff',
                                                border: '1px solid #e5e6eb',
                                                width: '100%',
                                                resize: 'none',
                                                height: '40px',
                                                borderRadius: '8px',
                                            }}
                                        />
                                    </div>
                                </FormItem>
                            </RowComponent>
                        </div>
                    </div>
                </RowComponent>

                {/* 描述 */}
                <RowComponent style={{ marginTop: 24 }}>
                    <Text className={styles.subtitle}>
                        {locale['menu.application.info.basic.descript']}
                    </Text>
                </RowComponent>
                <RowComponent style={{ marginTop: 8 }}>
                    <FormItem
                        field="description"
                        validateTrigger={['onBlur', 'onChange']}
                        style={{ marginBottom: 0 }}
                    >
                        <div style={{ position: 'relative', width: '100%' }}>
                            <TextArea
                                placeholder={
                                    locale['menu.application.info.basic.placeholder.descript']
                                }
                                maxLength={100}
                                value={description}
                                onChange={(value) => {
                                    setDescription(value);
                                    form.setFieldValue('description', value);
                                }}
                                style={{
                                    backgroundColor: '#fff',
                                    border: '1px solid #e5e6eb',
                                    width: '100%',
                                    resize: 'none',
                                    height: '64px',
                                    borderRadius: '8px',
                                }}
                            />
                            <div
                                style={{
                                    position: 'absolute',
                                    bottom: '8px',
                                    right: '8px',
                                    fontSize: '12px',
                                    color: 'rgba(0, 0, 0, 0.45)',
                                    pointerEvents: 'none',
                                }}
                            >
                                {description.length}/100
                            </div>
                        </div>
                    </FormItem>
                </RowComponent>
            </Modal>

            {/* 删除确认弹窗 */}
            <Modal
                visible={confirmDeleteVisible}
                title={locale['menu.application.workflow.delete.title']}
                onCancel={handleCancelDelete}
                closeIcon={<IconClose />}
                className={styles.confirmDeleteModal}
                maskClosable={false}
            >
                <div className={styles.modalContent}>
                    <Text className={styles.modalContentText}>
                        {locale['menu.application.agent.delete.content']}
                    </Text>
                </div>
                <div className={styles.modalFooter}>
                    <Space>
                        <Button
                            onClick={handleCancelDelete}
                            className={styles.cancelDeleteBtn}
                        >
                            {locale['cancelBut']}
                        </Button>
                        <Button
                            type="primary"
                            status="danger"
                            onClick={handleConfirmDelete}
                            className={styles.confirmDeleteBtn}
                        >
                            {locale['deleteBut']}
                        </Button>
                    </Space>
                </div>
            </Modal>
        </>
    );
}

export default WorkflowList;
