import axiosInstance from './interceptors';
import { endpoints } from './api-endpoints';

export interface KnowledgeListResponse {
  user: {
    id: string;
    user_name: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string | null;
    type: string;
    role: string;
    full_name: string;
    source: string;
    external_id: string;
    avatar: string;
    permissions: string[];
    agent_actions: string[];
    create_date: string;
    update_date: string;
    regionCode: string;
  };
  count: number;
  name: string;
  display_name: string;
  description: string;
  labels: string[];
  type: string;
  vector_store: {
    provider: string;
  };
  text_embedding: {
    provider: string;
    model: string;
    dimension: number;
  };
  create_date: string;
  create_user_id: string;
}

export interface KnowledgeFilesParams {
  page: number;
  size: number;
  start_id?: string | null;
  with_vector: boolean;
  included_payloads: string[];
  search_pairs: Array<{
    key: string;
    value: string;
  }>;
}

export interface KnowledgeFilesResponse {
  count: number;
  next_id: string;
  items: Array<{
    id: string;
    data: {
      text: string;
      dataSource: string;
      fileId: string;
      fileName: string;
      fileSource: string;
      answer?: string;
    };
  }>;
}

export interface KnowledgeCreateParams {
  collection_name: string;
  collection_type: string;
  dimension: number;
  provider: string;
  model: string;
  description: string;
  labels: string[];
}

export interface KnowledgeFileCreateParams {
  id?: string;
  text: string;
  data_source: string;
  payload: Record<string, unknown>;
}

export interface UploadFileParams {
  files: Array<{
    file_name: string;
    file_data: string;
    file_source: string;
  }>;
}

/**
 * 获取知识库列表
 * @param keyWord 关键字
 * @param labels 标签
 * @param type 类型
 * @returns Promise<KnowledgeListResponse[]>
 */
export async function getKnowledgeList(
  params: {
    keyWord?: string,
    labels?: string,
    type: string
  }
): Promise<KnowledgeListResponse[]> {
  try {
    const response = await axiosInstance.get(endpoints.vectorCollectionsUrl, {
      params: {
        KeyWord: params.keyWord,
        Labels: params.labels,
        type: params.type
      }
    });
    return response.data;
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    throw error;
  }
}

/**
 * 获取知识库内容(old)
 * @param id 知识库id
 * @returns Promise<KnowledgeFilesResponse>
 */
export async function getKnowledgeFiles(
  id: string,
  params: KnowledgeFilesParams
): Promise<KnowledgeFilesResponse> {
  try {
    const response = await axiosInstance.post(endpoints.vectorKnowledgePageListUrl.replace("{collection}", id), {
      page: params.page,
      size: params.size,
      start_id: params.start_id,
      with_vector: params.with_vector,
      included_payloads: params.included_payloads,
      search_pairs: params.search_pairs
    });
    return response.data;
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    throw error;
  }
}

export interface KnowledgeFilesParams2 {
  page?: number;
  size?: number;
  sort?: string | null;
  order?: string;
  offset?: number;
  returnTotal?: boolean;
  fileIds?: string[] | null;
  fileNames?: string[] | null;
  contentTypes?: string[] | null;
  fileSources?: string[] | null;
}

export interface KnowledgeFilesResponse2 {
  count: number;
  items: {
    file_id: string;
    file_name: string;
    file_source: string;
    file_extension: string;
    content_type: string;
    file_url: string;
    ref_data: {
      id: string;
      name: string;
      type: string;
      url: string;
      data: {
        property1: string;
        property2: string;
      };
    };
    user: {
      id: string;
      user_name: string;
      first_name: string;
      last_name: string;
      email: string;
      phone: string | null;
      type: string;
      role: string;
      full_name: string;
      source: string;
      external_id: string;
      avatar: string;
      permissions: string[];
      agent_actions: string[];
      create_date: string;
      update_date: string;
      regionCode: string;
    };
    create_date: string;

  }[];
}

/**
 * 获取知识库内容
 * @param id 知识库id
 * @returns Promise<KnowledgeFilesResponse>
 */
export async function getKnowledgeFiles2(
  id: string,
  params: KnowledgeFilesParams2
): Promise<KnowledgeFilesResponse2> {
  try {
    const response = await axiosInstance.post(endpoints.knowledgeDocumentPageListUrl.replace("{collection}", id), {
      page: params.page,
      size: params.size,
      sort: params.sort,
      order: params.order,
      offset: params.offset,
      returnTotal: params.returnTotal,
      fileIds: params.fileIds,
      fileNames: params.fileNames,
      contentTypes: params.contentTypes,
      fileSources: params.fileSources
    });
    return response.data;
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    throw error;
  }
}

/**
 * 删除知识库
 * @param params 查询参数
 * @returns Promise<KnowledgeListResponse>
 */
export async function deleteKnowledge(
  id: string,
): Promise<KnowledgeListResponse> {
  try {
    const response = await axiosInstance.delete(endpoints.vectorCollectionDeleteUrl.replace("{collection}", id));
    return response.data;
  } catch (error) {
    console.error('删除知识库失败:', error);
    throw error;
  }
}

/** 
 * 删除知识库vector文件
 * @param id 知识库id
 * @param fileId 文件id
 * @returns Promise<KnowledgeListResponse>
 */
export async function deleteVectorKnowledgeFile(
  id: string,
  fileId: string
): Promise<KnowledgeListResponse> {
  try {
    const response = await axiosInstance.delete(endpoints.vectorKnowledgeDeleteUrl.replace("{collection}", id).replace("{id}", fileId));
    return response.data;
  } catch (error) {
    console.error('删除知识库文件失败:', error);
    throw error;
  }
}

/** 
 * 删除知识库document文件
 * @param id 知识库id
 * @param fileId 文件id
 * @returns Promise<KnowledgeListResponse>
 */
export async function deleteDocumentKnowledgeFile(
  id: string,
  fileId: string
): Promise<KnowledgeListResponse> {
  try {
    const response = await axiosInstance.delete(endpoints.knowledgeDocumentDeleteUrl.replace("{collection}", id).replace("{fileId}", fileId));
    return response.data;
  } catch (error) {
    console.error('删除知识库文件失败:', error);
    throw error;
  }
}

/**
 * 判断知识库是否存在
 * @param params 查询参数
 * @returns Promise<KnowledgeListResponse>
 */
export async function isKnowledgeExist(
  id: string,
): Promise<KnowledgeListResponse> {
  try {
    const response = await axiosInstance.get(endpoints.vectorCollectionExistUrl.replace("{collection}", id));
    return response.data;
  } catch (error) {
    console.error('判断知识库是否存在失败:', error);
    throw error;
  }
}

/**
 * 创建知识库
 * @param params 查询参数
 * @returns Promise<KnowledgeListResponse>
 */
export async function createKnowledge(
  params: KnowledgeCreateParams
): Promise<KnowledgeListResponse> {
  try {
    const response = await axiosInstance.post(endpoints.vectorCollectionCreateUrl, {
      collection_name: params.collection_name,
      collection_type: params.collection_type,
      dimension: params.dimension,
      provider: params.provider,
      model: params.model,
      description: params.description,
      labels: params.labels
    });
    return response.data;
  } catch (error) {
    console.error('创建知识库失败:', error);
    throw error;
  }
}

/**
 * 获取知识库内容
 * @param id 知识库id
 * @returns Promise<KnowledgeFilesResponse>
 */
export async function getKnowledgeFilesDetail(
  id: string,
  fileId: string
): Promise<KnowledgeFilesResponse> {
  try {
    const response = await axiosInstance.get(endpoints.knowledgeDocumentDetailUrl.replace("{collection}", id).replace("{fileId}", fileId));
    return response.data;
  } catch (error) {
    console.error('获取知识库内容失败:', error);
    throw error;
  }
}

/**
 * 创建知识库文件
 * @param params 查询参数
 * @returns Promise<KnowledgeListResponse>
 */
export async function createKnowledgeFile(
  id: string,
  params: KnowledgeFileCreateParams
) {
  try {
    const response = await axiosInstance.post(endpoints.vectorKnowledgeCreateUrl.replace("{collection}", id), {
      text: params.text,
      data_source: params.data_source,
      payload: params.payload
    });
    return response.data;
  } catch (error) {
    console.error('创建知识库文件失败:', error);
    throw error;
  }
}

/**
 * 更新知识库文件内容
 * @param id 知识库id
 * @param params 更新参数
 * @returns Promise<KnowledgeListResponse>
 */
export async function updateKnowledgeFile(
  knowledgeName: string,
  params: KnowledgeFileCreateParams
) {
  try {
    const response = await axiosInstance.put(endpoints.vectorKnowledgeUpdateUrl.replace("{collection}", knowledgeName), {
      id: params.id,
      text: params.text,
      data_source: params.data_source,
      payload: params.payload
    });
    return response.data;
  } catch (error) {
    console.error('更新知识库文件失败:', error);
    throw error;
  }
}

/**
 * 上传文件 
 * @param id 知识库id
 * @param files 文件列表
 * @returns Promise<KnowledgeListResponse>
 */
export async function uploadFile(
  id: string,
  files: UploadFileParams
) {
  try {
    const response = await axiosInstance.post(
      endpoints.knowledgeDocumentUploadUrl.replace("{collection}", id),
      files,
      {
        timeout: 30000
      }
    );
    return response.data;
  } catch (error) {
    console.error('上传文件失败:', error);
    throw error;
  }
}

/**
 * 获取知识库标签 
 * @param type 知识库类型
 * @returns Promise<string[]>
 */
export async function getKnowledgeLabels(
  type: string
): Promise<string[]> {
  try {
    const response = await axiosInstance.get(endpoints.knowledgeLabelsUrl, {
      params: {
        collectionType: type
      }
    });
    return response.data;
  } catch (error) {
    console.error('获取知识库标签失败:', error);
    throw error;
  }
}

/**
 * 更新知识库
 * @param params 更新参数
 * @returns Promise<KnowledgeListResponse>
 */
export async function updateKnowledge(
  collection_name: string,
  data: {
    display_name: string,
    description: string,
    labels: string[],
  }
) {
  try {
    const response = await axiosInstance.put(endpoints.vectorCollectionUpdateUrl.replace("{collection}", collection_name), data);
    return response.data;
  } catch (error) {
    console.error('更新知识库失败:', error);
    throw error;
  }
}