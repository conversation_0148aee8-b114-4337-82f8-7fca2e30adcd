import { useState, useEffect, useRef } from 'react';
import {
  Card,
  Grid,
  Typography,
  Upload,
  Input,
  Select,
  InputNumber,
  Button,
  Space,
  Tag,
  Switch,
  Form,
  Message,
} from '@arco-design/web-react';
import IconUpload from '@/assets/model/IconPlus.svg';
import IconModel from '@/assets/model/ModelIocn.svg';
import IconTagClose from '@/assets/model/IconTagClose.svg';
import styles from './style/index.module.less';
import {
  getLlmTypeList,
  getLlmProviderList,
  createLlmModel,
} from '@/lib/services/llm-model-service';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { GlobalState } from '@/store/index';
import { LlmProviderResponse } from '@/types/llmModelType';

const { Row, Col } = Grid;
const { Title, Text } = Typography;
const FormItem = Form.Item;

// 创建一个不显示必填图标的 Form.Item
const FormItemWithoutRequiredMark = (props) => {
  const { children, ...rest } = props;
  return (
    <Form.Item
      {...rest}
      requiredSymbol={false} // 禁用 Arco Design 默认的必填标记
    >
      {children}
    </Form.Item>
  );
};

// 自定义的必填图标组件
const RequiredIcon = () => <span className={styles.requiredIcon}>*</span>;

// 自定义表单项标签组件
const CustomLabel = ({ label, required }) => {
  return (
    <Space>
      <span>{label}</span>
      {required && <RequiredIcon />}
    </Space>
  );
};

const ModeDetaillPage = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [tags, setTags] = useState([]);
  const [modelTypes, setModelTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [providers, setProviders] = useState<LlmProviderResponse[]>([]);
  const [providersLoading, setProvidersLoading] = useState(false);
  const selectedModel = useSelector(
    (state: GlobalState) => state.selectedModel
  ); // 从全局状态中获取 selectedModel
  const isViewMode = !!selectedModel; // 判断是否为查看详情模式
  const [isEditing, setIsEditing] = useState(false);
  // 添加状态跟踪表单是否有变化
  const [formChanged, setFormChanged] = useState(false);
  // 保存原始标签数据以便比较
  const [originalTags, setOriginalTags] = useState([]);
  // 保存原始表单值的引用
  const originalFormValuesRef = useRef({});
  const [selectedModelType, setSelectedModelType] = useState(null); // 添加状态跟踪选中的模型类型

  // 根据供应商ID获取供应商对象
  const getProviderById = (providerId: string) => {
    return providers.find(provider => provider.id === providerId);
  };

  // 初始化表单数据
  useEffect(() => {
    if (selectedModel) {
      const initialValues = {
        id: selectedModel.id || '',
        modelId: selectedModel.modelId || '',
        provider: selectedModel.llmProviderId || '',
        name: selectedModel.name || '',
        deployName: selectedModel.deployName || '',
        modelVersion: selectedModel.version || '',
        apiVersion: selectedModel.apiVersion || '',
        modelType: selectedModel.type || 0,
        multiModal: selectedModel.multiModal || false,
        allowGenerateImage: selectedModel.imageGeneration || false,
        promptCost: selectedModel.promptCost || 0,
        completionCost: selectedModel.completionCost || 0,
        embeddingDimension: selectedModel.dimension || '',
        maxTokens: selectedModel.maxTokens || '',
        temperature: selectedModel.temperature || 1,
        isEnabled: selectedModel.isEnabled || false,
        createdTime: new Date(selectedModel.createdTime).toLocaleString() || '',
        updatedTime: new Date(selectedModel.updatedTime).toLocaleString() || '',
      };

      form.setFieldsValue(initialValues);
      // 保存原始表单值以便比较
      originalFormValuesRef.current = JSON.parse(JSON.stringify(initialValues));
      // 设置初始模型类型
      setSelectedModelType(selectedModel.type || 0);

      setTags(selectedModel.tags || []);
      setOriginalTags(selectedModel.tags || []);
    } else {
      form.resetFields();
      originalFormValuesRef.current = {};
      setTags([]);
      setOriginalTags([]);
      setSelectedModelType(null);
    }
  }, [form, selectedModel]);

  // 处理表单值变化
  const handleFormValuesChange = (changedValues, allValues) => {
    if (isEditing) {
      // 如果模型类型发生变化，更新状态以触发重新渲染
      if ('modelType' in changedValues) {
        setSelectedModelType(changedValues.modelType);
      }
      // 任何值发生变化时，设置表单已修改状态
      const formHasChanged =
        JSON.stringify(allValues) !==
        JSON.stringify(originalFormValuesRef.current);
      const tagsHasChanged =
        JSON.stringify(tags) !== JSON.stringify(originalTags);
      setFormChanged(formHasChanged || tagsHasChanged);
    }
  };

  // 重置编辑状态
  const resetEditState = () => {
    setIsEditing(true);
    // 更新原始表单值引用
    originalFormValuesRef.current = JSON.parse(
      JSON.stringify(form.getFieldsValue())
    );
    // 重置表单变化状态
    setFormChanged(false);
  };

  // 添加模型标签
  const handleAddTag = () => {
    if (tags.length >= 3) {
      Message.warning('标签最多添加3个');
      return;
    }
    // 直接添加一个空标签，然后自动聚焦到它
    const newTags = [...tags, ''];
    setTags(newTags);
    // 检查标签是否发生变化
    setFormChanged(JSON.stringify(newTags) !== JSON.stringify(originalTags));
  };

  // 处理标签变化
  const handleTagChange = (newTags) => {
    setTags(newTags);
    // 检查标签是否发生变化
    setFormChanged(JSON.stringify(newTags) !== JSON.stringify(originalTags));
  };

  // 处理标签删除
  const handleTagClose = (index) => {
    if (!isEditing) return;
    const newTags = [...tags];
    newTags.splice(index, 1);
    setTags(newTags);
    // 检查标签是否发生变化
    setFormChanged(JSON.stringify(newTags) !== JSON.stringify(originalTags));
  };

  // 更新模型
  // const handleSubmit = async () => {
  //   // 如果不是编辑模式，则进入编辑模式
  //   if (!isEditing) {
  //     resetEditState();
  //     return;
  //   }

  //   // 如果表单没有变化，不执行保存
  //   if (!formChanged) {
  //     return;
  //   }

  //   try {
  //     // 验证表单
  //     const values = await form.validate();

  //     // 过滤掉空标签
  //     const nonEmptyTags = tags.filter((tag) => tag.trim() !== '');

  //     // 以表单数据为基础，补充 selectedModel 中的数据
  //     const requestData = {
  //       ...values,
  //       id: values.id !== undefined ? values.id : selectedModel.id || '',
  //       // provider字段存储的是供应商ID，直接用作llmProviderId
  //       llmProviderId:
  //         values.provider !== undefined
  //           ? values.provider
  //           : selectedModel.llmProviderId || '',
  //       modelId:
  //         values.modelId !== undefined
  //           ? values.modelId
  //           : selectedModel.modelId || '',
  //       icon:
  //         values.icon !== undefined ? values.icon : selectedModel.icon || '',
  //       name:
  //         values.name !== undefined
  //           ? values.name
  //           : selectedModel.name || values.deployName || '',
  //       deployName:
  //         values.deployName !== undefined
  //           ? values.deployName
  //           : selectedModel.deployName || '',
  //       version:
  //         values.modelVersion !== undefined
  //           ? values.modelVersion
  //           : selectedModel.version || '',
  //       apiVersion:
  //         values.apiVersion !== undefined
  //           ? values.apiVersion
  //           : selectedModel.apiVersion || '',
  //       type:
  //         values.modelType !== undefined
  //           ? values.modelType
  //           : selectedModel.type || 0,
  //       tags: nonEmptyTags, // 使用过滤后的标签数组
  //       multiModal:
  //         values.multiModal !== undefined
  //           ? values.multiModal
  //           : selectedModel.multiModal || false,
  //       imageGeneration:
  //         values.allowGenerateImage !== undefined
  //           ? values.allowGenerateImage
  //           : selectedModel.imageGeneration || false,
  //       promptCost:
  //         values.promptCost !== undefined
  //           ? values.promptCost
  //           : selectedModel.promptCost || 0,
  //       completionCost:
  //         values.completionCost !== undefined
  //           ? values.completionCost
  //           : selectedModel.completionCost || 0,
  //       dimension:
  //         values.embeddingDimension !== undefined && values.embeddingDimension !== ''
  //           ? parseInt(values.embeddingDimension, 10)
  //           : null,
  //       maxTokens:
  //         values.maxTokens !== undefined && values.maxTokens !== ''
  //           ? values.maxTokens
  //           : null,
  //       temperature:
  //         values.temperature !== undefined
  //           ? parseFloat(values.temperature)
  //           : selectedModel.temperature || 1,
  //       isEnabled:
  //         values.isEnabled !== undefined
  //           ? values.isEnabled
  //           : selectedModel.isEnabled || false,
  //     };

  //     console.log('提交的 requestData:', requestData);
  //     // 调用更新接口
  //     const response = await createLlmModel(requestData);

  //     if (response) {
  //       Message.success('模型更新成功');
  //       // 退出编辑模式
  //       setIsEditing(false);
  //       // 更新原始表单值和标签数据
  //       originalFormValuesRef.current = JSON.parse(
  //         JSON.stringify(form.getFieldsValue())
  //       );
  //       setOriginalTags([...tags]);
  //       setFormChanged(false);
  //       // 更新全局状态
  //       dispatch({
  //         type: 'update-selected-model',
  //         payload: { selectedModel: { ...selectedModel, ...requestData } },
  //       });
  //       navigate('/model', { state: { fromModel: true } });
  //     } else {
  //       Message.error('模型更新失败');
  //     }
  //   } catch (error) {
  //     console.log('更新模型失败:', error);
  //     Message.error('模型更新失败，请检查输入或稍后重试');
  //   }
  // };

  // 调用 getLlmTypeList 获取模型类型
  useEffect(() => {
    const fetchModelTypes = async () => {
      setLoading(true);
      try {
        const response = await getLlmTypeList();
        if (response) {
          setModelTypes(response);
        } else {
          Message.error('获取模型类型失败');
        }
      } catch (error) {
        console.error('Failed to fetch model types:', error);
        Message.error('获取模型类型失败，请稍后重试');
      } finally {
        setLoading(false);
      }
    };

    fetchModelTypes();
  }, []); // 空依赖数组，确保只在组件挂载时调用

  // 调用 getLlmProviderList 获取供应商列表
  useEffect(() => {
    const fetchProviders = async () => {
      setProvidersLoading(true);
      try {
        const response = await getLlmProviderList();
        if (response) {
          setProviders(response);

          // 如果已有选中的模型，并且供应商数据已加载，确保供应商显示正确
          if (selectedModel && selectedModel.llmProviderId) {
            // 由于供应商数据已更新，不需要特别处理，Select组件会基于value自动显示正确的选项
          }
        } else {
          Message.error('获取供应商列表失败');
        }
      } catch (error) {
        console.error('Failed to fetch providers:', error);
        Message.error('获取供应商列表失败，请稍后重试');
      } finally {
        setProvidersLoading(false);
      }
    };

    fetchProviders();
  }, [selectedModel]);

  // 取消或返回
  const handleCancel = () => {
    dispatch({
      type: 'update-selected-model',
      payload: { selectedModel: null },
    }); // 清空 selectedModel
    navigate('/model', { state: { fromModel: true } });
  };

  return (
    <div className={styles.addModelContainer}>
      <Title
        heading={4}
        className={styles.addModelTitle}
        style={{ marginBottom: '24px' }}
      >
        模型详情
      </Title>
      <Form
        form={form}
        autoComplete="off"
        layout="vertical"
        initialValues={{
          multiModal: false,
          allowGenerateImage: false,
          isEnabled: false,
        }}
        requiredSymbol={false}
        onValuesChange={handleFormValuesChange}
      >
        <Row gutter={[40, 0]}>
          <Col span={12}>
            <Card bordered={false} style={{ padding: '0px' }}>
              <Row className={styles.addModelHeader}>
                <div className={styles.iconAndName}>
                  <div className={styles.addModelIconWrapper}>
                    <IconModel className={styles.addModelIcon} />
                  </div>
                  <div className={styles.divider}></div>
                  <div className={styles.nameFormContainer}>
                    <FormItemWithoutRequiredMark
                      label="对外展示名称"
                      field="name"
                      style={{ marginBottom: '0px' }}
                    >
                      <Input
                        placeholder="请输入"
                        className={styles.nameInput}
                        disabled={!isEditing}
                      />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="模型部署名称" required={true} />}
                    field="deployName"
                    rules={[{ required: true, message: '请输入模型部署名称' }]}
                  >
                    <Input
                      placeholder="请输入"
                      className={styles.deployNameInput}
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark label="模型ID" field="modelId">
                    <Input
                      placeholder="请输入"
                      className={styles.idInput}
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <FormItemWithoutRequiredMark className={styles.tagFormItem}>
                <Row
                  justify="space-between"
                  align="center"
                  style={{ marginBottom: '8px' }}
                >
                  <Space direction="vertical" size={'mini'}>
                    <Text
                      style={{
                        color: '#5c5c5c',
                        fontWeight: '600',
                        fontSize: '14px',
                      }}
                    >
                      标签
                    </Text>
                    <Text className={styles.labelExact}>标签最多添加3个</Text>
                  </Space>
                  {isEditing && (
                    <Button
                      className={styles.addTagBtn}
                      onClick={handleAddTag}
                    >
                      添加
                    </Button>
                  )}
                </Row>
                {tags.length > 0 && (
                  <div className={styles.selectedItemList}>
                    {tags.map((tag, index) => (
                      <Row
                        key={`tag-${index}`}
                        className={styles.selectedItemRow}
                      >
                        <Input
                          autoFocus={tag === ''}
                          value={tag}
                          onChange={(value) => {
                            if (value && value.length > 20) {
                              return;
                            }
                            const newTags = [...tags];
                            newTags[index] = value;
                            handleTagChange(newTags);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              e.stopPropagation();
                            }
                          }}
                          placeholder={`标签 ${index + 1}`}
                          disabled={!isEditing}
                          className={!isEditing ? styles.disabledTagInput : ''}
                          suffix={
                            <IconTagClose
                              className={`${styles.deleteIcon} ${!isEditing ? styles.disabledDeleteIcon : ''}`}
                              onClick={
                                !isEditing
                                  ? undefined
                                  : () => handleTagClose(index)
                              }
                            />
                          }
                        />
                      </Row>
                    ))}
                  </div>
                )}
              </FormItemWithoutRequiredMark>
              <div>
                <div className={styles.switchContainer}>
                  <Space
                    className={styles.switchLeftContent}
                    direction="vertical"
                    size={4}
                  >
                    <Text className={styles.Switchlabel}>是否启用</Text>
                    <Text className={styles.SwitchTitle}>模型控制</Text>
                  </Space>
                  <div className={styles.switchRightContent}>
                    <FormItemWithoutRequiredMark
                      field="isEnabled"
                      triggerPropName="checked"
                      style={{ margin: 0 }}
                    >
                      <Switch disabled={!isEditing} />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
                <div className={styles.switchContainer}>
                  <Space
                    className={styles.switchLeftContent}
                    direction="vertical"
                    size={4}
                  >
                    <Text className={styles.Switchlabel}>
                      是否允许发送图片/视频
                    </Text>
                    <Text className={styles.SwitchTitle}>模型控制</Text>
                  </Space>
                  <div className={styles.switchRightContent}>
                    <FormItemWithoutRequiredMark
                      field="multiModal"
                      triggerPropName="checked"
                      style={{ margin: 0 }}
                    >
                      <Switch disabled={!isEditing} />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
                <div className={styles.switchContainer}>
                  <Space
                    className={styles.switchLeftContent}
                    direction="vertical"
                    size={4}
                  >
                    <Text className={styles.Switchlabel}>是否允许生成图片</Text>
                    <Text className={styles.SwitchTitle}>模型控制</Text>
                  </Space>
                  <div className={styles.switchRightContent}>
                    <FormItemWithoutRequiredMark
                      field="allowGenerateImage"
                      triggerPropName="checked"
                      style={{ margin: 0 }}
                    >
                      <Switch disabled={!isEditing} />
                    </FormItemWithoutRequiredMark>
                  </div>
                </div>
              </div>
            </Card>
          </Col>

          <Col span={12} className={styles.Card}>
            <Card bordered={false}>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="供应商" required={true} />}
                    field="provider"
                    rules={[{ required: true, message: '请选择供应商' }]}
                  >
                    <Select
                      placeholder="请选择"
                      loading={providersLoading}
                      disabled={!isEditing}
                    >
                      {providers.map((provider) => (
                        <Select.Option key={provider.id} value={provider.id}>
                          {provider.provider}
                        </Select.Option>
                      ))}
                    </Select>
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={<CustomLabel label="模型类型" required={true} />}
                    field="modelType"
                    rules={[{ required: true, message: '请选择模型类型' }]}
                  >
                    <Select
                      placeholder="请选择"
                      loading={loading}
                      disabled={!isEditing}
                    >
                      {modelTypes &&
                        Object.entries(modelTypes).map(([key, value]) => (
                          <Select.Option key={value} value={value}>
                            {key}
                          </Select.Option>
                        ))}
                    </Select>
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label="模型版本"
                    field="modelVersion"
                  >
                    <Input
                      placeholder="请输入"
                      className={styles.modelVersionInput}
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label="API版本"
                    field="apiVersion"
                  >
                    <Input
                      placeholder="请输入"
                      className={styles.apiVersionInput}
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={
                      <CustomLabel label="输入成本（每千Token/元）" required={true} />
                    }
                    field="promptCost"
                    rules={[{ required: true, message: '请输入输入成本（每千Token/元）' }]}
                  >
                    <InputNumber
                      min={0}
                      placeholder="0"
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={
                      <CustomLabel label="输出成本（每千Token/元）" required={true} />
                    }
                    field="completionCost"
                    rules={[{ required: true, message: '请输入输出成本（每千Token/元）' }]}
                  >
                    <InputNumber
                      min={0}
                      placeholder="0"
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={
                      <CustomLabel 
                        label="最大输出（Token数）" 
                        required={selectedModelType === 1 || selectedModelType === 2} 
                      />
                    }
                    field="maxTokens"
                    className={styles.maxTokens}
                    rules={[
                      {
                        validator: (value, callback) => {
                          const modelType = selectedModelType;
                          // 如果是Chat(2)或Text(1)类型
                          if (modelType === 1 || modelType === 2) {
                            if (value === undefined || value === null || value === '') {
                              callback('当模型类型为Chat或Text时，最大输出Token数必填');
                            } else if (value === 0) {
                              callback('最大输出Token数不能为0');
                            } else {
                              callback();
                            }
                          } else {
                            // 其他类型，如果有值则不能为0
                            if (value !== undefined && value !== null && value !== '' && value === 0) {
                              callback('最大输出Token数不能为0');
                            } else {
                              callback();
                            }
                          }
                        }
                      }
                    ]}
                  >
                    <InputNumber
                      min={0}
                      placeholder={
                        selectedModelType === 1 || selectedModelType === 2 
                          ? "请输入（必填）" 
                          : "请输入（可选）"
                      }
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label={
                      <CustomLabel 
                        label="嵌入维度" 
                        required={selectedModelType === 4} 
                      />
                    }
                    field="embeddingDimension"
                    rules={[
                      {
                        validator: (value, callback) => {
                          const modelType = selectedModelType;
                          // 如果是Embedding(4)类型
                          if (modelType === 4) {
                            if (value === undefined || value === null || value === '') {
                              callback('当模型类型为Embedding时，嵌入维度必填');
                            } else {
                              const numValue = parseInt(value, 10);
                              if (isNaN(numValue) || numValue <= 0) {
                                callback('嵌入维度不能为0');
                              } else {
                                callback();
                              }
                            }
                          } else {
                            // 其他类型，如果有值则不能为0
                            if (value !== undefined && value !== null && value !== '') {
                              const numValue = parseInt(value, 10);
                              if (isNaN(numValue) || numValue <= 0) {
                                callback('嵌入维度不能为0');
                              } else {
                                callback();
                              }
                            } else {
                              callback();
                            }
                          }
                        }
                      }
                    ]}
                  >
                    <Input
                      placeholder={
                        selectedModelType === 4 
                          ? "请输入（必填）" 
                          : "请输入（可选）"
                      }
                      className={styles.embeddingDimensionInput}
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark 
                    label={<CustomLabel label="Temperature" required={true} />}
                    field="temperature"
                    rules={[
                      { required: true, message: '请输入Temperature' },
                      { 
                        validator: (value, callback) => {
                          if (value !== undefined && value !== null && value !== '') {
                            const numValue = parseFloat(value);
                            if (isNaN(numValue) || numValue <= 0 || numValue > 1) {
                              callback('Temperature必须是0-1之间的数字，且不能为0');
                            } else {
                              callback();
                            }
                          } else {
                            callback();
                          }
                        }
                      }
                    ]}
                  >
                    <InputNumber
                      min={0}
                      max={1}
                      step={0.1}
                      placeholder="请输入（0.1-1）"
                      // className={styles.temperatureInput}
                      disabled={!isEditing}
                    />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label="创建时间"
                    field="createdTime"
                  >
                    <Input className={styles.deployNameInput} disabled />
                  </FormItemWithoutRequiredMark>
                </Col>
                <Col span={12}>
                  <FormItemWithoutRequiredMark
                    label="更新时间"
                    field="updatedTime"
                  >
                    <Input className={styles.deployNameInput} disabled />
                  </FormItemWithoutRequiredMark>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
        <Space
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            paddingTop: '16px',
            borderTop: '1px solid #f5f5f5',
            marginTop: '16px'
          }}
          size={8}
        >
          <Button
            type="secondary"
            onClick={handleCancel}
            className={styles.cancelButton}
          >
            返回
          </Button>
          {/* <Button
            type="primary"
            onClick={handleSubmit}
            className={`${styles.submitButton} ${isEditing && !formChanged ? styles.submitButtonDisabled : ''
              }`}
            disabled={isEditing && !formChanged}
          >
            {isEditing ? '保存' : '编辑'}
          </Button> */}
        </Space>
      </Form>
    </div>
  );
};

export default ModeDetaillPage;
