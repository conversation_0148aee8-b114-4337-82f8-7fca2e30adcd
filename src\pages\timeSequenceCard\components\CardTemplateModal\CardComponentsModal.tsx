import React from 'react';
import { Modal, Card, Typography, Space, Button, Grid, Tag } from '@arco-design/web-react';
// import IconSmileComponent from '@/assets/application/IconSmileComponent.svg';
// import IconFontComponent from '@/assets/application/IconFontComponent.svg';
// import IconSoundComponent from '@/assets/application/IconSoundComponent.svg';
// import IconLinkComponent from '@/assets/application/IconLinkComponent.svg';
// import IconVideoComponent from '@/assets/application/IconVideoComponent.svg';
// import IconTagComponent from '@/assets/application/IconTagComponent.svg';
// import IconFileComponent from '@/assets/application/IconFileComponent.svg';
// import IconImageComponent from '@/assets/application/IconImageComponent.svg';
// import IconTitleComponent from '@/assets/application/IconTitleComponent.svg';
import IconPlay from '@/assets/application/IconPlay.svg';
import IconImagePlus from '@/assets/application/IconImagePlus.svg';
import IconVideoPlus from '@/assets/application/IconVideoPlus.svg';
import IconSoundPlus from '@/assets/application/IconSoundPlus.svg';
import IconSmilePlus from '@/assets/application/IconSmilePlus.svg';
import IconClose from '@/assets/application/IconClose.svg';
import IconSmile from '@/assets/application/IconSmile.svg';
import IconTitle from '@/assets/application/IconTitle.svg';
import IconFont from '@/assets/application/IconFont.svg';
import IconTag from '@/assets/application/IconTag.svg';
import IconFile from '@/assets/application/IconFile.svg';
import IconImage from '@/assets/application/IconImage.svg';
import IconVideo from '@/assets/application/IconVideo.svg';
import IconSound from '@/assets/application/IconSound.svg';
import IconLink from '@/assets/application/IconLink.svg';
import styles from './style/cardComponentsModal.module.less';

const { Title, Text } = Typography;
const { Row, Col } = Grid;

// Define template types
const componentTypes = {
    ICON: 'icon',
    TITLE: 'title',
    TEXT: 'text',
    TAG: 'tag',
    FILE: 'file',
    IMAGE: 'image',
    VIDEO: 'video',
    AUDIO: 'audio',
    LINK: 'link'
};

// 生成组件ID的辅助函数
const generateComponentId = (type, templateItems = []) => {
    // 统计当前类型在模板中已有的数量
    const existingCount = templateItems.filter(item => item.type === type).length;
    const nextNumber = existingCount + 1;
    return `${type}_${nextNumber.toString().padStart(3, '0')}`;
};

// Define templates with their structure items
const cardTemplates = [
    {
        id: 'text-card',
        name: '文本组件',
        useCustomPreview: true,
        items: [
            {
                id: 'title_001',
                type: componentTypes.TITLE,
                name: '标题',
                description: '标题组件',
                icon: <IconTitle />,
            },
            {
                id: 'text_001',
                type: componentTypes.TEXT,
                name: '文字',
                description: '文本组件',
                icon: <IconFont />,
            },
            {
                id: 'tag_001',
                type: componentTypes.TAG,
                name: '标签',
                description: '标签组件',
                icon: <IconTag />,
            },
        ],
    },
    {
        id: 'image-card',
        name: '图片组件',
        useCustomPreview: true,
        items: [
            {
                id: 'image_001',
                type: componentTypes.IMAGE,
                name: '图片',
                description: '图片组件',
                icon: <IconImage />,
            },
            {
                id: 'tag_001',
                type: componentTypes.TAG,
                name: '标签',
                description: '标签组件',
                icon: <IconTag />,
            },
        ],
    },
    {
        id: 'video-card',
        name: '视频组件',
        useCustomPreview: true,
        items: [
            {
                id: 'video_001',
                type: componentTypes.VIDEO,
                name: '视频',
                description: '视频组件',
                icon: <IconVideo />,
            },
            {
                id: 'tag_001',
                type: componentTypes.TAG,
                name: '标签',
                description: '标签组件',
                icon: <IconTag />,
            },
        ],
    },
    {
        id: 'audio-card',
        name: '音频组件',
        useCustomPreview: true,
        items: [
            {
                id: 'audio_001',
                type: componentTypes.AUDIO,
                name: '音频',
                description: '音频组件',
                icon: <IconSound />,
            },
            {
                id: 'tag_001',
                type: componentTypes.TAG,
                name: '标签',
                description: '标签组件',
                icon: <IconTag />,
            },
        ],
    },
    {
        id: 'file-card',
        name: '文档组件',
        useCustomPreview: true,
        items: [
            {
                id: 'title_001',
                type: componentTypes.TITLE,
                name: '标题',
                description: '标题组件',
                icon: <IconTitle />,
            },
            {
                id: 'file_001',
                type: componentTypes.FILE,
                name: '文档',
                description: '文档组件',
                icon: <IconFile />,
            },
            {
                id: 'text_001',
                type: componentTypes.TEXT,
                name: '文字',
                description: '文本组件',
                icon: <IconFont />,
            },
        ],
    },
    {
        id: 'link-card',
        name: '链接组件',
        useCustomPreview: true,
        items: [
            {
                id: 'icon_001',
                type: componentTypes.ICON,
                name: '图标',
                description: '图标组件',
                icon: <IconSmile />,
            },
            {
                id: 'title_001',
                type: componentTypes.TITLE,
                name: '标题',
                description: '标题组件',
                icon: <IconTitle />,
            },
            {
                id: 'text_001',
                type: componentTypes.TEXT,
                name: '文字',
                description: '文本组件',
                icon: <IconFont />,
            },
            {
                id: 'link_001',
                type: componentTypes.LINK,
                name: '链接',
                description: '链接组件',
                icon: <IconLink />,
            },
        ],
    },
];

interface CardTemplateModalProps {
    visible: boolean;
    onClose: () => void;
    onUseTemplate: (items: any[]) => void;
    existingItems?: any[]; // 添加已有结构项的参数
}

const CardComponentsModal: React.FC<CardTemplateModalProps> = ({
    visible,
    onClose,
    onUseTemplate,
    existingItems = [] // 默认为空数组
}) => {
    // Function to generate unique IDs for each item based on existing items
    const prepareTemplateItems = (items) => {
        // 创建一个临时数组，包含已有的项和即将添加的项，用于计算正确的ID编号
        const tempItems = [...existingItems];
        
        return items.map(item => {
            // 基于当前的tempItems生成唯一ID
            const uniqueId = generateComponentId(item.type, tempItems);
            
            const newItem = {
                ...item,
                id: uniqueId
            };
            
            // 将新项添加到临时数组，以便下一个item的ID计算正确
            tempItems.push(newItem);
            
            return newItem;
        });
    };

    const handleUseTemplate = (template) => {
        const preparedItems = prepareTemplateItems(template.items);
        onUseTemplate(preparedItems);
        onClose();
    };

    // Function to render custom preview for each template type
    const renderTemplatePreview = (template) => {
        switch (template.id) {
            case 'text-card':
                return (
                    <div className={styles.textCardTemplatePreview}>
                        <Space direction='vertical' size={4}>
                            <div className={styles.previewTitle}>这是标题</div>
                            <div className={styles.previewParagraph}>这是一段文字</div>
                        </Space>
                        <div className={styles.tagList}>
                            <Tag>这是标签</Tag>
                            <Tag>这是标签</Tag>
                            <Tag>这是标签</Tag>
                        </div>
                    </div>
                );
            case 'image-card':
                return (
                    <div className={styles.imageCardTemplatePreview}>
                        <div className={styles.imagePlaceholder}>
                            <div className={styles.placeholderIcon}>
                                <IconImagePlus />
                            </div>
                        </div>
                        <div className={styles.tagList}>
                            <Tag>这是标签</Tag>
                            <Tag>这是标签</Tag>
                            <Tag>这是标签</Tag>
                        </div>
                    </div>
                );
            case 'video-card':
                return (
                    <div className={styles.videoCardTemplatePreview}>
                        <div className={styles.videoPlaceholder}>
                            <div className={styles.placeholderIcon}>
                                <IconVideoPlus />
                            </div>
                            <div className={styles.videoPlayBtn}>
                                <IconPlay />
                            </div>
                        </div>
                        <div className={styles.tagList}>
                            <Tag>这是标签</Tag>
                            <Tag>这是标签</Tag>
                            <Tag>这是标签</Tag>
                        </div>
                    </div>
                );
            case 'audio-card':
                return (
                    <div className={styles.audioCardTemplatePreview}>
                        <div className={styles.audioPlaceholder}>
                            <div className={styles.placeholderIcon}>
                                <IconSoundPlus />
                            </div>
                            <div className={styles.audioPlayBtn}>
                                <IconPlay />
                            </div>
                        </div>
                        <div className={styles.tagList}>
                            <Tag>这是标签</Tag>
                            <Tag>这是标签</Tag>
                            <Tag>这是标签</Tag>
                        </div>
                    </div>
                );
            case 'file-card':
                return (
                    <div className={styles.fileCardTemplatePreview}>
                        <Space direction='vertical' size={4}>
                            <div className={styles.previewTitle}>这是标题</div>
                            <div className={styles.previewParagraph}>这是一段文字</div>
                        </Space>
                        <div className={styles.fileItem}>
                            <Text>文档.pdf</Text>
                        </div>
                    </div>
                );
            case 'link-card':
                return (
                    <div className={styles.linkCardTemplatePreview}>
                        <Space direction='vertical' size={8}>
                            <div className={styles.icon}>
                                <IconSmilePlus />
                            </div>
                            <div className={styles.previewTitle}>这是标题</div>
                            <div className={styles.previewParagraph}>这是一段文字</div>
                            <div className={styles.linkItem}>
                                <Text>.com</Text>
                            </div>
                        </Space>
                    </div>
                );
            default:
                return null;
        }
    };

    return (
        <Modal
            visible={visible}
            title="选择组件"
            onCancel={onClose}
            footer={null}
            className={styles.templateModal}
            style={{ width: 1200 }}
            closeIcon={<IconClose />}
        >
            <div className={styles.templateContainer}>
                <Row gutter={[24, 24]}>
                    {cardTemplates.map((template) => (
                        <Col span={8} key={template.id}>
                            <Card title={template.name} className={styles.templateCard}>
                                <div className={styles.templatePreview}>
                                    {renderTemplatePreview(template)}
                                </div>
                                <div className={styles.templateHover}>
                                    <Button
                                        type="primary"
                                        size="large"
                                        onClick={() => handleUseTemplate(template)}
                                        className={styles.useButton}
                                    >
                                        添加
                                    </Button>
                                </div>
                            </Card>
                        </Col>
                    ))}
                </Row>
            </div>
        </Modal>
    );
};

export default CardComponentsModal;