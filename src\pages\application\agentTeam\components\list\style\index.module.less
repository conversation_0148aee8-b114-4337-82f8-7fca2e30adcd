/* 隐藏滚动条 */
::-webkit-scrollbar {
  display: none;
  /* 对于基于 WebKit 的浏览器 */
}

/* 对于IE和Edge */
html {
  -ms-overflow-style: none;
  /* IE 和 Edge */
}

/* 对于Firefox */
* {
  scrollbar-width: none;
  /* Firefox */
}

:global(.arco-popover-content) {
  padding: 0;
  background: #FFFFFF;
}

:global(.arco-popover-content.arco-popover-content-right) {
  padding: 8px;
  width: 160px;
  border: none;
  border: 1px solid #f5f5f5;
  border-radius: 8px;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

  :global(.arco-btn) {
    background-color: transparent;
    width: 144px;
    text-align: left;
  }
}

.actionBtn {
  padding: 4px 8px;
  border-radius: 4px;
  width: 144px;
  display: flex;
  justify-content: flex-start;
}

.enableBtn {
  color: #2ba471 !important;
  background: #ffffff !important;

  &:hover {
    background: #f7fcfa !important;
  }
}

.disableBtn {
  color: #d54941 !important;
  background-color: #ffffff !important;

  &:hover {
    background: #fef8f8 !important;
  }
}

.deleteBtn {
  background-color: #ffffff !important;
  color: #333333 !important;

  &:hover {
    background: #f5f5f5 !important;
  }
}

:global(.agent-team-select-popup .arco-select-popup) {
  width: 200px;
  margin-right: 24px;
  border-radius: 8px;

  :global(.arco-select-popup-inner) {
    width: 200px;
    max-height: 400px;
    overflow-y: auto;

    /* 恢复滚动条样式 */
    &::-webkit-scrollbar {
      display: block;
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    :global(.arco-select-option) {
      height: 40px;
      width: 178px;
      line-height: 40px;
      margin: 4px 8px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.02) !important;
        border-radius: 8px;
        transition: all 0.2s ease;
      }
    }
  }
}

.customModal {
  padding: 8px;

  :global(.arco-modal-header) {
    border: none;

    :global(.arco-modal-title) {
      text-align: left !important;
      font-size: 18px;
      font-weight: 600;
      color: RGBA(0, 0, 0, 0.8);
    }
  }

  :global(.arco-modal-content) {
    padding-top: 14px;

    :global(.arco-form) {
      :global(.arco-form-item) {
        display: flex;
        flex-direction: column;

        .label {
          font-size: 14px;
          font-weight: 600;
          display: inline-block;
          padding-bottom: 6px;
          color: RGBA(0, 0, 0, 0.65);
        }

        .subLabel {
          font-size: 14px;
          font-weight: 400;
          color: RGBA(0, 0, 0, 0.35);
          padding-left: 6px;
        }

        .subLabel2 {
          font-size: 12px;
          font-weight: 400;
          color: RGBA(0, 0, 0, 0.35);
          padding: 0 0 6px 0;
        }

        :global(.arco-form-label-item) {
          text-align: left !important;
          flex-basis: auto !important;

          :global(.arco-form-item-symbol) {
            display: none !important;
          }
        }

        :global(.arco-input) {
          background-color: #ffffff !important;
          border-radius: 4px;
          border: 1px solid RGBA(0, 0, 0, 0.08);
          height: 40px;
          color: RGBA(0, 0, 0, 0.8);
          font-size: 14px;

          &::placeholder {
            font-size: 14px;
            font-weight: 400;
            color: RGBA(0, 0, 0, 0.16);
          }
        }
      }

      :global(.arco-btn) {
        height: 40px;
        width: 80px;
        border-radius: 4px;
        border: 1px solid RGBA(0, 0, 0, 0.08);
        color: RGBA(0, 0, 0, 0.65);

        span {
          font-size: 14px;
          font-weight: 400;
          color: RGBA(0, 0, 0, 0.65);
        }
      }

      .customBlock {
        width: 100%;

        :global(.arco-form-item) {
          margin-bottom: 8px;

          :global(.arco-input-tag) {
            background-color: #ffffff !important;
            border-radius: 4px;
            border: 1px solid RGBA(0, 0, 0, 0.08);
            height: 40px;

            :global(.arco-input-tag-view) {
              display: flex;
              justify-content: space-between;
              align-items: center;
              height: 40px;
            }

            :global(.arco-input-tag-inner) {
              input {
                color: RGBA(0, 0, 0, 0.8);
                font-size: 14px;

                &::placeholder {
                  font-size: 14px;
                  font-weight: 400;
                  color: RGBA(0, 0, 0, 0.16);
                }
              }
            }

            :global(.arco-input-tag-suffix) {
              border-radius: 12px;
              color: RGBA(0, 0, 0, 0.35);
              width: 24px;
              height: 24px;
              display: flex;
              justify-content: center;
              align-items: center;
              padding-right: 0 !important;
              font-size: 13px;

              &:hover {
                background-color: RGBA(0, 0, 0, 0.02);
                color: RGBA(0, 0, 0, 0.65);
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }

  :global(.arco-modal-footer) {
    border: none;
  }
}

.customContainer {
  display: flex;
  flex-direction: column;
  padding-top: 8px;
  will-change: transform;
  -webkit-overflow-scrolling: touch;
  position: relative;

  .loadingContainer {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    z-index: 1;

    .loadingText {
      margin-top: 12px;
      color: RGBA(0, 0, 0, 0.5);
      font-size: 14px;
    }
  }

  .emptyContainer {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;

    svg {
      width: 120px;
      height: 120px;
      margin-bottom: 16px;
    }

    .emptyText {
      font-size: 14px;
      color: RGBA(0, 0, 0, 0.45);
      line-height: 22px;
    }
  }

  .butFont {
    font-size: 14px;
    font-weight: 500;
  }

  .blueBut {
    background-color: RGBA(68, 85, 242, 0.95);
    color: #ffffff;
    border-radius: 8px;
    width: 104px;
    height: 40px;

    &:hover {
      color: #ffffff !important;
      background-color: RGBA(71, 86, 223, 1) !important;
    }
  }

  .normalBut {
    background-color: RGBA(68, 85, 242, 0.04);
    color: RGBA(68, 85, 242, 0.95);
    border-radius: 4px;
    width: 104px;
    height: 40px;

    &:hover {
      color: RGBA(68, 85, 242, 0.95) !important;
      background-color: RGBA(240, 241, 254, 1) !important;
    }
  }

  .importApp {
    background-color: #ffffff;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    padding: 8px 24px;
    height: 40px;
    color: #5c5c5c;
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;

    &:hover {
      background-color: #00000005 !important;
      border: 1px solid #00000014 !important;
      color: #000000A3 !important;
    }
  }

  .rowEndCenter {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .countAppText {
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: #adadad;
    margin-right: 8px;
  }

  .searchBox {
    width: 240px;
    height: 40px;
    border-radius: 8px;
    outline: none;
    border: 1px solid #f5f5f5;
    font-size: 14px;
    font-weight: 400;
    background-color: #ffffff;
    margin-right: 8px;
    transition: all 0.2s ease;

    &:hover {
      background-color: RGBA(0, 0, 0, 0.02);
    }

    &:focus {
      background-color: #ffffff;
    }

    :global(.arco-input-inner-wrapper) {
      background-color: transparent;
      border-radius: 8px;
    }

    input::placeholder {
      color: #d6d6d6;
    }
  }

  .selectBox {
    height: 40px;
    width: 160px;
    margin-right: 2px;
    border-radius: 8px;
    border: 1px solid #f5f5f5;
    transition: all 0.2s ease;

    &:hover {
      background-color: RGBA(0, 0, 0, 0.02);
    }

    &:focus-within {
      background-color: #ffffff;
    }

    :global(.arco-select-view) {
      height: 40px;
      width: 160px;
      border-radius: 8px;
      background-color: transparent;
      display: flex;
      align-items: center;
    }

    :global(.arco-select-view-value) {
      color: #d6d6d6;
      display: flex;
      align-items: center;
    }

    :global(.arco-select-view-value[title='']) {
      color: #d6d6d6;
    }
  }

  .customCardBox {
    height: calc(100vh - 160px);
    overflow-y: auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    padding-top: 16px;
    margin-bottom: 100px;
    position: relative;
    will-change: transform;
    align-content: flex-start;

    @media screen and (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media screen and (max-width: 992px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media screen and (max-width: 576px) {
      grid-template-columns: repeat(1, 1fr);
    }

    .rowStartCenter {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    .customCard {
      margin-bottom: 0;
      margin-right: 0;
      min-width: 240px;
      height: 240px;
      border-radius: 8px;
      border: 1px solid #f5f5f5;
      cursor: pointer;
      transition: all 0.3s;
      padding: 0;
      overflow: hidden;

      &:hover {
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
      }

      :global(.arco-card-body) {
        padding: 0;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
      }

      .agentTeamInfo {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        padding: 20px 24px;
        box-sizing: border-box;
        position: relative;

        @media screen and (max-width: 1400px) {
          padding: 16px 20px;
        }

        @media screen and (max-width: 1200px) {
          padding: 14px 16px;
        }

        .cardContent {
          flex: 1;
          display: flex;
          flex-direction: column;
        }

        .nameWrapper {
          display: flex;
          align-items: center;

          .icon {
            width: 48px;
            height: 48px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0;
          }

          .name {
            color: #333333;
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            word-break: break-word;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
          }
        }

        .description {
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
          color: #5c5c5c;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
          margin-top: 4px;
        }

        .labelWrapper {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-top: 4px;
        }

        .cardFooter {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 16px;
          position: relative;

          .metaInfo {
            flex: 1;
            overflow: hidden;
            display: flex;
            justify-content: space-between;

            .statusInfo {
              display: flex;
              align-items: center;
              width: 100%;
              gap: 8px;
            }

            .createTime {
              display: flex;
              align-items: center;
            }

            .metaText {
              font-weight: 400;
              font-size: 12px;
              line-height: 20px;
              color: #adadad;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .statusWrapper {
              display: flex;
              align-items: center;
              padding: 2px 8px;
              border-radius: 4px;
              flex-shrink: 0;
              width: fit-content;
              opacity: 1;
            }

            .statusTag {
              font-size: 12px;
              font-weight: 500;
              line-height: 20px;
              display: inline-block;
              white-space: nowrap;
            }
          }

          .triggerBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            width: 32px;
            height: 32px;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

            &:hover {
              background: #fafafa;
            }
          }
        }
      }

      .folderIcon {
        max-width: 48px;
        max-height: 48px;
        display: flex;
        align-items: center;
        padding-top: 0;

        img {
          max-width: 48px;
          max-height: 48px;
        }
      }

      .groupName {
        color: RGBA(0, 0, 0, 0.8);
        font-size: 20px;
        font-weight: 600;
      }

      .tag {
        padding: 2px 6px;
        border-radius: 4px;
        border: 1px solid #ebebeb;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        margin-right: 8px;
        color: #5c5c5c;
      }

      .footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        position: absolute;
        bottom: 40px;
        left: 24px;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        width: calc(100% - 48px);
        z-index: 1;

        .statusContainer {
          display: flex;
          gap: 8px;
          align-items: center;
          min-width: 0;
          transition: all 0.2s ease;
          position: absolute;
          right: 0;
          opacity: 1;
          transform: translateX(0);
          width: 100%;

          .metaText {
            font-size: 12px;
            font-weight: 400;
            color: RGBA(0, 0, 0, 0.32);
            line-height: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            margin-right: 8px;
          }

          .statusWrapper {
            display: flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 4px;
            flex-shrink: 0;
          }

          .statusTag {
            font-size: 12px;
            font-weight: 500;
            line-height: 20px;
            display: inline-block;
            white-space: nowrap;
          }
        }

        .hoverContainer {
          display: flex;
          gap: 8px;
          align-items: center;
          min-width: 0;
          transition: all 0.2s ease;
          position: absolute;
          right: 0;
          opacity: 0;
          transform: translateX(10px);
          width: 100%;

          .metaText {
            font-size: 12px;
            font-weight: 400;
            color: RGBA(0, 0, 0, 0.32);
            line-height: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
            margin-right: 8px;
          }

          .iconMoreContainer {
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s ease;
            flex-shrink: 0;

            &:active {
              background-color: rgba(0, 0, 0, 0.04);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            &.active {
              background-color: rgba(0, 0, 0, 0.04);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }

      &:hover {
        .statusContainer {
          opacity: 0;
          transform: translateX(-10px);
        }

        .hoverContainer {
          opacity: 1;
          transform: translateX(0);
        }
      }
    }
  }
}

.profileTag {
  display: inline-block;
  padding: 2px 8px;
  background-color: #ffffff;
  border: 1px solid #ebebeb;
  border-radius: 4px;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #5c5c5c;
  cursor: default;
}

.tooltipProfilesContainer {
  padding: 4px;
  max-width: 200px;
}

.tooltipProfileItem {
  padding: 4px 8px;
  margin-bottom: 4px;
  background-color: #ffffff;
  border: 1px solid RGBA(0, 0, 0, 0.15);
  border-radius: 4px;
  font-size: 12px;
  color: RGBA(0, 0, 0, 0.65);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:last-child {
    margin-bottom: 0;
  }
}

.uploadContainer {
  padding: 24px;

  :global(.arco-upload-drag) {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed RGBA(0, 0, 0, 0.15);
    border-radius: 8px;
    background-color: RGBA(0, 0, 0, 0.02);
    transition: all 0.3s;

    &:hover {
      border-color: RGBA(68, 85, 242, 0.95);
      background-color: RGBA(68, 85, 242, 0.04);
    }
  }
}

.title {
  font-size: 18px;
  color: RGBA(0, 0, 0, 0.8);
  padding: 24px 0px 16px 24px;
  margin: 0;
  font-weight: 500;
}

.uploadContent {
  text-align: center;
}

.uploadText {
  font-size: 16px;
  font-weight: 500;
  color: RGBA(0, 0, 0, 0.65);
  margin-bottom: 8px;
}

.uploadIcon {
  width: 24px;
  height: 24px;
}

.uploadHint {
  font-size: 14px;
  color: RGBA(0, 0, 0, 0.45);
}

.uploadModal {
  :global(.arco-modal) {
    padding: 0;
  }

  :global(.arco-modal-content) {
    padding: 0;
    height: 272px;
    display: flex;
    flex-direction: column;
  }

  :global(.arco-modal-close-icon) {
    top: 28px;
    right: 24px;
    font-size: 18px;
    color: #000000A3;
  }
}

.uploadContainer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 0px;

  :global(.arco-upload-drag) {
    width: 100%;
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: RGBA(0, 0, 0, 0.02);
    transition: all 0.3s;

    &:hover {
      background-color: RGBA(0, 0, 0, 0.02);
    }
  }
}

.uploadContent {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.uploadTitle {
  font-size: 16px;
  font-weight: 500;
  color: rgb(68, 85, 242);
  margin-bottom: 4px;
}

.uploadSubtitle {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}


.chooseModal {
  :global(.arco-modal) {
    padding: 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 32px;
    gap: 24px;
    background: #FFFFFF;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.12);
    border-radius: 8px;
  }

  :global(.arco-modal-content) {
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  :global(.arco-modal-close-icon) {
    top: 32px;
    right: 32px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.65);
  }

  .warningBox {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 12px;
    gap: 4px;
    width: 576px;
    margin-top: 0px;
    margin-left: 24px;
    background: linear-gradient(90deg, rgba(68, 85, 242, 0.04) 0%, rgba(68, 85, 242, 0) 100%), #4455F20A; // 修改背景颜色
    border-radius: 8px;

    .warningIcon {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
    }

    .warningText {
      width: 592px;
      font-style: normal;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
      color: rgba(0, 0, 0, 0.65);
      display: flex;
      align-items: center;
    }
  }

  .fileInfo {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 12px;
    margin-left: 24px;
    margin-top: 8px;
    gap: 16px;
    width: 592px;
    height: 72px;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.01)), #FFFFFF;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 8px;

    .zipIcon {
      width: 48px;
      height: 48px;
      margin-left: 12px;
    }

    .fileDetails {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      padding: 0;
      gap: 4px;

      .fileName {
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.8);
      }

      .fileSize {
        font-family: 'PingFang SC';
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }

  .appInfo {
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
    margin-top: 8px;
    margin-left: 24px;
    gap: 24px;
    width: 592px;
    height: 72px;
    background: #FFFFFF;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 8px;

    .appContent {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0;
      gap: 16px;

      .appIcon {
        width: 48px;
        height: 48px;
        margin-left: 12px;
      }

      .appDetails {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 4px;

        .appTitle {
          display: flex;
          flex-direction: row;
          align-items: center;
          gap: 16px;

          .appName {
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.8);
          }

          .tags {
            display: flex;
            gap: 4px;

            .tag {
              padding: 2px 6px;
              background: rgba(0, 0, 0, 0.02);
              border-radius: 4px;
              font-size: 12px;
              color: rgba(0, 0, 0, 0.65);
            }
          }
        }

        .appMeta {
          font-size: 12px;
          line-height: 20px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
  }

  .tagetTitle {
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    margin-left: 24px;
    margin-top: 16px;
    color: rgba(0, 0, 0, 0.8);
  }

  .optionsTitle {
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    margin-left: 24px;
    margin-top: 24px;
    color: rgba(0, 0, 0, 0.8);
  }

  .optionsContainer {
    display: flex;
    padding: 0;
    gap: 8px;
    margin-left: 24px;
  }

  .optionBox {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 8px;
    padding: 12px;
    gap: 16px;
    width: 292px;
    height: 64px;
    border-radius: 8px;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.08);


    &:hover {
      background: rgba(0, 0, 0, 0.02);
    }

    &.selected {
      background: linear-gradient(0deg, rgba(68, 85, 242, 0.04), rgba(68, 85, 242, 0.04)), #FFFFFF;
      border: 1px solid #4455F2;
    }

    .optionRadio {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 1px solid rgba(0, 0, 0, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 12px;

      .radioSelected {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: rgb(68, 85, 242);
      }

      .radioNormal {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }
    }

    .optionContent {
      display: flex;
      flex-direction: column;
      gap: 4px;
      margin-left: 8px;

      .optionTitle {
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: rgba(0, 0, 0, 0.8);
      }

      .optionDesc {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }

  .footer {
    margin-top: 24px;
    margin-right: 24px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: flex-end;
    padding: 0;
    gap: 8px;
    width: auto;
    height: 40px;

    .operateButGroup {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 8px;

      .text {
        font-size: 14px;
        font-weight: 600;
      }

      .but {
        border-radius: 8px;
        width: 76px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .cancelBut {
        background-color: RGBA(250, 250, 250, 1);
        border: 1px solid rgba(0, 0, 0, 0.08);

        .text {
          color: RGBA(0, 0, 0, 0.65);
        }
      }

      .createBut {
        background-color: #4455F2;

        .text {
          color: #FFFFFF;
        }
      }
    }
  }
}

.backToTop {
  position: fixed;
  left: 55%;
  bottom: 40px;
  width: 130px;
  transform: translateX(-50%);
  height: 38px;
  border-radius: 24px;
  background: #fff;
  border: 1px solid RGBA(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  z-index: 9999;
  gap: 8px;

  span {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.64);
    font-weight: 500;
  }

  &:hover {
    background: #f2f3f5;
  }

  &.hidden {
    opacity: 0;
    pointer-events: none;
  }
}

.popoverContent {
  width: 120px;
  display: flex;
  flex-direction: column;

  p {
    width: 100%;
    padding: 6px 8px;
    border-radius: 6px;
    margin-bottom: 4px;
    cursor: pointer;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }
}