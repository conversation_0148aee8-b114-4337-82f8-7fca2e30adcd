import { Form, Switch } from 'antd';

const html4excelTip = `与 General 切片方法配合使用。未开启状态下，表格文件（XLSX、XLS（Excel 97-2003））会按行解析为键值对。开启后，表格文件会被解析为 HTML 表格。若原始表格超过 12 行，系统会自动按每 12 行拆分为多个 HTML 表格。欲了解更多详情，请参阅 https://ragflow.io/docs/dev/enable_excel2html。`

const ExcelToHtml = () => {
  return (
    <Form.Item
      name={['parser_config', 'html4excel']}
      label={'表格转HTML'}
      initialValue={false}
      valuePropName="checked"
      tooltip={html4excelTip}
    >
      <Switch />
    </Form.Item>
  );
};

export default ExcelToHtml;
