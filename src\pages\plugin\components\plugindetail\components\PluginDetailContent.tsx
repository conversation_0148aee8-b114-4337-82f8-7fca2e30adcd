import React from 'react';
import { Table, Input, Tag, Tabs, Card, Typography, Button, Message, Grid } from '@arco-design/web-react';
import styles from '../style/index.module.less';
import IconCopy from '@/assets/plugin/IconCopy.svg';
import PluginIconPlus from '@/assets/plugin/PluginIconPlus.svg';
import IconNetworkActivity from '@/assets/plugin/IconNetworkActivity.svg';
import IconAgentUsage from '@/assets/plugin/IconAgentUsage.svg';
import IconExecutionTime from '@/assets/plugin/IconExecutionTime.svg';
import ReactMarkdown from 'react-markdown';
import { Space } from 'antd';

const { Text, Title } = Typography;
const TabPane = Tabs.TabPane;
const { Row } = Grid;

interface PluginDetailContentProps {
    pluginData: any;
}

// 插件详情组件
function PluginDetailContent({ pluginData }: PluginDetailContentProps) {
    // 从assembly字段中提取版本信息
    const extractVersion = (assembly) => {
        if (!assembly) return '';
        const versionMatch = assembly?.match(/Version=([\d\.]+)/);
        if (versionMatch && versionMatch[1]) {
            return `V${versionMatch[1]}`;
        }
        return '';
    };

    // 模拟统计数据
    const statsData = [
        {
            value: '163',
            label: '智能体使用'
        },
        {
            value: '198ms',
            label: '执行时间'
        },
        {
            value: '15M',
            label: '调用量'
        }
    ];

    // 模拟参数表格数据
    const tableData = {
        columns: [
            {
                title: '参数名',
                dataIndex: 'name',
                width: '30%',
                render: (text, record) => (
                    <span className={styles.paramNameWrapper}>
                        {text}
                        {record.required && <span className={styles.requiredMark}>*</span>}
                    </span>
                ),
            },
            {
                title: '参数说明',
                dataIndex: 'description',
                width: '70%',
                render: (_, record) => (
                    <div className={styles.paramDescWrapper}>
                        {record.example && (
                            <div className={styles.exampleWrapper}>
                                <Input
                                    placeholder={record.example}
                                    style={{ width: '100%', marginBottom: 8 }}
                                    disabled
                                />
                            </div>
                        )}
                        <div>
                            <Text style={{ marginRight: 0 }}>{record.type}</Text>
                            <Text>·</Text>
                            <Text>{record.description}</Text>
                        </div>
                    </div>
                ),
            }
        ],
        data: [
            {
                name: 'skip_cache',
                type: 'boolean',
                description: '是否跳过缓存，default value is false',
                required: false
            },
            {
                name: 'url',
                type: 'string',
                description: '网页url、pdf url、抖音视频url、docx url、csv url。',
                example: 'https://csdn.net',
                required: false
            }
        ]
    };

    // 模拟请求体和返回体数据
    const apiData = {
        request: {
            "skip_cache": false,
            "url": "https://csdn.net"
        },
        response: {
            "success": true,
            "message": "请求成功",
            "data": {
                "title": "CSDN - 专业开发者社区",
                "content": "CSDN是全球知名中文IT技术交流平台,创建于1999年,包含原创博客、精品问答、职业培训、技术论坛、资源下载等产品服务,提供原创、优质、完整内容的专业IT技术开发社区.",
                "links": [
                    "https://blog.csdn.net/",
                    "https://download.csdn.net/",
                    "https://edu.csdn.net/"
                ]
            }
        }
    };

    // 复制功能实现
    const handleCopy = (content) => {
        navigator.clipboard.writeText(content)
            .then(() => {
                Message.success('已复制到剪贴板');
            })
            .catch(() => {
                Message.error('复制失败');
            });
    };

    // 模拟概览内容
    const overviewContent = pluginData?.description || `
### 1.功能描述
该插件是一个强大的网页内容抓取工具。它不仅能够返回网页的原始内容，包括标题、内容、链接等，而且还可以对这些内容进行筛选和解析。
无论是需要进行网页内容分析，还是需要从众多网页中提取有价值的信息，这个插件都能轻松应对。

(1) 网页信息提取: 网页标题、内容等系统都一应俱全。
(2) 精准快速的抓取: 只需要提供网页的URL，就能快速获取完整的网页内容，无需任何编程操作。
(3) 高度兼容: 该插件可以处理各种类型的网页，包括但不限于HTML、PDF等，无论是静态页面还是动态页面，都能准确无误地抓取内容。

### 2.使用说明
确定你需要抓取的网页URL有效。
插件会在短时间内返回该网页的原始内容，包括标题、内容等。

### 3.注意事项
请确保入参的URL是有效的，否则插件无法返回任何内容。
当处理大量的网页时，请注意可能存在的网络流量和处理时间问题。

### 4.适用场景剖析
(1) 搜索引擎优化：通过抓取插件可以探索网页的内容和结构，分析关键词和元数据，帮助优化搜索引擎排名。
(2) 竞品分析: 可用于抓取竞争对手的网站内容，进行比较和分析。
(3) 新闻聚合: 对新闻网站进行抓取，实现自动化的新闻聚合和发布。
(4) 学术研究: 帮助研究人员从网络上获取大量资料，进行数据挖掘和学术研究。
(5) 市场调查: 通过抓取在线购物网站的商品信息，帮助企业洞察市场变化，进行精准营销。
    `;

    return (
        <div className={styles.mainContent}>
            <div className={styles.leftContent}>
                <Row className={styles.metaInfo}>
                    {pluginData?.icon_url ? (
                        <img 
                            src={pluginData.icon_url} 
                            alt={pluginData.name} 
                            className={styles.pluginIcon}
                            style={{ width: 48, height: 48, marginRight: 16 }}
                        />
                    ) : (
                        <PluginIconPlus />
                    )}
                    <Space direction='vertical' size={0}>
                        <Title className={styles.title}>{pluginData?.name || '插件名称'}</Title>
                        <Space className={styles.meta}>
                            <Text>版本: {extractVersion(pluginData?.assembly) || '0.0.1'}</Text>
                            <Text>|</Text>
                            <Text>@AI4C</Text>
                            <Text>|</Text>
                            <Text>创建时间：2024/10/25</Text>
                        </Space>
                    </Space>
                </Row>
                <div className={styles.overview}>
                    <div className={styles.markdownContent}>
                        <ReactMarkdown components={{
                            h1: ({ node, ...props }) => <Title heading={2} {...props} />,
                            h2: ({ node, ...props }) => <Title heading={3} {...props} />,
                            h3: ({ node, ...props }) => <Title heading={4} {...props} />,
                            code: ({ node, inline, className, children, ...props }) =>
                                inline ? (
                                    <code className={styles.inlineCode} {...props}>
                                        {children}
                                    </code>
                                ) : (
                                    <pre className={styles.codeBlock}>
                                        <code {...props}>{children}</code>
                                    </pre>
                                )
                        }}>
                            {overviewContent}
                        </ReactMarkdown>
                    </div>
                </div>
            </div>
            {/* <div className={styles.rightContent}>
                <div className={styles.statsCards}>
                    {statsData.map((stat, index) => (
                        <Card key={index} className={styles.statCard}>
                            <div className={styles.statItem}>
                                <Text className={styles.statValue}>{stat.value}</Text>
                                <Text className={styles.statLabel}>{stat.label}</Text>
                                {index === 0 && <div className={styles.statIcon}><IconAgentUsage /></div>}
                                {index === 1 && <div className={styles.statIcon}><IconExecutionTime /></div>}
                                {index === 2 && <div className={styles.statIcon}><IconNetworkActivity /></div>}
                            </div>
                        </Card>
                    ))}
                </div>
                <div className={styles.paramsTableSection}>
                    <Typography.Title heading={5}>参数</Typography.Title>
                    <Table
                        columns={tableData.columns}
                        data={tableData.data}
                        pagination={false}
                        className={styles.paramsTable}
                        rowKey="name"
                        border={false}
                    />
                </div>
                <div className={styles.codeCardSection}>
                    <Tabs defaultActiveTab="request" type="card-gutter">
                        <TabPane key="request" title="请求体">
                            <Card className={styles.codeCard}>
                                <pre className={styles.codeBlock}>
                                    {JSON.stringify(apiData.request, null, 2)}
                                </pre>
                            </Card>
                        </TabPane>
                        <TabPane key="response" title="返回体">
                            <Card className={styles.codeCard}>
                                <div className={styles.codeHeader}>
                                    <Text className={styles.codeType}>[/] json 格式</Text>
                                    <Button
                                        type="text"
                                        size="mini"
                                        icon={<IconCopy />}
                                        onClick={() => handleCopy(JSON.stringify(apiData.response, null, 2))}
                                        className={styles.codeCopy}
                                    >
                                        复制
                                    </Button>
                                </div>
                                <pre className={styles.codeBlock}>
                                    {JSON.stringify(apiData.response, null, 2)}
                                </pre>
                            </Card>
                        </TabPane>
                    </Tabs>
                </div>
            </div> */}
        </div>
    );
}

export default PluginDetailContent; 