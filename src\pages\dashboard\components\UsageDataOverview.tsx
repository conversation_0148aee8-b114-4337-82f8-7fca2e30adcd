import React from 'react';
import { Grid, Typography, Space } from '@arco-design/web-react';
import styles from './style/DataOverview.module.less';
import IconArrowRise from '@/assets/dashboard/IconArrowRise.svg';
import IconArrowFall from '@/assets/dashboard/IconArrowFall.svg';

const { Row, Col } = Grid;
const { Text, Title } = Typography;

const UsageDataOverview = () => {
    const cardList = [
        {
            title: 'Token用量总计',
            value: '1,620',
            dayInfo: '今日消耗162Token',
            avgInfo: '平均每日消耗162Token'
        },
        {
            title: '本季度Token用量',
            value: '2,160',
            change: -22,
            compareText: '对比上个季度'
        },
        {
            title: '本月Token用量',
            value: '120',
            change: 22,
            compareText: '对比上个月'
        },
        {
            title: '本周Token用量',
            value: '6,210',
            change: 22,
            compareText: '对比上周'
        },
    ];

    return (
        <div className={styles.container}>
            <Row className={styles.titleWrapper}>
                <Title heading={5} className={styles.pageTitle}>监测</Title>
            </Row>
            <Row gutter={8}>
                {cardList.map((item, index) => (
                    <Col key={index} span={6}>
                        <div className={styles.card}>
                            <Space className={styles.cardContent} direction='vertical' size={16}>
                                <div className={styles.header}>
                                    <Text className={styles.title}>{item.title}</Text>
                                    <Text className={styles.value}>{item.value}</Text>
                                </div>
                                {index === 0 && (
                                    <div className={styles.footer}>
                                        <Space size={8} className={styles.dayInfo}>
                                            <Text className={styles.dayText}>{item.dayInfo}</Text>
                                            <Text className={styles.avgText}>{item.avgInfo}</Text>
                                        </Space>
                                    </div>
                                )}
                                {index > 0 && (
                                    <div className={styles.footer}>
                                        <Space style={{ marginBottom: '0px' }}>
                                            <Text className={`${styles.change} ${item.change > 0 ? styles.positive : styles.negative}`}>
                                                {item.change > 0 ? <IconArrowRise /> : <IconArrowFall />}
                                                <span className={styles.changeText}>
                                                    {Math.abs(item.change)}%
                                                </span>
                                            </Text>
                                            <Text className={styles.compareText}>{item.compareText}</Text>
                                        </Space>
                                    </div>
                                )}
                            </Space>
                        </div>
                    </Col>
                ))}
            </Row>
        </div>
    );
};

export default UsageDataOverview; 