server {
    listen       80;
    server_name  dev-test-llm.ai4c.cn;

    access_log  /var/log/nginx/host.access.log  main;
    error_log  /var/log/nginx/error.log  error;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri /index.html;
    }

    ## 全局代理配置
    #location /elsa {
    #    proxy_pass http://ai4c-aibrain-elsa:13000/elsa;
    #    proxy_set_header Authorization $http_authorization;
    #}
    #location /_content {
    #    proxy_pass http://ai4c-aibrain-elsa:13000/_content;
    #    # 移除硬编码的 Authorization 头（由客户端携带）
    #    proxy_set_header Authorization $http_authorization;
    #}
    #location /_framework {
    #    proxy_pass http://ai4c-aibrain-elsa:13000/_framework;
    #    # 移除硬编码的 Authorization 头（由客户端携带）
    #    proxy_set_header Authorization $http_authorization;
    #}
    #location /workflows {
    #    proxy_pass http://ai4c-aibrain-elsa:13000/workflows;
    #    # 移除硬编码的 Authorization 头（由客户端携带）
    #    proxy_set_header Authorization $http_authorization;
    #}
    # 精准拦截CSS请求（网页3路径匹配）
    location = /_content/MudBlazor/MudBlazor.min.css {
        alias /usr/local/etc/nginx/custom_styles/mudblazor.min.css; # 网页7路径规范
        add_header Content-Type "text/css";
        access_log off;
        # expires 7d;
        add_header Expires "0";
    }

    location /_content/Elsa.Studio.Shell/css/shell.css {
        alias /usr/local/etc/nginx/custom_styles/shell.css;
        add_header Content-Type "text/css";
        access_log off;
        # expires 7d;
        add_header Expires "0";
    }

    location /_content/Radzen.Blazor/css/material-base.css {
        alias /usr/local/etc/nginx/custom_styles/material-base.css;
        add_header Content-Type "text/css";
        access_log off;
        # expires 7d;
        add_header Expires "0";
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}