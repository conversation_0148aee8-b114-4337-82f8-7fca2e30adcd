.container {
    height: 100%;

    .mainTabs {
        :global(.arco-tabs-header-nav::before) {
            display: none;
        }

        :global(.arco-tabs-header-ink) {
            display: none;
        }

        :global(.arco-tabs-header-title) {
            padding: 0;
            font-weight: 600;
            font-size: 20px;
            line-height: 32px;
            color: #a6a6a6;

            &:hover {
                color: #a6a6a6;
            }
        }

        :global(.arco-tabs-header-title-active) {
            color: #333333;

            &:hover {
                color: #333333;
            }
        }

        :global(.arco-tabs-content) {
            padding-top: 8px;
        }

        :global(.arco-tabs-header-nav-line.arco-tabs-header-nav-horizontal > .arco-tabs-header-title:first-of-type) {
            margin-left: 0;
        }

        :global(.arco-tabs-header-nav-line .arco-tabs-header-title) {
            margin: 0 12px;
        }
        :global(.arco-tabs-header-wrapper) {
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f5f5f5;
        }
    }

    .mainContent {
        display: flex;
        gap: 24px;

        .leftContent {
            flex: 1;
            max-width: 50%;

            .metaInfo {
                display: flex;
                align-items: center;
                gap: 16px;
                margin-bottom: 24px;

                .meta {
                    :global(.arco-typography) {
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 20px;
                        color: #adadad;
                    }
                }

                .title {
                    font-weight: 600;
                    font-size: 20px;
                    line-height: 32px;
                    color: #333333;
                    margin-bottom: 0px;
                }
            }

            .overview {
                .markdownContent {
                    border-radius: 4px;
                    font-size: 14px;
                    line-height: 1.6;

                    :global(.arco-typography) {
                        margin-bottom: 16px;
                    }

                    :global(h3) {
                        font-size: 16px;
                        font-weight: 600;
                        margin-top: 24px;
                        margin-bottom: 16px;
                        color: #1D2129;
                    }

                    :global(h4) {
                        font-size: 14px;
                        font-weight: 600;
                        margin-top: 16px;
                        margin-bottom: 8px;
                        color: #1D2129;
                    }

                    :global(p) {
                        margin-bottom: 16px;
                        color: #4E5969;
                    }
                }
            }
        }

        .rightContent {
            flex: 1;
            max-width: 50%;

            .statsCards {
                display: flex;
                gap: 8px;
                margin-bottom: 24px;

                .statCard {
                    flex: 1;
                    border-radius: 8px;
                    border: 1px solid #f5f5f5;

                    :global(.arco-card-body) {
                        padding: 16px;
                    }

                    .statItem {
                        display: flex;
                        flex-direction: column;
                        align-items: flex-start;
                        position: relative;
                        padding-right: 24px;

                        .statValue {
                            font-weight: 600;
                            font-size: 20px;
                            line-height: 32px;
                            color: #333333;
                        }

                        .statLabel {
                            font-weight: 400;
                            font-size: 12px;
                            line-height: 20px;
                            color: #adadad;
                        }

                        .statIcon {
                            position: absolute;
                            right: 0;
                            top: 50%;
                            transform: translateY(-50%);
                            opacity: 1;
                        }
                    }
                }
            }

            .statsContent {
                display: flex;
                justify-content: space-around;
                padding: 24px 8px;
                border: 1px solid #ebebeb;
                border-radius: 4px;
                margin-bottom: 24px;

                .statItem {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    width: 100%;

                    &:nth-child(1),
                    &:nth-child(2) {
                        border-right: 1px solid #ebebeb;
                    }

                    .statValue {
                        font-size: 20px;
                        font-weight: 600;
                        color: #595959;
                    }

                    .statLabel {
                        font-size: 12px;
                        font-weight: 400;
                        color: #a6a6a6;
                    }
                }
            }

            .paramsTableSection {
                margin-bottom: 24px;

                :global(.arco-table-th-item) {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                    color: #adadad;
                    background-color: #f5f5f5;
                    padding: 8px 12px;
                }

                :global(.arco-typography) {
                    font-weight: 600;
                    font-size: 14px;
                    color: #333333;
                    margin-bottom: 8px;
                }

                :global(.arco-table-td) {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                    color: #adadad;
                    margin-bottom: 8px;
                    border-bottom: 1px solid #f5f5f5;
                    
                    &:nth-child(3) {
                        border-bottom: none !important;
                    }
                    &:nth-child(4) {
                        border-bottom: none;
                    }
                }

                .paramNameWrapper {
                    display: flex;
                    align-items: center;

                    .requiredMark {
                        color: #F53F3F;
                        margin-left: 4px;
                    }
                }

                .paramDescWrapper {
                    display: flex;
                    flex-direction: column;

                    :global(.arco-input-disabled) {
                        padding: 8px 12px;
                        background-color: #fcfcfc;
                        border-radius: 8px;
                        border: 1px solid #f5f5f5;
                    }
                    
                    :global(.arco-typography) {
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 24px;
                        color: #5c5c5c;
                    }

                    .exampleWrapper {
                        margin-bottom: 4px;
                    }
                }

                .paramsTable {
                    border-radius: 8px;
                    border: 1px solid #ebebeb;
                    // width: 100%;
                }
            }

            .codeCardSection {
                :global(.arco-card-body) {
                    padding: 0;
                }

                :global(.arco-tabs-content) {
                    padding: 0;
                    border: 1px solid #ebebeb;
                    border-radius: 4px;
                }

                :global(.arco-tabs-header) {
                    padding: 0;
                    margin-bottom: 8px;
                }

                :global(.arco-tabs-header-title) {
                    padding: 0;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 24px;
                    color: #a6a6a6;
                    border: none;
                    background-color: transparent;
                    margin-left: 0;

                    &:first-child {
                        margin-right: 16px;
                    }
                }

                :global(.arco-tabs-header-title.arco-tabs-header-title-active) {
                    color: #595959;
                }

                :global(.arco-tabs-header-nav::before) {
                    display: none;
                }

                .codeCard {
                    background-color: var(--color-bg-2);
                    border-radius: 4px;
                    box-shadow: none;

                    .codeHeader {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 8px 16px;
                        background-color: #ebebeb;
                        border-top-left-radius: 4px;
                        border-top-right-radius: 4px;

                        .codeType {
                            color: #a6a6a6;
                            font-weight: 600;
                            font-size: 12px;
                        }

                        .codeCopy {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            gap: 4px;
                            padding: 2px;
                            font-weight: 400;
                            font-size: 12px;
                            color: #595959;
                        }
                    }

                    .codeBlock {
                        padding: 16px;
                        margin: 0;
                        background-color: #fff;
                        font-family: monospace;
                        font-size: 14px;
                        white-space: pre-wrap;
                        word-break: break-all;
                        overflow-x: auto;
                    }
                }
            }
        }
    }

    // 设置页样式
    .settingContent {

        .formItem {
            margin-bottom: 24px;
            height: 100%;

            .formLabel {
                font-weight: bold;
                font-size: 14px;
                line-height: 24px;
                color: #333333;
                margin-bottom: 8px;
            }

            .iconAndName {
                display: flex;
                align-items: center;
                width: 100%;

                .iconWrapper {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 72px;
                    height: 72px;
                    border-radius: 8px;
                    background-color: #f5f5f5;
                    flex-shrink: 0;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

                    .pluginIcon {
                        width: 100%;
                        height: 100%;
                    }
                }

                .divider {
                    width: 1px;
                    height: 72px;
                    background-color: #f5f5f5;
                    margin: 0 24px;
                }

                .nameInputContainer {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    height: 72px;

                    :global(.arco-input) {
                        padding: 8px;
                        background-color: transparent;
                        border-radius: 8px;
                        border: 1px solid #ebebeb;
                        transition: all 0.3s;

                        &:hover {
                            background-color: #fafafa;
                            border-color: #ebebeb;
                        }

                        &::placeholder {
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 24px;
                            color: #d6d6d6;
                        }
                    }
                }
            }

            :global(.arco-textarea-wrapper) {
                min-height: 80px;
            }

            :global(.arco-textarea) {
                background-color: transparent;
                min-height: 64px;
                border: 1px solid #ebebeb;
                border-radius: 8px;
                resize: none;
                min-height: 88px;

                &::placeholder {
                    color: #d6d6d6;
                }
            }

            :global(.arco-textarea-word-limit) {
                color: #adadad;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
            }
        }

        .switchItem {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            height: 100%;

            .switchLabel {
                display: flex;
                flex-direction: column;
                gap: 4px;
                max-width: 70%;

                :global(.arco-typography) {
                    &:first-child {
                        font-size: 14px;
                        color: #1D2129;
                    }

                    &:last-child {
                        font-size: 12px;
                        color: #86909C;
                    }
                }
            }

            .switchControl {
                display: flex;
                justify-content: flex-end;
                min-width: 100px;

                :global(.arco-switch) {
                    background-color: #C9CDD4;
                }

                :global(.arco-switch-checked) {
                    background-color: #4B5CF2;
                }
            }
        }

        .buttonWrapper {
            margin-top: 32px;

            :global(.arco-btn) {
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 8px 24px;
                border-radius: 8px;
                height: 40px;

                &:global(.arco-btn-primary) {
                    background-color: #4B5CF2;

                }
            }
        }

        .settingForm {
            margin-top: 24px;

            .settingCard {
                margin-bottom: 16px;

                :global(.arco-card-body) {
                    padding: 24px;
                }
            }

            .settingHeader {
                margin-bottom: 16px;

                :global(.arco-typography) {
                    font-weight: 600;
                    font-size: 14px;
                    color: #1D2129;
                    margin: 0;
                }
            }

            .settingItem {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 24px;

                &:last-child {
                    margin-bottom: 0;
                }

                .settingLabel {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                    max-width: 70%;

                    :global(.arco-typography) {
                        &:first-child {
                            font-size: 14px;
                            color: #1D2129;
                        }

                        &:last-child {
                            font-size: 12px;
                            color: #86909C;
                        }
                    }
                }

                .settingControl {
                    display: flex;
                    justify-content: flex-end;
                    min-width: 100px;

                    :global(.arco-switch) {
                        background-color: #C9CDD4;
                    }

                    :global(.arco-switch-checked) {
                        background-color: #4B5CF2;
                    }

                    :global(.arco-input) {
                        border-radius: 4px;
                    }
                }
            }

            .settingButtons {
                display: flex;
                gap: 16px;
                margin-top: 32px;

                :global(.arco-btn) {
                    padding: 8px 16px;

                    &:global(.arco-btn-primary) {
                        background-color: #4B5CF2;
                    }
                }
            }
        }
    }
}

// 响应式调整
@media screen and (max-width: 1200px) {
    .container {
        .mainContent {
            flex-direction: column;

            .leftContent,
            .rightContent {
                max-width: 100%;
            }
        }
    }
}

// 通用代码样式
.inlineCode {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0 4px;
    border-radius: 2px;
    font-family: monospace;
}

.codeBlock {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 16px;
    border-radius: 4px;
    font-family: monospace;
    overflow-x: auto;
}