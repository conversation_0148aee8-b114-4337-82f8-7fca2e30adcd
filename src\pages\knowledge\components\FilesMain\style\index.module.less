.filesMain {
    display: flex;
    flex-direction: column;
    height: 100%;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f5;

        .createBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            background-color: #4455f2;
            font-weight: 500;
            font-size: 14px;
            line-height: 24px;
            color: #ffffff;
            border-radius: 8px;
            transition: all 0.3s;
            height: 40px;

            &:hover {
                background-color: #3144f1;
            }
        }

        //搜索输入框
        :global(.arco-input-inner-wrapper) {
            padding: 8px;
            background-color: #ffffff;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            ::placeholder {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                color: #d6d6d6;
            }

            :global(.arco-input) {
                padding-top: 0;
                padding-bottom: 0;
                padding-left: 8px;
            }
        }

        //筛选select
        :global(.arco-select-size-default.arco-select-single .arco-select-view) {
            padding: 8px;
            height: auto;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
            border-radius: 8px;
            border: 1px solid #f5f5f5;
            background-color: #ffffff;
            transition: all 0.2s;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            :global(.arco-select-prefix) {
                margin-right: 4px;
            }

            :global(.arco-select-view-input) {
                &::placeholder {
                    color: #d6d6d6;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 24px;
                }
            }
        }

        .fileNumber {
            color: #adadad;
            font-size: 14px;
            font-weight: 400;
            white-space: nowrap;
        }
    }

    .content {
        flex: 1;
        overflow-y: auto;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        grid-gap: 16px;
        grid-auto-rows: min-content;
        position: relative;
        will-change: transform;
        box-sizing: border-box;
        scrollbar-width: none;
        -ms-overflow-style: none;
        &::-webkit-scrollbar {
            display: none;
        }

        // 添加媒体查询，处理小屏幕设备
        @media screen and (max-width: 1200px) {
            grid-template-columns: repeat(3, 1fr);
        }

        @media screen and (max-width: 992px) {
            grid-template-columns: repeat(2, 1fr);
        }

        @media screen and (max-width: 576px) {
            grid-template-columns: repeat(1, 1fr);
        }

        .fileCard {
            border: 1px solid #f5f5f5;
            height: 240px;
            box-sizing: border-box;
            cursor: pointer;
            border-radius: 8px;
            padding: 20px 24px;
            display: flex;
            justify-content: space-between;
            min-height: 200px;
            transition: all 0.3s;
            position: relative;
            width: 100%;
            max-width: 100%;
            overflow: hidden;

            // 添加响应式内边距
            @media screen and (max-width: 1400px) {
                padding: 16px 20px;
            }

            @media screen and (max-width: 1200px) {
                padding: 14px 16px;
            }

            &:hover {
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

                .fileInfo {
                    .fileFooter {
                        .defaultInfo {
                            opacity: 0;
                        }

                        .hoverInfo {
                            opacity: 1;
                            pointer-events: auto;
                        }
                    }
                }

                .triggerBtn {
                    opacity: 1;
                }
            }

            :global(.arco-card-body) {
                padding: 0;
                display: flex;
                flex-direction: column;
                width: 100%;
                height: 100%;
            }

            .fileInfo {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 100%;

                .nameWrapper {
                    display: flex;
                    align-items: center;

                    .icon {
                        width: 48px;
                        height: 48px;
                        border-radius: 8px;

                        img {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                    }

                    .name {
                        font-size: 16px;
                        font-weight: 600;
                        color: #333333;
                        cursor: pointer;
                    }
                }

                .fileDetails {
                    display: flex;
                    flex-direction: column;

                    .tags {
                        display: flex;
                        align-items: center;
                        gap: 6px;
                        flex-wrap: wrap;
                    }

                    .tagLabel {
                        align-self: flex-start;
                        padding: 2px 6px;
                        border: 1px solid #ebebeb;
                        border-radius: 4px;
                        font-weight: 400;
                        font-size: 12px;
                        color: #5c5c5c;
                        background-color: #fafafa;
                    }

                    .fileType {
                        align-self: flex-start;
                        padding: 2px 6px;
                        border: none;
                        border-radius: 4px;
                        font-weight: 400;
                        font-size: 12px;

                        &.system {
                            background-color: #e8e0fc;
                            color: #713cee;
                        }

                        &.user {
                            background-color: #fdf2e1;
                            color: #cb9039;
                        }
                    }

                    .description {
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 24px;
                        letter-spacing: 0%;
                        color: #5c5c5c;
                        line-clamp: 2;
                        display: -webkit-box;
                        -webkit-line-clamp: 1;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                    }
                }

                .fileFooter {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 16px;
                    position: relative;

                    // 默认显示的文件信息
                    .defaultInfo {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        width: 100%;
                        opacity: 1;
                        transition: opacity 0.3s ease;

                        .fileStats {
                            .fileCount {
                                font-weight: 400;
                                font-size: 12px;
                                color: #adadad;
                            }

                            .fileSize {
                                font-weight: 400;
                                font-size: 12px;
                                color: #adadad;
                            }
                        }

                        .statusInfo {
                            .statusTag {
                                font-weight: 400;
                                font-size: 12px;
                                line-height: 20px;
                                border-radius: 4px;
                                padding: 2px 6px;
                                border: none;
                            }

                            .statusAvailable {
                                background-color: #f7fcfa;
                                color: #2ba471;
                            }

                            .statusUnavailable {
                                background-color: #fef8f8;
                                color: #d54941;
                            }
                        }
                    }

                    // 悬浮时显示的元信息和操作按钮
                    .hoverInfo {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        display: flex;
                        justify-content: space-between;
                        align-items: flex-end;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                        pointer-events: none;

                        .metaText {
                            :global(.arco-typography) {
                                font-weight: 400;
                                font-size: 12px;
                                color: #adadad;
                            }
                        }
                    }
                }
            }

            .triggerBtn {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 4px;
                background-color: #ffffff;
                border-radius: 8px;
                border: 1px solid #f5f5f5;
                opacity: 0;
                transition: all 0.3s;

                &:hover {
                    background-color: #fafafa;
                }

                img {
                    width: 16px;
                    height: 16px;
                }
            }
        }

        .emptyContainer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            grid-column: 1 / -1;
            background-color: #ffffff;

            :global(.arco-space) {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            :global(.arco-typography) {
                font-weight: 500;
                font-size: 14px;
                line-height: 24px;
                color: #5c5c5c;
                text-align: center;
            }
        }
    }
}

.confirmDeleteModal {
    position: relative;
    padding: 24px;
    width: 480px;
    border-radius: 16px;

    .modalContent {
        display: flex;
        flex-direction: column;

        .modalContentText {
            font-weight: 400;
            font-size: 14px;
            color: #5c5c5c;
        }
    }

    .modalFooter {
        margin-top: 24px;
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .cancelDeleteBtn,
        .confirmDeleteBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 18px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
        }

        .cancelDeleteBtn {
            background-color: #ffffff;
            color: #5c5c5c;
            border: 1px solid #ebebeb;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }
        }

        .confirmDeleteBtn {
            background-color: #d54941;
            color: #ffffff;

            &:hover {
                background-color: #cd463e;
                color: #ffffff;
            }
        }
    }

    :global(.arco-modal-header) {
        padding: 0;
        height: auto;
        border-bottom: none;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            color: #333333;
            margin-bottom: 24px;
            text-align: left;
        }
    }

    :global(.arco-modal-content) {
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 24px;
        top: 24px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }
}

.actionBtn {
    padding: 4px 8px;
    border-radius: 4px;
    width: 80px;
    display: flex;
    justify-content: flex-start;
    background-color: #ffffff !important;
    color: #333333;

    &:hover {
        background-color: #f5f5f5 !important;
    }
}

.deleteBtn {
    color: #d54941;

    &:hover {
        background-color: #fef8f8 !important;
    }
}

:global(.arco-popover-content.arco-popover-content-right) {
    padding: 8px;
    width: 100px;
    border: none;
    border: 1px solid #f5f5f5;
    border-radius: 8px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

    :global(.arco-btn) {
        background-color: transparent;
        width: 84px;
        text-align: left;
    }
}

// 暗色主题支持
body[arco-theme='dark'] {
    .filesMain {
        .header {
            border-bottom: 1px solid #2e3440;

            .createBtn {
                background-color: #4455f2;

                &:hover {
                    background-color: #3144f1;
                }
            }

            .fileNumber {
                color: #78909c;
            }
        }

        .content {
            .fileCard {
                border: 1px solid #2e3440;
                background-color: #1a1a1a;

                &:hover {
                    background-color: #1f1f1f;
                }

                .fileInfo {
                    .nameWrapper {
                        .name {
                            color: #ffffff;
                        }
                    }

                    .fileDetails {
                        .description {
                            color: #78909c;
                        }

                        .tagLabel {
                            background-color: #2e3440;
                            color: #78909c;
                            border-color: #2e3440;
                        }

                        .fileType {
                            &.system {
                                background-color: rgba(232, 224, 252, 0.2);
                                color: #9d7bff;
                            }

                            &.user {
                                background-color: rgba(253, 242, 225, 0.2);
                                color: #ffb366;
                            }
                        }
                    }

                    .fileFooter {
                        .defaultInfo {
                            .fileStats {
                                .fileCount,
                                .fileSize {
                                    color: #78909c;
                                }
                            }
                        }

                        .hoverInfo {
                            .metaText {
                                :global(.arco-typography) {
                                    color: #78909c;
                                }
                            }
                        }
                    }
                }

                .triggerBtn {
                    background-color: #2e3440;
                    border-color: #2e3440;

                    &:hover {
                        background-color: #374151;
                    }
                }
            }

            .emptyContainer {
                background-color: #1a1a1a;

                :global(.arco-typography) {
                    color: #78909c;
                }
            }
        }
    }
} 