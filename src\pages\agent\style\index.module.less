  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none; /* 对于基于 WebKit 的浏览器 */
  }
  
  /* 对于IE和Edge */
  html {
    -ms-overflow-style: none; /* IE 和 Edge */
  }
  
  /* 对于Firefox */
  * {
    scrollbar-width: none; /* Firefox */
  }

.container {
  
  .tabs {
    :global(.arco-tabs-header-nav::before) {
        display: none;
    }

    :global(.arco-tabs-header-ink) {
        display: none;
    }

    :global(.arco-tabs-header-title) {
        padding: 0;
        font-weight: 600;
        font-size: 20px;
        color: #a6a6a6;
        margin-left: 0px !important;

        &:hover {
            color: #a6a6a6;
        }
    }

    :global(.arco-tabs-header-title-active) {
        color: #333333;

        &:hover {
            color: #333333;
        }
    }

    :global(.arco-tabs-content) {
        padding-top:0px;
    }
  }
  :global(.arco-popover-content) {
    padding: 0 12px 8px 12px;
    :global(.arco-popover-inner-content) {
      p{
        cursor: pointer;
        padding-left: 8px;
        display: flex;
        align-items: center;
        height: 34px !important;
        width: 104px !important;
        text-align: left;
        color: RGBA(0, 0, 0, .65);
        border-radius: 4px;
        &:hover{
          background-color: RGBA(0, 0, 0, .02);
        }
      }
      .red{
        color: RGBA(213, 73, 65, .95) !important;
      }
    }
  }
  
  .customModal{
    padding: 8px;
    :global(.arco-modal-header) {
      border: none;
      :global(.arco-modal-title) {
        text-align: left !important;
        font-size: 18px;
        font-weight: 600;
        color: RGBA(0, 0, 0, .8);
      }
    }
    :global(.arco-modal-content) {
      padding-top: 14px;
      :global(.arco-form){
        :global(.arco-form-item) {
          display: flex;
          flex-direction: column;
          .label{
            font-size: 14px;
            font-weight: 600;
            display: inline-block;
            padding-bottom: 6px;
            color: RGBA(0, 0, 0, .65);
          }
          .subLabel{
            font-size: 14px;
            font-weight: 400;
            color: RGBA(0, 0, 0, .35);
            padding-left: 6px;
          }
          .subLabel2{
            font-size: 12px;
            font-weight: 400;
            color: RGBA(0, 0, 0, .35);
            padding: 0 0 6px 0;
          }
          :global(.arco-form-label-item){
            text-align: left !important;
            flex-basis: auto !important;
            :global(.arco-form-item-symbol) {
              display: none !important;
            }
          }
          :global(.arco-input) {
            background-color: #FFFFFF !important;
            border-radius: 4px;
            border: 1px solid RGBA(0, 0, 0, .08);
            height: 40px;
            color: RGBA(0, 0, 0, .8);
            font-size: 14px;
            &::placeholder{
              font-size: 14px;
              font-weight: 400;
              color: RGBA(0, 0, 0, .16);
            }
          }
        }
        :global(.arco-btn) {
          height: 40px;
          width: 80px;
          border-radius: 4px;
          border: 1px solid RGBA(0, 0, 0, .08);
          color: RGBA(0, 0, 0, .65);
          span{
            font-size: 14px;
            font-weight: 400;
            color: RGBA(0, 0, 0, .65);
          }
        }
        .customBlock {
          width: 100%;
          :global(.arco-form-item) {
            margin-bottom: 8px;
            :global(.arco-input-tag) {
              background-color: #FFFFFF !important;
              border-radius: 4px;
              border: 1px solid RGBA(0, 0, 0, .08);
              height: 40px;
              :global(.arco-input-tag-view) {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 40px;
              }
  
              :global(.arco-input-tag-inner) {
                input {
                  color: RGBA(0, 0, 0, .8);
                  font-size: 14px;
                  &::placeholder{
                    font-size: 14px;
                    font-weight: 400;
                    color: RGBA(0, 0, 0, .16);
                  }
                }
              }
              :global(.arco-input-tag-suffix){
                border-radius: 12px;
                color: RGBA(0, 0, 0, .35);
                width: 24px;
                height: 24px;
                display: flex;
                justify-content: center;
                align-items: center;
                padding-right: 0 !important;
                font-size: 13px;
                &:hover {
                  background-color: RGBA(0, 0, 0, .02);
                  color: RGBA(0, 0, 0, .65);
                  cursor: pointer;
                }
              }
            }
          }
        }
      }
    }
    :global(.arco-modal-footer) {
      border: none;
    }
  }
  
  
  
  .customContainer{
    .butFont {
      font-size: 14px;
      font-weight: 600;
    }
    .blueBut {
      background-color: RGBA(68, 85, 242, .95);
      color: #FFFFFF;
      border-radius: 4px;
      width: 104px;
      height: 40px;
      &:hover {
        color: #FFFFFF !important;
        background-color: RGBA(71, 86, 223, 1) !important;
      }
    }
    .normalBut {
      background-color: RGBA(68, 85, 242, .04);
      color: RGBA(68, 85, 242, .95);
      border-radius: 4px;
      width: 104px;
      height: 40px;
      &:hover {
        color: RGBA(68, 85, 242, .95) !important;
        background-color: RGBA(240, 241, 254, 1) !important;
      }
    }
    
  
    .rowEndCenter {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .countAppText {
      font-size: 14px;
      font-weight: 400;
      color: RGBA(0, 0, 0, .35);
      margin-right: 16px;
    }
    .searchBox {
      width: 320px;
      height: 40px;
      border-radius: 4px;
      outline: none;
      border: 1px solid RGBA(0, 0, 0, .08);
      font-size: 14px;
      font-weight: 400;
      background-color: #FFFFFF;
      margin-right: 16px;
      :global(.arco-input-inner-wrapper) {
        background-color: #FFFFFF;
      }
      input::placeholder{
        color: RGBA(0, 0, 0, .65);
      }
    }
    .selectBox{
      height: 42px;
      width: 202px;
      border-radius: 4px;
      border: 1px solid RGBA(0, 0, 0, .08);
      :global(.arco-select-view){
        
        height: 40px;
        width: 200px;
        background-color: #FFFFFF;
      }
    }
    .customCardBox {
      height: calc(100vh - 200px);
      overflow-y: auto;
      display: flex;
      justify-content: flex-start;
      justify-items: flex-start;
      flex-wrap: wrap;
      padding: 4px;
      .rowStartCenter{
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
      .customCard {
        cursor: pointer;
        margin-bottom: 16px;
        margin-right: 16px;
        min-width: 240px;
        height: 240px;
        border-radius: 4px;
        border: 1px solid RGBA(68, 85, 242, .08);
        width: calc((100% - 80px) / 4);
        max-width: calc((100% - 80px) / 4);
        .folderIcon{
          max-width: 48px;
          max-height: 48px;
          margin-right: 16px;
          margin-bottom: 16px;
          img {
            max-width: 48px;
            max-height: 48px;
          }
        }
        .groupName{
          color: RGBA(0, 0, 0, .8);
          font-size: 20px;
          font-weight: 600;
        }
        .tag{
          padding: 2px 6px;
          border-radius: 4px;
          border: 1px solid RGBA(0, 0, 0, .08);
          font-weight: 400;
          font-size: 12px;
          margin-right: 4px;
          color: RGBA(0, 0, 0, .65);
        }
        .description {
          font-size: 14px;
          font-weight: 400;
          color: RGBA(0, 0, 0, .65);
          line-height: 24px;
        }
        .options{
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: absolute;
          bottom: 24px;
          left: 24px;
          width: calc(100% - 48px);
  
        }
      }
    }
  }
}
