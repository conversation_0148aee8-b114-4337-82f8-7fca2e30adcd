import { Modal, Button, Input, Grid, Select, Checkbox } from '@arco-design/web-react';
import Option from '@arco-design/web-react/es/Select/option';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import styles from './style/index.module.less';
import { useState, useEffect } from 'react';
import IconClose from '@/assets/application/close.svg';
import useLocale from '@/utils/useLocale';
import ButtonComponent from '@arco-design/web-react/es/Button';
import Text from '@arco-design/web-react/es/Typography/text';

const { TextArea } = Input;
const { Row, Col } = Grid;

const FunctionModel = ({ visible, onClose, data, currentData }) => {
    const locale = useLocale();

    useEffect(() => {
        setAcpToolName(currentData.name || '');
        setAcpToolDescription(currentData.description || '');
        
        // 将传入的parameters格式转换为组件内部使用的格式
        if (currentData.parameters) {
            const { properties, required = [] } = currentData.parameters;
            if (properties) {
                const params: ParamItem[] = Object.keys(properties).map(name => ({
                    name,
                    type: properties[name].type || 'string',
                    description: properties[name].description || '',
                    required: required.includes(name)
                }));
                setAcpToolParams(params);
            } else {
                setAcpToolParams([]);
            }
        } else {
            setAcpToolParams([]);
        }
    }, [currentData]);

    interface ParamItem {
        required: boolean;
        name: string;
        type: string;
        description: string;
    }

    const [acpToolName, setAcpToolName] = useState('');
    const [acpToolDescription, setAcpToolDescription] = useState('');
    const [acpToolParams, setAcpToolParams] = useState<ParamItem[]>([]);

    // 关闭模态框
    const handleClose = () => {
        // 重置表单数据
        setAcpToolName('');
        setAcpToolDescription('');
        setAcpToolParams([]);
        onClose();
    };

    const paramTypes = [
        { value: 'string', label: '字符串' },
        { value: 'number', label: '数字' },
        { value: 'boolean', label: '布尔值' },
        { value: 'object', label: '对象' },
        { value: 'array', label: '数组' }
    ];

    const footContainer = () => {
        // 判断是否可以提交表单
        const canSubmit = acpToolName && acpToolDescription;
        
        return (
            <RowComponent className={styles.operateButGroup}>
                <ButtonComponent
                    type="secondary"
                    style={{ marginRight: 12 }}
                    className={[styles.cancelBut, styles.but]}
                    onClick={handleClose}
                >
                    <Text className={styles.text}>
                        取消
                    </Text>
                </ButtonComponent>
                <ButtonComponent
                    type="primary"
                    className={[styles.createBut, styles.but, !canSubmit && styles.disabled]}
                    disabled={!canSubmit}
                    onClick={() => {
                        // 构建参数对象
                        const parameters = {
                            type: "object",
                            properties: {},
                            required: []
                        };
                        
                        // 填充参数信息
                        acpToolParams.forEach(param => {
                            parameters.properties[param.name] = {
                                type: param.type,
                                description: param.description
                            };
                            
                            if (param.required) {
                                parameters.required.push(param.name);
                            }
                        });
                        
                        // 检查是否是编辑现有数据
                        if (currentData && currentData.name) {
                            // 编辑模式：更新现有数据
                            const index = data.findIndex(item => item.name === currentData.name);
                            if (index !== -1) {
                                data[index] = {
                                    name: acpToolName,
                                    description: acpToolDescription,
                                    parameters: parameters
                                };
                            }
                        } else {
                            // 新增模式：添加到数据中
                            data.push({
                                name: acpToolName,
                                description: acpToolDescription,
                                parameters: parameters
                            });
                        }
                        
                        handleClose();
                    }}
                >
                    <Text className={styles.text}>
                        确认
                    </Text>
                </ButtonComponent>
            </RowComponent>
        )
    }

    return (
        <Modal
            visible={visible}
            onCancel={handleClose}
            footer={footContainer()}
            maskClosable={false}
            className="ACPtoolModel"
            wrapClassName="acp-tool-modal-wrap"
            closeIcon={<IconClose />}
        >
            <div className="ACPtool-modal-title">{locale['menu.application.info.setting.placeholder.addFunction']}</div>
            <div className="ACPtool-modal-content">
                {/* 名称 */}
                <RowComponent style={{ marginTop: 16 }}>
                    <Text className={styles.subtitle}>{locale['menu.application.info.basic.names']}</Text>
                </RowComponent>
                <RowComponent style={{ marginTop: 8 }}>
                    <div style={{ position: 'relative', width: '30%' }}>
                        <TextArea
                            placeholder={locale['menu.application.info.basic.names']}
                            maxLength={50}
                            value={acpToolName}
                            onChange={(value) => {
                                // 只允许英文字母、数字、下划线
                                const filteredValue = value.replace(/[^a-zA-Z0-9_]/g, '');
                                setAcpToolName(filteredValue);
                            }}
                            style={{
                                backgroundColor: '#fff',
                                border: '1px solid #e5e6eb',
                                width: '100%',
                                resize: 'none',
                                height: '40px',
                                borderRadius: '8px',
                                padding: '8px 12px',
                                lineHeight: '24px',
                                paddingRight: '40px'
                            }}
                        />
                        <div style={{
                            position: 'absolute',
                            top: '50%',
                            right: '8px',
                            transform: 'translateY(-50%)',
                            fontSize: '12px',
                            color: 'rgba(0, 0, 0, 0.45)',
                            pointerEvents: 'none'
                        }}>
                            {acpToolName.length}/50
                        </div>
                    </div>
                </RowComponent>

                {/* 描述 */}
                <RowComponent style={{ marginTop: 16 }}>
                    <Text className={styles.subtitle}>{locale['menu.application.info.basic.descript']}</Text>
                </RowComponent>
                <RowComponent style={{ marginTop: 8 }}>
                    <div style={{ position: 'relative', width: '100%' }}>
                        <TextArea
                            placeholder={locale['menu.application.info.basic.descript']}
                            maxLength={100}
                            value={acpToolDescription}
                            onChange={(value) => {
                                setAcpToolDescription(value);
                            }}
                            style={{
                                backgroundColor: '#fff',
                                border: '1px solid #e5e6eb',
                                width: '100%',
                                resize: 'none',
                                height: '64px',
                                borderRadius: '8px',

                            }}
                        />
                        <div style={{
                            position: 'absolute',
                            bottom: '8px',
                            right: '8px',
                            fontSize: '12px',
                            color: 'rgba(0, 0, 0, 0.45)',
                            pointerEvents: 'none'
                        }}>
                            {acpToolDescription.length}/100
                        </div>
                    </div>
                </RowComponent>

                {/* 参数 */}
                <RowComponent className={styles.titleRow} >
                    <div className={styles.titleContent}>
                        <Text className={styles.subtitle}>
                            输入参数
                        </Text>
                        <Text className={styles.subtitlePlaceholder}>
                            用于存储每个用户使用项目过程中，需要持久化存储和读取的数据
                        </Text>
                    </div>
                    <Button
                        className={styles.addParamsBut}
                        onClick={() => {
                            setAcpToolParams([...acpToolParams, {
                                required: false,
                                name: '',
                                type: 'string',
                                description: ''
                            }]);
                        }}
                    >
                        <Text className={styles.operateText}>
                            {locale['menu.application.template.setting.adds']}
                        </Text>
                    </Button>
                </RowComponent>
                <Col className={styles.paramsContainer}>
                    {acpToolParams.length > 0 && (
                        <>
                            {/* 表头 */}
                            <Row className={styles.headerRow}>
                                <div className={styles.headerCheckbox}>必传参数</div>
                                <div className={styles.headerName}>变量名</div>
                                <div className={styles.headerType}>变量类型</div>
                                <div className={styles.headerDescription}>描述</div>
                            </Row>
                            {/* 渲染已选择的参数 */}
                            <div className={styles.selectedItemList}>
                                {acpToolParams.map((param, index) => (
                                    <Row key={index} className={styles.selectedItemRow}>
                                        <Checkbox
                                            checked={param.required}
                                            onChange={(checked) => {
                                                const newParams = [...acpToolParams];
                                                newParams[index].required = checked;
                                                setAcpToolParams(newParams);
                                            }}
                                            style={{
                                                width: '30px',
                                                borderRadius: '8px',
                                            }}
                                        />
                                        <div style={{ position: 'relative', width: '200px' }}>
                                            <TextArea
                                                placeholder={locale['menu.application.info.basic.names'] + ' ' + (index + 1)}
                                                maxLength={50}
                                                value={param.name}
                                                onChange={(value) => {
                                                    // 只允许英文字母、数字、下划线
                                                    const filteredValue = value.replace(/[^a-zA-Z0-9_]/g, '');
                                                    const newParams = [...acpToolParams];
                                                    newParams[index].name = filteredValue;
                                                    setAcpToolParams(newParams);
                                                }}
                                                style={{
                                                    backgroundColor: '#fff',
                                                    border: '1px solid #e5e6eb',
                                                    width: '200px',
                                                    resize: 'none',
                                                    height: '40px',
                                                    borderRadius: '8px',
                                                    padding: '8px 12px',
                                                    lineHeight: '24px',
                                                    paddingRight: '40px'
                                                }}
                                            />
                                            <div style={{
                                                position: 'absolute',
                                                top: '50%',
                                                right: '8px',
                                                transform: 'translateY(-50%)',
                                                fontSize: '12px',
                                                color: 'rgba(0, 0, 0, 0.45)',
                                                pointerEvents: 'none'
                                            }}>
                                                {param.name.length}/50
                                            </div>
                                        </div>
                                        <Select
                                            value={param.type}
                                            placeholder="类型"
                                            onChange={(value) => {
                                                const newParams = [...acpToolParams];
                                                newParams[index].type = value;
                                                setAcpToolParams(newParams);
                                            }}
                                            className={styles.typeSelect}
                                        >
                                            {paramTypes.map(type => (
                                                <Option key={type.value} value={type.value}>
                                                    {type.label}
                                                </Option>
                                            ))}
                                        </Select>
                                        <div style={{ position: 'relative', flex: 1 }}>
                                            <TextArea
                                                placeholder="描述"
                                                maxLength={200}
                                                value={param.description}
                                                onChange={(value) => {
                                                    const newParams = [...acpToolParams];
                                                    newParams[index].description = value;
                                                    setAcpToolParams(newParams);
                                                }}
                                                style={{
                                                    backgroundColor: '#fff',
                                                    border: '1px solid #e5e6eb',
                                                    width: '100%',
                                                    resize: 'none',
                                                    height: '40px',
                                                    borderRadius: '8px',
                                                    padding: '8px 12px',
                                                    lineHeight: '24px',
                                                    paddingRight: '40px'
                                                }}
                                            />
                                            <div style={{
                                                position: 'absolute',
                                                top: '50%',
                                                right: '8px',
                                                transform: 'translateY(-50%)',
                                                fontSize: '12px',
                                                color: 'rgba(0, 0, 0, 0.45)',
                                                pointerEvents: 'none'
                                            }}>
                                                {param.description.length}/200
                                            </div>
                                        </div>
                                        <IconClose
                                            className={styles.deleteIcon}
                                            onClick={() => {
                                                const newParams = [...acpToolParams];
                                                newParams.splice(index, 1);
                                                setAcpToolParams(newParams);
                                            }}
                                        />
                                    </Row>
                                ))}
                            </div>
                        </>
                    )}
                </Col>
            </div>
        </Modal>)
};

export default FunctionModel;