.settingContainer {
  :global(.arco-card-body){
    padding: 0;
  }

}

.cardContainer {
  width: 100%;
  background-color: #fff;
  overflow: hidden;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .headerTitleWrapper {
    display: flex;
    flex-direction: column;

    :global(h6.arco-typography) {
      margin-bottom: 4px;
      font-weight: 600;
      font-size: 14px;
      line-height: 24px;
      color: #333333;
    }

    :global(.arco-typography-secondary) {
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      color: #adadad;
    }
  }

  .headerButtonWrapper {

    .handleAddDataSource,
    .handleAddChannel {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent;
      border: 1px solid #ebebeb;
      border-radius: 8px;
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
      color: #333333;
      padding: 8px 24px;

      &:hover {
        background-color: #fafafa;
        color: #333333;
        border-color: #ebebeb;
      }
    }
  }
}

.listContainer {
  padding: 8px;
  background-color: #fcfcfc;
  border: 1px solid #f5f5f5;
  border-radius: 4px;

  :global(.arco-list-item) {
    padding: 0 !important;
  }
}

.listItem {
  background-color: #ffffff;
  border: 1px solid #f5f5f5;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: background-color 0.2s;
  overflow: hidden;

  &:hover {
    background-color: #ffffff;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.listItemMain {
  width: 100%;

  .listItemContent {
    display: flex;
    align-items: center;
    padding: 8px;
  }
}

.listItemIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;


  .notionIcon {
    width: 24px !important;
    height: 24px !important;
    background-color: #000 !important;
    border-radius: 4px !important;
    color: white !important;
    font-weight: 500;
    font-size: 16px;
  }

  .apiIcon {
    width: 24px !important;
    height: 24px !important;
    background-color: #727e97 !important;
    border-radius: 4px !important;
    color: white !important;
    font-weight: 500;
    font-size: 16px;
  }

  .channelIcon {
    width: 24px !important;
    height: 24px !important;
    background-color: #3370ff !important;
    border-radius: 4px !important;
    color: white !important;
    font-weight: 500;
    font-size: 16px;
  }
}

.listItemTextContent {
  flex: 1;
  min-width: 0;


  :global(.arco-typography) {
    b {
      font-weight: 500;
      color: #1d2129;
      font-size: 14px;
      line-height: 22px;
    }

    &[type="secondary"] {
      font-size: 12px;
      line-height: 20px;
      color: #86909c;
    }
  }
}

.listItemAction {
  display: flex;
  align-items: center;

}

.actionButton {
  color: #0e51a3;
  padding: 4px;
  min-width: auto;
  height: auto;
  border-radius: 4px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #165dff;
  }

}

.sectionMargin {
  margin-top: 24px;
}

.navigationCard {
  background-color: #fff;
}

.anchorContainer {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.navigationTitle {
  margin-bottom: 20px;
  padding-left: 8px;
  font-weight: 600;
  font-size: 16px;
  color: #1d2129;
}

.navItem {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.2s;
  color: #adadad;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;


  &:hover {
    background-color: #f5f7fa;
    color: #165dff;
  }
}

.navIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background-color: #f2f3f5;
  color: #86909c;
}

:global(.arco-anchor) {
  width: 200px;

  :global(.arco-anchor-link-title) {
    padding: 0;
    border-radius: 8px;
  }
}



:global(.arco-anchor-link-active) {

  .navItem {
    background-color: #fafafa;
    color: #4455f2;

    .navIcon {
      background-color: #f0f1fe;
      color: #4455f2;
    }
  }
}

// 数据源模态框样式
.dataSourceModal {
  position: relative;
  padding: 24px;
  width: 640px;
  border-radius: 16px;
  border: 1px solid #f5f5f5;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

  .modalContent {
    display: flex;
    flex-direction: column;
  }

  .modalFooter {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .cancelBtn,
    .saveBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      line-height: 24px;
      height: 40px;
    }

    .cancelBtn {
      background-color: #ffffff;
      color: #5c5c5c;
      border: 1px solid #ebebeb;

      &:hover {
        background-color: #fafafa;
        border-color: #ebebeb;
      }
    }

    .saveBtn {
      background-color: #4455f2;
      color: #ffffff;
      
      &:hover:not(.disabledBtn) {
        background-color: #4152e9;
      }
    }

    .disabledBtn {
      background-color: #c3c8fa !important;
      color: #ffffff !important;
      cursor: not-allowed;
      
      &:hover {
        background-color: #c3c8fa !important;
      }
    }
  }

  .requiredIcon {
    color: #4455f2;
    font-size: 15px;
    line-height: 16px;
    margin-left: 4px;
    position: relative;
    top: -1px;
    font-weight: bold;
  }

  :global(.arco-form-label-item) {
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    line-height: 24px;
  }

  :global(.arco-space-item) {
    margin-right: 0 !important;
  }

  :global(.arco-modal-header) {
    padding: 0;
    height: auto;
    border-bottom: none;

    :global(.arco-modal-title) {
      font-weight: 600;
      font-size: 20px;
      line-height: 32px;
      color: #333333;
      margin-bottom: 16px;
      text-align: left;
    }
  }

  :global(.arco-form-item) {
    margin-bottom: 24px;

    :global(.arco-form-item > .arco-form-label-item) {
      font-weight: 600;
      font-size: 14px;
      color: #333333;
      line-height: 24px;
    }
  }

  :global(.arco-input) {
    padding: 8px 12px;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    background-color: transparent;

    &::placeholder {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #d6d6d6;
    }
  }

  :global(.arco-input-disabled) {
    background-color: #fcfcfc;
  }

  :global(.arco-modal-content) {
    padding: 0;
    width: 100%;
    height: 100%;
  }

  :global(.arco-modal-footer) {
    display: none;
  }

  :global(.arco-modal-close-icon) {
    position: absolute;
    right: 32px;
    top: 32px;
    font-size: 12px;
    cursor: pointer;
    color: var(--color-text-1);
  }

  :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
    background-color: #4152e9;
    color: #ffffff;
  }
}

// 渠道模态框样式
.channelModal {
  position: relative;
  padding: 24px;
  width: 640px;
  border-radius: 16px;
  border: 1px solid #f5f5f5;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);

  .channelHeader {
    display: flex;
    margin-bottom: 24px;
    width: 100%;

    .iconAndName {
      display: flex;
      align-items: center;
      width: 100%;

      .channelIconWrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 72px;
        height: 72px;
        border-radius: 8px;
        background-color: #f0f1fe;
        flex-shrink: 0;

        .channelModalIcon {
          width: 32px;
          height: 32px;
          color: #4455f2;
        }
      }

      .divider {
        width: 1px;
        height: 72px;
        background-color: #f5f5f5;
        margin: 0 24px;
      }

      .nameFormContainer {
        flex: 1;
        
        .nameFormItem {
          margin-bottom: 0;
        }
      }
    }
  }

  .modalContent {
    display: flex;
    flex-direction: column;
  }

  .modalFooter {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 24px;

    .cancelBtn,
    .saveBtn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 24px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      line-height: 24px;
      height: 40px;
    }

    .cancelBtn {
      background-color: #ffffff;
      color: #5c5c5c;
      border: 1px solid #ebebeb;

      &:hover {
        background-color: #fafafa;
        border-color: #ebebeb;
      }
    }

    .saveBtn {
      background-color: #4455f2;
      color: #ffffff;
      
      &:hover:not(.disabledBtn) {
        background-color: #4152e9;
      }
    }

    .disabledBtn {
      background-color: #c3c8fa !important;
      color: #ffffff !important;
      cursor: not-allowed;
      
      &:hover {
        background-color: #c3c8fa !important;
      }
    }
  }

  .requiredIcon {
    color: #4455f2;
    font-size: 15px;
    line-height: 16px;
    margin-left: 4px;
    position: relative;
    top: -1px;
    font-weight: bold;
  }

  .descriptionInput {
    resize: none;
    min-height: 64px;
  }

  :global(.arco-form-label-item) {
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    line-height: 24px;
  }

  :global(.arco-space-item) {
    margin-right: 0 !important;
  }

  :global(.arco-modal-header) {
    padding: 0;
    height: auto;
    border-bottom: none;

    :global(.arco-modal-title) {
      font-weight: 600;
      font-size: 20px;
      line-height: 32px;
      color: #333333;
      margin-bottom: 16px;
      text-align: left;
    }
  }

  :global(.arco-form-item) {
    margin-bottom: 24px;

    :global(.arco-form-item > .arco-form-label-item) {
      font-weight: 600;
      font-size: 14px;
      color: #333333;
      line-height: 24px;
    }
  }

  :global(.arco-input) {
    padding: 8px 12px;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    background-color: transparent;

    &::placeholder {
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
      color: #d6d6d6;
    }
  }

  :global(.arco-textarea) {
    background-color: transparent;
    border: 1px solid #ebebeb;
    border-radius: 8px;
    resize: none;

    &::placeholder {
      color: #d6d6d6;
    }
  }

  :global(.arco-textarea-word-limit) {
    color: #adadad;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
  }

  :global(.arco-modal-content) {
    padding: 0;
    width: 100%;
    height: 100%;
  }

  :global(.arco-modal-footer) {
    display: none;
  }

  :global(.arco-modal-close-icon) {
    position: absolute;
    right: 32px;
    top: 32px;
    font-size: 12px;
    cursor: pointer;
    color: var(--color-text-1);
  }

  :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
    background-color: #4152e9;
    color: #ffffff;
  }
}

:global {
  .arco-modal-mask {
    background: rgba(0, 0, 0, 0.08);
  }
}