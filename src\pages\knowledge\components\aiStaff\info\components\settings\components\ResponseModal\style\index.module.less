:global(.response-modal-wrap) {
    :global(.arco-modal) {
        display: flex;
        flex-direction: column;
        max-height: 80vh;
        position: relative;
    }

    :global(.arco-modal-header) {
        flex-shrink: 0;
    }

    :global(.arco-modal-content) {
        flex: 1;
        overflow-y: auto;
        max-height: calc(80vh - 120px);
        min-height: 500px;
    }

    :global(.arco-modal-footer) {
        flex-shrink: 0;
        border-top: 1px solid rgba(0, 0, 0, 0.06);
        padding: 16px 24px;
        background-color: #fff;
        position: sticky;
        bottom: 0;
        width: 100%;
        z-index: 100;
    }
}

:global(.responseModel) {
    height: 80%;
    width: 70%;
    border-radius: 10px;

    :global(.arco-modal-content) {
        padding: 24px 24px;

    }

    :global(.response-modal-title) {
        font-size: 20px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        display: block;
    }

    :global(.response-modal-content) {
        margin-top: 16px;
        margin-bottom: 16px;

        .selectRowBox {
            height: 40px;
            width: 30%;
            border-radius: 4px;
            border: 1px solid RGBA(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;

            :global(.arco-select-view) {
                background: transparent;
                height: 40px;
                width: 100%;
                padding-right: 6px;

                :global(.arco-select-suffix) {
                    width: 20px;
                }
            }
        }
    }

    .titleRow {
        margin-top: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        width: 100%;

        .titleContent {
            display: flex;
            flex-direction: column;
            gap: 6px;

            .subtitle {
                font-size: 14px;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.85);
                display: block;
            }

            .subtitlePlaceholder {
                font-size: 12px;
                font-weight: 400;
                color: RGBA(0, 0, 0, .35);
            }

        }

        .switchContainer {
            display: flex;
            align-items: center;
            gap: 8px;
            height: 40px;
            width: 76px;
            justify-content: center;
            border-radius: 8px;
            background-color: transparent;
            padding: 0 2px;

            :global(.arco-switch) {
                min-width: 40px;
                height: 24px;
            }

            :global(.arco-switch-checked) {
                background-color: RGBA(13, 41, 254, 0.95) !important;
            }
        }
    }

    .subtitle {
        font-size: 14px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        display: block;
    }

    .operateButGroup {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 0;
        margin: 0;

        .text {
            font-size: 14px;
            font-weight: 600;
        }

        .but {
            border-radius: 4px;
            width: 76px;
            height: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .cancelBut {
            background-color: RGBA(250, 250, 250, 1);

            .text {
                color: RGBA(0, 0, 0, 0.65);
            }
        }

        .createBut {
            background-color: #4455F2;

            .text {
                color: white;
            }

            &.disabled {
                opacity: 0.32;
            }
        }
    }

}