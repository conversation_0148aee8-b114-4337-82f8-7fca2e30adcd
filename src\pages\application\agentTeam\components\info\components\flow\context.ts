import React, { createContext } from 'react';

import { TimeSeriesCard } from '@/types';

export const FlowContext = createContext<{
  handleUpdateNode?: (data: any) => void;
  resetNodeStatus?: () => void;
  getVersionListByNode?: any;
  selectedNodes?: any;
  handleDeleteNode?: (nodeId: string) => void;
  isClickBlank?: boolean;
  setIsClickBlank?: (isClickBlank: boolean) => void;
  currentNodeVersionData?: Partial<TimeSeriesCard>[];
  handleAddTimeSeriesCard?: (data: any) => void;
  nodes?: any;
  isRequestAddAgent?: boolean;
  handleActivateAgentVersion?: (data: any) => void;
  setCurrentNodeVersionData?: (data: any) => void;
  handleUpdateEdge?: (data: any) => void;
  resetEdge?: () => void;
  setIsLoading?: (isLoading: boolean) => void;
  isLoading?: boolean;
  setIsInitialized?: (isInitialized: boolean) => void;
  isInitialized?: boolean;
}>({});
