import { LanguageTranslationMap } from '@/pages/knowledge/components/file-manager/src/constants/common';
import { ResponseGetType } from '@/pages/knowledge/components/file-manager/src/interfaces/database/base';
import { IToken } from '@/pages/knowledge/components/file-manager/src/interfaces/database/chat';
import { ITenantInfo } from '@/pages/knowledge/components/file-manager/src/interfaces/database/knowledge';
import { ILangfuseConfig } from '@/pages/knowledge/components/file-manager/src/interfaces/database/system';
import {
  ISystemStatus,
  ITenant,
  ITenantUser,
  IUserInfo,
} from '@/pages/knowledge/components/file-manager/src/interfaces/database/user-setting';
import { ISetLangfuseConfigRequestBody } from '@/pages/knowledge/components/file-manager/src/interfaces/request/system';
import userService, {
  addTenantUser,
  agreeTenant,
  deleteTenantUser,
  listTenant,
  listTenantUser,
} from '@/pages/knowledge/components/file-manager/src/services/user-service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Modal, message } from 'antd';
import DOMPurify from 'dompurify';
import { isEmpty } from 'lodash';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

export const useFetchUserInfo = (): ResponseGetType<IUserInfo> => {
  const { i18n } = useTranslation();

  const { data, isFetching: loading } = useQuery(
    ['userInfo'],
    async () => {
      const { data } = await userService.user_info();
      if (data.code === 0) {
        i18n.changeLanguage(
          LanguageTranslationMap[
            data.data.language as keyof typeof LanguageTranslationMap
          ]
        );
      }
      return data?.data ?? {};
    },
    {
      initialData: {},
      cacheTime: 0,
    }
  );

  return { data, loading };
};

export const useFetchTenantInfo = (
  showEmptyModelWarn = false
): ResponseGetType<ITenantInfo> => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const { data, isFetching: loading } = useQuery(
    ['tenantInfo'],
    async () => {
      debugger
      const result = await userService.get_tenant_info();
      const { data: res } = result;

      if (res.code === 0) {
        // llm_id is chat_id
        // asr_id is speech2txt
        const { data } = res;
        if (
          showEmptyModelWarn &&
          (isEmpty(data.embd_id) || isEmpty(data.llm_id))
        ) {
          Modal.warning({
            title: t('common.warn'),
            content: (
              <div
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(t('setting.modelProvidersWarn')),
                }}
              ></div>
            ),
            onOk() {
              navigate('/user-setting/model');
            },
          });
        }
        data.chat_id = data.llm_id;
        data.speech2text_id = data.asr_id;

        return data;
      }

      return res;
    },
    {
      initialData: {},
      cacheTime: 0,
    }
  );

  return { data, loading };
};

export const useSelectParserList = (): Array<{
  value: string;
  label: string;
}> => {
  const { data: tenantInfo } = useFetchTenantInfo(true);

  const parserList = useMemo(() => {
    const parserArray: Array<string> = tenantInfo?.parser_ids?.split(',') ?? [];
    return parserArray.map((x) => {
      const arr = x.split(':');
      return { value: arr[0], label: arr[1] };
    });
  }, [tenantInfo]);

  return parserList;
};

export const useSaveSetting = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const {
    data,
    isLoading: loading,
    mutateAsync,
  } = useMutation(
    async (userInfo: { new_password: string } | Partial<IUserInfo>) => {
      const { data } = await userService.setting(userInfo);
      if (data.code === 0) {
        message.success(t('message.modified'));
        queryClient.invalidateQueries(['userInfo']);
      }
      return data?.code;
    }
  );

  return { data, loading, saveSetting: mutateAsync };
};

export const useFetchSystemVersion = () => {
  const [version, setVersion] = useState('');
  const [loading, setLoading] = useState(false);

  const fetchSystemVersion = useCallback(async () => {
    try {
      setLoading(true);
      const { data } = await userService.getSystemVersion();
      if (data.code === 0) {
        setVersion(data.data);
        setLoading(false);
      }
    } catch (error) {
      setLoading(false);
    }
  }, []);

  return { fetchSystemVersion, version, loading };
};

export const useFetchSystemStatus = () => {
  const [systemStatus, setSystemStatus] = useState<ISystemStatus>(
    {} as ISystemStatus
  );
  const [loading, setLoading] = useState(false);

  const fetchSystemStatus = useCallback(async () => {
    setLoading(true);
    const { data } = await userService.getSystemStatus();
    if (data.code === 0) {
      setSystemStatus(data.data);
      setLoading(false);
    }
  }, []);

  return {
    systemStatus,
    fetchSystemStatus,
    loading,
  };
};

export const useFetchManualSystemTokenList = () => {
  const {
    data,
    isLoading: loading,
    mutateAsync,
  } = useMutation(async () => {
    const { data } = await userService.listToken();

    return data?.data ?? [];
  });

  return { data, loading, fetchSystemTokenList: mutateAsync };
};

export const useFetchSystemTokenList = () => {
  const {
    data,
    isFetching: loading,
    refetch,
  } = useQuery<IToken[]>(
    ['fetchSystemTokenList'],
    async () => {
      const { data } = await userService.listToken();

      return data?.data ?? [];
    },
    {
      initialData: [],
      cacheTime: 0,
    }
  );

  return { data, loading, refetch };
};

export const useRemoveSystemToken = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isLoading: loading,
    mutateAsync,
  } = useMutation(async (token: string) => {
    const { data } = await userService.removeToken({}, token);
    if (data.code === 0) {
      message.success(t('message.deleted'));
      queryClient.invalidateQueries(['fetchSystemTokenList']);
    }
    return data?.data ?? [];
  });

  return { data, loading, removeToken: mutateAsync };
};

export const useCreateSystemToken = () => {
  const queryClient = useQueryClient();

  const {
    data,
    isLoading: loading,
    mutateAsync,
  } = useMutation(async (params: Record<string, any>) => {
    const { data } = await userService.createToken(params);
    if (data.code === 0) {
      queryClient.invalidateQueries(['fetchSystemTokenList']);
    }
    return data?.data ?? [];
  });

  return { data, loading, createToken: mutateAsync };
};

export const useListTenantUser = () => {
  const { data: tenantInfo } = useFetchTenantInfo();
  const tenantId = tenantInfo.tenant_id;
  const {
    data,
    isFetching: loading,
    refetch,
  } = useQuery<ITenantUser[]>(
    ['listTenantUser', tenantId],
    async () => {
      const { data } = await listTenantUser(tenantId);

      return data?.data ?? [];
    },
    {
      initialData: [],
      cacheTime: 0,
      enabled: !!tenantId,
    }
  );

  return { data, loading, refetch };
};

export const useAddTenantUser = () => {
  const { data: tenantInfo } = useFetchTenantInfo();
  const queryClient = useQueryClient();
  const {
    data,
    isLoading: loading,
    mutateAsync,
  } = useMutation(async (email: string) => {
    const { data } = await addTenantUser(tenantInfo.tenant_id, email);
    if (data.code === 0) {
      queryClient.invalidateQueries(['listTenantUser']);
    }
    return data?.code;
  });

  return { data, loading, addTenantUser: mutateAsync };
};

export const useDeleteTenantUser = () => {
  const { data: tenantInfo } = useFetchTenantInfo();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isLoading: loading,
    mutateAsync,
  } = useMutation(
    async ({ userId, tenantId }: { userId: string; tenantId?: string }) => {
      const { data } = await deleteTenantUser({
        tenantId: tenantId ?? tenantInfo.tenant_id,
        userId,
      });
      if (data.code === 0) {
        message.success(t('message.deleted'));
        queryClient.invalidateQueries(['listTenantUser']);
        queryClient.invalidateQueries(['listTenant']);
      }
      return data?.data ?? [];
    }
  );

  return { data, loading, deleteTenantUser: mutateAsync };
};

export const useListTenant = () => {
  const { data: tenantInfo } = useFetchTenantInfo();
  const tenantId = tenantInfo.tenant_id;
  const {
    data,
    isFetching: loading,
    refetch,
  } = useQuery<ITenant[]>(
    ['listTenant', tenantId],
    async () => {
      const { data } = await listTenant();

      return data?.data ?? [];
    },
    {
      initialData: [],
      cacheTime: 0,
      enabled: !!tenantId,
    }
  );

  return { data, loading, refetch };
};

export const useAgreeTenant = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isLoading: loading,
    mutateAsync,
  } = useMutation(async (tenantId: string) => {
    const { data } = await agreeTenant(tenantId);
    if (data.code === 0) {
      message.success(t('message.operated'));
      queryClient.invalidateQueries(['listTenant']);
    }
    return data?.data ?? [];
  });

  return { data, loading, agreeTenant: mutateAsync };
};

export const useSetLangfuseConfig = () => {
  const { t } = useTranslation();
  const {
    data,
    isLoading: loading,
    mutateAsync,
  } = useMutation(async (params: ISetLangfuseConfigRequestBody) => {
    const { data } = await userService.setLangfuseConfig(params);
    if (data.code === 0) {
      message.success(t('message.operated'));
    }
    return data?.code;
  });

  return { data, loading, setLangfuseConfig: mutateAsync };
};

export const useDeleteLangfuseConfig = () => {
  const { t } = useTranslation();
  const {
    data,
    isLoading: loading,
    mutateAsync,
  } = useMutation(async () => {
    const { data } = await userService.deleteLangfuseConfig();
    if (data.code === 0) {
      message.success(t('message.deleted'));
    }
    return data?.code;
  });

  return { data, loading, deleteLangfuseConfig: mutateAsync };
};

export const useFetchLangfuseConfig = () => {
  const { data, isFetching: loading } = useQuery<ILangfuseConfig>(
    ['fetchLangfuseConfig'],
    async () => {
      const { data } = await userService.getLangfuseConfig();

      return data?.data;
    },
    {
      cacheTime: 0,
    }
  );

  return { data, loading };
};
