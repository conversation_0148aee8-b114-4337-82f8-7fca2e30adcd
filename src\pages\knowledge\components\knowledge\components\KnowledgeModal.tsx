import {
  useState,
  useRef,
  useImperativeHandle,
  useMemo,
  useReducer,
  forwardRef,
  useCallback,
  CSSProperties,
} from 'react';
import {
  Button,
  Divider,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  Image,
  InputTag,
} from '@arco-design/web-react';
import {
  useCreateKnowledge,
  useUpdateKnowledge,
} from '../hooks/knowledge-hooks';
import { getKbDetail } from '../services/knowledge-service';
import IconKnowledge from '../svg/IconKnowledge.svg';
import KnowledgeMemberModal from './KnowledgeMemberModal';
import { IconPlus } from '@arco-design/web-react/icon';
import IconCloseTag from '@/assets/close.svg';

const Option = Select.Option;
const FormItem = Form.Item;

// 新增/修改知识库
const KnowledgeModal = forwardRef((props, ref) => {
  const [visible, setVisible] = useState(false);
  // const [tags, setTags] = useState<string[]>([]);

  const { loading: createLoading, createKnowledge } = useCreateKnowledge();

  const { saveKnowledgeConfiguration, loading: updateLoading } =
    useUpdateKnowledge(true);

  const [form] = Form.useForm();

  const permission = Form.useWatch('permission', form);

  const id = Form.useWatch('id', form);

  const confirmLoading = useMemo(
    () => (id ? updateLoading : createLoading),
    [id, createLoading]
  );

  const [tags, dispatchTag] = useReducer((state, action) => {
    switch (action.type) {
      case 'add':
        return [...state, `标签${state.length + 1}`];
        break;
      case 'remove':
        const _state = state.slice();
        _state.splice(action.index, 1);
        return _state;
      case 'change':
        return action.value;
    }
  }, []);

  const onOpen = async (row?: any) => {
    form.resetFields();
    if (row) {
      const { id } = row;
      const {
        data: { data: knowledgeDetails },
      } = await getKbDetail(id);

      form.setFieldsValue(knowledgeDetails);

      dispatchTag({
        type: 'change',
        value: knowledgeDetails?.tags || [],
      });
    }
    setVisible(true);
  };

  useImperativeHandle(ref, () => ({
    open: onOpen,
  }));

  const title = useMemo<string>(() => {
    return id ? '修改库' : '创建库';
  }, [id]);

  const KnowledgeMemberModalRef = useRef<{
    open: () => void;
  }>();

  const hideModal = useCallback(() => {
    setVisible(false);
  }, []);

  const handleOk = async () => {
    const ret = await form.validate();
    const { id, ...values } = ret;
    if (id) {
      await saveKnowledgeConfiguration({
        ...values,
        kb_id: id,
      });
    } else {
      await createKnowledge(values);
    }
    hideModal();
  };

  const handleCancel = () => {
    dispatchTag({
      type: 'change',
      value: [],
    });
    setVisible(false);
  };

  // 自定义footer
  const customFooter = (
    <div className={`flex justify-end gap-[12px] mt-[24px]`}>
      <Button
        className={
          'h-[40px] p-[8px_24px] rounded-[8px] text-[14px] transition-all shadow-none border border-[#e8e8e8] bg-white text-[#666] hover:border-[#d9d9d9] hover:bg-[#fafafa] hover:text-[#333] focus:border-[#d9d9d9] focus:bg-[#fafafa] focus:text-[#333]'
        }
        onClick={handleCancel}
      >
        取消
      </Button>
      <Button
        type="primary"
        className={`h-[40px] p-[8px_24px] rounded-[8px] text-[14px] transition-all shadow-none border-none bg-[#4455f2] text-white hover:bg-[#3441d9] hover:text-whitefocus:bg-[#3441d9] focus:text-white [&.disabled]:bg-[#c3c8fa] [&.disabled]:border-none [&.disabled]:cursor-not-allowed [&.disabled]:text-white`}
        // loading={loading}
        // disabled={isConfirmDisabled}
        onClick={handleOk}
      >
        创建
      </Button>
    </div>
  );

  return (
    <Modal
      title={title}
      visible={visible}
      cancelText="取消"
      okText="创建"
      onCancel={handleCancel}
      alignCenter={false}
      className="min-w-[640px] [&_.arco-modal-content]:p-[24px_24px_0] [&_.arco-modal-footer]:border-0 [&_.arco-modal-footer]:pt-0 top-[5%] rounded-[16px] shadow-[0px_4px_8px_0px_#00000014] [&_.arco-modal-header]:border-0 [&_.arco-modal-title]:text-left [&_.arco-modal-header]:p-[24px_24px_0] [&_.arco-modal-header]:h-auto [&_.arco-modal-title]:font-bolder [&_.arco-modal-title]:text-[20px]"
      confirmLoading={confirmLoading}
      onOk={handleOk}
      footer={customFooter}
    >
      <Form form={form} layout="vertical" autoComplete="off">
        <FormItem field="id" hidden>
          <Input hidden name="id" />
        </FormItem>
        <FormItem field="parser_id" hidden>
          <Input hidden name="parser_id" />
        </FormItem>
        {/* 名称 */}
        <div className="grid grid-cols-[auto_auto_1fr] colum mb-[24px]">
          <IconKnowledge className="shrink-0" />
          <Divider className="h-full mx-[24px]" type="vertical" />
          <FormItem
            className="mb-0"
            label="名称"
            field="name"
            required
            rules={[
              {
                required: true,
                message: '请输入名称',
              },
            ]}
          >
            <Input
              className="bg-white border-[#ebebeb] rounded-[8px] h-[40px] placeholder:text-[#d6d6d6]"
              size="large"
              placeholder="请输入"
            />
          </FormItem>
        </div>
        {/* 描述 */}
        <FormItem label="描述" field="description">
          <Input.TextArea
            className="min-h-[64px] resize-none bg-white border-[#ebebeb] rounded-[8px] placeholder:text-[#d6d6d6]"
            placeholder="请输入"
            maxLength={100}
            showWordLimit
          />
        </FormItem>
        {/* 标签 */}
        <FormItem>
          <div className="flex items-center justify-between mb-[10px]">
            <div>
              <span>标签</span>
              <span className="text-[#adadad] text-[12px]">
                （标签至多添加3个）
              </span>
            </div>
            <Button
              className={
                'h-[40px] p-[8px_24px] bg-white border-[#ebebeb] rounded-[8px] text-[#5c5c5c] hover:bg-[#fcfcfc] hover:border-[#ebebeb]'
              }
              onClick={() => {
                if (tags.length >= 3) {
                  // 这里可以添加提示信息
                  return;
                }
                dispatchTag({
                  type: 'add',
                });
              }}
            >
              添加
            </Button>
          </div>
          {tags.length > 0 && (
            <div className="flex flex-col gap-[8px] p-[8px] bg-[#fcfcfc] border border-[#f5f5f5] rounded-[8px]">
              {tags.map((tag, index) => (
                <div key={`tag-${index}`} className={'w-full'}>
                  <Input
                    className="[&_.arco-input]:p-0 [&_.arco-input]:border-none [&_.arco-input]:rounded-[0px] [&_.arco-input]:bg-transparent [&_.arco-input-inner-wrapper]:p-[8px_8px_8px_12px] [&_.arco-input-inner-wrapper]:border [&_.arco-input-inner-wrapper]:border-[#f5f5f5] [&_.arco-input-inner-wrapper]:bg-white"
                    autoFocus={tag === ''}
                    value={tag}
                    onChange={(value) => {
                      if (value && value.length > 20) {
                        return;
                      }
                      const newTags = [...tags];
                      newTags[index] = value;
                      dispatchTag({
                        type: 'change',
                        value: newTags,
                      });
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        e.stopPropagation();
                      }
                    }}
                    placeholder={`标签 ${index + 1}`}
                    suffix={
                      <IconCloseTag
                        className={
                          'cursor-pointer size-[24px] flex items-center justify-center'
                        }
                        onClick={() => {
                          dispatchTag({
                            type: 'remove',
                            index: index,
                          });
                        }}
                      />
                    }
                  />
                </div>
              ))}
            </div>
          )}
        </FormItem>
        {/* 语言 */}
        <FormItem label="语言" field="language" initialValue="zh-CN">
          <Select className="[&_.arco-select-view]:text-[#5c5c5c] [&_.arco-select-view]:bg-white [&_.arco-select-view]:border-[#ebebeb] [&_.arco-select-view]:rounded-[8px] [&_.arco-select-view]:h-[40px] [&_.arco-select-view]:leading-[40px]">
            <Option value={'zh'}>中文</Option>
            <Option value={'en'}>英文</Option>
          </Select>
        </FormItem>
        {/* 权限 */}
        <FormItem label="权限">
          <FormItem field="permission" initialValue={'public'}>
            <Select className="[&_.arco-select-view]:text-[#5c5c5c] [&_.arco-select-view]:bg-white [&_.arco-select-view]:border-[#ebebeb] [&_.arco-select-view]:rounded-[8px] [&_.arco-select-view]:h-[40px] [&_.arco-select-view]:leading-[40px]">
              <Option value={'public'}>公开</Option>
              <Option value={'me'}>个人</Option>
            </Select>
          </FormItem>
          {/* {permission === '1' && (
            <div className="rounded-[8px] border border-[#f0f0f0] p-[4px] flex gap-[4px] flex-wrap">
              <ul>
                <li>
                  <Tag
                    className="h-[32px] text-[#464646] [&_.arco-tag-close-btn]:text-[#969696] rounded-[6px] bg-[#fafafa]"
                    closable
                    bordered
                  >
                    产品
                  </Tag>
                </li>
              </ul>
              <Button
                className="size-[32px] text-[#969696] border [&:not(:hover)]:bg-[#fafafa] border-[#ebebeb] rounded-[6px]"
                type="text"
                icon={<IconPlus />}
                onClick={() => KnowledgeMemberModalRef.current.open()}
              ></Button>
              <KnowledgeMemberModal ref={KnowledgeMemberModalRef} />
            </div>
          )} */}
        </FormItem>
      </Form>
    </Modal>
  );
});

export default KnowledgeModal;
