// llm-model.ts
import axiosInstance from './interceptors';
import {
    ModelListResponse,
    LlmProviderResponse,
    LlmModelKeyResponse,
    LlmProviderModelResponse,
    LlmModelCreateRequest,
    LlmModelKeyCreateRequest,
    LlmModelKeyUpdateRequest,
    LlmProviderCreateRequest,
    LlmModelConfigCreateRequest,
    LlmModelConfigResponse
} from '@/types/llmModelType';
import { endpoints } from './api-endpoints';


/**
 * 获取LLM模型列表
 * @param params 查询参数
 * @returns Promise<ModelListResponse>
 */
export async function getLlmModelList(): Promise<ModelListResponse[]> {
    try {
        const response = await axiosInstance.get(endpoints.llmListUrl);
        return response.data;
    } catch (error) {
        console.error('获取模型列表失败:', error);
        throw error;
    }
}

/**
 * 获取LLM提供者名称列表
 * @returns Promise<LlmProviderResponse[]>
 */
export async function getLlmProviderNameList(): Promise<string[]> {
    try {
        const response = await axiosInstance.get(endpoints.llmProvidersUrl);

        if (response.data) {
            return response.data || [];
        } else {
            console.error('获取LLM提供者列表失败:', response.data.message);
            return [];
        }
    } catch (error) {
        console.error('获取LLM提供者列表失败:', error);
        throw error;
    }
}

/**
 * 获取LLM提供者模型列表
 * @returns Promise<LlmProviderModelResponse[]>
 */
export async function getLlmProviderModelList(provider: string): Promise<LlmProviderModelResponse[]> {
    try {
        const url = endpoints.llmProviderModelsUrl.replace('{provider}', provider);
        const response = await axiosInstance.get(url);

        if (response.data) {
            return response.data || [];
        } else {
            console.error('获取LLM提供者模型列表失败:', response.data.message);
            return [];
        }
    } catch (error) {
        console.error('获取LLM提供者模型列表失败:', error);
        throw error;
    }
}

/**
 * 获取LLM提供者模型列表(通过providerId)
 * @param llmProviderId 供应商ID
 * @returns Promise<LlmProviderModelResponse[]>
 */
export async function getLlmProviderModelListById(llmProviderId: string): Promise<LlmProviderModelResponse[]> {
    try {
        const url = endpoints.llmProviderModelsByIdUrl.replace('{llmProviderId}', llmProviderId);
        const response = await axiosInstance.get(url);

        if (response.data) {
            return response.data || [];
        } else {
            console.error('获取LLM提供者模型列表失败:', response.data.message);
            return [];
        }
    } catch (error) {
        console.error('获取LLM提供者模型列表失败:', error);
        throw error;
    }
}

/**
 * 获取LLM类型列表
 * @returns Promise<LlmTypes[]>
 */
export async function getLlmTypeList(): Promise<[]> {
    try {
        const response = await axiosInstance.get(endpoints.llmTypesListUrl);

        // 根据返回的HTTP状态码判断请求是否成功
        if (response) {
            return response.data || [];
        } else {
            console.error('获取LLM类型列表失败:', response.data.message);
            return [];
        }
    } catch (error) {
        console.error('获取LLM类型列表失败:', error);
        throw error;
    }
}

/**
 * 创建LLM模型
 * @param data 模型创建请求参数
 * @returns Promise<boolean> 返回是否创建成功
 */
export async function createLlmModel(data: LlmModelCreateRequest): Promise<boolean> {
    try {
        const response = await axiosInstance.post(endpoints.llmCreateUrl, data);

        if (typeof response.data === 'boolean') {
            return response.data;
        } else {
            console.error('创建模型失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望 boolean 类型');
        }
    } catch (error) {
        console.error('创建模型失败:', error);
        throw error;
    }
}

/**
 * 删除LLM模型
 * @param id 模型ID
 * @returns Promise<boolean> 返回是否删除成功
 */
export async function deleteLlmModel(id: string): Promise<boolean> {
    try {
        const url = endpoints.llmDeleteUrl.replace('{id}', id);
        const response = await axiosInstance.delete(url);

        if (typeof response.data === 'boolean') {
            return response.data;
        } else {
            console.error('删除模型失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望 boolean 类型');
        }
    } catch (error) {
        console.error('删除模型失败:', error);
        throw error;
    }
}

/**
 * 获取LLM提供者列表
 * @returns Promise<LlmProviderResponse[]> 返回提供者信息列表
 */
export async function getLlmProviderList(): Promise<LlmProviderResponse[]> {
    try {
        const response = await axiosInstance.get(endpoints.llmProvidersListUrl);
        return response.data || [];
    } catch (error) {
        console.error('获取LLM提供者列表失败:', error);
        throw error;
    }
}

/**
 * 创建LLM提供者
 * @param data 提供者创建请求参数
 * @returns Promise<boolean> 返回是否创建成功
 */
export async function createLlmProvider(data: LlmProviderCreateRequest): Promise<boolean> {
    try {
        const response = await axiosInstance.post(endpoints.llmProviderCreateUrl, data);

        if (typeof response.data === 'boolean') {
            return response.data;
        } else {
            console.error('创建LLM提供者失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望 boolean 类型');
        }
    } catch (error) {
        console.error('创建LLM提供者失败:', error);
        throw error;
    }
}

/**
 * 更新LLM提供者
 * @param data 提供者更新请求参数
 * @returns Promise<boolean> 返回是否更新成功
 */
export async function updateLlmProvider(data: LlmProviderCreateRequest): Promise<boolean> {
    try {
        const response = await axiosInstance.post(endpoints.llmProviderUpdateUrl, data);

        if (typeof response.data === 'boolean') {
            return response.data;
        } else {
            console.error('更新LLM提供者失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望 boolean 类型');
        }
    } catch (error) {
        console.error('更新LLM提供者失败:', error);
        throw error;
    }
}

/**
 * 删除LLM提供者
 * @param id 提供者ID
 * @returns Promise<boolean> 返回是否删除成功
 */
export async function deleteLlmProvider(id: string): Promise<boolean> {
    try {
        const url = endpoints.llmProviderDeleteUrl.replace('{id}', id);
        const response = await axiosInstance.delete(url);

        if (typeof response.data === 'boolean') {
            return response.data;
        } else {
            console.error('删除LLM提供者失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望 boolean 类型');
        }
    } catch (error) {
        console.error('删除LLM提供者失败:', error);
        throw error;
    }
}

/**
 * 获取LLM模型密钥列表
 * @returns Promise<LlmModelKeyResponse[]>
 */
export async function getLlmModelKeys(): Promise<LlmModelKeyResponse[]> {
    try {
        const response = await axiosInstance.get(endpoints.llmkeyListUrl);

        if (Array.isArray(response.data)) {
            return response.data;
        } else {
            console.error('获取LLM模型密钥列表失败:', response.data?.message || '响应格式不正确');
            return [];
        }
    } catch (error) {
        console.error('获取LLM模型密钥列表失败:', error);
        throw error;
    }
}

/**
 * 根据供应商获取LLM模型密钥列表
 * @param provider 供应商名称
 * @returns Promise<LlmModelKeyResponse[]>
 */
export async function getLlmModelKeysByProvider(provider: string): Promise<LlmModelKeyResponse[]> {
    try {
        const url = endpoints.llmkeyByProviderUrl.replace('{provider}', provider);
        const response = await axiosInstance.get(url);

        if (Array.isArray(response.data)) {
            return response.data;
        } else {
            console.error('根据供应商获取LLM模型密钥列表失败:', response.data?.message || '响应格式不正确');
            return [];
        }
    } catch (error) {
        console.error('根据供应商获取LLM模型密钥列表失败:', error);
        throw error;
    }
}

/**
 * 新增LLM模型密钥
 * @param data 密钥创建请求参数
 * @returns Promise<boolean> 返回是否创建成功
 */
export async function createLlmModelKey(data: LlmModelKeyCreateRequest): Promise<boolean> {
    try {
        const response = await axiosInstance.post(endpoints.llmkeyCreateUrl, data);

        if (typeof response.data === 'boolean') {
            return response.data;
        } else {
            console.error('新增模型密钥失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望 boolean 类型');
        }
    } catch (error) {
        console.error('新增模型密钥失败:', error);
        throw error;
    }
}

/**
 * 删除LLM模型密钥
 * @param id 密钥ID
 * @returns Promise<boolean> 返回是否删除成功
 */
export async function deleteLlmModelKey(id: string): Promise<boolean> {
    try {
        const url = endpoints.llmkeyDeleteUrl.replace('{id}', id);
        const response = await axiosInstance.delete(url);

        if (typeof response.data === 'boolean') {
            return response.data;
        } else {
            console.error('删除模型密钥失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望 boolean 类型');
        }
    } catch (error) {
        console.error('删除模型密钥失败:', error);
        throw error;
    }
}

/**
 * 更新LLM模型密钥
 * @param data 密钥更新请求参数
 * @returns Promise<boolean> 返回是否更新成功
 */
export async function updateLlmModelKey(data: LlmModelKeyUpdateRequest): Promise<boolean> {
    try {
        const response = await axiosInstance.post(endpoints.llmkeyUpdateUrl, data);

        if (typeof response.data === 'boolean') {
            return response.data;
        } else {
            console.error('更新模型密钥失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望 boolean 类型');
        }
    } catch (error) {
        console.error('更新模型密钥失败:', error);
        throw error;
    }
}

/**
 * 获取LLM模型配置列表
 * @returns Promise<LlmModelConfigResponse[]> 返回模型配置列表
 */
export async function getLlmModelConfigList(): Promise<LlmModelConfigResponse[]> {
    try {
        const response = await axiosInstance.get(endpoints.llmConfigListUrl);
        if (Array.isArray(response.data)) {
            return response.data;
        } else {
            console.error('获取模型配置列表失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望数组');
        }
    } catch (error) {
        console.error('获取模型配置列表失败:', error);
        throw error;
    }
}

/**
 * 创建LLM模型配置
 * @param data 模型配置创建请求参数
 * @returns Promise<boolean> 返回是否创建成功
 */
export async function createLlmModelConfig(data: LlmModelConfigCreateRequest): Promise<boolean> {
    try {
        const response = await axiosInstance.post(endpoints.llmConfigCreateUrl, data);

        if (typeof response.data === 'boolean') {
            return response.data;
        } else {
            console.error('创建模型配置失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望 boolean 类型');
        }
    } catch (error) {
        console.error('创建模型配置失败:', error);
        throw error;
    }
}

/**
 * 删除LLM模型配置
 * @param id 模型配置ID
 * @returns Promise<boolean> 返回是否删除成功
 */
export async function deleteLlmModelConfig(id: string): Promise<boolean> {
    try {
        const url = endpoints.llmConfigDeleteUrl.replace('{id}', id);
        const response = await axiosInstance.delete(url);

        if (typeof response.data === 'boolean') {
            return response.data;
        } else {
            console.error('删除模型配置失败，响应格式不正确:', response.data);
            throw new Error('响应格式不正确，期望 boolean 类型');
        }
    } catch (error) {
        console.error('删除模型配置失败:', error);
        throw error;
    }
}