// src/types/pluginType.ts

// 定义插件列表请求参数接口
export interface PluginListParams {
    pager?: {
        page?: number;
        size?: number;
        count?: number;
        sort?: string | null;
        order?: string;
        offset?: number;
        returnTotal?: boolean;
    };
    useHook?: boolean;
    names?: Array<string> | null;
    similarName?: string | null;
}

// 定义插件列表响应数据接口
export interface Plugin {
    id?: string;
    name?: string;
    description?: string;
    assembly?: string;
    is_core?: boolean;
    icon_url?: string;
    agent_ids?: string[];
    settings_name?: string;
    module?: {
        id?: string;
        name?: string;
        description?: string;
        settings?: {
            name?: string;
        };
        iconUrl?: string;
        agentIds?: string[];
    };
    enabled?: boolean;
}

// 定义插件列表响应数据接口
export interface PluginListResponse {
    items?: Plugin[];
    count?: number;
}


// 上传插件的请求参数类型
export interface PluginUploadParams {
    file: File;
}

// 添加通用响应结构类型（可复用）
export interface PluginUploadResponse<T = any> {
    code: number;
    message: string;
    data: T;
}