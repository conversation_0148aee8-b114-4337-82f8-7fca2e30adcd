:root {
  --color-title-1: #333333;
}

.card {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #f5f5f5;
  border-radius: 8px;
  padding: 20px 24px;

  :global(.arco-card-body) {
    padding: 0;
  }

  :global(.arco-card-header) {
    padding: 0;
    height: 32px;
    margin-bottom: 16px;
  }

  .cardHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .HeaderTag {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;

      .title {
        color: var(--color-title-1);
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
      }
    }
  }

  .chart {
    width: 100%;
    min-height: 200px;
    transition: height 0.3s ease;
  }
}