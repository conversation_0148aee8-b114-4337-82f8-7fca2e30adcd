import React, { useState, useEffect } from 'react';
import { Input, Typography , Table, Spin } from '@arco-design/web-react';
import IconSearch from '@/assets/acp/IconSearch.svg';
import Icontool from '@/assets/acp/Icontool.svg';
import ResourceDetailModal from './ResourceDetailModal';
import { AcpTools } from '@/types/acpServerType';
import styles from './style/index.module.less';

const { Text } = Typography;

interface ResourcesListProps {
    acpServerId?: string;
    acpTools?: AcpTools[];
    loading?: boolean;
}

// 定义资源数据类型
interface ResourceData {
    id: string;
    name: string;
    type: string;
    description: string;
    params: string;
    required?: string[];
    paramDetails?: {  
        [key: string]: {
            title: string;
            type: string;
            description: string;
        };
    };
}

function ResourcesList({ acpServerId, acpTools = [], loading = false }: ResourcesListProps) {
    const [searchText, setSearchText] = useState('');
    const [acpResources, setAcpResources] = useState<ResourceData[]>([]);
    const [filteredResources, setFilteredResources] = useState<ResourceData[]>([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [selectedResource, setSelectedResource] = useState<ResourceData | null>(null);

    // 将AcpTools转换为ResourceData格式
    useEffect(() => {
        if (acpTools && acpTools.length > 0) {
            const convertedResources: ResourceData[] = acpTools.map((tool, index) => {
                // 使用protocolTool中的数据
                const protocolTool = tool.protocolTool;
                const name = protocolTool?.name || tool.name || '未命名工具';
                const description = protocolTool?.description || tool.description || '暂无描述';
                
                // 从inputSchema.properties中提取参数名称
                const properties = protocolTool?.inputSchema?.properties || {};
                const paramNames = Object.keys(properties);
                const params = paramNames.length > 0 ? paramNames.join(', ') : '无参数';
                
                // 转换paramDetails，添加description属性
                const paramDetails = Object.entries(properties).reduce((acc, [key, value]: [string, any]) => {
                    acc[key] = {
                        title: value.title || key,
                        type: value.type || 'string',
                        description: value.description || '无描述'
                    };
                    return acc;
                }, {} as { [key: string]: { title: string; type: string; description: string; } });
                
                return {
                    id: `${index + 1}`, 
                    name: name,
                    type: '工具', // 可以根据实际需要调整
                    description: description,
                    params: params,
                    paramDetails: paramDetails  // 传递转换后的详细参数信息
                };
            });
            setAcpResources(convertedResources);
        } else {
            // 如果没有数据，设置为空数组
            setAcpResources([]);
        }
    }, [acpTools]);

    // 表格列定义
    const columns = [
        {
            title: '名称',
            dataIndex: 'name',
            key: 'name',
            width: 300,
            render: (name) => (
                <div className={styles.nameColumn}>
                    <div className={styles.iconWrapper}>
                        <Icontool style={{ width: 48, height: 48 }} />
                    </div>
                    <Text className={styles.nameText}>{name}</Text>
                </div>
            )
        },
        {   
            title: '类型',
            dataIndex: 'type',
            key: 'type',
            width: 150,
            minWidth: 120,
            render: (type) => {
                const isSystemResource = type === '工具';
                const className = isSystemResource 
                    ? `${styles.resourceType} ${styles.systemResource}` 
                    : `${styles.resourceType} ${styles.dataResource}`;
                return (
                    <Text className={className}>{type}</Text>
                );
            }
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            ellipsis: true,
            render: (description) => (
                <Text className={styles.description} title={description}>
                    {description}
                </Text>
            )
        },
        {
            title: '参数',
            dataIndex: 'params',
            key: 'params',
            width: 200,
            minWidth: 120,
            ellipsis: true,
            render: (params) => (
                <Text className={styles.params} title={params}>{params}</Text>
            )
        }
    ];

    // 处理搜索
    useEffect(() => {
        if (searchText) {
            const filtered = acpResources.filter(resource => 
                resource.name.toLowerCase().includes(searchText.toLowerCase()) ||
                resource.description.toLowerCase().includes(searchText.toLowerCase()) ||
                resource.type.toLowerCase().includes(searchText.toLowerCase())
            );
            setFilteredResources(filtered);
        } else {
            setFilteredResources(acpResources);
        }
    }, [searchText, acpResources]);

    // 处理搜索输入变化
    const handleSearchChange = (value) => {
        setSearchText(value);
    };

    // 处理表格行点击事件
    const handleRowClick = (record) => {
        setSelectedResource(record);
        setModalVisible(true);
    };

    // 关闭模态框
    const handleModalClose = () => {
        setModalVisible(false);
        setSelectedResource(null);
    };

    if (loading) {
        return (
            <div className={styles.resourcesListContainer}>
                <div style={{ padding: '50px', textAlign: 'center' }}>
                    <Spin tip="加载ACP资源中..." />
                </div>
            </div>
        );
    }

    return (
        <div className={styles.resourcesListContainer}>
            <div className={styles.resourcesHeader}>
                <Input
                    prefix={<IconSearch />}
                    placeholder="搜索..."
                    className={styles.searchInput}
                    onChange={handleSearchChange}
                    allowClear
                />
                <Text className={styles.resourceCount}>
                    共 {filteredResources.length} 个资源
                </Text>
            </div>
            <Table
                columns={columns}
                data={filteredResources}
                className={styles.resourceTable}
                rowKey="id"
                pagination={false}
                onRow={(record) => ({
                    onClick: () => handleRowClick(record),
                    style: { cursor: 'pointer' }
                })}
                scroll={{
                    y: 'calc(100vh - 265px)'
                }}
            />
            
            <ResourceDetailModal
                visible={modalVisible}
                onClose={handleModalClose}
                resourceData={selectedResource}
            />
        </div>
    );
}

export default ResourcesList; 