import React, { memo, useMemo } from 'react';
import type { customNodeInterface } from '../types';
import { <PERSON>le, Position } from '@xyflow/react';

import icon1 from '@/assets/flow/icon_1.png';
import icon3 from '@/assets/flow/icon_3.png';
import icon5 from '@/assets/flow/icon_5.png';

import styles from '../style/rightNode.module.less';

import { useSelector, useDispatch } from 'react-redux';
import { GlobalState } from '@/store/index';
import { useNavigate } from 'react-router-dom';
// import MorePop from './morePop';

// 预先检测是否为客户端环境
const isClientSide = typeof window !== 'undefined';

const RightNode = (props: Partial<customNodeInterface>) => {
  const agentDetailMenuName = useSelector((state: GlobalState) => state.agentDetailMenuName);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { data, isConnectable } = props as any;

  // 判断data是否存在acp_tools工具
  const isExistAcpTools = useMemo(() => {
    if (data?.content?.acp_tools?.length) {
      return true;
    }
    return false;
  }, [data]);

  const updateBreadcrumbData = (newBreadcrumb) => {
    dispatch({
      type: 'update-breadcrumb-menu-name',
      payload: { breadcrumbMenuName: newBreadcrumb },
    });
  };

  const handleClickAgentDetail = (data: any) => {
    // 存储当前的activeTab，用于返回时恢复
    sessionStorage.setItem(`agentTeam_${data.applicationId}_activeTab`, 'routing');

    // 更新面包屑数据，使用详情页路径和卡片名称
    const breadcrumbData = new Map([[agentDetailMenuName, data.name]]);
    updateBreadcrumbData(breadcrumbData);

    navigate('/agent/info', { state: { id: data.id, path: 'routing', atId: data.applicationId } });
  };

  return (
    <div onClick={() => handleClickAgentDetail(data)}
      style={{
        cursor: 'pointer'
      }}>
      <div className={styles.RightNodeContainer}>
        <div className={styles.RightNodeTopContent}>
          <div className={styles.RightNodeContent}>
            <img className={styles.RightNodeLogo} src={icon1} />
            <div className={styles.RightNodeTitleBox}>
              <span className={styles.RightNodeTitleText}>{data.name || 'agent_name'}</span>
            </div>
          </div>
        </div>
        <div className={styles.RightNodeDescBox}>
          <span className={styles.RightNodeDescText}>{data?.description || 'agent_description'}</span>
        </div>
        <div className={styles.RightNodeBottomBox}>
          {data?.labels && data.labels.length !== 0 &&
            data.labels.map((item: any, i: number) => (
              <div className={styles.RightNodeBottomBtn} key={i}>
                <span className={styles.RightNodeBottomBtnText}>{item}</span>
              </div>
            ))}
        </div>
      </div>
      {isClientSide && (<Handle
        id="a"
        type="target"
        isConnectable={isConnectable}
        position={Position.Left}
        style={{
          backgroundImage: `url(${icon3})`,
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          width: '24px',
          height: '24px',
          backgroundColor: 'transparent',
          border: 'none',
        }}
      />
      )}
    </div>
  );
};

RightNode.displayName = 'RightNode';

export default memo(RightNode);
