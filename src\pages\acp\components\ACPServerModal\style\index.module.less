// ACPServerModal组件样式
.acpCreateModal {
    position: relative;
    padding: 24px;
    width: 640px;
    border-radius: 16px;

    :global(.arco-alert) {
        padding: 16px 12px !important;
    }

    .testConnectionAlert {
        margin-bottom: 16px;
        height: 32px !important;
        border-radius: 8px;
    }

    .successAlert {
        border: 1px solid #eef8f4 !important;

        :global(.arco-alert-content) {
            color: #2ba471;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
        }
    }

    .failedAlert {
        border: 1px solid #fcf1f0 !important;

        :global(.arco-alert-content) {
            color: #d54941;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
        }
    }

    .modalContent {
        display: flex;
        flex-direction: column;
        width: 100%;
        max-height: 480px;
        overflow-y: auto;

        /* 隐藏滚动条但保留滚动功能 */
        &::-webkit-scrollbar {
            display: none;
        }

        /* 兼容Firefox */
        scrollbar-width: none;

        /* 兼容IE和Edge */
        -ms-overflow-style: none;
    }

    .modalFooter {
        margin-top: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .testConnectionBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            line-height: 24px;
            height: 40px;
            background-color: #ffffff;
            color: #5c5c5c;
            border: 1px solid #ebebeb;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }

            &:disabled {
                background-color: #fcfcfc;
                color: #adadad;
                cursor: not-allowed;
            }
        }

        .rightBtns {
            display: flex;
            gap: 8px;
        }

        .cancelBtn,
        .saveBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            line-height: 24px;
            height: 40px;
        }

        .cancelBtn {
            background-color: #ffffff;
            color: #5c5c5c;
            border: 1px solid #ebebeb;

            &:hover {
                background-color: #fafafa;
                border-color: #ebebeb;
            }
        }

        .saveBtn {
            background-color: #4455f2;
            color: #ffffff;

            &:hover {
                color: #ffffff !important;
            }

            &:hover:not(.disabledBtn) {
                background-color: #4152e9;
            }
        }

        .disabledBtn {
            background-color: #c3c8fa !important;
            color: #ffffff !important;
            cursor: not-allowed;

            &:hover {
                background-color: #c3c8fa !important;
            }
        }
    }

    :global(.arco-form-label-item) {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: #333333;

        :global(.arco-space-align-center .arco-space-item) {
            margin-right: 0 !important;
        }
    }

    :global(.arco-modal-header) {
        padding: 0;
        height: auto;
        border-bottom: none;

        :global(.arco-modal-title) {
            font-weight: 600;
            font-size: 18px;
            line-height: 24px;
            color: #333333;
            margin-bottom: 16px;
            text-align: left;
            padding-bottom: 16px;
            border-bottom: 1px solid #f5f5f5;
        }
    }

    :global(.arco-form-item) {
        margin-bottom: 24px;

        :global(.arco-form-item > .arco-form-label-item) {
            font-weight: 600;
            font-size: 14px;
            line-height: 24px;
            color: #595959;
        }
    }

    :global(.arco-form-label-item > label) {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        color: #333333;
    }

    :global(.arco-input) {
        padding: 8px 12px;
        border: 1px solid #ebebeb;
        border-radius: 8px;
        background-color: #ffffff;

        &::placeholder {
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #d6d6d6;
        }
    }

    // 输入框禁用状态
    :global(.arco-input-disabled) {
        background-color: #fcfcfc;
        border-color: #ebebeb;
        color: #adadad;
        cursor: not-allowed;

        &::placeholder {
            color: #adadad !important;
        }
    }

    :global(.arco-textarea) {
        background-color: transparent;
        min-height: 64px;
        border: 1px solid #ebebeb;
        border-radius: 8px;
        resize: none;

        &::placeholder {
            color: #d6d6d6;
        }
    }

    :global(.arco-textarea-disabled) {
        background-color: #fcfcfc;
    }

    :global(.arco-textarea-word-limit) {
        color: #adadad;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
    }

    // 按钮禁用状态
    :global(.arco-btn-secondary.arco-btn-disabled) {
        background-color: #ffffff;
        color: #adadad;
        cursor: not-allowed;
    }

    :global(.arco-select-size-default.arco-select-single .arco-select-view) {
        padding: 8px;
        height: auto;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: #adadad;
        border-radius: 8px;
        border: 1px solid #ebebeb;
        background-color: #fcfcfc;
        transition: all 0.2s;

        &:hover {
            background-color: #fafafa;
        }
    }

    :global(.arco-select-popup) {
        padding: 8px;
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
        border-radius: 8px;
        border: 1px solid #f5f5f5;
        background-color: #ffffff;

        :global(.arco-select-option) {
            padding: 8px 12px;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            color: #333333;
            border-radius: 8px;
            transition: all 0.2s;
            margin-bottom: 4px;

            &:last-child {
                margin-bottom: 0;
            }

            &:hover {
                background-color: #fafafa;
            }
        }

        :global(.arco-select-option-selected) {
            background-color: #fafafa;
        }
        
        :global(.arco-select-option-active) {
            background-color: #fafafa;
        }
    }

    .cardRadio {
        flex: 1;
        margin-bottom: 0 !important;
        padding: 16px;
        border: 1px solid #EBEBEB;
        border-radius: 8px;
        // text-align: center;
        transition: all 0.3s;
        cursor: pointer;
        display: flex;
        align-items: center;
        // justify-content: center;

        &:hover {
            border-color: #C3C8FA;
        }

        :global(.arco-radio-label) {
            padding: 0;
            width: 100%;

        }

        // 选择指示器（圆点）隐藏，使用边框高亮替代
        // :global(.arco-radio-mask) {
        //     display: none;
        // }


        .cardRadioText {
            font-weight: 600;
            font-size: 14px;
            color: #333333;
        }
    }

    :global(.arco-btn-primary.arco-btn-disabled) {
        background-color: #f5f5f5;
        border-color: #ebebeb;
        color: #adadad;
        cursor: not-allowed;
    }

    :global(.arco-modal-content) {
        min-height: 160px;
        padding: 0;
        width: 100%;
        height: 100%;
    }

    :global(.arco-modal-footer) {
        display: none;
    }

    :global(.arco-modal-close-icon) {
        position: absolute;
        right: 24px;
        top: 24px;
        font-size: 12px;
        cursor: pointer;
        color: var(--color-text-1);
    }

    :global(.arco-btn-primary:not(.arco-btn-disabled):not(.arco-btn-loading):hover) {
        background-color: #e9e9ff;
        color: #4d5ef3;
    }

    // :global(.arco-select) {
    //     background-color: transparent;
    //     border: 1px solid #ebebeb;
    //     border-radius: 4px;
    // }

    .Attention {
        padding: 4px 8px;
        background-color: #f8f8ff;
        border: 1px solid #f0f2fe;
        border-radius: 8px;
    }

    .queryParamsFormItem {
        margin-bottom: 24px;

        .queryParamsContainer {
            .queryParamsHeader {
                margin-bottom: 8px;
                padding: 0 16px;
            }

            .selectedItemList {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .selectedItemRow {
                margin-bottom: 8px;
                padding: 0 8px;
            }
        }

        :global(.arco-input) {
            padding: 8px 56px 8px 12px;
        }
    }

    .additionalHeadersFormItem {
        :global(.arco-input) {
            padding: 8px 56px 8px 12px;
        }
    }

    .deleteOptionBtn {
        border: none !important;
        background: transparent !important;
        padding: 0 !important;
        width: 24px !important;
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer;

        &:hover {
            background-color: #f5f5f5 !important;
            border-radius: 4px !important;
        }

        :global(.arco-btn-icon) {
            margin-right: 0 !important;
        }
    }

    .addTagBtn {
        cursor: pointer;
        padding: 8px 24px;
        background-color: #ffffff !important;
        border: 1px solid #ebebeb !important;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #5c5c5c;
        height: 40px;
    }

    .labelExact {
        font-weight: 400;
        font-size: 12px;
        color: #adadad;
    }

    .templatePlaceholder {
        display: flex;
        flex-direction: column;
    }

    .clientTransportSection {
        padding-top: 24px;
        border-top: 1px solid #f5f5f5;

        .additionalHeadersFormItem {
            margin-top: 24px;

            :global(.arco-input) {
                padding: 8px 56px 8px 12px;
            }
        }

        .connectionTimeoutContainer {

            .sliderContainer {
                width: 100%;
                padding: 0 16px;
                background-color: transparent;
                border: 1px solid #ebebeb;
                border-radius: 8px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        //Switch组件样式
        :global(.arco-switch) {
            background-color: #d6d6d6;
        }

        :global(.arco-switch-checked) {
            background-color: #4455f2;
        }

        // 非编辑模式下关闭的Switch背景颜色
        :global(.arco-switch[disabled]:not(.arco-switch-checked)) {
            background-color: #e8e8e8;
            opacity: 1;
        }

        :global(.arco-switch[disabled]) {
            background-color: #c3c8fa;
        }

        :global(.arco-slider) {
            .arco-slider-track {
                background-color: #4455f2;
            }

            .arco-slider-handle {
                border: 2px solid #4455f2;
                background-color: #ffffff;

                &:hover {
                    border-color: #4152e9;
                }
            }
        }


        :global(.arco-slider-bar) {
            height: 4px;
            background-color: #4455f2;
        }


        :global(.arco-slider-road::before) {
            background-color: #ebebeb;
            height: 4px;
        }

        :global(.arco-slider-button::after) {
            background-color: #ffffff;
            width: 12px;
            height: 24px;
            border: 1px solid #ebebeb;
            box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.04);
            border-radius: 6px;
            top: -6px;
        }


    }
}

// 必填图标样式
.requiredIcon {
    color: #4455f2;
    font-size: 15px;
    line-height: 16px;
    margin-left: 4px;
    position: relative;
    top: -1px;
    font-weight: bold;
}

:global {
    .arco-modal-mask {
        background: rgba(0, 0, 0, 0.16);
    }
}