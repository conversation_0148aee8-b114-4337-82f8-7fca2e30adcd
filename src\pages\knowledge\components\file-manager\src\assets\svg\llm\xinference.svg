<?xml version="1.0" encoding="UTF-8"?>
<svg id="_图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
    viewBox="0 0 283.46 283.46">
    <defs>
        <style>
            .cls-1 {
                fill: url(#_未命名的渐变_5-2);
            }

            .cls-2 {
                fill: url(#_未命名的渐变_9);
            }

            .cls-3 {
                fill: url(#_未命名的渐变_5);
            }
        </style>
        <linearGradient id="_未命名的渐变_5" data-name="未命名的渐变 5" x1="27.03" y1="287.05" x2="253.15" y2="1.14"
            gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#e9a85e" />
            <stop offset="1" stop-color="#f52b76" />
        </linearGradient>
        <linearGradient id="_未命名的渐变_5-2" data-name="未命名的渐变 5" x1="25.96" y1="286.21" x2="252.09" y2=".3"
            xlink:href="#_未命名的渐变_5" />
        <linearGradient id="_未命名的渐变_9" data-name="未命名的渐变 9" x1="-474.33" y1="476.58" x2="-160.37" y2="476.58"
            gradientTransform="translate(669.07 -75.9) rotate(33.75)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#6a0cf5" />
            <stop offset="1" stop-color="#ab66f3" />
        </linearGradient>
    </defs>
    <g>
        <path class="cls-3"
            d="M96.16,145.42c7.71,8.57,16.96,16.66,27.5,23.7,8.96,5.99,18.29,10.89,27.66,14.64,17.56-16.62,32.16-36.17,43.09-57.84L257.92,0l-110.84,87.19c-20.47,16.1-37.72,35.87-50.92,58.23Z" />
        <path class="cls-1"
            d="M87.08,223.86c-7.97-5.33-15.5-10.92-22.59-16.7l-38.49,76.31,69.17-54.4c-2.71-1.69-5.41-3.41-8.09-5.2Z" />
    </g>
    <path class="cls-2"
        d="M229.81,101.27c20.81,27.65,26.93,58.36,12.79,79.51-20.63,30.88-76.59,29.69-124.98-2.64-48.39-32.34-70.89-83.58-50.25-114.46,14.14-21.15,44.86-27.25,78.36-18.6C87.76,20.46,32.59,22.86,11.22,54.85c-26.86,40.19,9.8,111.82,81.88,159.99,72.08,48.17,152.29,54.64,179.15,14.45,21.38-31.99,2.49-83.88-42.44-128.02Z" />
</svg>