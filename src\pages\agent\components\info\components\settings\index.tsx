import React, { useState, useEffect } from 'react';
import styles from './style/index.module.less';
import useLocale from '@/utils/useLocale';
import {
  Form,
  Image,
  Select,
  Message,
  Cascader,
  Modal,
  Button,
  Grid,
  Tooltip,
} from '@arco-design/web-react';
import IconSend from '@/assets/chat/send.svg';
import { useLocation } from 'react-router-dom';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import Text from '@arco-design/web-react/es/Typography/text';
import AddApplicationSettingIcon from '@/assets/application/addApplicationSetting.svg';
import ButtonComponent from '@arco-design/web-react/es/Button';
import WorkflowIcon from '@/assets/application/workflowIcon.png';
import AcpServerIcon from '@/assets/acp/acpServer.png';
import AcpServerIconSvg from '@/assets/acp/IconAcp.svg';
import AddIcon from '@/assets/application/addIcon.svg';
import IconCloseTag from '@/assets/close.svg';
import TreeModal from './components/TreeModal/TreeModal';
import {
  getLlmProviderNameList,
  getLlmProviderModelList,
} from '@/lib/services/llm-model-service';
import { AIPrompt, AgentResponse, getAgentList } from '@/lib/services/agent-service';
import { getWorkflowList } from '@/lib/services/workflow-service';
import { getKnowledgeList } from '@/lib/services/knowledge-service';
import { getUtilityList } from '@/lib/services/utilities-service';
import { Input, message } from 'antd';
import FunctionModel from './components/FunctionModal/index';
import ResponseModel from './components/ResponseModal/index';
import PromptTemplateModel from './components/PromptTemplateModal/index';
import { getAcpServerList, getAcpToolsById } from '@/lib/services/acp-server-service';
const { TextArea } = Input;
const { Row, Col } = Grid;
import { getTimeSequenceCardMetadataList } from '@/lib/services/timeSequenceCard-service';
import TimeCard from '@/assets/application/time_card.png';
import AttentionSmall from '@/assets/application/attention_small.svg';

interface ApplicationSettingsProps {
  agentData: AgentResponse | null;
  loading: boolean;
  onAgentDataUpdate: (newData: Partial<AgentResponse>) => void;
  isEditing: boolean;
}

interface WorkflowItem {
  id: string;
  name: string;
  description: string;
  createdTime: string;
  updatedTime: string;
  [key: string]: any;
}

interface KnowledgeItem {
  id: string;
  name: string;
  [key: string]: any;
}

interface UtilityItem {
  id: string;
  name: string;
  description: string;
  labels: string[];
  [key: string]: any;
}

interface AcpServerItem {
  id: string;
  name: string;
  description: string;
  createdTime: string;
  [key: string]: any;
}

interface DataValidationParam {
  required: boolean;
  field: string;
  type: string;
  description: string;
  redirect_to: string;
  field_type: string;
}

function ApplicationSettings({
  agentData,
  loading: parentLoading,
  onAgentDataUpdate,
  isEditing,
}: ApplicationSettingsProps) {
  // 智能体类型与配置项显示映射
  const agentTypeConfigMap = {
    routing: ['routingRule', 'responses', 'promptTemplate'],
    planning: ['routingRule', 'functions', 'responses', 'promptTemplate'],
    task: ['routingRule', 'sequenceCard', 'workflow', 'knowledge', 'tools', 'acpServer', 'functions', 'responses', 'promptTemplate'],
    static: ['responses', 'promptTemplate'],
    workflow: ['sequenceCard', 'responses', 'promptTemplate'],
  };

  // 检查当前智能体类型是否应该显示指定配置项
  const shouldShowConfigItem = (configItem: string): boolean => {
    if (!selectedType) return false;
    const allowedConfigs = agentTypeConfigMap[selectedType] || [];
    return allowedConfigs.includes(configItem);
  };

  const [isInitialized, setIsInitialized] = useState(false);
  const locale = useLocale();
  const [form] = Form.useForm();
  const [inputMessage, setInputMessage] = useState('');
  const [resultMessage, setResultMessage] = useState('');
  const [agentList, setAgentList] = useState<Array<{ id: string, name: string }>>([]);
  const [taskAgentList, setTaskAgentList] = useState<Array<{ id: string, name: string }>>([]);
  const [taskAgentListFetched, setTaskAgentListFetched] = useState(false);
  const [loadingAgents, setLoadingAgents] = useState(false);

  // TreeModalData
  const [workflowData, setWorkflowData] = useState([]);
  const [knowledgeData, setKnowledgeData] = useState([]);
  const [utilityData, setUtilityData] = useState([]);
  const [acpServerData, setAcpServerData] = useState([]);
  const [treeData, setTreeData] = useState([]);

  const [loadingData, setLoadingData] = useState({
    workflow: false,
    knowledge: false,
    utility: false,
  });
  const Option = Select.Option;
  const [visibleTreeModal, setVisibleTreeModal] = useState(false);
  const [checkedIds, setCheckedIds] = useState([]);
  const [modalTitle, setModalTitle] = useState('');
  const [modalType, setModalType] = useState('');
  const [loading, setLoading] = useState(false);
  const [selectedModalValue, setSelectedModalValue] = useState<any[]>([]);
  const [aiAssistantVisible, setAiAssistantVisible] = useState(false);
  const [functionModalVisible, setFunctionModalVisible] = useState(false);
  const [promptTemplateModalVisible, setPromptTemplateModalVisible] =
    useState(false);
  const [responseModalVisible, setResponseModalVisible] = useState(false);
  const [acpTimeSequenceCardSelections, setAcpTimeSequenceCardSelections] = useState({});

  // 智能体设置
  const [selectedType, setSelectedType] = useState<string | undefined>(
    agentData?.type
  );
  const [selectedRouteRule, setSelectedRouteRule] = useState<string | undefined>(undefined);
  const [selectedSecondaryRouteRule, setSelectedSecondaryRouteRule] = useState<string | undefined>(undefined);
  const [selectedFallbackAgent, setSelectedFallbackAgent] = useState<string | undefined>(undefined);
  const [workflowSetting, setWorkflowSetting] = useState<WorkflowItem[]>([]);
  const [knowledgeSetting, setKnowledgeSetting] = useState<KnowledgeItem[]>([]);
  const [utilitySetting, setUtilitySetting] = useState<UtilityItem[]>([]);
  const [acpServerSetting, setAcpServerSetting] = useState<AcpServerItem[]>([]);
  const [promptTemplateSetting, setPromptTemplateSetting] = useState<any[]>([]);
  const [functionSetting, setFunctionSetting] = useState<any[]>([]);
  const [responseSetting, setResponseSetting] = useState<any[]>([]);
  const [instruction, setInstruction] = useState<string>('');
  const [sequenceCardSetting, setSequenceCardSetting] = useState([]);
  const [sequenceCardData, setSequenceCardData] = useState([]);
  const [validationParams, setValidationParams] = useState<DataValidationParam[]>([]);
  const [agentListFetched, setAgentListFetched] = useState(false);
  const [toolsExpanded, setToolsExpanded] = useState(false);
  const [sequenceCardExpanded, setSequenceCardExpanded] = useState(false);
  const [workflowExpanded, setWorkflowExpanded] = useState(false);
  const [knowledgeExpanded, setKnowledgeExpanded] = useState(false);
  const [acpServerExpanded, setAcpServerExpanded] = useState(false);
  const [functionsExpanded, setFunctionsExpanded] = useState(false);
  const [responsesExpanded, setResponsesExpanded] = useState(false);
  const [promptTemplatesExpanded, setPromptTemplatesExpanded] = useState(false);
  const [acpServerNodeExpanded, setAcpServerNodeExpanded] = useState<{ [key: string]: boolean }>({});

  // 检查是否为Fallback Agent，如果是则不允许修改智能体类型
  const isFallbackAgent = agentData?.name?.endsWith('-Fallback Agent') || false;

  const [cascaderOptions, setCascaderOptions] = useState<any[]>([]);
  const [selectedModelValue, setSelectedModelValue] = useState<string[]>();
  const [modelConfig, setModelConfig] = useState<{ provider: string; model: string; maxTokens?: number | null; temperature?: number | null } | null>(null);
  const [providersFetched, setProvidersFetched] = useState(false);

  // 定义参数类型选项
  const paramTypes = [
    { value: 'string', label: '字符串' },
    { value: 'number', label: '数值' },
    { value: 'object', label: '对象' },
  ];

  // 定义智能体类型选项
  const agentTypes = [
    // { value: 'routing', label: '路由智能体' },
    { value: 'planning', label: '规划智能体' },
    { value: 'task', label: '任务智能体' },
    { value: 'static', label: '静态智能体' },
    { value: 'workflow', label: '工作流智能体' },
  ];

  const routeRuleTypes = [
    {
      value: 'reasoner',
      label: '推理',
      children: [
        { value: 'naive-reasoner', label: 'NaiveReasoner' },
        { value: 'one-step-forward-reasoner', label: 'One-Step-Forward-Reasoner' },
        { value: 'human-feedback-reasoner', label: 'Human-Feedback Reasoner' },
      ]
    },
    {
      value: 'planner',
      label: '规划',
      children: [
        { value: 'sql-planner', label: 'SQL-Planner' },
        { value: 'sequential-planner', label: 'Sequential-Planner' },
        { value: 'two-stage-planner', label: 'Two-Stage-Planner' },
      ]
    },
    { value: 'data-validation', label: '数据验证' },
    { value: 'fallback', label: '回退重定向' },
  ];

  // 根据智能体类型获取可用的路由规则选项
  const getAvailableRouteRules = () => {
    if (!selectedType) return [];

    switch (selectedType) {
      case 'routing':
        return routeRuleTypes.filter(rule => ['reasoner', 'planner'].includes(rule.value));
      case 'planning':
      case 'task':
        return routeRuleTypes.filter(rule => ['data-validation', 'fallback'].includes(rule.value));
      case 'static':
      case 'workflow':
        return [];
      default:
        return [];
    }
  };

  // 获取任务智能体列表
  const fetchTaskAgentList = async () => {
    const response = await getAgentList({
      Pager: {
        Page: 1,
        Size: 999,
      },
      type: 'task',
    });
    setTaskAgentList(response.items.map(item => ({
      id: item.id,
      name: item.name,
    })));
  };

  // 获取提供商列表
  const fetchProviders = async () => {
    try {
      setLoading(true);
      const providers = await getLlmProviderNameList();
      if (providers && Array.isArray(providers)) {
        const formattedProviders = (providers as string[]).map((providerId) => ({
          value: providerId,
          label: providerId,
          children: [],
          isLeaf: false
        }));
        setCascaderOptions(formattedProviders);
        setProvidersFetched(true);
      }
    } catch (error) {
      console.error('获取提供商列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载模型列表
  const loadMore = async (selectedOptions: string[], level: number): Promise<any[]> => {
    const providerId = selectedOptions[selectedOptions.length - 1];
    if (!providerId) return [];

    try {
      const models = await getLlmProviderModelList(providerId);

      if (models && Array.isArray(models)) {
        const modelOptions = models.map((model) => ({
          value: model.name,
          label: `${model.name}${model.version ? ` (${model.version})` : ''}`,
          isLeaf: true,
          data: model
        }));

        // 更新选项
        const newOptions = [...cascaderOptions];
        const target = newOptions.find(option => option.value === providerId);
        if (target) {
          target.children = modelOptions;
          setCascaderOptions(newOptions);
        }
        return newOptions;
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
    }
    return [];
  };

  // 处理供应商变更
  const handleProviderChange = async (providerId: string | string[]) => {
    if (!providerId || Array.isArray(providerId)) return;

    try {
      setLoading(true);
      const models = await getLlmProviderModelList(providerId);

      if (models && Array.isArray(models)) {
        const modelOptions = models.map((model) => ({
          value: model.name,
          label: `${model.name}${model.version ? ` (${model.version})` : ''}`,
          isLeaf: true,
          data: model
        }));

        // 更新选项
        const newOptions = [...cascaderOptions];
        const target = newOptions.find(option => option.value === providerId);
        if (target) {
          target.children = modelOptions;
          setCascaderOptions(newOptions);
        }
      }
    } catch (error) {
      console.error('处理供应商变更失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取工作流列表
  const fetchWorkflowList = async () => {
    try {
      setLoadingData((prev) => ({ ...prev, workflow: true }));
      const response = await getWorkflowList();

      if (response && Array.isArray(response)) {
        const formattedData = response.map((item) => ({
          id: item.id,
          title: item.name,
          description: item.description,
          createdTime: item.createdTime,
          updatedTime: item.updatedTime,
          parentId: '',
          level: 1,
          children: [],
        }));
        setWorkflowData(formattedData);
        return formattedData;
      }
      return [];
    } catch (error) {
      console.error('获取工作流列表失败:', error);
      Message.error({
        content: locale['menu.application.workflow.fetch.error'],
      });
      return [];
    } finally {
      setLoadingData((prev) => ({ ...prev, workflow: false }));
    }
  };

  // 获取知识库列表
  const fetchKnowledgeList = async (
    searchValue?: string,
    searchLabel?: string,
    knowledgeType = 'document',
    autoSetState = true
  ) => {
    try {
      setLoadingData((prev) => ({ ...prev, knowledge: true }));
      const response = await getKnowledgeList({
        type: knowledgeType,
        keyWord: searchValue,
        ...(searchLabel ? { labels: searchLabel } : {}),
      });

      if (response && Array.isArray(response)) {
        const formattedData = response.map((item) => ({
          id: item.name,
          title: `${item.name} (${item.type})`,
          description: item.description,
          labels: item.labels,
          createdTime: item.create_date,
          parentId: '',
          level: 1,
          children: [],
          type: item.type,
        }));

        // 只有在autoSetState为true时才设置状态
        if (autoSetState) {
          setKnowledgeData(formattedData);
        }
        return formattedData;
      }
      return [];
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      Message.error({
        content: locale['menu.application.knowledge.fetch.error'],
      });
      return [];
    } finally {
      setLoadingData((prev) => ({ ...prev, knowledge: false }));
    }
  };

  // 获取系统工具列表
  const fetchUtilityList = async (
    searchValue?: string,
    searchLabel?: string
  ) => {
    try {
      setLoadingData((prev) => ({ ...prev, utility: true }));
      const response = await getUtilityList(searchValue, searchLabel);

      if (response && Array.isArray(response)) {
        const formattedData = response.map((item) => ({
          id: item.name,
          title: item.display_name,
          parentId: '',
          level: 1,
          disabled: item.disabled,
          description: item.description,
          labels: item.tags,
          functions: item.functions || [],
          templates: item.templates || [],
          children: [
            ...(item.functions || []).map((func) => ({
              id: `${item.name}-${func.name}`,
              title: `函数: ${func.name}`,
              parentId: item.name,
              level: 2,
              type: 'function',
              name: func.name,
            })),
            ...(item.templates || []).map((template) => ({
              id: `${item.name}-${template.name}`,
              title: `模板: ${template.name}`,
              parentId: item.name,
              level: 2,
              type: 'template',
              name: template.name,
            })),
          ],
        }));
        setUtilityData(formattedData);
        return formattedData;
      }
      return [];
    } catch (error) {
      console.error('获取系统工具列表失败:', error);
      Message.error({
        content: locale['menu.application.utility.fetch.error'],
      });
      return [];
    } finally {
      setLoadingData((prev) => ({ ...prev, utility: false }));
    }
  };

  // 获取 ACP 工具列表
  const fetchAcpServerList = async () => {
    try {
      setLoadingData((prev) => ({ ...prev, acpServer: true }));
      const response = await getAcpServerList();

      if (response && Array.isArray(response)) {
        const formattedData = response
          .filter(item => item.is_available)
          .map((item) => ({
            id: item.id,
            title: item.name,
            description: item.description,
            isAvailable: item.is_available,
            createdTime: item.created_time,
            updatedTime: item.updated_time,
            createUserId: item.create_user_id,
            updateUserId: item.update_user_id,
            transportType: item.transport_type,
            location: item.location,
            parentId: '',
            level: 1,
            children: [],
            needsLazyLoad: true, //懒加载标识
            ...item,
          }));

        setAcpServerData(formattedData);
        return formattedData;
      }
      return [];
    } catch (error) {
      Message.error({
        content: '获取 ACP 工具列表失败',
      });
      return [];
    } finally {
      setLoadingData((prev) => ({ ...prev, acpServer: false }));
    }
  };

  // 获取时序卡片列表
  const fetchSequenceCardList = async (searchValue?: string, searchLabel?: string) => {
    try {
      setLoadingData(prev => ({ ...prev, agent: true }));
      const params: any = {
        Pager: {
          Page: 1,
          Size: 999,
        },
      };

      if (searchValue) {
        params.name = searchValue;
      }
      // if (searchLabel) {
      //   params.label = searchLabel;
      // }

      const response = await getTimeSequenceCardMetadataList(params);

      if (response) {
        const formattedData = response.items.map(item => ({
          id: item.id,
          title: item.display_name || item.name,
          description: item.description,
          labels: item.tags,
          createdTime: item.created_time,
        }));

        setSequenceCardData(formattedData);
        return formattedData;
      }
      return [];
    } catch (error) {
      console.error('获取时序卡片片列表失败:', error);
      Message.error({
        content: locale['menu.application.timeSequenceCard.fetch.error']
      });
      return [];
    } finally {
      setLoadingData(prev => ({ ...prev, agent: false }));
    }
  };

  const fetchSequenceCardData = async () => {
    setLoadingData(prev => ({ ...prev, sequenceCard: true }));
    const sequenceCardResult = await fetchSequenceCardList();

    if (agentData.ts_cards && sequenceCardResult.length > 0) {
      const selectedTimecards = agentData.ts_cards
        .map(t => sequenceCardResult.find(item => item.id === t))
        .filter(Boolean)
        .map(t => ({
          id: t.id,
          name: t.title,
          description: t.description,
          createdTime: t.createdTime,
          labels: t.labels,
        }));
      if (selectedTimecards.length > 0) {
        setSequenceCardSetting(selectedTimecards);
      }
    } else {
      setSequenceCardSetting([]);
    }

    console.log("sequenceCardData", sequenceCardData);
    setLoadingData(prev => ({ ...prev, sequenceCard: false }));
  };

  // 获取智能体列表
  const fetchAgentList = async () => {
    try {
      setLoadingAgents(true);
      const params = {
        Pager: {
          Page: 1,
          Size: 999, // 获取所有智能体
        },
        applicationId: agentData.applicationId
      };
      const { items } = await getAgentList(params);
      if (items && Array.isArray(items)) {
        setAgentList(items.map(agent => ({
          id: agent.id,
          name: agent.name
        })));
      }
    } catch (error) {
      console.error('获取智能体列表失败:', error);
      Message.error('获取智能体列表失败');
    } finally {
      setLoadingAgents(false);
    }
  };

  const fetchData = async () => {
    try {

      await fetchSequenceCardData();

      const workflowResult = await fetchWorkflowList();

      // 同时获取文档库和Q&A库数据
      const documentResult = await fetchKnowledgeList('', '', 'document', false);
      const qaResult = await fetchKnowledgeList('', '', 'question-answer', false);
      const knowledgeResult = [...documentResult, ...qaResult];

      // 手动设置knowledgeData为合并后的数据
      setKnowledgeData(knowledgeResult);

      // 设置初始数据
      if (agentData) {
        try {
          // 设置 instruction
          setInstruction(agentData.instruction || '');

          // 设置 LLM 配置
          if (agentData.llm_config && !agentData.llm_config.is_inherit) {
            const { provider, model } = agentData.llm_config;

            if (provider && provider.trim() !== '' && model && model.trim() !== '') {
              setSelectedModelValue([provider, model]);
              setModelConfig({ provider, model });
            }
          }

          // 设置路由规则
          if (agentData.routing_rules && agentData.routing_rules.length > 0) {
            // 检查数据验证规则
            const dataValidationRules = agentData.routing_rules.filter(rule => rule.type === 'data-validation');
            if (dataValidationRules.length > 0) {
              const validationRules = dataValidationRules.map(rule => ({
                required: rule.required || true,
                field: rule.field || '',
                type: rule.field_type || 'string',
                description: rule.description || '',
                redirect_to: rule.redirect_to || '',
                field_type: rule.type || 'data-validation'
              }));
              setValidationParams(validationRules);
              setSelectedRouteRule('data-validation');
            } else {
              // 检查fallback规则
              const fallbackRule = agentData.routing_rules.find(rule => rule.type === 'fallback');
              if (fallbackRule) {
                setSelectedRouteRule('fallback');
                setSelectedFallbackAgent(fallbackRule.redirect_to);
              } else {
                // 处理其他类型的路由规则
                const ruleData = agentData.routing_rules[0];

                // 主类型
                let mainType = null;
                const primaryRule = routeRuleTypes.find(rule =>
                  rule.value === ruleData.type ||
                  rule.value === ruleData.field
                );

                if (primaryRule) {
                  mainType = primaryRule.value;
                  setSelectedRouteRule(mainType);

                  // 匹配子类型
                  if (primaryRule.children && (mainType === 'reasoner' || mainType === 'planner')) {
                    const subType = primaryRule.children.find(child =>
                      child.label === ruleData.field ||
                      child.value === ruleData.field
                    );

                    if (subType) {
                      setSelectedSecondaryRouteRule(subType.value);
                    } else {
                      const fieldMap = {
                        'NaiveReasoner': 'naive-reasoner',
                        'Naive Reasoner': 'naive-reasoner',
                        'One-Step-Forward-Reasoner': 'one-step-forward-reasoner',
                        'Human-Feedback Reasoner': 'human-feedback-reasoner',
                        'SQL-Planner': 'sql-planner',
                        'Sequential-Planner': 'sequential-planner',
                        'Two-Stage-Planner': 'two-stage-planner'
                      };

                      const inferredSubType = fieldMap[ruleData.field];
                      if (inferredSubType) {
                        setSelectedSecondaryRouteRule(inferredSubType);
                      }
                    }
                  }
                }
              }
            }
          }

          // 设置工作流
          if (agentData.workflowId && workflowResult.length > 0) {
            const selectedWorkflow = workflowResult.find(
              (w) => w.id === agentData.workflowId
            );
            if (selectedWorkflow) {
              setWorkflowSetting([
                {
                  id: selectedWorkflow.id,
                  name: selectedWorkflow.title,
                  description: selectedWorkflow.description,
                  createdTime: selectedWorkflow.createdTime,
                  updatedTime: selectedWorkflow.updatedTime,
                },
              ]);
            }
          }

          // 设置知识库
          if (
            agentData.knowledge_bases &&
            Array.isArray(knowledgeResult) &&
            knowledgeResult.length > 0
          ) {
            const selectedKnowledge = agentData.knowledge_bases
              .map((kb) => knowledgeResult.find((k) => k.id === kb.name))
              .filter(Boolean)
              .map((k) => ({
                id: k.id,
                name: k.title,
                createdTime: k.createdTime,
                description: k.description,
                labels: k.labels,
                type: k.type,
                ...k,
              }));
            if (selectedKnowledge.length > 0) {
              setKnowledgeSetting(selectedKnowledge);
            }
          }

          // 设置工具
          if (agentData.utilities) {
            const selectedTools = agentData.utilities
              .map((util) => {

                return {
                  id: util.name,
                  name: util.display_name,
                  disabled: util.disabled,
                  description: util.description,
                  labels: util.tags,
                  functions: Array.isArray(util.functions)
                    ? util.functions.map((func) => ({
                      name: func.name,
                    }))
                    : [],
                  templates: Array.isArray(util.templates)
                    ? util.templates.map((template) => ({
                      name: template.name,
                    }))
                    : [],
                  children: [],
                  ...util,
                };
              })
              .filter(Boolean);

            if (selectedTools.length > 0) {
              setUtilitySetting(selectedTools);
            }
          }

          // 设置 ACP 工具 - 直接使用agentData中的数据
          if (agentData.acp_tools && agentData.acp_tools.length > 0) {
            const selectedAcpServers = agentData.acp_tools.map(configuredTool => {
              // 获取工具列表
              let selectedTools = [];
              if (Array.isArray(configuredTool.functions) && configuredTool.functions.length > 0) {
                selectedTools = configuredTool.functions.map(func => ({
                  name: func.name,
                  description: func.description,
                  artifact_metadata_item_id: func.artifact_metadata_item_id
                }));
              }
              return {
                id: configuredTool.server_id,
                name: configuredTool.name || configuredTool.server_id,
                description: configuredTool.description,
                createdTime: '',
                tools: selectedTools
              };
            });

            if (selectedAcpServers.length > 0) {
              setAcpServerSetting(selectedAcpServers);

              // 初始化展开状态 - 如果服务器有工具则默认不展开，用户可以手动展开
              const initialExpandedState = {};
              selectedAcpServers.forEach(server => {
                initialExpandedState[server.id] = false; // 默认不展开
              });
              setAcpServerNodeExpanded(initialExpandedState);
            }
          }

          // 设置函数
          if (Array.isArray(agentData.functions)) {
            setFunctionSetting(agentData.functions);
          }

          // 设置响应
          if (Array.isArray(agentData.responses)) {
            setResponseSetting(agentData.responses);
          }

          // 设置提示词模板
          if (Array.isArray(agentData.templates)) {
            setPromptTemplateSetting(agentData.templates);
          }
        } catch (error) {
          console.error('解析设置信息失败:', error);
        }

        // 设置表单初始值
        form.setFieldsValue({
          type: agentData.type,
        });

      }
    } catch (error) {
      console.error('获取数据失败:', error);
    }
  };

  // 在组件挂载时获取数据
  useEffect(() => {
    if (!isInitialized) {
      fetchData();
      setIsInitialized(true);
    }
  }, []);

  useEffect(() => {
    if (!isEditing && isInitialized) {
      fetchData();
    }
  }, [isEditing]);

  // 确保当选择数据校验路由规则时至少有一条校验字段数据
  useEffect(() => {
    if (selectedRouteRule === 'data-validation' && validationParams.length === 0) {
      // 如果选择了数据校验路由规则但没有校验字段，则添加一条默认数据
      setValidationParams([{
        required: true,
        field: '',
        field_type: 'data-validation',
        type: 'string',
        description: '',
        redirect_to: ''
      }]);
    }
  }, [selectedRouteRule]);

  // 监听表单数据变化
  useEffect(() => {
    const routingRulesData: Array<any> = [];

    if (selectedRouteRule) {
      console.log('selectedRouteRule', selectedRouteRule);
      if (['reasoner', 'planner'].includes(selectedRouteRule)) {

        const parentOption = routeRuleTypes.find(rule => rule.value === selectedRouteRule);
        const secondaryOption = parentOption?.children?.find(child => child.value === selectedSecondaryRouteRule);

        const rule: any = {
          type: selectedRouteRule,
          field: secondaryOption?.label || selectedSecondaryRouteRule,
          field_type: 'string',
          required: true
        };
        routingRulesData.push(rule);

      } else if (selectedRouteRule === 'data-validation') {
        // 只在还没有获取过智能体列表时才调用
        if (!agentListFetched) {
          fetchAgentList();
          setAgentListFetched(true);
        }
        validationParams.forEach(param => {
          if (param.field) { // 只添加有字段名的参数
            routingRulesData.push({
              type: 'data-validation',
              field: param.field,
              description: param.description,
              field_type: param.type,
              required: true,
              redirect_to: param.redirect_to
            });
          }
        });
      } else if (selectedRouteRule === 'fallback') {
        if (!taskAgentListFetched) {
          fetchTaskAgentList();
          setTaskAgentListFetched(true);
        }
        // 添加fallback规则
        if (selectedFallbackAgent) {
          routingRulesData.push({
            type: 'fallback',
            field: '',
            field_type: 'string',
            required: true,
            redirect_to: selectedFallbackAgent
          });
        }
      } else {
        setAgentListFetched(false);
      }
    } else {
      setAgentListFetched(false);
    }

    // 准备ACP服务器和工具数据
    const acpServerData = acpServerSetting
      .filter(server => server && server.id) // 确保服务器有效
      .map(server => {
        // 确保只包含已添加的工具，并过滤掉无效的工具
        const serverTools = Array.isArray(server.tools)
          ? server.tools.filter(tool => tool && tool.name)
          : [];

        return {
          server_id: server.id,
          name: server.name || '',
          description: server.description || '',
          functions: serverTools.map(tool => ({
            name: tool.name,
            description: tool.description || '',
            artifact_metadata_item_id: tool.artifact_metadata_item_id || ''
          }))
        };
      });

    const updateData = {
      type: selectedType,
      llm_config: {
        provider: modelConfig?.provider || null,
        model: modelConfig?.model || null,
        is_inherit: false,
        max_recursion_depth: agentData?.llm_config?.max_recursion_depth || null,
        max_tokens: modelConfig?.maxTokens || agentData?.llm_config?.max_tokens || null,
        temperature: modelConfig?.temperature || agentData?.llm_config?.temperature || null,
      },
      routing_rules: routingRulesData,
      workflowId: workflowSetting[0]?.id,
      knowledge_bases: knowledgeSetting.length
        ? knowledgeSetting.map((item) => ({
          name: item.id,
          type: item.type || '',
          disabled: item.disabled || false,
        }))
        : [],
      utilities: utilitySetting.length
        ? utilitySetting.map((item) => ({
          name: item.id,
          disabled: item.disabled || false,
          description: item.description || '',
          display_name: item.name || '',
          functions: item.functions || [],
        }))
        : [],
      functions: functionSetting,
      responses: responseSetting,
      templates: promptTemplateSetting,
      instruction: instruction,
      acp_tools: acpServerData,
      ts_cards: sequenceCardSetting.map(item => item.id),
    };
    onAgentDataUpdate(updateData);
  }, [
    selectedType,
    modelConfig,
    selectedRouteRule,
    selectedSecondaryRouteRule,
    selectedFallbackAgent,
    validationParams,
    workflowSetting,
    knowledgeSetting,
    utilitySetting,
    acpServerSetting,
    functionSetting,
    responseSetting,
    promptTemplateSetting,
    instruction,
    sequenceCardSetting,
    agentListFetched,
  ]);

  const openChooseModal = async (type: string) => {
    try {
      // 检查数据是否正在加载
      if (
        loadingData.workflow ||
        loadingData.knowledge ||
        loadingData.utility
      ) {
        Message.warning('数据加载中，请稍候...');
        return;
      }

      let currentData = [];

      // 如果数据为空，重新获取
      if (type === 'sequenceCard') {
        if (!sequenceCardData || sequenceCardData.length === 0) {
          currentData = await fetchSequenceCardList();
        } else {
          currentData = sequenceCardData;
        }
        setModalTitle(locale['menu.application.info.setting.addSequenceCard']);
        setModalType('sequenceCard');
        setCheckedIds(sequenceCardSetting.map(item => item.id));
      } else if (type === 'workflow') {
        if (!workflowData || workflowData.length === 0) {
          currentData = await fetchWorkflowList();
        } else {
          currentData = workflowData;
        }
        setModalTitle(locale['menu.application.info.setting.addWorkflow']);
        setModalType('workflow');
        setCheckedIds(workflowSetting.map((item) => item.id));
      } else if (type === 'knowledge') {
        if (!knowledgeData || knowledgeData.length === 0) {
          currentData = await fetchKnowledgeList('', '', 'document');
        } else {
          currentData = knowledgeData.filter(item => item.type === 'document');
        }
        setModalTitle(locale['menu.application.info.setting.addDocument']);
        setModalType('knowledge');
        setCheckedIds(knowledgeSetting.map((item) => item.id));
      } else if (type === 'tools') {
        if (!utilityData || utilityData.length === 0) {
          currentData = await fetchUtilityList();
        } else {
          currentData = utilityData;
        }
        setModalTitle(locale['menu.application.info.setting.tools']);
        setModalType('tools');
        setCheckedIds(utilitySetting.map((item) => item.id));
      } else if (type === 'acpServer') {
        if (!acpServerData || acpServerData.length === 0) {
          currentData = await fetchAcpServerList();
        } else {
          currentData = acpServerData;
        }
        setModalTitle(locale['menu.application.info.setting.AcpSetting']);
        setModalType('acpServer');

        // 收集服务器ID和工具ID
        const ids = [];
        const timeSequenceCardSelections = {};

        acpServerSetting.forEach(server => {
          // 添加服务器ID
          ids.push(server.id);

          // 添加工具ID
          if (Array.isArray(server.tools)) {
            server.tools.forEach(tool => {
              const toolId = `${server.id}-${tool.name}`;
              ids.push(toolId);

              // 如果工具有关联的时序卡片ID，设置到选择状态中
              if (tool.artifact_metadata_item_id) {
                timeSequenceCardSelections[toolId] = tool.artifact_metadata_item_id;
              }
            });
          }
        });

        setCheckedIds(ids);

        // 存储时序卡片选择状态供懒加载使用
        setAcpTimeSequenceCardSelections(timeSequenceCardSelections);

        // 为已配置的ACP服务器预加载子节点数据
        currentData = await Promise.all(currentData.map(async server => {
          // 检查这个服务器是否在已配置列表中
          const configuredServer = acpServerSetting.find(configured => configured.id === server.id);

          if (configuredServer && Array.isArray(configuredServer.tools) && configuredServer.tools.length > 0) {
            try {
              // 如果服务器已配置且有工具，主动加载子节点数据
              const tools = await getAcpToolsById(server.id);
              if (tools && Array.isArray(tools)) {
                server.children = tools.map(tool => {
                  const toolId = `${server.id}-${tool.name}`;
                  return {
                    id: toolId,
                    title: tool.name,
                    description: tool.description,
                    parentId: server.id,
                    level: 2,
                    type: 'tool',
                    name: tool.name,
                    preSelectedTimeSequenceCard: timeSequenceCardSelections[toolId]
                  };
                });
                server.needsLazyLoad = false;
              }
            } catch (error) {
              console.error(`预加载服务器 ${server.id} 的工具列表失败:`, error);
            }
          } else {
            // 为其他节点设置预选择的时序卡片
            server.children = server.children?.map(tool => {
              const toolId = `${server.id}-${tool.name}`;
              return {
                ...tool,
                preSelectedTimeSequenceCard: timeSequenceCardSelections[toolId]
              };
            });
          }

          return server;
        }));
      }

      const sortTreeData = [...currentData].sort((a, b) => {
        const aChecked = checkedIds.includes(a.id);
        const bChecked = checkedIds.includes(b.id);

        if (aChecked && !bChecked) return -1;
        if (!aChecked && bChecked) return 1;
        return 0;
      });

      setTreeData(sortTreeData);

      setVisibleTreeModal(true);
    } catch (error) {
      console.error('打开模态框失败:', error);
      Message.error('打开失败，请重试');
    }
  };

  const handleTreeConfirm = (selectedIds: string[]) => {
    if (!modalType) return;

    const getSelectedItems = (data: any[], ids: string[]) => {
      if (modalType === 'tools') {
        return data.reduce((acc: any[], item) => {
          const toolId = item.id;
          const children = item.children || [];

          if (ids.includes(toolId)) {
            const toolItem = {
              id: toolId,
              name: toolId,
              description: item.description,
              disabled: item.disabled || false,
              functions: children
                .filter((child) => child.type === 'function')
                .map((func) => ({
                  name: func.name,
                })),
              templates: children
                .filter((child) => child.type === 'template')
                .map((template) => ({
                  name: template.name,
                })),
            };

            acc.push(toolItem);
          }
          return acc;
        }, []);
      } else if (modalType === 'acpServer') {
        return data.reduce((acc: any[], item) => {
          // 检查服务器节点是否被选中
          const serverSelected = ids.includes(item.id);

          // 获取被选中的工具节点
          const selectedTools = (item.children || []).filter(tool => {
            const isIncluded = ids.includes(tool.id);
            return isIncluded;
          });

          // 如果服务器被选中或者有工具被选中
          if (serverSelected || selectedTools.length > 0) {
            const serverItem = {
              id: item.id,
              server_id: item.id,
              name: item.name,
              description: item.description,
              createdTime: item.createdTime,
              ...item,
              // 添加选中的工具信息（保留工具信息但不在UI中显示）
              tools: selectedTools.map(tool => ({
                name: tool.name,
                description: tool.description,
                artifact_metadata_item_id: tool.timeSequenceCardId || tool.preSelectedTimeSequenceCard || '',
              }))
            };

            acc.push(serverItem);
          }

          return acc;
        }, []);
      } else {
        return data.reduce((acc: any[], item) => {
          if (ids.includes(item.id)) {
            acc.push({
              id: item.id,
              name: item.title,
              ...item,
            });
          }
          if (item.children) {
            acc.push(...getSelectedItems(item.children, ids));
          }
          return acc;
        }, []);
      }
    };

    switch (modalType) {
      case 'sequenceCard':
        const selectedSequenceCards = getSelectedItems(sequenceCardData, selectedIds);
        setSequenceCardSetting(selectedSequenceCards);
        break;
      case 'workflow':
        const selectedWorkflows = getSelectedItems(workflowData, selectedIds);
        setWorkflowSetting(selectedWorkflows);
        break;
      case 'knowledge':
        const selectedKnowledge = getSelectedItems(knowledgeData, selectedIds);
        setKnowledgeSetting(selectedKnowledge);
        break;
      case 'tools':
        const selectedTools = getSelectedItems(utilityData, selectedIds);
        setUtilitySetting(selectedTools);
        break;
      case 'acpServer':
        // 懒加载使用treeData
        const selectedAcpServer = getSelectedItems(treeData, selectedIds);
        setAcpServerSetting(selectedAcpServer);

        // 初始化或更新展开状态
        setAcpServerNodeExpanded(prev => {
          const newState = { ...prev };
          selectedAcpServer.forEach(server => {
            // 如果是新的服务器，默认不展开
            if (!(server.id in newState)) {
              newState[server.id] = false;
            }
          });
          // 移除不再存在的服务器的展开状态
          Object.keys(newState).forEach(serverId => {
            if (!selectedAcpServer.some(server => server.id === serverId)) {
              delete newState[serverId];
            }
          });
          return newState;
        });
        break;
    }

    // setVisibleTreeModal(false);
    // setCheckedIds([]);
  };

  const handleModalClose = () => {
    setVisibleTreeModal(false);
    setCheckedIds([]);
    // 关闭模态框时重置数据
    setTreeData([]);
    setModalType('');
  };

  // 添加搜索处理函数
  const handleSearch = async (
    value: { name: string; label: string; knowledgeType?: string },
    type: string
  ) => {
    try {
      if (modalType === 'sequenceCard') {
        setLoadingData(prev => ({ ...prev, agent: true }));
        try {
          const searchResults = await fetchSequenceCardList(value.name, value.label);
          if (Array.isArray(searchResults)) {
            setTreeData(searchResults);
          }
        } catch (error) {
          console.error('搜索出错:', error);
          Message.error({
            content: locale['menu.application.timeSequenceCard.fetch.error']
          });
        } finally {
          setLoadingData(prev => ({ ...prev, agent: false }));
        }
      } else if (type === 'workflow') {
        setLoadingData((prev) => ({ ...prev, workflow: true }));
        if (!value.name && !value.label) {
          setTreeData([...workflowData]);
          return;
        }
        const searchResults = await fetchWorkflowList();
        if (Array.isArray(searchResults)) {
          const filteredResults = searchResults.filter((item) =>
            item.title.toLowerCase().includes(value.name.toLowerCase())
          );
          setTreeData(filteredResults);
        }
      } else if (type === 'knowledge') {
        setLoadingData((prev) => ({ ...prev, knowledge: true }));
        if (!value.name && !value.label) {
          if (knowledgeData && knowledgeData.length > 0) {
            const filteredData = knowledgeData.filter(item => item.type === (value.knowledgeType || 'document'));
            setTreeData(filteredData);
          } else {
            const searchResults = await fetchKnowledgeList('', '', value.knowledgeType || 'document');
            if (Array.isArray(searchResults)) {
              setTreeData(searchResults);
            }
          }
          return;
        }
        const searchResults = await fetchKnowledgeList(value.name, value.label, value.knowledgeType || 'document');
        if (Array.isArray(searchResults)) {
          const filteredResults = searchResults.filter(
            (item) =>
              item.title.toLowerCase().includes(value.name.toLowerCase()) &&
              (!value.label ||
                (item.labels && item.labels.includes(value.label)))
          );
          setTreeData(filteredResults);
        }
      } else if (type === 'tools') {
        setLoadingData((prev) => ({ ...prev, utility: true }));
        if (!value.name && !value.label) {
          const searchResults = await fetchUtilityList();
          if (Array.isArray(searchResults)) {
            setTreeData(searchResults);
          }
          return;
        }
        const searchResults = await fetchUtilityList(value.name, value.label);
        if (Array.isArray(searchResults)) {
          const filteredResults = searchResults.filter(
            (item) =>
              item.title.toLowerCase().includes(value.name.toLowerCase()) &&
              (!value.label ||
                (item.labels && item.labels.includes(value.label)))
          );
          setTreeData(filteredResults);
        }
      } else if (type === 'acpServer') {
        setLoadingData((prev) => ({ ...prev, acpServer: true }));
        if (!value.name && !value.label) {
          setTreeData([...acpServerData]);
          return;
        }
        const searchResults = await fetchAcpServerList();
        if (Array.isArray(searchResults)) {
          const filteredResults = searchResults.filter((item) =>
            item.title.toLowerCase().includes(value.name.toLowerCase())
          );
          setTreeData(filteredResults);
        }
      }
    } catch (error) {
      console.error('搜索出错:', error);
      Message.error({
        content: locale['menu.application.agent.fetch.error'],
      });
    } finally {
      setLoadingData((prev) => ({
        ...prev,
        workflow: false,
        knowledge: false,
        utility: false,
        acpServer: false,
      }));
    }
  };

  const handleSendMessage = async () => {
    const res = await AIPrompt({
      agentId: agentData?.id,
      requirements: inputMessage,
    });
    console.log(res);
    setResultMessage(res.data.content);
  };

  const handleAiAssistantClick = () => {
    setResultMessage('');
    setAiAssistantVisible(true);
  };

  const handleAiAssistantClose = () => {
    setAiAssistantVisible(false);
  };

  const handleFunctionModalClick = (app: any) => {
    setFunctionModalVisible(true);
    setSelectedModalValue(app);
  };

  const handleFunctionModalClose = () => {
    setFunctionModalVisible(false);
  };

  const handleResponseModalClick = (app: any) => {
    setResponseModalVisible(true);
    setSelectedModalValue(app);
  };

  const handleResponseModalClose = () => {
    setResponseModalVisible(false);
  };

  const handlePromptTemplateClick = (app: any) => {
    setPromptTemplateModalVisible(true);
    setSelectedModalValue(app);
  };

  const handlePromptTemplateModalClose = () => {
    setPromptTemplateModalVisible(false);
  };

  const leftContainer = () => {
    return (
      <div className={styles.leftContainer}>
        {/* 智能体类型 */}
        <RowComponent>
          <Text className={styles.subtitle}>
            {locale['menu.application.agent.info.setting.type']}
          </Text>
        </RowComponent>
        <RowComponent style={{ marginTop: 8 }} className={styles.selectRowBox}>
          <Select
            placeholder={
              locale['menu.application.agent.info.setting.placeholder.type']
            }
            value={selectedType || undefined}
            disabled={!isEditing || selectedType === 'routing' || isFallbackAgent}
            className={!selectedType ? styles.selectError : ''}
            onChange={(value) => {
              setSelectedType(value);
              form.setFieldsValue({ type: value });

              // 当智能体类型变更时，重置路由规则选择
              setSelectedRouteRule(undefined);
              setSelectedSecondaryRouteRule(undefined);
            }}
            renderFormat={(option, value) => {
              if (value === 'routing') return '路由智能体';
              return option?.children || value;
            }}
            style={{
              opacity: isFallbackAgent ? 0.6 : 1,
            }}
            triggerProps={{
              className: 'agent-info-select-popup',
            }}
          >
            {agentTypes.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </RowComponent>

        {/* 路由规则 */}
        {shouldShowConfigItem('routingRule') && (
          <>
            <RowComponent style={{ marginTop: 24 }}>
              <Text className={styles.subtitle}>
                {locale['menu.application.agent.info.setting.routeRule']}
              </Text>
            </RowComponent>
            <RowComponent style={{ marginTop: 8 }} className={styles.selectRowBox}>
              <Cascader
                disabled={!isEditing || !selectedType || ['static', 'workflow'].includes(selectedType)}
                placeholder={
                  locale[
                  'menu.application.agent.info.setting.placeholder.routeRule'
                  ]
                }
                options={getAvailableRouteRules()}
                value={selectedRouteRule && selectedSecondaryRouteRule ? [selectedRouteRule, selectedSecondaryRouteRule] : (selectedRouteRule ? [selectedRouteRule] : undefined)}
                allowClear
                onChange={(value) => {
                  if (Array.isArray(value) && value.length > 0) {
                    setSelectedRouteRule(value[0] as string);
                    setSelectedSecondaryRouteRule(value.length > 1 ? value[1] as string : undefined);
                  } else {
                    setSelectedRouteRule(undefined);
                    setSelectedSecondaryRouteRule(undefined);
                  }
                }}
                style={{ width: '100%' }}
                showSearch={false}
                expandTrigger="click"
                changeOnSelect
              />
            </RowComponent>
          </>
        )}

        {/* 路由规则-数据校验 */}
        {selectedRouteRule === 'data-validation' && (
          <>
            <RowComponent className={styles.titleRow} >
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  校验字段
                  <Tooltip
                    content="校验字段的值为空时，将交由选定的智能体执行下一步操作"
                    position="top"
                  >
                    <span style={{
                      display: 'inline-flex',
                      marginLeft: '4px',
                      cursor: 'pointer',
                      verticalAlign: 'middle'
                    }}>
                      <AttentionSmall />
                    </span>
                  </Tooltip>
                </Text>
              </div>
              <Button
                className={styles.addParamsBut}
                onClick={() => {
                  if (!isEditing) return;
                  setValidationParams([...validationParams, {
                    required: true,
                    field: '',
                    field_type: 'data-validation',
                    type: 'string',
                    description: '',
                    redirect_to: ''
                  }]);
                }}
                disabled={!isEditing}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer',
                }}
              >
                <Text className={styles.operateText}>
                  {locale['menu.application.template.setting.adds']}
                </Text>
              </Button>
            </RowComponent>
            <Col className={styles.paramsContainer}>
              {validationParams.length > 0 && (
                <>
                  {/* 表头 */}
                  <Row className={styles.headerRow}>
                    <div className={styles.headerName}>字段名</div>
                    <div className={styles.headerDescription}>描述</div>
                    <div className={styles.headerType}>类型</div>
                    <div className={styles.headerSelect}>选择智能体</div>
                  </Row>

                  {/* 渲染已选择的参数 */}
                  <div className={styles.selectedItemList}>
                    {validationParams.map((param, index) => (
                      <Row key={`validation-param-${index}`} className={styles.selectedItemRow}>
                        {/* 字段名 */}
                        <div className={styles.selectedItemCol}>
                          <TextArea
                            placeholder="请输入"
                            maxLength={50}
                            value={param.field}
                            disabled={!isEditing}
                            onChange={(e) => {
                              if (!isEditing) return;
                              const newParams = [...validationParams];
                              newParams[index].field = e.target.value;
                              setValidationParams(newParams);
                            }}
                            style={{
                              boxSizing: 'border-box',
                              display: 'flex',
                              flexDirection: 'row',
                              alignItems: 'center',
                              padding: '8px 12px',
                              gap: '8px',
                              width: '100%',
                              height: '40px',
                              background: '#FFFFFF',
                              borderRadius: '8px',
                              border: '1px solid #e5e6eb',
                              resize: 'none',
                              fontFamily: 'PingFang SC',
                              fontStyle: 'normal',
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '24px',
                              opacity: !isEditing ? 0.5 : 1,
                              cursor: !isEditing ? 'not-allowed' : 'text',
                            }}
                          />
                        </div>

                        {/* 描述 */}
                        <div className={styles.selectedItemCol}>
                          <TextArea
                            placeholder="请输入"
                            maxLength={200}
                            value={param.description}
                            disabled={!isEditing}
                            onChange={(e) => {
                              if (!isEditing) return;
                              const newParams = [...validationParams];
                              newParams[index].description = e.target.value;
                              setValidationParams(newParams);
                            }}
                            style={{
                              boxSizing: 'border-box',
                              display: 'flex',
                              flexDirection: 'row',
                              alignItems: 'center',
                              padding: '8px 12px',
                              gap: '8px',
                              width: '100%',
                              height: '40px',
                              background: '#FFFFFF',
                              borderRadius: '8px',
                              border: '1px solid #e5e6eb',
                              resize: 'none',
                              fontFamily: 'PingFang SC',
                              fontStyle: 'normal',
                              fontWeight: 400,
                              fontSize: '14px',
                              lineHeight: '24px',
                              opacity: !isEditing ? 0.5 : 1,
                              cursor: !isEditing ? 'not-allowed' : 'text',
                            }}
                          />
                        </div>

                        {/* 类型 */}
                        <div className={styles.selectedItemCol}>
                          <Select
                            value={param.type}
                            placeholder="请选择"
                            disabled={!isEditing}
                            onChange={(value) => {
                              if (!isEditing) return;
                              const newParams = [...validationParams];
                              newParams[index].type = value;
                              setValidationParams(newParams);
                            }}
                            style={{
                              boxSizing: 'border-box',
                              width: '100%',
                              height: '40px',
                              background: '#FFFFFF',
                              borderRadius: '8px',
                              opacity: !isEditing ? 0.5 : 1,
                              cursor: !isEditing ? 'not-allowed' : 'pointer',
                            }}
                            triggerProps={{
                              className: 'agent-info-select-popup',
                            }}
                          >
                            {paramTypes.map(type => (
                              <Option key={type.value} value={type.value}>
                                {type.label}
                              </Option>
                            ))}
                          </Select>
                        </div>

                        {/* 选择智能体 */}
                        <div className={styles.selectedItemCol}>
                          <Select
                            value={param.redirect_to || undefined}
                            placeholder="请选择"
                            disabled={!isEditing}
                            onChange={(value) => {
                              if (!isEditing) return;
                              const newParams = [...validationParams];
                              newParams[index].redirect_to = value;
                              setValidationParams(newParams);
                            }}
                            loading={loadingAgents}
                            style={{
                              boxSizing: 'border-box',
                              width: '100%',
                              height: '40px',
                              background: '#FFFFFF',
                              borderRadius: '8px',
                              opacity: !isEditing ? 0.5 : 1,
                              cursor: !isEditing ? 'not-allowed' : 'pointer',
                            }}
                            triggerProps={{
                              className: 'agent-info-select-popup',
                            }}
                          >
                            {agentList.map(agent => (
                              <Option key={agent.id} value={agent.id}>
                                {agent.name}
                              </Option>
                            ))}
                          </Select>
                        </div>

                        {/* 删除按钮 - 保持在行的最右侧 */}
                        <div style={{ width: '24px', display: 'flex', alignItems: 'center' }}>
                          {validationParams.length > 1 && isEditing && (
                            <IconCloseTag
                              className={styles.deleteIcon}
                              style={{
                                cursor: !isEditing ? 'not-allowed' : 'pointer'
                              }}
                              onClick={() => {
                                if (!isEditing) return;
                                const newParams = [...validationParams];
                                newParams.splice(index, 1);
                                setValidationParams(newParams);
                              }}
                            />
                          )}
                        </div>
                      </Row>
                    ))}
                  </div>
                </>
              )}
            </Col>
          </>
        )}

        {/* 路由规则-回调 */}
        {selectedRouteRule === 'fallback' && (
          <>
            <RowComponent className={styles.titleRow} >
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  重定向Agent
                </Text>
              </div>
            </RowComponent>
            {/* 选择智能体 */}
            <div className={styles.selectRowBox}>
              <Select
                value={selectedFallbackAgent || undefined}
                placeholder="请选择"
                disabled={!isEditing}
                onChange={(value) => {
                  if (!isEditing) return;
                  setSelectedFallbackAgent(value);
                }}
                loading={loadingAgents}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer',
                }}
                triggerProps={{
                  className: 'agent-info-select-popup',
                }}
              >
                {taskAgentList.map(agent => (
                  <Option key={agent.id} value={agent.id}>
                    {agent.name}
                  </Option>
                ))}
              </Select>
            </div>
          </>
        )}

        {/* 模型选择 */}
        <RowComponent style={{ marginTop: 24 }}>
          <Text className={styles.subtitle}>
            {locale['menu.application.agent.info.setting.chooseModel']}
          </Text>
        </RowComponent>
        <RowComponent style={{ marginTop: 8 }} className={styles.selectRowBox}>
          <div
            style={{ width: '100%' }}
            onClick={() => {
              if (!providersFetched) {
                fetchProviders();
              }
            }}
          >
            <Cascader
              placeholder={locale['menu.application.agent.info.setting.chooseModel']}
              disabled={!isEditing}
              options={cascaderOptions}
              style={{ width: '100%' }}
              allowClear
              loading={loading}
              changeOnSelect
              value={selectedModelValue}
              loadMore={loadMore}
              className={(() => {
                if (selectedModelValue && selectedModelValue.length === 1) {
                  return styles.selectError;
                }
                return '';
              })()}
              onChange={(value, selectedOptions) => {
                if (!value || (Array.isArray(value) && value.length === 0)) {
                  setSelectedModelValue(undefined);
                  setModelConfig(null);
                  return;
                }

                if (!Array.isArray(value) || !Array.isArray(selectedOptions)) {
                  setSelectedModelValue(undefined);
                  setModelConfig(null);
                  return;
                }

                setSelectedModelValue(value as string[]);

                if (value.length === 2 && selectedOptions.length === 2 && selectedOptions[1]?.data) {
                  const [provider, model] = value as string[];
                  const modelData = selectedOptions[1].data;
                  setModelConfig({
                    provider,
                    model,
                    maxTokens: modelData.maxTokens || null,
                    temperature: modelData.temperature || null
                  });
                } else if (value.length === 1) {
                  // 只选择了供应商
                  const provider = value[0] as string;
                  setModelConfig({
                    provider,
                    model: '',
                    maxTokens: null,
                    temperature: null
                  });
                  handleProviderChange(provider);
                } else {
                  setModelConfig(null);
                }
              }}
            />
          </div>
          {/* 错误提示 */}
          {selectedModelValue && selectedModelValue.length === 1 && (
            <RowComponent style={{ marginTop: 4 }}>
              <Text style={{ color: '#F53F3F', fontSize: '12px' }}>
                请选择模型
              </Text>
            </RowComponent>
          )}
        </RowComponent>

        {/* 时序卡片 */}
        {shouldShowConfigItem('sequenceCard') && (
          <>
            <RowComponent className={styles.titleRow} style={{ marginTop: 24 }}>
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.setting.addSequenceCard']}
                </Text>
                <Text className={styles.subtitlePlaceholder}>
                  {locale['menu.application.info.setting.placeholder.addSequenceCard']}
                </Text>
              </div>
              <Button
                className={styles.addApplication}
                onClick={() => openChooseModal('sequenceCard')}
                disabled={!isEditing}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer'
                }}>
                <Text className={styles.operateText}>
                  {locale['menu.application.template.setting.adds']}
                </Text>
              </Button>
            </RowComponent>

            <Col span={24} style={{ marginBottom: '8px' }} className={styles.selectedItemContainer}>
              {/* 渲染已选择的时序卡片 */}
              {sequenceCardSetting.length > 0 && (
                <div className={styles.selectedItemList} style={{ position: 'relative' }}>
                  {(sequenceCardExpanded ? sequenceCardSetting : sequenceCardSetting.slice(0, 3)).map(app => (
                    <Row key={app.id} className={styles.selectedItemRow}>
                      <Col className={styles.selectedItemCol}>
                        <Image
                          src={app.icon_url || TimeCard} // 使用默认图标
                          width={24}
                          height={24}
                          className={styles.agentIcon}
                        />
                        <Text className={styles.selectedItemText}>{app.name}</Text>
                        <Text className={styles.selectedItemTextContent}>{app.description}</Text>
                        <IconCloseTag
                          className={styles.deleteIcon}
                          style={{
                            cursor: !isEditing ? 'not-allowed' : 'pointer'
                          }}
                          onClick={() => {
                            if (!isEditing) return;
                            setSequenceCardSetting(sequenceCardSetting.filter(item => item.id !== app.id));
                          }}
                        />
                      </Col>
                    </Row>
                  ))}

                  {/* 折叠/展开按钮 */}
                  {sequenceCardSetting.length > 3 && (
                    <div className={styles.toggleButton} onClick={() => setSequenceCardExpanded(!sequenceCardExpanded)}>
                      <div className={styles.toggleArrow}>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path
                            d={sequenceCardExpanded ? "M12 10L8 6L4 10" : "M4 6L8 10L12 6"}
                            stroke="#86909C"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <Text className={styles.toggleText}>
                        {sequenceCardExpanded ? '收起' : `展开剩余的 ${sequenceCardSetting.length - 3} 个时序卡片`}
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </Col>
          </>
        )}

        {/* 工作流 */}
        {shouldShowConfigItem('workflow') && (
          <>
            <RowComponent className={styles.titleRow} style={{ marginTop: 24 }}>
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.setting.addWorkflow']}
                </Text>
                <Text className={styles.subtitlePlaceholder}>
                  {locale['menu.application.info.setting.placeholder.addWorkflow']}
                </Text>
              </div>
              <Button
                disabled={!isEditing}
                className={styles.addApplication}
                onClick={() => openChooseModal('workflow')}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer',
                }}
              >
                <Text className={styles.operateText}>
                  {locale['menu.application.template.setting.adds']}
                </Text>
              </Button>
            </RowComponent>
            <Col
              span={24}
              style={{ marginBottom: '8px' }}
              className={styles.selectedItemContainer}
            >
              {/* 渲染已选择的工作流 */}
              {workflowSetting.length > 0 && (
                <div className={styles.selectedItemList} style={{ position: 'relative' }}>
                  {(workflowExpanded ? workflowSetting : workflowSetting.slice(0, 3)).map((app) => (
                    <Row key={app.id} className={styles.selectedItemRow}>
                      <Col className={styles.selectedItemCol}>
                        <Image
                          src={app.icon_url || WorkflowIcon}
                          width={24}
                          height={24}
                          className={styles.agentIcon}
                        />
                        <Text className={styles.selectedItemText}>{app.name}</Text>
                        <Text className={styles.selectedItemTextContent}>{app.description}</Text>
                        <IconCloseTag
                          className={styles.deleteIcon}
                          style={{
                            cursor: !isEditing ? 'not-allowed' : 'pointer'
                          }}
                          onClick={() => {
                            if (!isEditing) return;
                            setWorkflowSetting(
                              workflowSetting.filter((item) => item.id !== app.id)
                            );
                          }}
                        />
                      </Col>
                    </Row>
                  ))}

                  {/* 折叠/展开按钮 */}
                  {workflowSetting.length > 3 && (
                    <div className={styles.toggleButton} onClick={() => setWorkflowExpanded(!workflowExpanded)}>
                      <div className={styles.toggleArrow}>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path
                            d={workflowExpanded ? "M12 10L8 6L4 10" : "M4 6L8 10L12 6"}
                            stroke="#86909C"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <Text className={styles.toggleText}>
                        {workflowExpanded ? '收起' : `展开剩余的 ${workflowSetting.length - 3} 个工作流`}
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </Col>
          </>
        )}

        {/* 知识库 */}
        {shouldShowConfigItem('knowledge') && (
          <>
            <RowComponent className={styles.titleRow} style={{ marginTop: 24 }}>
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.setting.addKnowledge']}
                </Text>
                <Text className={styles.subtitlePlaceholder}>
                  {locale['menu.application.info.setting.placeholder.addKnowledge']}
                </Text>
              </div>
              <Button
                className={styles.addApplication}
                onClick={() => openChooseModal('knowledge')}
                disabled={!isEditing}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer',
                }}
              >
                <Text className={styles.operateText}>
                  {locale['menu.application.template.setting.adds']}
                </Text>
              </Button>
            </RowComponent>
            <Col
              span={24}
              style={{ marginBottom: '8px' }}
              className={styles.selectedItemContainer}
            >
              {/* 渲染已选择的知识库 */}
              {knowledgeSetting.length > 0 && (
                <div className={styles.selectedItemList} style={{ position: 'relative' }}>
                  {(knowledgeExpanded ? knowledgeSetting : knowledgeSetting.slice(0, 3)).map((app) => (
                    <Row key={app.id} className={styles.selectedItemRow}>
                      <Col className={styles.selectedItemCol}>
                        <Image
                          src={app.icon_url || WorkflowIcon}
                          width={24}
                          height={24}
                          className={styles.agentIcon}
                        />
                        <Text className={styles.selectedItemText}>{app.name}</Text>
                        <Text className={styles.selectedItemTextContent}>{app.description}</Text>
                        <IconCloseTag
                          className={styles.deleteIcon}
                          style={{
                            cursor: !isEditing ? 'not-allowed' : 'pointer'
                          }}
                          onClick={() => {
                            if (!isEditing) return;
                            setKnowledgeSetting(
                              knowledgeSetting.filter((item) => item.id !== app.id)
                            );
                          }}
                        />
                      </Col>
                    </Row>
                  ))}

                  {/* 折叠/展开按钮 */}
                  {knowledgeSetting.length > 3 && (
                    <div className={styles.toggleButton} onClick={() => setKnowledgeExpanded(!knowledgeExpanded)}>
                      <div className={styles.toggleArrow}>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path
                            d={knowledgeExpanded ? "M12 10L8 6L4 10" : "M4 6L8 10L12 6"}
                            stroke="#86909C"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <Text className={styles.toggleText}>
                        {knowledgeExpanded ? '收起' : `展开剩余的 ${knowledgeSetting.length - 3} 个知识库`}
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </Col>
          </>
        )}

        {/* 系统工具 */}
        {shouldShowConfigItem('tools') && (
          <>
            <RowComponent className={styles.titleRow} style={{ marginTop: 24 }}>
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.setting.tools']}
                </Text>
                <Text className={styles.subtitlePlaceholder}>
                  {locale['menu.application.info.setting.placeholder.addTools']}
                </Text>
              </div>
              <Button
                className={styles.addApplication}
                onClick={() => openChooseModal('tools')}
                disabled={!isEditing}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer',
                }}
              >
                <Text className={styles.operateText}>
                  {locale['menu.application.template.setting.adds']}
                </Text>
              </Button>
            </RowComponent>
            <Col
              span={24}
              style={{ marginBottom: '8px' }}
              className={styles.selectedItemContainer}
            >
              {/* 渲染已选择的工具 */}
              {utilitySetting.length > 0 && (
                <div className={styles.selectedItemList} style={{ position: 'relative' }}>
                  {(toolsExpanded ? utilitySetting : utilitySetting.slice(0, 3)).map((app) => (
                    <Row key={app.id} className={styles.selectedItemRow}>
                      <Col className={styles.selectedItemCol}>
                        <Image
                          src={app.icon_url || WorkflowIcon}
                          width={24}
                          height={24}
                          className={styles.agentIcon}
                        />
                        <Text className={styles.selectedItemText}>{app.name}</Text>
                        <Text className={styles.selectedItemTextContent}>{app.description}</Text>
                        <IconCloseTag
                          className={styles.deleteIcon}
                          style={{
                            cursor: !isEditing ? 'not-allowed' : 'pointer'
                          }}
                          onClick={() => {
                            if (!isEditing) return;
                            setUtilitySetting(
                              utilitySetting.filter((item) => item.id !== app.id)
                            );
                          }}
                        />
                      </Col>
                    </Row>
                  ))}

                  {/* 折叠/展开按钮 */}
                  {utilitySetting.length > 3 && (
                    <div className={styles.toggleButton} onClick={() => setToolsExpanded(!toolsExpanded)}>
                      <div className={styles.toggleArrow}>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path
                            d={toolsExpanded ? "M12 10L8 6L4 10" : "M4 6L8 10L12 6"}
                            stroke="#86909C"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <Text className={styles.toggleText}>
                        {toolsExpanded ? '收起' : `展开剩余的 ${utilitySetting.length - 3} 个系统工具`}
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </Col>
          </>
        )}

        {/* ACP 配置 */}
        {shouldShowConfigItem('acpServer') && (
          <>
            <RowComponent className={styles.titleRow} style={{ marginTop: 24 }}>
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.setting.AcpSetting']}
                </Text>
                <Text className={styles.subtitlePlaceholder}>
                  {locale['menu.application.info.setting.placeholder.addAcpTools']}
                </Text>
              </div>
              <Button
                className={styles.addApplication}
                onClick={() => openChooseModal('acpServer')}
                disabled={!isEditing}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer',
                }}
              >
                <Text className={styles.operateText}>
                  {locale['menu.application.template.setting.adds']}
                </Text>
              </Button>
            </RowComponent>
            <Col
              span={24}
              style={{ marginBottom: '8px' }}
              className={styles.selectedItemContainer}
            >
              {/* 渲染已选择的acp工具 */}
              {acpServerSetting.length > 0 && (
                <div className={styles.selectedItemList} style={{ position: 'relative' }}>
                  {(acpServerExpanded ? acpServerSetting : acpServerSetting.slice(0, 3)).map((app) => (
                    <div key={app.id}>
                      {/* 父节点：ACP服务器 */}
                      <Row className={styles.selectedItemRow}>
                        <Col className={styles.selectedItemCol}>
                          <div
                            style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', flex: 1 }}
                            onClick={() => {
                              if (app.tools && app.tools.length > 0) {
                                setAcpServerNodeExpanded(prev => ({
                                  ...prev,
                                  [app.id]: !prev[app.id]
                                }));
                              }
                            }}
                          >
                            {/* 展开/收起箭头 */}
                            {app.tools && app.tools.length > 0 && (
                              <div
                                className={styles.expandArrow}
                                style={{
                                  marginRight: '8px',
                                  transform: acpServerNodeExpanded[app.id] ? 'rotate(90deg)' : 'rotate(0deg)',
                                  transition: 'transform 0.2s ease'
                                }}
                              >
                                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                                  <path
                                    d="M4 9L7 6L4 3"
                                    stroke="#86909C"
                                    strokeWidth="1.5"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  />
                                </svg>
                              </div>
                            )}
                            <AcpServerIconSvg style={{ width: '24px', height: '24px', marginRight: '8px' }} />
                            <Text className={styles.selectedItemText}>
                              {app.name}
                              {app.tools && app.tools.length > 0 && (
                                <span style={{
                                  width: '100px',
                                  marginLeft: '12px',
                                  fontSize: '12px',
                                  color: '#86909c',
                                  backgroundColor: '#f2f3f5',
                                  padding: '2px 6px',
                                  borderRadius: '4px'
                                }}>
                                  {app.tools.length} 个工具
                                </span>
                              )}
                            </Text>
                            <Text className={styles.selectedItemTextContent}>{app.description || ''}</Text>
                          </div>
                          <IconCloseTag
                            className={styles.deleteIcon}
                            style={{
                              cursor: !isEditing ? 'not-allowed' : 'pointer'
                            }}
                            onClick={() => {
                              if (!isEditing) return;
                              setAcpServerSetting(
                                acpServerSetting.filter((item) => item.id !== app.id)
                              );
                              // 清理展开状态
                              setAcpServerNodeExpanded(prev => {
                                const newState = { ...prev };
                                delete newState[app.id];
                                return newState;
                              });
                            }}
                          />
                        </Col>
                      </Row>

                      {/* 子节点：ACP工具 */}
                      {acpServerNodeExpanded[app.id] && app.tools && app.tools.length > 0 && (
                        <div className={styles.childNodesList} style={{ paddingLeft: '18px', marginTop: '8px' }}>
                          {app.tools.map((tool, toolIndex) => (
                            <Row key={`${app.id}-${tool.name}-${toolIndex}`} className={styles.selectedItemRow} style={{ marginBottom: '4px' }}>
                              <Col className={styles.selectedItemCol}>
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                  <div
                                    style={{
                                      width: '24px',
                                      height: '24px',
                                      marginRight: '8px',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      backgroundColor: '#f7f8fa',
                                      borderRadius: '4px'
                                    }}
                                  >
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                      <path
                                        d="M2.5 4.5L8 2L13.5 4.5L8 7L2.5 4.5Z"
                                        stroke="#86909C"
                                        strokeWidth="1.2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                      />
                                      <path
                                        d="M2.5 7.5L8 10L13.5 7.5"
                                        stroke="#86909C"
                                        strokeWidth="1.2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                      />
                                      <path
                                        d="M2.5 10.5L8 13L13.5 10.5"
                                        stroke="#86909C"
                                        strokeWidth="1.2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                      />
                                    </svg>
                                  </div>
                                  <Text className={styles.selectedChildItemText} style={{ fontSize: '14px', color: '#4e5969' }}>
                                    {tool.name}
                                  </Text>
                                  <Text className={styles.selectedChildItemTextContent}>{tool.description || ''}</Text>

                                </div>
                                <IconCloseTag
                                  className={styles.deleteIcon}
                                  style={{
                                    cursor: !isEditing ? 'not-allowed' : 'pointer',
                                    opacity: 0.7
                                  }}
                                  onClick={() => {
                                    if (!isEditing) return;
                                    // 从工具列表中移除该工具
                                    const updatedServers = acpServerSetting.map(server => {
                                      if (server.id === app.id) {
                                        const updatedTools = server.tools?.filter((t, idx) =>
                                          !(t.name === tool.name && idx === toolIndex)
                                        ) || [];
                                        return {
                                          ...server,
                                          tools: updatedTools
                                        };
                                      }
                                      return server;
                                    });
                                    setAcpServerSetting(updatedServers);
                                  }}
                                />
                              </Col>
                            </Row>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}

                  {/* 折叠/展开按钮 */}
                  {acpServerSetting.length > 3 && (
                    <div className={styles.toggleButton} onClick={() => setAcpServerExpanded(!acpServerExpanded)}>
                      <div className={styles.toggleArrow}>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path
                            d={acpServerExpanded ? "M12 10L8 6L4 10" : "M4 6L8 10L12 6"}
                            stroke="#86909C"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <Text className={styles.toggleText}>
                        {acpServerExpanded ? '收起' : `展开剩余的 ${acpServerSetting.length - 3} 个acp工具`}
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </Col>
          </>
        )}

        {/* 函数 */}
        {shouldShowConfigItem('functions') && (
          <>
            <RowComponent className={styles.titleRow} style={{ marginTop: 24 }}>
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.setting.function']}
                </Text>
                <Text className={styles.subtitlePlaceholder}>
                  {locale['menu.application.info.setting.placeholder.addFunction']}
                </Text>
              </div>
              <Button
                className={styles.addApplication}
                onClick={() => handleFunctionModalClick([])}
                disabled={!isEditing}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer',
                }}
              >
                <Text className={styles.operateText}>
                  {locale['menu.application.template.setting.adds']}
                </Text>
              </Button>
            </RowComponent>
            <Col
              span={24}
              style={{ marginBottom: '8px' }}
              className={styles.selectedItemContainer}
            >
              {/* 渲染已选择的函数 */}
              {functionSetting.length > 0 && (
                <div className={styles.selectedItemList} style={{ position: 'relative' }}>
                  {(functionsExpanded ? functionSetting : functionSetting.slice(0, 3)).map((app) => (
                    <Row key={app.id} className={styles.selectedItemRow}>
                      <Col
                        className={styles.selectedItemCol}
                      >
                        <Text className={styles.selectedItemName}
                          style={{
                            cursor: 'pointer'
                          }}
                          onClick={() => handleFunctionModalClick(app)}>{app.name}</Text>
                        <Text className={styles.selectedItemTextContent}>
                          {app.description}
                        </Text>
                        <IconCloseTag
                          className={styles.deleteIcon}
                          style={{
                            cursor: !isEditing ? 'not-allowed' : 'pointer'
                          }}
                          onClick={() => {
                            if (!isEditing) return;
                            setFunctionSetting(
                              functionSetting.filter((item) => item.name !== app.name)
                            );
                          }}
                        />
                      </Col>
                    </Row>
                  ))}

                  {/* 折叠/展开按钮 */}
                  {functionSetting.length > 3 && (
                    <div className={styles.toggleButton} onClick={() => setFunctionsExpanded(!functionsExpanded)}>
                      <div className={styles.toggleArrow}>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path
                            d={functionsExpanded ? "M12 10L8 6L4 10" : "M4 6L8 10L12 6"}
                            stroke="#86909C"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <Text className={styles.toggleText}>
                        {functionsExpanded ? '收起' : `展开剩余的 ${functionSetting.length - 3} 个函数`}
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </Col>
          </>
        )}

        {/* 响应 */}
        {shouldShowConfigItem('responses') && (
          <>
            <RowComponent className={styles.titleRow} style={{ marginTop: 24 }}>
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.setting.response']}
                </Text>
                <Text className={styles.subtitlePlaceholder}>
                  {locale['menu.application.info.setting.placeholder.addResponse']}
                </Text>
              </div>
              <Button
                className={styles.addApplication}
                onClick={() => handleResponseModalClick([])}
                disabled={!isEditing}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer',
                }}
              >
                <Text className={styles.operateText}>
                  {locale['menu.application.template.setting.adds']}
                </Text>
              </Button>
            </RowComponent>
            <Col
              span={24}
              style={{ marginBottom: '8px' }}
              className={styles.selectedItemContainer}
            >
              {/* 渲染已选择的响应 */}
              {responseSetting.length > 0 && (
                <div className={styles.selectedItemList} style={{ position: 'relative' }}>
                  {(responsesExpanded ? responseSetting : responseSetting.slice(0, 3)).map((app) => (
                    <Row key={app.intent} className={styles.selectedItemRow}>
                      <Col
                        className={styles.selectedItemCol}
                      >
                        <Text className={styles.selectedItemName}
                          style={{
                            cursor: 'pointer'
                          }}
                          onClick={() => handleResponseModalClick(app)}>
                          {app.intent}
                        </Text>
                        <Text className={styles.selectedItemTextContent}>
                          {app.content}
                        </Text>
                        <IconCloseTag
                          className={styles.deleteIcon}
                          style={{
                            cursor: !isEditing ? 'not-allowed' : 'pointer'
                          }}
                          onClick={() => {
                            if (!isEditing) return;
                            setResponseSetting(
                              responseSetting.filter((item) => item.intent !== app.intent)
                            );
                          }}
                        />
                      </Col>
                    </Row>
                  ))}

                  {/* 折叠/展开按钮 */}
                  {responseSetting.length > 3 && (
                    <div className={styles.toggleButton} onClick={() => setResponsesExpanded(!responsesExpanded)}>
                      <div className={styles.toggleArrow}>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path
                            d={responsesExpanded ? "M12 10L8 6L4 10" : "M4 6L8 10L12 6"}
                            stroke="#86909C"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <Text className={styles.toggleText}>
                        {responsesExpanded ? '收起' : `展开剩余的 ${responseSetting.length - 3} 个响应`}
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </Col>
          </>
        )}

        {/* 提示词模板 */}
        {shouldShowConfigItem('promptTemplate') && (
          <>
            <RowComponent className={styles.titleRow} style={{ marginTop: 24 }}>
              <div className={styles.titleContent}>
                <Text className={styles.subtitle}>
                  {locale['menu.application.info.setting.promptTemplate']}
                </Text>
                <Text className={styles.subtitlePlaceholder}>
                  {
                    locale[
                    'menu.application.info.setting.placeholder.addPromptTemplate'
                    ]
                  }
                </Text>
              </div>
              <Button
                className={styles.addApplication}
                onClick={() => handlePromptTemplateClick([])}
                disabled={!isEditing}
                style={{
                  opacity: !isEditing ? 0.5 : 1,
                  cursor: !isEditing ? 'not-allowed' : 'pointer',
                }}
              >
                <Text className={styles.operateText}>
                  {locale['menu.application.template.setting.adds']}
                </Text>
              </Button>
            </RowComponent>
            <Col
              span={24}
              style={{ marginBottom: '8px' }}
              className={styles.selectedItemContainer}
            >
              {/* 渲染已选择的提示词模板 */}
              {promptTemplateSetting.length > 0 && (
                <div className={styles.selectedItemList} style={{ position: 'relative' }}>
                  {(promptTemplatesExpanded ? promptTemplateSetting : promptTemplateSetting.slice(0, 3)).map((app) => (
                    <Row key={app.id} className={styles.selectedItemRow}>
                      <Col
                        className={styles.selectedItemCol}
                      >
                        <Text className={styles.selectedItemName}
                          style={{
                            cursor: 'pointer'
                          }}
                          onClick={() => handlePromptTemplateClick(app)}>{app.name}</Text>
                        <Text className={styles.selectedItemTextContent}>
                          {app.content}
                        </Text>
                        <IconCloseTag
                          className={styles.deleteIcon}
                          style={{
                            cursor: !isEditing ? 'not-allowed' : 'pointer'
                          }}
                          onClick={() => {
                            if (!isEditing) return;
                            setPromptTemplateSetting(
                              promptTemplateSetting.filter(
                                (item) => item.name !== app.name
                              )
                            );
                          }}
                        />
                      </Col>
                    </Row>
                  ))}

                  {/* 折叠/展开按钮 */}
                  {promptTemplateSetting.length > 3 && (
                    <div className={styles.toggleButton} onClick={() => setPromptTemplatesExpanded(!promptTemplatesExpanded)}>
                      <div className={styles.toggleArrow}>
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                          <path
                            d={promptTemplatesExpanded ? "M12 10L8 6L4 10" : "M4 6L8 10L12 6"}
                            stroke="#86909C"
                            strokeWidth="1.5"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <Text className={styles.toggleText}>
                        {promptTemplatesExpanded ? '收起' : `展开剩余的 ${promptTemplateSetting.length - 3} 个提示词模板`}
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </Col>
          </>
        )}
      </div>
    );
  };

  const rightContainer = () => {
    return (
      <div className={styles.rightContainer}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <div>
            <RowComponent>
              <Text className={styles.subtitle}>
                {locale['menu.application.agent.info.setting.promptTemplate']}
              </Text>
            </RowComponent>
            <RowComponent style={{ marginTop: 6 }}>
              <Text className={styles.subtitlePlaceholder}>
                {
                  locale[
                  'menu.application.agent.info.setting.placeholder.layout'
                  ]
                }
              </Text>
            </RowComponent>
          </div>
          <div>
            <ButtonComponent
              type="primary"
              style={{
                marginLeft: 12,
                opacity: !isEditing ? 0.5 : 1,
                cursor: !isEditing ? 'not-allowed' : 'pointer'
              }}
              className={styles.aiAssistantButton}
              onClick={handleAiAssistantClick}
              disabled={!isEditing}
            >
              AI助手
            </ButtonComponent>
          </div>
        </div>

        <RowComponent style={{ marginTop: 8 }}>
          <textarea
            disabled={!isEditing}
            className={styles.textarea}
            placeholder={
              locale['menu.application.agent.info.setting.placeholder.textarea']
            }
            value={instruction}
            onChange={(e) => {
              const newValue = e.target.value;
              setInstruction(newValue);
              form.setFieldsValue({ instruction: newValue });
            }}
          />
        </RowComponent>
      </div>
    );
  };

  const ModalWindows = () => {
    return (
      <div className={styles.modalWindows}>
        {/* AI助手弹窗 */}
        <Modal
          visible={aiAssistantVisible}
          onCancel={handleAiAssistantClose}
          footer={null}
          maskClosable={false}
          className="aiAssistantModel"
        >
          <div className="ai-modal-title">AI助手</div>
          <div className="ai-instruction-text">指令</div>
          <div className="ai-input-container">
            <TextArea
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              placeholder="详细描述你的需求"
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
            <div
              className="ai-send-button"
              onClick={() => {
                handleSendMessage();
              }}
            >
              <IconSend />
            </div>
          </div>
          <div className="ai-instruction-text">生成的提示词</div>
          <div className="ai-input-container">
            <TextArea
              style={{ height: 288 }}
              value={resultMessage}
              placeholder="生成的提示词将显示在这里"
              disabled={true}
            />
          </div>

          <RowComponent className={styles.operateButGroup}>
            <ButtonComponent
              type="secondary"
              style={{ marginRight: 8 }}
              className={[styles.cancelBut, styles.but]}
              onClick={handleAiAssistantClose}
            >
              <Text className={styles.text}>取消</Text>
            </ButtonComponent>
            <ButtonComponent
              type="primary"
              className={[
                styles.createBut,
                styles.but,
                !resultMessage && styles.disabled,
              ]}
              onClick={() => {
                if (resultMessage) {
                  setInstruction(resultMessage);
                  form.setFieldsValue({ instruction: resultMessage });
                  handleAiAssistantClose();
                }
              }}
              loading={loading}
            >
              <Text className={styles.text}>
                {locale['menu.application.opreate.save']}
              </Text>
            </ButtonComponent>
          </RowComponent>
        </Modal>

        {/* 函数弹窗 */}
        <FunctionModel
          currentData={selectedModalValue}
          data={functionSetting}
          visible={functionModalVisible}
          onClose={handleFunctionModalClose}
        />

        {/* 响应弹窗 */}
        <ResponseModel
          currentData={selectedModalValue}
          data={responseSetting}
          visible={responseModalVisible}
          onClose={handleResponseModalClose}
        />

        {/* 提示模板弹窗 */}
        <PromptTemplateModel
          currentData={selectedModalValue}
          data={promptTemplateSetting}
          visible={promptTemplateModalVisible}
          onClose={handlePromptTemplateModalClose}
        />
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <div
        className={styles.customContainer}
      >
        {leftContainer()}
        {rightContainer()}
        {ModalWindows()}
      </div>

      <TreeModal
        type={modalType}
        title={modalTitle}
        visible={visibleTreeModal}
        onClose={handleModalClose}
        treeData={treeData}
        checkedIds={checkedIds}
        onCheck={setCheckedIds}
        onConfirm={handleTreeConfirm}
        onSearch={(value) => handleSearch(value, modalType)}
        loading={loadingData[modalType === 'tools' ? 'utility' : modalType]}
        agentTimeSequenceCards={sequenceCardSetting}
        acpTimeSequenceCardSelections={acpTimeSequenceCardSelections}
      />
    </div>
  );
}

export default ApplicationSettings;
