import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import {
  Modal,
  Tree,
  Checkbox,
  Image,
  Input,
  Button,
  Spin,
  Select,
  Tooltip,
} from '@arco-design/web-react';
import IconClose from '@/assets/application/close.svg';
import styles from './style/index.module.less';
import IconSearch from "@/assets/application/search.svg";
import group from '@/assets/application/folderIcon.png';
import agent from '@/assets/application/agentIcon1.png';
import timeSequenceCard from '@/assets/application/timeSequenceCardIcon1.png';
import workflow from '@/assets/application/workflowIcon.png';
import IconKnowledge from '@/assets/knowledge/IconKnowledge.png';
import fileIcon from '@/assets/application/fileIcon.png';
import useLocale from '@/utils/useLocale';
import Text from '@arco-design/web-react/es/Typography/text';
import ColComponent from '@arco-design/web-react/es/Grid/col';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import ButtonComponent from '@arco-design/web-react/es/Button';
import { getAgentLabelOptions } from '@/lib/services/agent-service';

const Option = Select.Option;

const TreeModal = ({
  type,
  title,
  visible,
  onClose,
  treeData,
  checkedIds,
  onCheck,
  onConfirm,
  onSearch,
  loading,
  onHasMoreChange,
  initialHasMore = true,
}) => {
  const locale = useLocale();
  const [searchValue, setSearchValue] = useState('');
  const searchTimerRef = useRef(null);
  const [labelSearch, setLabelSearch] = useState<string>('');
  const [labelOptions, setLabelOptions] = useState<{ value: string; label: string }[]>([]);
  
  // 滚动相关状态和引用
  const treeContainerRef = useRef<HTMLDivElement>(null);
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const pageSize = 16;

  // 加载更多数据
  const loadMoreData = useCallback(async () => {
    if (isLoadingMore || !hasMore || loading) return;

    setIsLoadingMore(true);
    try {
      const nextPage = currentPage + 1;
      const searchParams = {
        name: searchValue,
        label: labelSearch,
        page: nextPage,
        size: pageSize
      };

      if (onSearch) {
        const result = await onSearch(searchParams);
        if (result && result.data && Array.isArray(result.data)) {
          setCurrentPage(nextPage);
          setHasMore(result.hasMore);
          onHasMoreChange?.(result.hasMore);
        } else {
          setHasMore(false);
          onHasMoreChange?.(false);
        }
      }
    } catch (error) {
      console.error('加载更多数据失败:', error);
      setHasMore(false);
    } finally {
      setIsLoadingMore(false);
    }
  }, [currentPage, isLoadingMore, hasMore, loading, searchValue, labelSearch, pageSize, onSearch]);

  // 滚动监听函数
  const handleScroll = useCallback(() => {
    if (!treeContainerRef.current) return;

    const container = treeContainerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;

    // 处理加载更多数据 - 当滚动到距离底部50px时触发
    const distanceToBottom = scrollHeight - scrollTop - clientHeight;
    if (!loading && !isLoadingMore && hasMore && distanceToBottom < 50) {
      loadMoreData();
    }
  }, [loading, isLoadingMore, hasMore, loadMoreData]);



  useEffect(() => {
    if (type === 'agent') {
      getAgentLabelOptions().then(res => {
        setLabelOptions(res.data.map(item => ({ value: item, label: item })));
      });
    }
  }, [type]);

  // 当模态框显示状态改变时重置搜索值
  useEffect(() => {
    if (!visible) {
      setSearchValue('');
      setLabelSearch('');
      // 重置分页状态
      setCurrentPage(1);
      setHasMore(true);
      setIsLoadingMore(false);
      if (searchTimerRef.current) {
        clearTimeout(searchTimerRef.current);
      }
    } else {
      // 模态框显示时，设置初始hasMore状态
      setHasMore(initialHasMore);
      setCurrentPage(1); // 确保页码重置
      setIsLoadingMore(false); // 确保加载状态重置
    }
  }, [visible, initialHasMore]);

  // 添加滚动监听
  useEffect(() => {
    const container = treeContainerRef.current;
    if (!container || !visible) return;

    // 使用防抖处理滚动事件，避免频繁触发
    let scrollTimeout: NodeJS.Timeout;

    const scrollListener = () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }

      scrollTimeout = setTimeout(() => {
        handleScroll();
      }, 50);
    };

    container.addEventListener('scroll', scrollListener);

    return () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      container.removeEventListener('scroll', scrollListener);
    };
  }, [handleScroll, visible]);

  // 监听Tree数据变化，检查滚动位置
  useEffect(() => {
    if (!visible || !treeData || treeData.length === 0 || loading || isLoadingMore) {
      return;
    }

    // 数据变化后短暂延迟，确保DOM已更新
    const checkScrollTimeout = setTimeout(() => {
      if (!treeContainerRef.current) return;

      const container = treeContainerRef.current;
      const { scrollTop, scrollHeight, clientHeight } = container;

      // 如果内容高度小于容器高度，且还有更多数据，自动加载
      if (scrollHeight <= clientHeight && hasMore && !isLoadingMore && !loading) {
        loadMoreData();
      }
      
      // 如果显示的数据太少（小于8条），且还有更多数据，也自动加载
      const visibleDataCount = treeData?.length || 0;
      if (visibleDataCount > 0 && visibleDataCount < 8 && hasMore && !isLoadingMore && !loading) {
        loadMoreData();
      }
    }, 300);

    return () => clearTimeout(checkScrollTimeout);
  }, [treeData, visible, hasMore, isLoadingMore, loading, loadMoreData]);

  // 处理搜索条件
  const handleSearchChange = (value) => {
    setSearchValue(value);
    // 重置分页状态
    setCurrentPage(1);
    setHasMore(true);
    setIsLoadingMore(false);

    if (searchTimerRef.current) {
      clearTimeout(searchTimerRef.current);
    }

    if (!visible) return;

    // 无论是否为空值都触发搜索
    searchTimerRef.current = setTimeout(async () => {
      if (onSearch) {
        const result = await onSearch({
          name: value,
          label: labelSearch,
          page: 1,
          size: pageSize
        });
        // 基于服务器返回的hasMore状态设置
        if (result && result.hasMore !== undefined) {
          setHasMore(result.hasMore);
          onHasMoreChange?.(result.hasMore);
        } else {
          setHasMore(false);
          onHasMoreChange?.(false);
        }
      }
    }, 300);
  };

  // 处理标签变化
  const handleLabelSearchChange = (value: string) => {
    setLabelSearch(value);
    // 重置分页状态
    setCurrentPage(1);
    setHasMore(true);
    setIsLoadingMore(false);

    if (searchTimerRef.current) {
      clearTimeout(searchTimerRef.current);
    }

    if (!visible) return;

    // 无论是否为空值都触发搜索
    searchTimerRef.current = setTimeout(async () => {
      if (onSearch) {
        const result = await onSearch({
          name: searchValue,
          label: value || '',
          page: 1,
          size: pageSize
        });
        // 基于服务器返回的hasMore状态设置
        if (result && result.hasMore !== undefined) {
          setHasMore(result.hasMore);
          onHasMoreChange?.(result.hasMore);
        } else {
          setHasMore(false);
          onHasMoreChange?.(false);
        }
      }
    }, 300);
  };

  // 查找节点工具函数
  const findNodeById = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // 计算选中叶子节点数量
  const selectedCount = useMemo(() => {
    return checkedIds.filter((id) => {
      const node = findNodeById(treeData, id);
      return node && (!node.children || node.children.length === 0);
    }).length;
  }, [checkedIds, treeData]);

  // 递归获取所有子节点ID（包括嵌套子节点）
  const getAllChildIds = (node) => {
    let ids = [];
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        ids.push(child.id);
        ids = ids.concat(getAllChildIds(child));
      });
    } else if (node.childrenData && node.childrenData.length > 0) {
      node.childrenData.forEach((child) => {
        ids.push(child.id);
        ids = ids.concat(getAllChildIds(child));
      });
    }
    return ids;
  };

  // 递归查找父节点并更新状态
  const updateParentStatus = (nodeId, newCheckedIds) => {
    const node = findNodeById(treeData, nodeId);
    if (!node?.parentId) return;

    const parent = findNodeById(treeData, node.parentId);
    if (!parent?.children) return;

    // 计算父节点的子节点选中情况
    // const allChildIds = parent.children.flatMap(child => [child.id, ...getAllChildIds(child)]);
    const allChecked = parent.children.every(
      (child) =>
        newCheckedIds.includes(child.id) &&
        getAllChildIds(child).every((cid) => newCheckedIds.includes(cid))
    );

    // const someChecked = parent.children.some(child =>
    //   newCheckedIds.includes(child.id) ||
    //   getAllChildIds(child).some(cid => newCheckedIds.includes(cid))
    // );

    // 更新父节点状态
    let updatedIds = [...newCheckedIds];
    if (allChecked && !updatedIds.includes(parent.id)) {
      updatedIds.push(parent.id);
    } else if (!allChecked && updatedIds.includes(parent.id)) {
      updatedIds = updatedIds.filter((id) => id !== parent.id);
    }

    // 递归更新祖先节点
    if (JSON.stringify(updatedIds) !== JSON.stringify(newCheckedIds)) {
      updateParentStatus(parent.id, updatedIds);
      return updatedIds;
    }
    return newCheckedIds;
  };

  // 处理选中/取消选中
  const handleCheck = (node, checked) => {
    let newCheckedIds = [...checkedIds];
    const childIds = getAllChildIds(node);

    if (checked) {
      newCheckedIds = Array.from(
        new Set([...newCheckedIds, node.id, ...childIds])
      );
    } else {
      newCheckedIds = newCheckedIds.filter(
        (id) => id !== node.id && !childIds.includes(id)
      );
    }

    // 自底向上更新父节点状态
    newCheckedIds = updateParentStatus(node.id, newCheckedIds) || newCheckedIds;

    // 自底向上更新祖节点
    if (node.level === 3) {
      const parent = findNodeById(treeData, node.parentId);
      newCheckedIds =
        updateParentStatus(parent.id, newCheckedIds) || newCheckedIds;
    }
    onCheck(Array.from(new Set(newCheckedIds)));

    onConfirm(Array.from(new Set(newCheckedIds)));
  };

  const isIndeterminate = (node) => {
    if (!node.childrenData) return false;

    const ids = getAllChildIds(node);
    if (ids.every((element) => checkedIds.includes(element))) {
      return false;
    }

    return node.childrenData.some(
      (child) =>
        checkedIds.includes(child.id) ||
        (child.childrenData && isIndeterminate(child))
    );
  };

  // 自定义节点渲染
  const renderTitle = (node) => {
    const isLeaf = !node.childrenData;
    const checked = checkedIds.includes(node.id);
    const indeterminate = isIndeterminate(node);

    const processedLabels = Array.isArray(node.labels)
      ? node.labels
        .map(item => {
          try {
            return typeof item === 'string'
              ? item.replace(/[\[\]"\\]/g, '').trim()
              : item;
          } catch {
            return item;
          }
        })
        .filter(item => typeof item === 'string' && item.length > 0)
      : [];

    return (
      <div className={styles.customTreeRow}>
        <Image
          className={styles.customIcon}
          src={
            type === 'agent'
              ? node.icon_url
                ? node.icon_url
                : agent
              : type === 'workflow'
                ? node.level === 1
                  ? group
                  : workflow
                : type === 'knowledge'
                  ? node.level === 1
                    ? IconKnowledge
                    : node.level === 2
                      ? group
                      : fileIcon
                  : timeSequenceCard
          }
        />
        <div className={styles.contentArea}>
          <div className={styles.nameArea}>
            <div className={styles.name}>{node.title}</div>
            {node.description && (
              <Tooltip
                content={node.description}
                position="top"
                style={{ maxWidth: 300 }}
              >
                <div className={styles.description}>{node.description}</div>
              </Tooltip>
            )}
            {processedLabels.length > 0 && (
              <div className={styles.labels}>
                {processedLabels.map((label, index) => (
                  <span key={index} className={styles.label}>
                    {label}
                  </span>
                ))}
              </div>
            )}
          </div>

          <div className={styles.meta}>
            <span className={styles.metaItem}>
              创建时间: {node.createdTime ? new Date(node.createdTime).toLocaleDateString() : '-'}
            </span>
          </div>
        </div>
        <div className={styles.rightArea}>
          {checked && (
            <div className={styles.addedTextArea}>
              <Text className={styles.addedText}>已添加</Text>
            </div>
          )}
          
          <div className={styles.actionArea}>
            <Button
              className={`${styles.actionButton} ${checked ? styles.remove : styles.add}`}
              onClick={() => handleCheck(node, !checked)}
            >
              {checked ? '移除' : '添加'}
            </Button>
          </div>
        </div>


      </div>
    );
  };

  return (
    <Modal
      title={title}
      visible={visible}
      onOk={() => {
        onClose();
      }}
      onCancel={onClose}
      className={styles.customModal}
      footer={null}
      closeIcon={<IconClose />}
    >

      <RowComponent className={styles.searchRow}>
        <Input
          className={styles.searchBox}
          prefix={<IconSearch />}
          placeholder={locale['menu.application.header.basic.search.placeholder']}
          value={searchValue}
          onChange={handleSearchChange}
          onClear={() => {
            setSearchValue('');
            onSearch && onSearch('');
          }}
          allowClear
        />
        <Select
          placeholder={
            locale['menu.application.agent.search.tags']
          }
          className={styles.selectBox}
          value={labelSearch || undefined}
          onChange={handleLabelSearchChange}
          allowClear
          triggerProps={{
            autoAlignPopupWidth: false,
            position: 'bl',
          }}
        >
          {labelOptions.map((option) => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      </RowComponent>

      <div style={{ borderTop: '1px solid rgba(0, 0, 0, 0.1)', margin: '16px 0' }} />

      <div className={styles.treeContainer} ref={treeContainerRef}>
        <Spin loading={loading} style={{ display: 'block', height: '100%' }}>
          {(!treeData || treeData.length === 0) ? (
            <div style={{ 
              textAlign: 'center', 
              padding: '40px 16px',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'rgba(0, 0, 0, 0.45)'
            }}>
              {searchValue ? '没有找到匹配的结果' : '暂无数据'}
            </div>
          ) : (
            <>
              <Tree
                treeData={treeData}
                renderTitle={renderTitle}
                checkable={false}
                style={{ padding: '8px' }}
              />
              {/* 加载更多提示 */}
              {isLoadingMore && (
                <div style={{ 
                  textAlign: 'center', 
                  padding: '16px',
                  color: 'rgba(0, 0, 0, 0.45)',
                  fontSize: '14px'
                }}>
                  加载中...
                </div>
              )}
              {!hasMore && treeData.length > 0 && (
                <div style={{ 
                  textAlign: 'center', 
                  padding: '16px',
                  color: 'rgba(0, 0, 0, 0.25)',
                  fontSize: '12px'
                }}>
                  已加载全部数据
                </div>
              )}
            </>
          )}
        </Spin>

      </div>
    </Modal>
  );
};

export default TreeModal;
