/**
 * 将字节转换为MB
 * @param bytes 字节数
 * @param decimals 保留小数位数，默认为2位
 * @returns 格式化后的MB字符串
 */
export function bytesToMB(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 MB';
  
  const mb = bytes / (1024 * 1024);
  return `${mb.toFixed(decimals)} MB`;
}

/**
 * 将字节转换为MB数值
 * @param bytes 字节数
 * @returns MB数值
 */
export function bytesToMBNumber(bytes: number): number {
  return bytes / (1024 * 1024);
}

/**
 * 智能格式化字节大小
 * @param bytes 字节数
 * @param decimals 保留小数位数，默认为2位
 * @returns 格式化后的大小字符串（自动选择合适的单位）
 */
export function formatBytes(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}