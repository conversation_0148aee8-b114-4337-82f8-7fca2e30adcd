import React, { useState, useEffect, useRef } from 'react';
import {
  Layout,
  Tabs,
  Message,
  Button,
  Space,
  Modal,
  Typography,
} from '@arco-design/web-react';
import styles from './style/index.module.less';
import { useSelector, useDispatch } from 'react-redux';
import { GlobalState } from '@/store/index';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import IconSmile from '@/assets/application/IconSmile.svg';
import IconTitle from '@/assets/application/IconTitle.svg';
import IconFont from '@/assets/application/IconFont.svg';
import IconTag from '@/assets/application/IconTag.svg';
import IconFile from '@/assets/application/IconFile.svg';
import IconImage from '@/assets/application/IconImage.svg';
import IconVideo from '@/assets/application/IconVideo.svg';
import IconSound from '@/assets/application/IconSound.svg';
import IconLink from '@/assets/application/IconLink.svg';
import IconClose from '@/assets/application/IconClose.svg';
import { isEqual } from 'lodash';
import TimeSequenceCardBasicInfo from './components/TimeSequenceCardBasicInfo';
import TimeSequenceCardConfiguration, {
  TimeSequenceCardConfigurationRef,
} from './components/TimeSequenceCardConfiguration';
import {
  submitTimeSequenceCardData,
  getTimeSequenceCardMetadataDetail,
  toggleTimeSequenceCardEnabled,
  validateTimeSequenceCardContentSchema,
} from '@/lib/services/timeSequenceCard-service';

// 生成默认版本号
const generateDefaultVersion = () => 'v1.0.0';

// 增加版本号
const incrementVersion = (version: string) => {
  // 如果没有版本号或格式不正确，返回默认版本号
  if (!version || !version.startsWith('v')) {
    return generateDefaultVersion();
  }

  try {
    // 解析版本号，格式为 vX.Y.Z
    const versionParts = version.substring(1).split('.');
    if (versionParts.length !== 3) {
      return generateDefaultVersion();
    }

    const major = parseInt(versionParts[0], 10);
    const minor = parseInt(versionParts[1], 10);
    const patch = parseInt(versionParts[2], 10) + 1;

    // 直接返回递增后的版本号，仅递增补丁版本
    return `v${major}.${minor}.${patch}`;
  } catch (error) {
    console.error('解析版本号失败:', error);
    return generateDefaultVersion();
  }
};

// 获取组件的标准化显示名称
const getComponentDisplayName = (type) => {
  switch (type) {
    case 'icon':
      return '图标';
    case 'title':
      return '标题';
    case 'text':
      return '文本';
    case 'tag':
      return '标签';
    case 'file':
      return '文档';
    case 'image':
      return '图片';
    case 'video':
      return '视频';
    case 'audio':
      return '音频';
    case 'link':
      return '链接';
    default:
      return '组件';
  }
};

const TimeSequenceCardPage = () => {
  const [activeTab, setActiveTab] = useState('basicInfo');
  const selectedTemplateItems = useSelector(
    (state: GlobalState) => state.selectedTemplateItems
  );
  const timeSequenceCardDetail = useSelector(
    (state: GlobalState) => state.timeSequenceCardDetail
  );
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams<{ id?: string }>();
  const locationState = location.state || {};
  const { Text } = Typography;
  const isTemplateItemsUpdating = useRef(false); // 防止重复更新的标志
  const prevSelectedTemplateItems = useRef(null);
  const cardId = params.id || locationState.id; // 优先从URL参数获取ID，如果没有则从location.state获取
  const isDetailMode = cardId ? true : locationState.mode === 'detail'; // 如果有ID参数，则为详情模式；否则根据location.state.mode判断
  const cardCode = locationState.code;
  const [isEditMode, setIsEditMode] = useState(!isDetailMode); // 编辑模式状态 - 在创建模式下默认为可编辑状态
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [disableModalVisible, setDisableModalVisible] = useState(false); // 禁用确认Modal状态
  const configurationRef = useRef<TimeSequenceCardConfigurationRef>(null); // 修正：使用正确的类型

  useEffect(() => {
    if (isDetailMode && cardId) {
      fetchTimeSequenceCardDetail(cardId);
    }

    // 在组件卸载时清除全局状态中的卡片详情数据
    return () => {
      dispatch({
        type: 'update-timeSequence-card-detail',
        payload: { timeSequenceCardDetail: null },
      });
    };
  }, [isDetailMode, cardId, dispatch]);

  // 获取时序卡片详情数据
  const fetchTimeSequenceCardDetail = async (id) => {
    try {
      setLoading(true);
      const response = await getTimeSequenceCardMetadataDetail(id);

      // 解析 content_schema，提取结构信息
      const structureItems = [];
      if (
        response.content_schema &&
        response.content_schema.properties &&
        response.content_schema.properties.parts
      ) {
        try {
          const parts = response.content_schema.properties.parts;

          // 确保parts是数组格式
          const partsArray = Array.isArray(parts) ? parts : [];

          // 遍历 parts 数组，构建结构项数组
          partsArray.forEach((part, index) => {
            if (part && part.id) {
              // 从part数据中提取组件类型
              let componentType = 'text'; // 默认类型

              if (
                part.type === 'text' &&
                part.metadata &&
                part.metadata.text_type
              ) {
                componentType = part.metadata.text_type;
              } else if (
                part.type === 'file' &&
                part.metadata &&
                part.metadata.file_type
              ) {
                // 根据file_type映射到组件类型
                switch (part.metadata.file_type) {
                  case 'image':
                    componentType = 'image';
                    break;
                  case 'video':
                    componentType = 'video';
                    break;
                  case 'audio':
                    componentType = 'audio';
                    break;
                  case 'document':
                  default:
                    componentType = 'file';
                    break;
                }
              } else if (part.type === 'data') {
                componentType = 'data';
                // 检查data类型的metadata，如果是custom则映射为custom类型
                if (part.metadata && part.metadata.data_type === 'custom') {
                  componentType = 'custom';
                }
              }

              // 根据类型获取对应的图标
              let icon;
              switch (componentType) {
                case 'icon':
                  icon = <IconSmile />;
                  break;
                case 'title':
                  icon = <IconTitle />;
                  break;
                case 'text':
                  icon = <IconFont />;
                  break;
                case 'tag':
                  icon = <IconTag />;
                  break;
                case 'file':
                  icon = <IconFile />;
                  break;
                case 'image':
                  icon = <IconImage />;
                  break;
                case 'video':
                  icon = <IconVideo />;
                  break;
                case 'audio':
                  icon = <IconSound />;
                  break;
                case 'link':
                  icon = <IconLink />;
                  break;
                default:
                  icon = <IconSmile />;
              }

              // 使用part中的id
              const itemId = part.id;

              structureItems.push({
                id: itemId,
                type: componentType,
                name: getComponentDisplayName(componentType) || componentType,
                description: `${getComponentDisplayName(componentType)}组件`,
                icon: icon,
                partData: part, // 保存完整的part数据
              });
            }
          });
        } catch (error) {
          console.error('解析 content_schema 失败:', error);
          console.warn('content_schema 结构:', response.content_schema);
        }
      }

      // 存储到Redux的完整数据
      const detailData = {
        basicInfo: {
          id: response.id,
          name: response.name,
          description: response.description,
          license: response.license,
          tags: response.tags || [],
          version: response.version,
          is_enabled: response.is_enabled,
          is_default: response.is_default,
          code: response.code,
        },
        structureItems: structureItems,
        rawResponse: response, // 保存原始响应数据
      };

      // 更新到Redux
      dispatch({
        type: 'update-timeSequence-card-detail',
        payload: { timeSequenceCardDetail: detailData },
      });
    } catch (error) {
      console.error('获取时序卡片详情失败:', error);
      Message.error(
        '获取时序卡片详情失败: ' +
          (error.response?.data?.message || error.message || '服务器错误')
      );

      // 即使出错也要确保基本的数据结构存在
      dispatch({
        type: 'update-timeSequence-card-detail',
        payload: {
          timeSequenceCardDetail: {
            basicInfo: {
              id: cardId,
              name: '',
              description: '',
              license: '',
              tags: [],
              version: 'v1.0.0',
              is_enabled: false,
              is_default: false,
              code: '',
            },
            structureItems: [],
            rawResponse: null,
          },
        },
      });
    } finally {
      setLoading(false);
    }
  };

  // 如果有选中的模板或者是空模板（空白卡片），初始化Redux状态
  useEffect(() => {
    // 避免不必要的更新
    if (isTemplateItemsUpdating.current) {
      return;
    }

    // selectedTemplateItems存在（可能是空数组或有内容）且不是详情模式
    if (selectedTemplateItems !== null && !isDetailMode) {
      // 检查是否与之前的值相同
      if (isEqual(prevSelectedTemplateItems.current, selectedTemplateItems)) {
        return;
      }

      prevSelectedTemplateItems.current = selectedTemplateItems;
      setActiveTab('basicInfo');

      // 标记正在更新
      isTemplateItemsUpdating.current = true;

      // 更新到Redux状态
      if (!timeSequenceCardDetail) {
        dispatch({
          type: 'update-timeSequence-card-detail',
          payload: {
            timeSequenceCardDetail: {
              basicInfo: {},
              structureItems: selectedTemplateItems || [], // 确保即使为null也会初始化为空数组
              rawResponse: null,
            },
          },
        });
      } else {
        // 如果已有详情数据，则只更新structureItems
        dispatch({
          type: 'update-timeSequence-card-detail',
          payload: {
            timeSequenceCardDetail: {
              ...timeSequenceCardDetail,
              structureItems: selectedTemplateItems || [], // 确保即使为null也会初始化为空数组
            },
          },
        });
      }

      // 重置标记
      setTimeout(() => {
        isTemplateItemsUpdating.current = false;
      }, 0);
    }
  }, [selectedTemplateItems, isDetailMode, dispatch, timeSequenceCardDetail]);

  // 在组件卸载时清除全局状态中的模板数据
  useEffect(() => {
    return () => {
      dispatch({
        type: 'update-selected-template-items',
        payload: { selectedTemplateItems: null },
      });
    };
  }, [dispatch]);

  // 处理从基础信息到配置的切换
  const handleNextToConfiguration = () => {
    // 不再需要在这里保存数据，由子组件通过Redux直接管理
    setActiveTab('configuration');
  };

  // 处理Tab切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 保存时提交完整的时序卡片数据
  const handleSubmitTimeSequenceCard = async () => {
    try {
      setSubmitting(true);

      // 从Redux获取最新的完整数据
      const currentDetail = timeSequenceCardDetail;
      if (!currentDetail || !currentDetail.basicInfo) {
        throw new Error('缺少必要的时序卡片数据');
      }

      // 从配置组件获取当前的JSON数据
      let contentSchema;
      try {
        contentSchema = configurationRef.current?.getCurrentJsonData();
        if (!contentSchema) {
          // 如果无法从配置组件获取数据，尝试从Redux状态构建
          console.warn('无法从配置组件获取数据，尝试从当前结构项构建');
          if (
            currentDetail.structureItems &&
            currentDetail.structureItems.length > 0
          ) {
            contentSchema = {
              type: 'object',
              properties: {
                parts: currentDetail.structureItems
                  .map((item) => item.partData)
                  .filter(Boolean),
              },
              required: ['parts'],
            };
          } else {
            throw new Error('无法获取有效的配置数据，请检查组件配置');
          }
        }
      } catch (error) {
        console.error('获取配置数据失败:', error);
        throw new Error('无法获取配置数据，请检查组件配置');
      }

      // 先进行JSON数据校验
      await validateTimeSequenceCardContentSchema(contentSchema);

      // 提取基本信息
      const basicInfoData = currentDetail.basicInfo;

      // 如果是新创建时，版本号为v1.0.0；如果是更新现有数据，递增版本号
      let versionToSave = basicInfoData.version;
      if (!isDetailMode) {
        // 新建时确保使用v1.0.0
        versionToSave = 'v1.0.0';
      } else if (isDetailMode) {
        // 详情模式下更新时，递增版本号
        versionToSave = incrementVersion(basicInfoData.version);
      }

      // 创建提交的数据对象，包含更新后的版本号
      const dataToSubmit = {
        ...basicInfoData,
        version: versionToSave,
        content_schema: contentSchema, // 使用配置组件提供的JSON数据
      };

      // 调用服务层函数提交数据（现在只需要传递基本信息，JSON数据已包含在其中）
      await submitTimeSequenceCardData(dataToSubmit, []);

      // 提交成功
      Message.success(
        basicInfoData.id ? '更新时序卡片成功' : '创建时序卡片成功'
      );

      // 清除状态
      dispatch({
        type: 'update-selected-template-items',
        payload: { selectedTemplateItems: null },
      });

      dispatch({
        type: 'update-timeSequence-card-detail',
        payload: { timeSequenceCardDetail: null },
      });

      // 返回列表页
      navigate('/timeSequenceCard');
    } catch (error) {
      console.error('保存时序卡片数据失败:', error);
      const basicInfoData = timeSequenceCardDetail?.basicInfo || {};

      // 如果是校验错误，提供更友好的错误信息
      if (
        error.message?.includes('校验失败') ||
        error.response?.status === 400
      ) {
        Message.error('时序卡片数据格式校验失败，请检查配置是否正确');
      } else {
        Message.error(
          (basicInfoData.id ? '更新' : '创建') +
            '时序卡片失败: ' +
            (error.response?.data?.message || error.message || '服务器错误')
        );
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 处理返回列表页
  const handleBackToList = () => {
    navigate('/timeSequenceCard');
  };

  // 切换编辑/保存模式
  const handleToggleEditMode = () => {
    // 如果是详情模式且当前时序卡片处于启用状态，显示禁用确认Modal
    if (
      isDetailMode &&
      !isEditMode &&
      timeSequenceCardDetail?.basicInfo?.is_enabled
    ) {
      setDisableModalVisible(true);
      return;
    }

    // 切换编辑模式
    setIsEditMode(!isEditMode);
  };

  // 处理禁用并编辑时序卡片
  const handleDisableAndEdit = async () => {
    try {
      setSubmitting(true);

      if (!timeSequenceCardDetail?.basicInfo?.code) {
        throw new Error('缺少时序卡片代码');
      }

      // 调用API禁用时序卡片
      await toggleTimeSequenceCardEnabled(
        timeSequenceCardDetail.basicInfo.code,
        false
      );

      // 更新Redux状态
      dispatch({
        type: 'update-timeSequence-card-detail',
        payload: {
          timeSequenceCardDetail: {
            ...timeSequenceCardDetail,
            basicInfo: {
              ...timeSequenceCardDetail.basicInfo,
              is_enabled: false,
            },
          },
        },
      });

      // 关闭Modal并进入编辑模式
      setDisableModalVisible(false);
      setIsEditMode(true);

      Message.success('时序卡片已禁用，可以进行编辑');
    } catch (error) {
      console.error('禁用时序卡片失败:', error);
      Message.error('禁用失败，请重试！');
    } finally {
      setSubmitting(false);
    }
  };

  // 处理取消禁用操作
  const handleCancelDisable = () => {
    setDisableModalVisible(false);
  };

  // 处理保存时序卡片数据
  const handleSaveTimeSequenceCard = async () => {
    try {
      setSubmitting(true);

      // 从Redux获取最新的完整数据
      const currentDetail = timeSequenceCardDetail;
      if (!currentDetail || !currentDetail.basicInfo) {
        throw new Error('缺少必要的时序卡片数据');
      }

      // 从配置组件获取当前的JSON数据
      let contentSchema;
      try {
        contentSchema = configurationRef.current?.getCurrentJsonData();
        if (!contentSchema) {
          // 如果无法从配置组件获取数据，尝试从Redux状态构建
          console.warn('无法从配置组件获取数据，尝试从当前结构项构建');
          if (
            currentDetail.structureItems &&
            currentDetail.structureItems.length > 0
          ) {
            contentSchema = {
              type: 'object',
              properties: {
                parts: currentDetail.structureItems
                  .map((item) => item.partData)
                  .filter(Boolean),
              },
              required: ['parts'],
            };
          } else {
            throw new Error('无法获取有效的配置数据，请检查组件配置');
          }
        }
      } catch (error) {
        console.error('获取配置数据失败:', error);
        throw new Error('无法获取配置数据，请检查组件配置');
      }

      // 先进行JSON数据校验
      await validateTimeSequenceCardContentSchema(contentSchema);

      // 提取基本信息
      const basicInfoData = currentDetail.basicInfo;

      // 如果是详情模式下的编辑保存，递增版本号
      let versionToSave = basicInfoData.version;
      if (isDetailMode && isEditMode) {
        // 递增版本号
        versionToSave = incrementVersion(basicInfoData.version);
      }

      // 创建提交的数据对象，包含更新后的版本号
      const dataToSubmit = {
        ...basicInfoData,
        version: versionToSave,
        content_schema: contentSchema, // 使用配置组件提供的JSON数据
      };

      // 调用服务层函数提交数据（现在只需要传递基本信息，JSON数据已包含在其中）
      await submitTimeSequenceCardData(dataToSubmit, []);

      // 提交成功
      Message.success('保存时序卡片成功');

      // 重置编辑模式
      setIsEditMode(false);

      // 清除状态
      dispatch({
        type: 'update-selected-template-items',
        payload: { selectedTemplateItems: null },
      });

      dispatch({
        type: 'update-timeSequence-card-detail',
        payload: { timeSequenceCardDetail: null },
      });

      // 返回列表页
      navigate('/timeSequenceCard');
    } catch (error) {
      console.error('保存时序卡片数据失败:', error);

      // 如果是校验错误，提供更友好的错误信息
      if (
        error.message?.includes('校验失败') ||
        error.response?.status === 400
      ) {
        Message.error('时序卡片数据格式校验失败，请检查配置是否正确');
      } else {
        Message.error(
          '保存失败: ' +
            (error.response?.data?.message || error.message || '服务器错误')
        );
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Layout className={styles.timeSequenceCardLayout}>
      <Tabs
        activeTab={activeTab}
        onChange={handleTabChange}
        className={styles.cardTabs}
      >
        <Tabs.TabPane key="basicInfo" title="基础信息">
          <TimeSequenceCardBasicInfo
            onNext={
              isDetailMode && !isEditMode ? null : handleNextToConfiguration
            }
            loading={loading}
            isEditMode={isEditMode}
            onToggleEditMode={handleToggleEditMode}
            isDetailMode={isDetailMode}
            onSave={handleSaveTimeSequenceCard}
            submitting={submitting}
          />
        </Tabs.TabPane>
        <Tabs.TabPane key="configuration" title="配置">
          <TimeSequenceCardConfiguration
            ref={configurationRef}
            onFinish={
              isDetailMode && !isEditMode ? null : handleSubmitTimeSequenceCard
            }
            loading={loading}
            onBack={isDetailMode ? handleBackToList : null}
            isEditMode={isEditMode}
            onToggleEditMode={handleToggleEditMode}
            isDetailMode={isDetailMode}
            onSave={handleSaveTimeSequenceCard}
            submitting={submitting}
          />
        </Tabs.TabPane>
      </Tabs>
      {/* 禁用确认Modal */}
      <Modal
        visible={disableModalVisible}
        title="禁用时序卡片"
        onCancel={handleCancelDisable}
        closeIcon={<IconClose />}
        className={styles.confirmEditingModal}
        maskClosable={false}
      >
        <div className={styles.modalContent}>
          <Text className={styles.modalContentText}>
            时序卡片正在使用无法编辑，请确认是否禁用？
          </Text>
        </div>
        <div className={styles.modalFooter}>
          <Space>
            <Button
              onClick={handleCancelDisable}
              className={styles.cancelEditingBtn}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleDisableAndEdit}
              className={styles.confirmEditingBtn}
              loading={submitting}
            >
              禁用并编辑
            </Button>
          </Space>
        </div>
      </Modal>
    </Layout>
  );
};

export default TimeSequenceCardPage;
