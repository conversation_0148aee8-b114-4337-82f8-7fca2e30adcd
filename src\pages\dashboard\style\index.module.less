@layout-text-color: #333333;

/* 在你的 CSS 文件中 */
.layout-content>* {
  margin-bottom: 16px;
}

/* 最后一个元素不需要底部间距 */
.layout-content>*:last-child {
  margin-bottom: 0;
}

.layout-content {
  height: calc(100vh - 155px);
  overflow: hidden;
  overflow-y: auto;

  /* 隐藏滚动条 - 各浏览器兼容 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */

  &::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari, Opera */
  }
}

.layout {

  .layoutheader {
    font-weight: 600;
    font-size: 20px;
    line-height: 32px;
    color: @layout-text-color;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f5f5f5;
  }

  .tabs {
    :global(.arco-tabs-header-nav::before) {
      display: none;
    }

    :global(.arco-tabs-header-ink) {
      display: none;
    }

    :global(.arco-tabs-header-title) {
      padding: 0;
      font-weight: 600;
      font-size: 20px;
      color: #a6a6a6;
      margin: 0 12px;

      &:hover {
        color: #a6a6a6;
      }
    }

    :global(.arco-tabs-header-title-active) {
      color: #333333;

      &:hover {
        color: #333333;
      }
    }

    :global(.arco-tabs-content) {
      padding-top: 16px;
    }

    :global(.arco-tabs-header-nav-line.arco-tabs-header-nav-horizontal > .arco-tabs-header-scroll .arco-tabs-header-title:first-of-type) {
      margin-left: 0;
    }

    :global(.arco-tabs-header-wrapper) {
      padding-bottom: 16px;
      border-bottom: 1px solid #f5f5f5;
    }
  }
}

.card {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  background-color: var(--color-bg-2);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  padding: 16px;

  .cardHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .HeaderTag {
      display: flex;
      align-items: center;

      .title {
        margin-left: 8px;
        font-size: 16px;
        font-weight: 500;
        color: @layout-text-color;
      }
    }
  }

  .chart {
    width: 100%;
    height: 100%;
  }
}

.row {
  margin-bottom: 16px;
}