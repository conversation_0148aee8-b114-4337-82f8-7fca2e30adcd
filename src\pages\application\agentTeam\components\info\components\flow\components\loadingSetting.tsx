import React, { useEffect, useContext } from 'react';
import { useNodesInitialized } from '@xyflow/react';
import { FlowContext } from '../context';

const LoadingSetting: React.FC = () => {
  const { setIsLoading, isInitialized, setIsInitialized } =
    useContext(FlowContext);
  // 监听所有节点是否初始化完成
  const isNodesInitialized = useNodesInitialized();

  useEffect(() => {
    if (isInitialized) return;
    if (isNodesInitialized) {
      console.log('isNodesInitialized', isNodesInitialized);
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [isNodesInitialized]);

  return (
    <div className="w-screen h-screen bg-[#FFFFFFA3] fixed z-[999]">
      <div
        className="absolute top-[40px] left-[50%] translate-x-[-50%] px-[24px] py-[8px] bg-[#fff] 
           rounded-[16px] shadow-[0px_4px_8px_0px_#00000014] border-[1px] border-[#0000000A]"
      >
        <span className="text-[#000000CC] text-[14px] font-medium leading-[24px]">
          加载中...
        </span>
      </div>
    </div>
  );
};

export default LoadingSetting;
