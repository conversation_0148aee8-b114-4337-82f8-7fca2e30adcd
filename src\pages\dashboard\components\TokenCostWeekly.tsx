import React, { useEffect, useRef, useState } from 'react';
import { Card, Spin } from '@arco-design/web-react';
import { Column } from '@antv/g2plot';
import styles from './style/TokenCostWeekly.module.less';
import IconTokenCostWeekly from '@/assets/dashboard/TokenCostWeekly.svg'

type DataType = {
    date: string;
    count: number;
};

const TokenCostWeekly = () => {
    const chartRef = useRef(null);
    const containerRef = useRef(null);
    const [loading, setLoading] = useState(false);
    const [chartHeight, setChartHeight] = useState(300); // 默认高度

    // 周数据，根据设计稿提供
    const weeklyData: DataType[] = [
        { date: '第1周 (1.1-1.7)', count: 350 },
        { date: '第2周 (1.8-1.14)', count: 450 },
        { date: '第3周 (1.15-1.21)', count: 350 },
        { date: '第4周 (1.22-1.28)', count: 450 },
        { date: '第5周 (1.29-2.4)', count: 215 },
        { date: '第6周 (2.5-2.11)', count: 515 },
        { date: '第7周 (2.12-2.18)', count: 315 },
        { date: '第8周 (2.19-2.25)', count: 450 },
    ];

    const updateChartSize = () => {
        if (!containerRef.current) return;

        // 根据容器宽度计算适当的高度
        const parent = containerRef.current.parentElement;
        const containerWidth = parent.clientWidth;

        // 使用比例来确定高度，并确保最小高度为200px，最大高度为300px
        const calculatedHeight = Math.min(Math.max(containerWidth * 0.33, 200), 300);

        setChartHeight(calculatedHeight);

        // 如果图表已经初始化，更新其尺寸
        if (chartRef.current) {
            chartRef.current.changeSize(containerWidth, calculatedHeight);
        }
    };

    const initChart = () => {
        if (!containerRef.current) return;

        // 获取容器尺寸
        const containerWidth = containerRef.current.clientWidth;

        const column = new Column(containerRef.current, {
            data: weeklyData,
            padding: [20, 10, 20, 30],
            xField: 'date',
            yField: 'count',
            color: '#4455f2',
            autoFit: true, // 开启自动适配
            xAxis: {
                grid: null,
                line: {
                    style: {
                        stroke: '#f5f5f5', 
                    },
                },
                label: {
                    style: {
                        fill: '#adadad',
                        fontSize: 12, 
                        fontWeight: 'normal', 
                    },
                    offset: 8, 
                    autoRotate: false, 
                    autoHide: true, 
                },
            },
            yAxis: {
                grid: {
                    line: {
                        style: {
                            stroke: '#f5f5f5',
                        },
                    },
                },
                // 设置Y轴的刻度线
                tickCount: 6,
                label: {
                    // 格式化Y轴标签，使用非线性刻度
                    formatter: (val) => {
                        if (val === '0') return '0';
                        if (+val <= 20) return val;
                        if (+val <= 100) return val;
                        if (+val <= 1000) return `${+val}`;
                        return `${(+val / 1000).toFixed(0)}k`;
                    },
                }
            },
            columnStyle: {
                radius: [4, 4, 4, 4], // 柱状图圆角
            },
            tooltip: {
                showMarkers: false,
                // 自定义提示框内容，显示完整的周信息
                formatter: (datum) => {
                    return {
                        name: '费用',
                        value: datum.count,
                        title: datum.date,
                    };
                }
            },
            state: {
                active: {
                    style: {
                        fill: '#6b7afb', // 悬浮时的颜色
                    },
                },
            },
            // 添加柱状图的间距设置
            columnWidthRatio: 0.6, // 增加柱状图宽度占比，使其看起来不那么拥挤
            minColumnWidth: 20, // 最小柱宽
            maxColumnWidth: 40, // 最大柱宽
            // 设置初始高度
            height: chartHeight,
        });

        chartRef.current = column;
        column.render();
    };

    useEffect(() => {
        // 初始设置
        updateChartSize();
        initChart();

        // 添加窗口大小变化监听
        const handleResize = () => {
            updateChartSize();
        };

        window.addEventListener('resize', handleResize);

        // 清理函数
        return () => {
            window.removeEventListener('resize', handleResize);
            if (chartRef.current) {
                chartRef.current.destroy();
            }
        };
    }, []);

    return (
        <Card
            title={
                <div className={styles.cardHeader}>
                    <div className={styles.HeaderTag}>
                        <IconTokenCostWeekly />
                        <div className={styles.title}>每周Token费用统计（¥）</div>
                    </div>
                </div>
            }
            className={styles.card}
        >
            <Spin loading={loading} style={{ width: '100%' }}>
                <div
                    ref={containerRef}
                    className={styles.chart}
                    style={{ height: `${chartHeight}px` }}
                />
            </Spin>
        </Card>
    );
};

export default TokenCostWeekly; 