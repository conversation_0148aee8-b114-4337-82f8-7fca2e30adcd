import React from 'react';
import { Spin } from '@arco-design/web-react';
import styles from '../style/layout.module.less';

// React 18兼容的懒加载实现，替换@loadable/component
function LoadingComponent() {
  return (
    <div className={styles.spin}>
      <Spin />
    </div>
  );
}

// 使用React.lazy替换@loadable/component
export default (loader: () => Promise<any>) => {
  const Component = React.lazy(loader);
  
  // 添加preload方法以保持API兼容性
  (Component as any).preload = loader;
  
  return Component;
};
