.knowledgeTabContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .tabs {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;

        :global(.arco-tabs-header-nav::before) {
            display: none;
        }

        :global(.arco-tabs-header-ink) {
            display: none;
        }

        :global(.arco-tabs-header-title) {
            padding: 0;
            font-weight: 600;
            font-size: 20px;
            color: #a6a6a6;
            margin: 0 12px;

            &:hover {
                color: #a6a6a6;
            }
        }

        :global(.arco-tabs-header-title-active) {
            color: #333333;

            &:hover {
                color: #333333;
            }
        }

        :global(.arco-tabs-content) {
            flex: 1;
            padding-top: 0px;
            overflow: hidden;
        }

        :global(.arco-tabs-header-nav-line.arco-tabs-header-nav-horizontal > .arco-tabs-header-scroll .arco-tabs-header-title:first-of-type) {
            margin-left: 0;
        }

        :global(.arco-tabs-header-wrapper) {
            padding-bottom: 16px;
        }

        :global(.arco-tabs-content-item) {
            height: 100%;
            overflow: hidden;
        }
    }

    .tabContent {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .aiStaffContent {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .emptyState {
            text-align: center;
            color: #a6a6a6;
            
            p {
                font-size: 16px;
                margin: 0;
            }
        }
    }

    .fileManagerContainer {
        height: 100%;
        display: flex;
        flex-direction: column;

        .fileManagerHeader {    
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;

            .backButton {
                font-weight: 600;
                font-size: 20px;
                line-height: 32px;
                color: #333333;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .fileManagerContent {
            flex: 1;
            overflow: hidden;
        }
    }
}

body[arco-theme='dark'] {
    .knowledgeTabContainer {
        .tabs {
            :global(.arco-tabs-header-title) {
                color: #78909c;

                &:hover {
                    color: #78909c;
                }
            }

            :global(.arco-tabs-header-title-active) {
                color: #ffffff;

                &:hover {
                    color: #ffffff;
                }
            }

            :global(.arco-tabs-header-wrapper) {
                border-bottom: 1px solid #2e3440;
            }
        }

        .aiStaffContent {
            .emptyState {
                color: #78909c;
            }
        }

        .fileManagerContainer {
            .fileManagerHeader {
                border-bottom: 1px solid #2e3440;

                .backButton {
                    color: #4455f2;
                    
                    &:hover {
                        background-color: #2e3440;
                        color: #5a6cf2;
                    }
                }
            }
        }
    }
}