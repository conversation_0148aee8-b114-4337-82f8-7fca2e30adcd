import {
  Dropdown,
  Flex,
  Input,
  MenuProps,
  Space,
  message,
  Button as AntdButton,
  Select,
} from 'antd';
import { useMemo, useCallback, useEffect, useState } from 'react';
import {
  DownOutlined,
  FileOutlined,
  FileTextOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { IDocumentInfo } from '../interfaces/database/document';
import {
  useSetNextDocumentStatus,
  useRunNextDocument,
  useRemoveNextDocument,
} from '../hooks/document-hooks';
import { useShowDeleteConfirm } from '../hooks/common-hooks';
import { RunningStatus } from '../constants/knowledge';

import EnableIcon from '../svg/enable.svg';
import DisableIcon from '../svg/disable.svg';
import RunIcon from '../svg/run.svg';
import CancelIcon from '../svg/cancel.svg';
import DeleteIcon from '../svg/delete.svg';
import { Button } from '@arco-design/web-react';

interface IProps {
  selectedRowKeys: string[];
  searchString: string;
  handleInputChange: React.ChangeEventHandler<HTMLInputElement>;
  showDocumentUploadModal(): void;
  showCreateModal(): void;
  setShowSelection(show: boolean): void;
  documents: IDocumentInfo[];
  total: number;
}

const DocumentToolbar = ({
  selectedRowKeys,
  searchString,
  handleInputChange,
  showDocumentUploadModal,
  showCreateModal,
  setShowSelection,
  documents,
  total,
}: IProps) => {
  const [showDeleteBtn, setShowDeleteBtn] = useState<boolean>(false);
  const { setDocumentStatus } = useSetNextDocumentStatus();
  const showDeleteConfirm = useShowDeleteConfirm();
  const { runDocumentByIds } = useRunNextDocument();
  const { removeDocument } = useRemoveNextDocument();

  const onChangeStatus = useCallback(
    (enabled: boolean) => {
      selectedRowKeys.forEach((id) => {
        setDocumentStatus({ status: enabled, documentId: id });
      });
    },
    [selectedRowKeys]
  );

  const handleEnableClick = useCallback(() => {
    onChangeStatus(true);
  }, [onChangeStatus]);

  const handleDisableClick = useCallback(() => {
    onChangeStatus(false);
  }, [onChangeStatus]);

  const runDocument = useCallback(
    (run: number) => {
      runDocumentByIds({
        documentIds: selectedRowKeys,
        run,
        shouldDelete: false,
      });
    },
    [runDocumentByIds, selectedRowKeys]
  );

  const handleRunClick = useCallback(() => {
    runDocument(1);
  }, [runDocument]);

  const handleCancelClick = useCallback(() => {
    runDocument(2);
  }, [runDocument]);

  const handleDelete = useCallback(() => {
    if (!selectedRowKeys || selectedRowKeys.length === 0) {
      return message.error('请选择要删除的文件');
    }
    const deletedKeys = selectedRowKeys.filter(
      (x) =>
        !documents
          .filter((y) => y.run === RunningStatus.RUNNING)
          .some((y) => y.id === x)
    );
    if (deletedKeys.length === 0) {
      return message.error('正在解析的文档不能被删除');
    }
    showDeleteConfirm({
      onOk: () => {
        removeDocument(deletedKeys);
      },
    });
  }, [selectedRowKeys, showDeleteConfirm, documents, removeDocument]);

  const items = useMemo<MenuProps['items']>(() => {
    return [
      // {
      //   key: '0',
      //   onClick: handleEnableClick,
      //   label: (
      //     <Flex gap={10}>
      //       <EnableIcon></EnableIcon>
      //       <b>启动</b>
      //     </Flex>
      //   ),
      // },
      // {
      //   key: '1',
      //   onClick: handleDisableClick,
      //   label: (
      //     <Flex gap={10}>
      //       <DisableIcon></DisableIcon>
      //       <b>禁用</b>
      //     </Flex>
      //   ),
      // },
      // { type: 'divider' },
      // {
      //   key: '2',
      //   onClick: handleRunClick,
      //   label: (
      //     <Flex gap={10}>
      //       <RunIcon></RunIcon>
      //       <b>解析</b>
      //     </Flex>
      //   ),
      // },
      // {
      //   key: '3',
      //   onClick: handleCancelClick,
      //   label: (
      //     <Flex gap={10}>
      //       <CancelIcon />
      //       <b>取消</b>
      //     </Flex>
      //   ),
      // },
      // { type: 'divider' },
      {
        key: '4',
        onClick: () => {
          setShowSelection(true)
          setShowDeleteBtn(true)
        },
        label: (
          <Flex gap={10}>
            <span className="size-[24px] inline-block align-middle">
              <DeleteIcon width={18} />
            </span>
            <b>删除</b>
          </Flex>
        ),
      },
    ];
  }, [
    handleDelete,
    // handleRunClick,
    // handleCancelClick,
    // handleDisableClick,
    // handleEnableClick,
  ]);

  // const actionItems: MenuProps['items'] = useMemo(() => {
  //   return [
  //     {
  //       key: '1',
  //       onClick: showDocumentUploadModal,
  //       label: (
  //         <div>
  //           <Button type="link">
  //             <Space>
  //               <FileTextOutlined />
  //               本地文件
  //             </Space>
  //           </Button>
  //         </div>
  //       ),
  //     },
  //     { type: 'divider' },
  //     {
  //       key: '3',
  //       onClick: showCreateModal,
  //       label: (
  //         <div>
  //           <Button type="link">
  //             <FileOutlined />
  //             新建空文件
  //           </Button>
  //         </div>
  //       ),
  //     },
  //   ];
  // }, [showDocumentUploadModal, showCreateModal]);

  const disabled = selectedRowKeys.length === 0;

  const handleSortChange = () => {};

  return (
    <div className="h-[32px] flex justify-between items-center my-[10px] py-[24px]">
      <Button
        className="h-[40px] p-[8px_24px] rounded-[8px]"
        size="large"
        type="primary"
        onClick={showDocumentUploadModal}
      >
        添加文件
      </Button>

      <Space>
        <span className="text-[#adadad]">共 {total} 个文件</span>
        <Select
          className="min-w-[160px]"
          defaultValue="1"
          style={{ width: 120 }}
          onChange={handleSortChange}
          options={[{ value: '1', label: '按创建时间排序' }]}
        />
        <Input
          placeholder="AI搜索..."
          value={searchString}
          style={{ width: 220 }}
          allowClear
          onChange={handleInputChange}
          prefix={<SearchOutlined />}
        />
        {showDeleteBtn ? (
          <>
            <AntdButton danger onClick={handleDelete}>
              删除
            </AntdButton>
            <AntdButton color="default" onClick={() => {
              setShowSelection(false)
              setShowDeleteBtn(false)
            }}>
              取消
            </AntdButton>
          </>
        ) : (
          <Dropdown menu={{ items }} placement="bottom" arrow={false}>
            <AntdButton>批量操作</AntdButton>
          </Dropdown>
        )}

        {/* <Dropdown menu={{ items: actionItems }} trigger={['click']}>
          <Button type="primary" icon={<PlusOutlined />}>
            新增文件
          </Button>
        </Dropdown> */}
      </Space>
    </div>
  );
};

export default DocumentToolbar;
